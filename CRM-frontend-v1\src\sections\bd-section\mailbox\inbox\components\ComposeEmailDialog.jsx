import React, { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import './compose-email.css';
import { Dropdown, Input, message, Typography, Select, Button } from 'antd';
import { Get, Post } from 'actions/API/apiActions';
import { API_URLS } from 'constants/apiUrls';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import { textEditorFormats, textEditorModules } from 'constants/textEditorModules';
import { emailTemplateTypes, toTitleCase } from 'constants/emailTemplateConstants';
import ScheduleSendDialog from './ScheduleSendDialog';

const ComposeEmailDialog = ({ id, onClose, fromEmail }) => {
  const [from, setFrom] = useState(fromEmail);
  const [to, setTo] = useState('');
  const [state, setState] = useState('open');
  const [addBcc, setAddBcc] = useState(false);
  const [openScheduleEmail, setOpenScheduleEmail] = useState(false);
  const [bcc, setBcc] = useState('');
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [plainText, setPlainText] = useState('');
  const [emailTemplate, setEmailTemplate] = useState('');
  const [emailTemplates, setEmailTemplates] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [filePreviews, setFilePreviews] = useState([]);
  const [fileUrls, setFileUrls] = useState([]);
  const [fileNames, setFileNames] = useState([]);

  const templateOptions = [
    { value: '', label: 'Select email template type' },
    ...emailTemplateTypes.map((type) => ({ value: type, label: toTitleCase(type) }))
  ];

  const items = [
    {
      label: 'Schedule Send',
      key: '1',
      icon: <Icon icon="mdi:clock-time-two-outline" width="16" height="16" />,
      onClick: () => setOpenScheduleEmail(true)
    }
  ];

  const menuProps = { items, onClick: () => {} };

  const getEmailTemplates = async () => {
    try {
      await Get(
        {},
        API_URLS.getTemplatesByType.replace(':type', emailTemplate),
        (response) => {
          setEmailTemplates(response?.data || []);
        },
        (error) => {
          message.error(error?.response?.data?.message || 'Failed to get email templates. Try refreshing the page!');
        }
      );
    } catch {
      message.error('Failed to get email templates. Try refreshing the page!');
    }
  };

  useEffect(() => {
    if (emailTemplate) {
      getEmailTemplates();
    } else {
      setEmailTemplates([]);
    }
  }, [emailTemplate]);

  const handleEmailTemplateChange = (templateId) => {
    const selectedTemplate = emailTemplates.find((tpl) => tpl.id === templateId);
    if (!selectedTemplate) {
      setSubject('');
      setContent('');
      return;
    }
    setSubject(selectedTemplate.subject);
    setContent(selectedTemplate.body);
  };
  const onContentChange = (value) => {
    setContent(value);
    const tmp = document.createElement('div');
    tmp.innerHTML = value;
    setPlainText(tmp.textContent || tmp.innerText || '');
  };

  const handleSendEmail = () => {
    if (!to || !subject || !content) {
      message.error('To, Subject and Content are all required before sending.');
      return;
    }
    Post(
      { from, to, subject, body: content, bcc: bcc ? [bcc] : [] },
      API_URLS.sendEmail,
      () => {
        message.success('Email sent successfully');
        onClose();
      },
      (error) => {
        message.error(
          Array.isArray(error?.response?.data?.message)
            ? error.response.data.message.join('\n')
            : typeof error?.response?.data?.message === 'string'
              ? error.response.data.message.split(', ').join('\n')
              : 'Failed to send email. Try again!'
        );
      }
    );
  };

  // const handleSaveToDraft = () => {
  //   Post(
  //     { email: to, type: 'DRAFT', category: '', sender: from, recipient: to, subject, body: plainText, bcc: bcc ? [bcc] : [], name: from },
  //     API_URLS.saveEmailAsDraft,
  //     () => {},
  //     error => {
  //       message.error(error?.response?.data?.message || 'Failed to save email to drafts. Try again!');
  //     }
  //   );
  // };

  // const handleScheduleSendEmail = date => {
  //   Post(
  //     { email: to, type: 'SENT', sender: from, recipient: to, subject, body: content, bcc: bcc ? [bcc] : [], schedule_date: date },
  //     API_URLS.scheduleEmail,
  //     () => {
  //       message.success('Email scheduled successfully');
  //       setOpenScheduleEmail(false);
  //     },
  //     error => {
  //       message.error(error?.response?.data?.message || 'Failed to schedule email. Try again!');
  //     }
  //   );
  // };

  const handleSendWithAttachments = () => {
    if (!to || !subject || !content) {
      message.error('To, Subject and Content are all required before sending.');
      return;
    }
    const payload = {
      from,
      to: Array.isArray(to) ? to : [to],
      subject,
      body: content,
      cc: [],
      bcc: bcc ? [bcc] : [],
      replyTo: [],
      attachments,
      fileUrls: fileUrls
    };
    Post(
      payload,
      API_URLS.sendEmailWithAttachments,
      () => {
        message.success('Email with attachment(s) sent successfully');
        onClose();
      },
      (error) => {
        message.error(
          Array.isArray(error?.response?.data?.message)
            ? error.response.data.message.join('\n')
            : typeof error?.response?.data?.message === 'string'
              ? error?.response?.data?.message.split(', ').join('\n')
              : 'Failed to send email with attachments. Try again!'
        );
      }
    );
  };

  const handleSaveToDraft = () => {
    Post(
      { email: to, type: 'DRAFT', category: '', sender: from, recipient: to, subject, body: plainText, bcc: bcc ? [bcc] : [], name: from },
      API_URLS.saveEmailAsDraft,
      () => {},
      error => {
        message.error(error?.response?.data?.message || 'Failed to save email to drafts. Try again!');
      }
    );
  };

  const handleScheduleSendEmail = date => {
    Post(
      { email: to, type: 'SENT', sender: from, recipient: to, subject, body: content, bcc: bcc ? [bcc] : [], schedule_date: date },
      API_URLS.scheduleEmail,
      () => {
        message.success('Email scheduled successfully');
        setOpenScheduleEmail(false);
      },
      error => {
        message.error(error?.response?.data?.message || 'Failed to schedule email. Try again!');
      }
    );
  };

  const onFilesChange = async (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const newFileUrls = [];
    const newFileNames = [];

    const newAttachments = [];
    const newPreviews = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const formData = new FormData();
      console.log('FILE', file);
      formData.append('file', file);

      await Post(
        formData,
        API_URLS.uploadFileToS3Bucket,
        (response) => {
          console.log('response', response);
          newFileUrls.push(response);
          newFileNames.push(file.name);
        },
        (error) => {
          message.error(error?.response?.data?.message || 'Failed to schedule email. Try again!');
        },
        { headers: { 'Content-Type': 'multipart/form-data' } }
      );

      setFileUrls((prev) => [...prev, ...newFileUrls]);
      setFileNames((prev) => [...prev, ...newFileNames]);

      const reader = new FileReader();
      await new Promise((resolve, reject) => {
        reader.onload = () => {
          const result = reader.result;
          const base64Content = result.split(',')[1];
          newAttachments.push({ filename: file.name, content: base64Content, contentType: file.type || 'application/octet-stream' });
          newPreviews.push(file.name);
          resolve();
        };
        reader.onerror = (err) => {
          reject(err);
        };
        reader.readAsDataURL(file);
      });
    }
    setAttachments(newAttachments);
    setFilePreviews(newPreviews);
  };

  const removeAttachmentAtIndex = (idx) => {
    setAttachments((prev) => prev.filter((_, i) => i !== idx));
    setFilePreviews((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
    <div
      className={`compose-dialog ${state}`}
      style={{
        bottom: state === 'minimized' ? 10 : undefined,
        right:
          state === 'minimized'
            ? id * 220 + 10
            : state === 'open' && id === 0
              ? 0
              : state === 'open' && id === 1
                ? 'calc(100% - 57%)'
                : state === 'open' && id === 2
                  ? 'calc(100% - 57%)'
                  : undefined,
        width: state !== 'minimized' && state !== 'open' ? '90vw' : undefined,
        height: state !== 'minimized' && state !== 'open' ? '80vh' : undefined,
        top: state !== 'minimized' && state !== 'open' ? 70 : undefined,
        left: state !== 'minimized' && state !== 'open' ? 60 : undefined
      }}
    >
      <div className="dialog-header">
        <span>New Message</span>
        <div>
          <Icon icon="mdi:minus" onClick={() => setState('minimized')} />
          <Icon
            icon={state === 'maximized' ? 'mdi:window-restore' : 'mdi:window-maximize'}
            onClick={() => setState(state === 'maximized' ? 'open' : 'maximized')}
          />
          <Icon
            icon="mdi:close"
            onClick={() => {
              if (from?.trim() || to?.trim() || subject?.trim() || content?.trim()) {
                handleSaveToDraft();
              }
              onClose();
            }}
          />
        </div>
      </div>
      {state !== 'minimized' && (
        <div className="dialog-body">
          <div>
            <Select
              defaultValue=""
              style={{ backgroundColor: 'white', borderRadius: 0, height: '45px', marginTop: '10px', marginBottom: '10px', width: '100%' }}
              onChange={(value) => setEmailTemplate(value)}
              options={templateOptions}
            />
          </div>
          <div>
            <Select
              defaultValue=""
              style={{ backgroundColor: 'white', borderRadius: 0, height: '45px', marginTop: '10px', marginBottom: '10px', width: '100%' }}
              onChange={(value) => handleEmailTemplateChange(value)}
              options={[
                { value: '', label: 'Select email templates' },
                ...emailTemplates.map((tpl) => ({ value: tpl.id, label: tpl.name }))
              ]}
            />
          </div>
          <Input
            placeholder="From"
            style={{ backgroundColor: 'white', borderRadius: 0, height: '45px', marginTop: '10px', marginBottom: '10px' }}
            onChange={(e) => setFrom(e.target.value)}
            value={from}
          />
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            {!addBcc && (
              <Typography.Text
                style={{ fontSize: '14px', fontWeight: 500, fontFamily: 'Poppins', cursor: 'pointer', color: '#1A84DE' }}
                onClick={() => setAddBcc(true)}
              >
                BCC
              </Typography.Text>
            )}
          </div>
          <Input
            placeholder="Recipients"
            style={{ backgroundColor: 'white', borderRadius: 0, height: '45px' }}
            onChange={(e) => setTo(e.target.value)}
            value={to}
          />
          {addBcc && (
            <Input
              placeholder="BCC"
              style={{ backgroundColor: 'white', borderRadius: 0, height: '45px', marginTop: '10px' }}
              onChange={(e) => setBcc(e.target.value)}
              value={bcc}
            />
          )}
          <Input
            placeholder="Subject"
            style={{ backgroundColor: 'white', borderRadius: 0, height: '45px', marginTop: '10px', marginBottom: '10px' }}
            onChange={(e) => setSubject(e.target.value)}
            value={subject}
          />
          <Typography.Text style={{ fontSize: '14px', fontWeight: 500, fontFamily: 'Poppins' }}>Email Content</Typography.Text>
          <div>
            <ReactQuill
              theme="snow"
              value={content}
              onChange={onContentChange}
              modules={textEditorModules}
              formats={textEditorFormats}
              style={{ height: '300px' }}
            />
          </div>
          <br />
          <br />
          <div style={{ marginTop: '30px' }}>
            <label style={{ display: 'block', fontWeight: 500, marginBottom: '4px', color: '#555' }}>Attach File(s)</label>
            <input
              accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,image/*"
              type="file"
              multiple
              onChange={onFilesChange}
              style={{ marginBottom: '8px' }}
            />
            {filePreviews.length > 0 && (
              <ul style={{ paddingLeft: '16px', marginTop: 0 }}>
                {filePreviews.map((name, idx) => (
                  <li key={idx} style={{ marginBottom: '4px' }}>
                    {name}{' '}
                    <Button type="link" style={{ padding: 0 }} onClick={() => removeAttachmentAtIndex(idx)}>
                      Remove
                    </Button>
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div style={{ marginTop: '10px' }}>
            <Dropdown.Button
              type="primary"
              menu={menuProps}
              onClick={() => {
                if (attachments.length > 0) {
                  handleSendWithAttachments();
                } else {
                  handleSendEmail();
                }
              }}
              style={{ backgroundColor: '#1A84DE', borderRadius: '6px', padding: '4px 8px', width: '110px' }}
              icon={<Icon icon="iconamoon:arrow-down-2-fill" width="16" height="16" />}
              buttonsRender={([leftButton, rightButton]) => [
                React.cloneElement(leftButton, {
                  style: {
                    backgroundColor: '#1A84DE',
                    color: 'white',
                    borderTopRightRadius: 0,
                    borderBottomRightRadius: 0,
                    borderRight: '1px solid rgba(255, 255, 255, 0.15)'
                  }
                }),
                React.cloneElement(rightButton, {
                  style: {
                    backgroundColor: '#1A84DE',
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                    padding: '4px 8px'
                  }
                })
              ]}
            >
              Send
            </Dropdown.Button>
          </div>
        </div>
      )}
      {openScheduleEmail && (
        <ScheduleSendDialog
          open={openScheduleEmail}
          onClose={() => setOpenScheduleEmail(false)}
          onSubmit={(date) => handleScheduleSendEmail(date)}
        />
      )}
    </div>
  );
};

export default ComposeEmailDialog;
