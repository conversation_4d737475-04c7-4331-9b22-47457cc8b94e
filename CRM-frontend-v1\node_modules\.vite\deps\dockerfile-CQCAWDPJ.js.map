{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/dockerfile.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\n\nvar from = \"from\";\nvar fromRegex = new RegExp(\"^(\\\\s*)\\\\b(\" + from + \")\\\\b\", \"i\");\n\nvar shells = [\"run\", \"cmd\", \"entrypoint\", \"shell\"];\nvar shellsAsArrayRegex = new RegExp(\"^(\\\\s*)(\" + shells.join('|') + \")(\\\\s+\\\\[)\", \"i\");\n\nvar expose = \"expose\";\nvar exposeRegex = new RegExp(\"^(\\\\s*)(\" + expose + \")(\\\\s+)\", \"i\");\n\nvar others = [\n  \"arg\", \"from\", \"maintainer\", \"label\", \"env\",\n  \"add\", \"copy\", \"volume\", \"user\",\n  \"workdir\", \"onbuild\", \"stopsignal\", \"healthcheck\", \"shell\"\n];\n\n// Collect all Dockerfile directives\nvar instructions = [from, expose].concat(shells).concat(others),\n    instructionRegex = \"(\" + instructions.join('|') + \")\",\n    instructionOnlyLine = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s*)(#.*)?$\", \"i\"),\n    instructionWithArguments = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s+)\", \"i\");\n\nexport const dockerFile = simpleMode({\n  start: [\n    // Block comment: This is a line starting with a comment\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: fromRegex,\n      token: [null, \"keyword\"],\n      sol: true,\n      next: \"from\"\n    },\n    // Highlight an instruction without any arguments (for convenience)\n    {\n      regex: instructionOnlyLine,\n      token: [null, \"keyword\", null, \"error\"],\n      sol: true\n    },\n    {\n      regex: shellsAsArrayRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"array\"\n    },\n    {\n      regex: exposeRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"expose\"\n    },\n    // Highlight an instruction followed by arguments\n    {\n      regex: instructionWithArguments,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"arguments\"\n    },\n    {\n      regex: /./,\n      token: null\n    }\n  ],\n  from: [\n    {\n      regex: /\\s*$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      // Line comment without instruction arguments is an error\n      regex: /(\\s*)(#.*)$/,\n      token: [null, \"error\"],\n      next: \"start\"\n    },\n    {\n      regex: /(\\s*\\S+\\s+)(as)/i,\n      token: [null, \"keyword\"],\n      next: \"start\"\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  single: [\n    {\n      regex: /(?:[^\\\\']|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  double: [\n    {\n      regex: /(?:[^\\\\\"]|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  array: [\n    {\n      regex: /\\]/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/,\n      token: \"string\"\n    }\n  ],\n  expose: [\n    {\n      regex: /\\d+$/,\n      token: \"number\",\n      next: \"start\"\n    },\n    {\n      regex: /[^\\d]+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\\d+/,\n      token: \"number\"\n    },\n    {\n      regex: /[^\\d]+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  arguments: [\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      push: \"double\"\n    },\n    {\n      regex: /'(?:[^\\\\']|\\\\.)*'?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      push: \"single\"\n    },\n    {\n      regex: /[^#\"']+[\\\\`]$/,\n      token: null\n    },\n    {\n      regex: /[^#\"']+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /[^#\"']+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n});\n\n"], "mappings": ";;;;;;AAEA,IAAI,OAAO;AACX,IAAI,YAAY,IAAI,OAAO,gBAAgB,OAAO,QAAQ,GAAG;AAE7D,IAAI,SAAS,CAAC,OAAO,OAAO,cAAc,OAAO;AACjD,IAAI,qBAAqB,IAAI,OAAO,aAAa,OAAO,KAAK,GAAG,IAAI,cAAc,GAAG;AAErF,IAAI,SAAS;AACb,IAAI,cAAc,IAAI,OAAO,aAAa,SAAS,WAAW,GAAG;AAEjE,IAAI,SAAS;AAAA,EACX;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAS;AAAA,EACtC;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAU;AAAA,EACzB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAAe;AACrD;AAGA,IAAI,eAAe,CAAC,MAAM,MAAM,EAAE,OAAO,MAAM,EAAE,OAAO,MAAM;AAA9D,IACI,mBAAmB,MAAM,aAAa,KAAK,GAAG,IAAI;AADtD,IAEI,sBAAsB,IAAI,OAAO,YAAY,mBAAmB,iBAAiB,GAAG;AAFxF,IAGI,2BAA2B,IAAI,OAAO,YAAY,mBAAmB,UAAU,GAAG;AAE/E,IAAM,aAAa,WAAW;AAAA,EACnC,OAAO;AAAA;AAAA,IAEL;AAAA,MACE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,SAAS;AAAA,MACvB,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,WAAW,MAAM,OAAO;AAAA,MACtC,KAAK;AAAA,IACP;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,WAAW,IAAI;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,WAAW,IAAI;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,WAAW,IAAI;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA;AAAA,MAEE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,OAAO;AAAA,MACrB,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO,CAAC,MAAM,SAAS;AAAA,MACvB,MAAM;AAAA,IACR;AAAA;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,MACE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA;AAAA,IAEA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF,CAAC;", "names": []}