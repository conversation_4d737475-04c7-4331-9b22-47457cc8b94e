{"version": 3, "sources": ["../../@codesandbox/nodebox/build/index.mjs", "../../@codesandbox/sandpack-client/dist/clients/node/index.mjs"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\n// ../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/pad.js\nvar require_pad = __commonJS({\n  \"../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/pad.js\"(exports, module) {\n    module.exports = function pad(num, size) {\n      var s = \"000000000\" + num;\n      return s.substr(s.length - size);\n    };\n  }\n});\n\n// ../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/fingerprint.browser.js\nvar require_fingerprint_browser = __commonJS({\n  \"../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/fingerprint.browser.js\"(exports, module) {\n    var pad = require_pad();\n    var env = typeof window === \"object\" ? window : self;\n    var globalCount = Object.keys(env).length;\n    var mimeTypesLength = navigator.mimeTypes ? navigator.mimeTypes.length : 0;\n    var clientId = pad((mimeTypesLength + navigator.userAgent.length).toString(36) + globalCount.toString(36), 4);\n    module.exports = function fingerprint() {\n      return clientId;\n    };\n  }\n});\n\n// ../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/getRandomValue.browser.js\nvar require_getRandomValue_browser = __commonJS({\n  \"../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/lib/getRandomValue.browser.js\"(exports, module) {\n    var getRandomValue;\n    var crypto = typeof window !== \"undefined\" && (window.crypto || window.msCrypto) || typeof self !== \"undefined\" && self.crypto;\n    if (crypto) {\n      lim = Math.pow(2, 32) - 1;\n      getRandomValue = function() {\n        return Math.abs(crypto.getRandomValues(new Uint32Array(1))[0] / lim);\n      };\n    } else {\n      getRandomValue = Math.random;\n    }\n    var lim;\n    module.exports = getRandomValue;\n  }\n});\n\n// ../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/index.js\nvar require_cuid = __commonJS({\n  \"../../node_modules/.pnpm/cuid@2.1.8/node_modules/cuid/index.js\"(exports, module) {\n    var fingerprint = require_fingerprint_browser();\n    var pad = require_pad();\n    var getRandomValue = require_getRandomValue_browser();\n    var c = 0;\n    var blockSize = 4;\n    var base = 36;\n    var discreteValues = Math.pow(base, blockSize);\n    function randomBlock() {\n      return pad((getRandomValue() * discreteValues << 0).toString(base), blockSize);\n    }\n    function safeCounter() {\n      c = c < discreteValues ? c : 0;\n      c++;\n      return c - 1;\n    }\n    function cuid3() {\n      var letter = \"c\", timestamp = new Date().getTime().toString(base), counter = pad(safeCounter().toString(base), blockSize), print = fingerprint(), random = randomBlock() + randomBlock();\n      return letter + timestamp + counter + print + random;\n    }\n    cuid3.slug = function slug() {\n      var date = new Date().getTime().toString(36), counter = safeCounter().toString(36).slice(-4), print = fingerprint().slice(0, 1) + fingerprint().slice(-1), random = randomBlock().slice(-2);\n      return date.slice(-2) + counter + print + random;\n    };\n    cuid3.isCuid = function isCuid(stringToCheck) {\n      if (typeof stringToCheck !== \"string\")\n        return false;\n      if (stringToCheck.startsWith(\"c\"))\n        return true;\n      return false;\n    };\n    cuid3.isSlug = function isSlug(stringToCheck) {\n      if (typeof stringToCheck !== \"string\")\n        return false;\n      var stringLength = stringToCheck.length;\n      if (stringLength >= 7 && stringLength <= 10)\n        return true;\n      return false;\n    };\n    cuid3.fingerprint = fingerprint;\n    module.exports = cuid3;\n  }\n});\n\n// ../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/createDeferredExecutor.js\nvar require_createDeferredExecutor = __commonJS({\n  \"../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/createDeferredExecutor.js\"(exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    exports.createDeferredExecutor = void 0;\n    function createDeferredExecutor() {\n      const executor = (resolve, reject) => {\n        executor.state = \"pending\";\n        executor.resolve = (data) => {\n          if (executor.state !== \"pending\") {\n            return;\n          }\n          executor.result = data;\n          const onFulfilled = (value) => {\n            executor.state = \"fulfilled\";\n            return value;\n          };\n          return resolve(data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled));\n        };\n        executor.reject = (reason) => {\n          if (executor.state !== \"pending\") {\n            return;\n          }\n          queueMicrotask(() => {\n            executor.state = \"rejected\";\n          });\n          return reject(executor.rejectionReason = reason);\n        };\n      };\n      return executor;\n    }\n    exports.createDeferredExecutor = createDeferredExecutor;\n  }\n});\n\n// ../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/DeferredPromise.js\nvar require_DeferredPromise = __commonJS({\n  \"../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/DeferredPromise.js\"(exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    exports.DeferredPromise = void 0;\n    var createDeferredExecutor_1 = require_createDeferredExecutor();\n    var DeferredPromise4 = class extends Promise {\n      #executor;\n      resolve;\n      reject;\n      constructor(executor = null) {\n        const deferredExecutor = (0, createDeferredExecutor_1.createDeferredExecutor)();\n        super((originalResolve, originalReject) => {\n          deferredExecutor(originalResolve, originalReject);\n          executor?.(deferredExecutor.resolve, deferredExecutor.reject);\n        });\n        this.#executor = deferredExecutor;\n        this.resolve = this.#executor.resolve;\n        this.reject = this.#executor.reject;\n      }\n      get state() {\n        return this.#executor.state;\n      }\n      get rejectionReason() {\n        return this.#executor.rejectionReason;\n      }\n      then(onFulfilled, onRejected) {\n        return this.#decorate(super.then(onFulfilled, onRejected));\n      }\n      catch(onRejected) {\n        return this.#decorate(super.catch(onRejected));\n      }\n      finally(onfinally) {\n        return this.#decorate(super.finally(onfinally));\n      }\n      #decorate(promise) {\n        return Object.defineProperties(promise, {\n          resolve: { configurable: true, value: this.resolve },\n          reject: { configurable: true, value: this.reject }\n        });\n      }\n    };\n    exports.DeferredPromise = DeferredPromise4;\n  }\n});\n\n// ../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/index.js\nvar require_build = __commonJS({\n  \"../../node_modules/.pnpm/@open-draft+deferred-promise@2.1.0/node_modules/@open-draft/deferred-promise/build/index.js\"(exports) {\n    \"use strict\";\n    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {\n      if (k2 === void 0)\n        k2 = k;\n      var desc = Object.getOwnPropertyDescriptor(m, k);\n      if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = { enumerable: true, get: function() {\n          return m[k];\n        } };\n      }\n      Object.defineProperty(o, k2, desc);\n    } : function(o, m, k, k2) {\n      if (k2 === void 0)\n        k2 = k;\n      o[k2] = m[k];\n    });\n    var __exportStar = exports && exports.__exportStar || function(m, exports2) {\n      for (var p in m)\n        if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports2, p))\n          __createBinding(exports2, m, p);\n    };\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    __exportStar(require_createDeferredExecutor(), exports);\n    __exportStar(require_DeferredPromise(), exports);\n  }\n});\n\n// ../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/MemoryLeakError.js\nvar require_MemoryLeakError = __commonJS({\n  \"../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/MemoryLeakError.js\"(exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    exports.MemoryLeakError = void 0;\n    var MemoryLeakError = class extends Error {\n      emitter;\n      type;\n      count;\n      constructor(emitter, type, count) {\n        super(`Possible EventEmitter memory leak detected. ${count} ${type.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`);\n        this.emitter = emitter;\n        this.type = type;\n        this.count = count;\n        this.name = \"MaxListenersExceededWarning\";\n      }\n    };\n    exports.MemoryLeakError = MemoryLeakError;\n  }\n});\n\n// ../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/Emitter.js\nvar require_Emitter = __commonJS({\n  \"../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/Emitter.js\"(exports) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    exports.Emitter = void 0;\n    var MemoryLeakError_1 = require_MemoryLeakError();\n    var _events, _maxListeners, _hasWarnedAboutPotentialMemortyLeak, _getListeners, getListeners_fn, _removeListener, removeListener_fn, _wrapOnceListener, wrapOnceListener_fn, _internalEmit, internalEmit_fn;\n    var _Emitter = class {\n      constructor() {\n        __privateAdd(this, _getListeners);\n        __privateAdd(this, _removeListener);\n        __privateAdd(this, _wrapOnceListener);\n        __privateAdd(this, _internalEmit);\n        __privateAdd(this, _events, void 0);\n        __privateAdd(this, _maxListeners, void 0);\n        __privateAdd(this, _hasWarnedAboutPotentialMemortyLeak, void 0);\n        __privateSet(this, _events, /* @__PURE__ */ new Map());\n        __privateSet(this, _maxListeners, _Emitter.defaultMaxListeners);\n        __privateSet(this, _hasWarnedAboutPotentialMemortyLeak, false);\n      }\n      static listenerCount(emitter, eventName) {\n        return emitter.listenerCount(eventName);\n      }\n      setMaxListeners(maxListeners) {\n        __privateSet(this, _maxListeners, maxListeners);\n        return this;\n      }\n      getMaxListeners() {\n        return __privateGet(this, _maxListeners);\n      }\n      eventNames() {\n        return Array.from(__privateGet(this, _events).keys());\n      }\n      emit(eventName, ...data) {\n        const listeners = __privateMethod(this, _getListeners, getListeners_fn).call(this, eventName);\n        listeners.forEach((listener) => {\n          listener.apply(this, data);\n        });\n        return listeners.length > 0;\n      }\n      addListener(eventName, listener) {\n        __privateMethod(this, _internalEmit, internalEmit_fn).call(this, \"newListener\", eventName, listener);\n        const nextListeners = __privateMethod(this, _getListeners, getListeners_fn).call(this, eventName).concat(listener);\n        __privateGet(this, _events).set(eventName, nextListeners);\n        if (__privateGet(this, _maxListeners) > 0 && this.listenerCount(eventName) > __privateGet(this, _maxListeners) && !__privateGet(this, _hasWarnedAboutPotentialMemortyLeak)) {\n          __privateSet(this, _hasWarnedAboutPotentialMemortyLeak, true);\n          const memoryLeakWarning = new MemoryLeakError_1.MemoryLeakError(this, eventName, this.listenerCount(eventName));\n          console.warn(memoryLeakWarning);\n        }\n        return this;\n      }\n      on(eventName, listener) {\n        return this.addListener(eventName, listener);\n      }\n      once(eventName, listener) {\n        return this.addListener(eventName, __privateMethod(this, _wrapOnceListener, wrapOnceListener_fn).call(this, eventName, listener));\n      }\n      prependListener(eventName, listener) {\n        const listeners = __privateMethod(this, _getListeners, getListeners_fn).call(this, eventName);\n        if (listeners.length > 0) {\n          const nextListeners = [listener].concat(listeners);\n          __privateGet(this, _events).set(eventName, nextListeners);\n        } else {\n          __privateGet(this, _events).set(eventName, listeners.concat(listener));\n        }\n        return this;\n      }\n      prependOnceListener(eventName, listener) {\n        return this.prependListener(eventName, __privateMethod(this, _wrapOnceListener, wrapOnceListener_fn).call(this, eventName, listener));\n      }\n      removeListener(eventName, listener) {\n        const listeners = __privateMethod(this, _getListeners, getListeners_fn).call(this, eventName);\n        if (listeners.length > 0) {\n          __privateMethod(this, _removeListener, removeListener_fn).call(this, listeners, listener);\n          __privateGet(this, _events).set(eventName, listeners);\n          __privateMethod(this, _internalEmit, internalEmit_fn).call(this, \"removeListener\", eventName, listener);\n        }\n        return this;\n      }\n      off(eventName, listener) {\n        return this.removeListener(eventName, listener);\n      }\n      removeAllListeners(eventName) {\n        if (eventName) {\n          __privateGet(this, _events).delete(eventName);\n        } else {\n          __privateGet(this, _events).clear();\n        }\n        return this;\n      }\n      listeners(eventName) {\n        return Array.from(__privateMethod(this, _getListeners, getListeners_fn).call(this, eventName));\n      }\n      listenerCount(eventName) {\n        return __privateMethod(this, _getListeners, getListeners_fn).call(this, eventName).length;\n      }\n      rawListeners(eventName) {\n        return this.listeners(eventName);\n      }\n    };\n    var Emitter2 = _Emitter;\n    _events = new WeakMap();\n    _maxListeners = new WeakMap();\n    _hasWarnedAboutPotentialMemortyLeak = new WeakMap();\n    _getListeners = new WeakSet();\n    getListeners_fn = function(eventName) {\n      return __privateGet(this, _events).get(eventName) || [];\n    };\n    _removeListener = new WeakSet();\n    removeListener_fn = function(listeners, listener) {\n      const index = listeners.indexOf(listener);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n      return [];\n    };\n    _wrapOnceListener = new WeakSet();\n    wrapOnceListener_fn = function(eventName, listener) {\n      const onceListener = (...data) => {\n        this.removeListener(eventName, onceListener);\n        listener.apply(this, data);\n      };\n      return onceListener;\n    };\n    _internalEmit = new WeakSet();\n    internalEmit_fn = function(internalEventName, eventName, listener) {\n      this.emit(\n        internalEventName,\n        ...[eventName, listener]\n      );\n    };\n    __publicField(Emitter2, \"defaultMaxListeners\", 10);\n    exports.Emitter = Emitter2;\n  }\n});\n\n// ../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/index.js\nvar require_lib = __commonJS({\n  \"../../node_modules/.pnpm/strict-event-emitter@0.4.3/node_modules/strict-event-emitter/lib/index.js\"(exports) {\n    \"use strict\";\n    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {\n      if (k2 === void 0)\n        k2 = k;\n      var desc = Object.getOwnPropertyDescriptor(m, k);\n      if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = { enumerable: true, get: function() {\n          return m[k];\n        } };\n      }\n      Object.defineProperty(o, k2, desc);\n    } : function(o, m, k, k2) {\n      if (k2 === void 0)\n        k2 = k;\n      o[k2] = m[k];\n    });\n    var __exportStar = exports && exports.__exportStar || function(m, exports2) {\n      for (var p in m)\n        if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports2, p))\n          __createBinding(exports2, m, p);\n    };\n    Object.defineProperty(exports, \"__esModule\", { value: true });\n    __exportStar(require_Emitter(), exports);\n    __exportStar(require_MemoryLeakError(), exports);\n  }\n});\n\n// src/messages.ts\nvar import_cuid = __toESM(require_cuid());\n\n// ../../node_modules/.pnpm/outvariant@1.4.0/node_modules/outvariant/lib/index.mjs\nvar POSITIONALS_EXP = /(%?)(%([sdjo]))/g;\nfunction serializePositional(positional, flag) {\n  switch (flag) {\n    case \"s\":\n      return positional;\n    case \"d\":\n    case \"i\":\n      return Number(positional);\n    case \"j\":\n      return JSON.stringify(positional);\n    case \"o\": {\n      if (typeof positional === \"string\") {\n        return positional;\n      }\n      const json = JSON.stringify(positional);\n      if (json === \"{}\" || json === \"[]\" || /^\\[object .+?\\]$/.test(json)) {\n        return positional;\n      }\n      return json;\n    }\n  }\n}\nfunction format(message, ...positionals) {\n  if (positionals.length === 0) {\n    return message;\n  }\n  let positionalIndex = 0;\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex];\n      const value = serializePositional(positional, flag);\n      if (!isEscaped) {\n        positionalIndex++;\n        return value;\n      }\n      return match;\n    }\n  );\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(\" \")}`;\n  }\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, \"%\");\n  return formattedMessage;\n}\nvar STACK_FRAMES_TO_IGNORE = 2;\nfunction cleanErrorStack(error) {\n  if (!error.stack) {\n    return;\n  }\n  const nextStack = error.stack.split(\"\\n\");\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE);\n  error.stack = nextStack.join(\"\\n\");\n}\nvar InvariantError = class extends Error {\n  constructor(message, ...positionals) {\n    super(message);\n    this.message = message;\n    this.name = \"Invariant Violation\";\n    this.message = format(message, ...positionals);\n    cleanErrorStack(this);\n  }\n};\nvar invariant = (predicate, message, ...positionals) => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals);\n  }\n};\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const isConstructor = ErrorConstructor.prototype.name != null;\n    const error = isConstructor ? new ErrorConstructor(format(message, positionals)) : ErrorConstructor(format(message, positionals));\n    throw error;\n  }\n};\n\n// src/messages.ts\nvar import_deferred_promise = __toESM(require_build());\n\n// src/logger.ts\nvar FLAG = window.localStorage[\"CSB_EMULATOR_DEBUG\"];\nvar DEFAULT = \"\\x1B[0m\";\nvar GREEN = \"\\x1B[32;1m\";\nvar RED = \"\\x1B[31m\";\nvar BLUE = \"\\x1B[34m\";\nvar YELLOW = \"\\x1B[33;1m\";\nvar MAGENTA = \"\\x1B[35;1m\";\nvar CYAN = \"\\x1B[36;1m\";\nvar COLOR_SCOPE = {\n  preview: YELLOW,\n  emulator: MAGENTA,\n  runtime: CYAN,\n  bridge: BLUE,\n  \"runtime:worker\": CYAN\n};\nfunction createDebug(scope) {\n  return function debug3(message, ...data) {\n    if (FLAG === \"true\") {\n      const direction = () => {\n        if (message.includes(\"sender\"))\n          return `${GREEN}sender`;\n        if (message.includes(\"receiver\"))\n          return `${RED}receiver`;\n        return \"\";\n      };\n      const cleanMessage = message.replace(/\\[.+\\]:/, \"\");\n      console.debug(`${COLOR_SCOPE[scope]}${scope}:${direction()}${DEFAULT}:${cleanMessage}`, ...data);\n    }\n  };\n}\n\n// src/messages.ts\nvar debug = createDebug(\"emulator\");\nvar MessageReceiver = class {\n  emitter;\n  senderPort = null;\n  constructor() {\n    this.emitter = new EventTarget();\n    this.waitForHandshake();\n  }\n  waitForHandshake() {\n    const handshakePromise = new import_deferred_promise.DeferredPromise();\n    const handshakeListener = (message) => {\n      const { data } = message;\n      debug(\"[message-receiver]: incoming\", message);\n      if (data.type === \"internal/handshake\") {\n        invariant(\n          message.ports.length > 0,\n          \"Failed to confirm a MessageReceiver handshake: received event has no ports\"\n        );\n        this.senderPort = message.ports[0];\n        this.addMessageListener();\n        debug(\"[message-receiver]: handshake received!\", this.senderPort);\n        this.send(\"internal/handshake/done\");\n        debug(\"[message-receiver]: finish handshake\");\n      }\n    };\n    window.addEventListener(\"message\", handshakeListener);\n    handshakePromise.then(() => {\n      window.removeEventListener(\"message\", handshakeListener);\n    });\n    window.parent.postMessage({ type: \"internal/ready\" }, \"*\");\n    return handshakePromise;\n  }\n  addMessageListener() {\n    invariant(\n      this.senderPort,\n      \"[MessageReceiver] Failed to add a message listener: sender port is not defined. Did you forget to await a handshake?\"\n    );\n    this.senderPort.onmessage = (evt) => {\n      const data = evt.data;\n      if (data.type == null) {\n        return;\n      }\n      this.emitter.dispatchEvent(\n        new MessageEvent(data.type, {\n          data: data.payload\n        })\n      );\n    };\n  }\n  on(event, listener, options) {\n    this.emitter.addEventListener(\n      event,\n      async (message) => {\n        if (!(message instanceof MessageEvent)) {\n          return;\n        }\n        const { operationId, payload } = message.data;\n        try {\n          const listenerPayload = await listener(payload);\n          this.send(\"internal/operation/done\", { operationId, listenerPayload });\n        } catch (error) {\n          if (error instanceof Error) {\n            this.send(\"internal/operation/failed\", { operationId, error });\n          }\n        }\n      },\n      options\n    );\n  }\n  send(event, ...data) {\n    invariant(\n      this.senderPort,\n      '[MessageReceiver] Failed to send a message \"%j\": sender port is not defined. Did you forget to await a handshake?',\n      event\n    );\n    const payload = data[0] || {};\n    debug('[message-receiver]: send \"%s\"', event, payload);\n    this.senderPort.postMessage({ type: event, payload });\n  }\n};\nvar MessageSender = class {\n  constructor(target) {\n    this.target = target;\n    this.emitter = new EventTarget();\n    this.channel = new MessageChannel();\n    this.receiverPort = this.channel.port1;\n    const receiverReadyPromise = new import_deferred_promise.DeferredPromise();\n    const handshakeListener = (message) => {\n      if (message.data.type === \"internal/ready\") {\n        debug(\"[message-sender]: runtime is ready\");\n        receiverReadyPromise.resolve();\n      }\n    };\n    window.addEventListener(\"message\", handshakeListener);\n    receiverReadyPromise.then(() => {\n      window.removeEventListener(\"message\", handshakeListener);\n    });\n    this.receiverReadyPromise = receiverReadyPromise;\n    this.receiverPort.onmessage = (evt) => {\n      const data = evt.data;\n      if (data.type != null) {\n        debug('[message-sender]: emitting \"%s\" event...', data.type, data.payload);\n        this.emitter.dispatchEvent(new MessageEvent(data.type, { data: data.payload }));\n      }\n    };\n  }\n  emitter;\n  channel;\n  receiverPort;\n  receiverReadyPromise;\n  async handshake() {\n    const handshakePromise = new import_deferred_promise.DeferredPromise();\n    await this.receiverReadyPromise;\n    debug(\"[message-sender]: sending handshake\");\n    this.target.postMessage(\n      {\n        type: \"internal/handshake\"\n      },\n      \"*\",\n      [this.channel.port2]\n    );\n    this.on(\"internal/handshake/done\", () => {\n      handshakePromise.resolve();\n      clearTimeout(rejectionTimeout);\n    });\n    const rejectionTimeout = setTimeout(() => {\n      handshakePromise.reject(new Error(\"MessageSender: Handshake timeout\"));\n    }, 5e3);\n    return handshakePromise;\n  }\n  on(event, listener, options) {\n    debug('[message-sender]: add listener \"%s\"', event);\n    this.emitter.addEventListener(\n      event,\n      (message) => {\n        if (message instanceof MessageEvent) {\n          listener(message);\n        }\n      },\n      options\n    );\n  }\n  off(event, listener, options) {\n    this.emitter.removeEventListener(event, listener, options);\n  }\n  async send(event, ...data) {\n    const operationPromise = new import_deferred_promise.DeferredPromise();\n    const operationId = (0, import_cuid.default)();\n    const payload = data[0] || {};\n    debug('[message-sender]: send \"%s\" (%s)', event, operationId, payload);\n    this.receiverPort.postMessage({ type: event, payload: { operationId, payload } });\n    debug('[message-sender]: adding done listener for \"%s\" (%s)', event, operationId);\n    const handleOperationDone = (doneEvent) => {\n      const { data: data2 } = doneEvent;\n      if (data2.operationId === operationId) {\n        const listenerPayload = data2.listenerPayload || {};\n        debug('[message-sender]: resolving \"%s (%s) promise!', event, operationId);\n        operationPromise.resolve({\n          ...listenerPayload,\n          operationId: data2.operationId\n        });\n      }\n    };\n    const handleOperationFailed = (failEvent) => {\n      const { data: data2 } = failEvent;\n      if (data2.operationId === operationId) {\n        debug('[message-sender]: rejecting \"%s (%s) promise!', event, operationId);\n        operationPromise.reject(data2.error);\n      }\n    };\n    this.on(\"internal/operation/done\", handleOperationDone);\n    this.on(\"internal/operation/failed\", handleOperationFailed);\n    return operationPromise.finally(() => {\n      this.emitter.removeEventListener(\"internal/operation/done\", handleOperationDone);\n      this.emitter.removeEventListener(\"internal/operation/failed\", handleOperationFailed);\n    });\n  }\n};\n\n// src/Nodebox.ts\nvar import_deferred_promise3 = __toESM(require_build());\n\n// src/modules/fs.ts\nvar import_cuid2 = __toESM(require_cuid());\nvar FileSystemApi = class {\n  constructor(channel) {\n    this.channel = channel;\n  }\n  async init(files) {\n    await this.channel.send(\"fs/init\", { files });\n  }\n  async readFile(path, encoding) {\n    const response = await this.channel.send(\"fs/readFile\", { path, encoding }).catch((error) => {\n      throw new Error(format('Failed to read file at path \"%s\"', path), { cause: error });\n    });\n    if (!response) {\n      throw new Error(\"File not found\");\n    }\n    return response.data;\n  }\n  async writeFile(path, content, options) {\n    let encoding = void 0;\n    let recursive = false;\n    if (typeof options === \"object\") {\n      encoding = options.encoding;\n      recursive = !!options.recursive;\n    } else if (typeof options === \"string\") {\n      encoding = options;\n    }\n    await this.channel.send(\"fs/writeFile\", { path, content, encoding, recursive }).catch((error) => {\n      throw new Error(format('Failed to write file at path \"%s\"', path), { cause: error });\n    });\n  }\n  async readdir(path) {\n    const response = await this.channel.send(\"fs/readdir\", { path }).catch((error) => {\n      throw new Error(format('Failed to read directory at path \"%s\"', path), { cause: error });\n    });\n    if (!response) {\n      throw new Error(\"Directory not found\");\n    }\n    return response.data;\n  }\n  async mkdir(path, options) {\n    const recursive = !!options?.recursive;\n    await this.channel.send(\"fs/mkdir\", { path, recursive }).catch((error) => {\n      throw new Error(format('Failed to make directory at path \"%s\"', path), { cause: error });\n    });\n  }\n  async stat(path) {\n    const response = await this.channel.send(\"fs/stat\", { path }).catch((error) => {\n      throw new Error(format('Failed to stat file at path \"%s\"', path), { cause: error });\n    });\n    if (!response) {\n      throw new Error(\"File not found\");\n    }\n    return response.data;\n  }\n  async rm(path, options) {\n    const { force, recursive } = options || {};\n    await this.channel.send(\"fs/rm\", { path, force, recursive }).catch((error) => {\n      throw new Error(format('Failed to remove file at path \"%s\"', path), { cause: error });\n    });\n  }\n  async watch(includes, excludes, listener) {\n    const watcherId = (0, import_cuid2.default)();\n    await this.channel.send(\"fs/watch\", { watcherId, includes, excludes });\n    this.channel.on(\"fs/watch-event\", ({ data }) => {\n      if (data.watcherId === watcherId && listener) {\n        const evt = { ...data };\n        delete evt.watcherId;\n        listener(evt);\n      }\n    });\n    return {\n      dispose: () => this.channel.send(\"fs/unwatch\", { watcherId })\n    };\n  }\n};\n\n// src/modules/shell.ts\nvar import_strict_event_emitter = __toESM(require_lib());\nvar ShellApi = class {\n  constructor(channel) {\n    this.channel = channel;\n  }\n  create() {\n    return new ShellProcess(this.channel);\n  }\n};\nvar ShellProcess = class {\n  constructor(channel) {\n    this.channel = channel;\n    this.state = \"running\";\n    this.stdout = new import_strict_event_emitter.Emitter();\n    this.stderr = new import_strict_event_emitter.Emitter();\n    this.stdin = {\n      write: (data) => {\n        if (!this.id) {\n          throw new Error(\"Failed to write to stdin, no process is currently running\");\n        }\n        return this.channel.send(\"shell/stdin\", { data, workerId: this.id });\n      }\n    };\n    this.forwardStdEvents();\n  }\n  id;\n  state;\n  stdout;\n  stderr;\n  stdin;\n  forwardStdEvents() {\n    this.channel.on(\"worker/tty\", (message) => {\n      const { data } = message;\n      if (data.workerId !== this.id) {\n        return;\n      }\n      switch (data.payload.type) {\n        case \"out\": {\n          this.stdout.emit(\"data\", data.payload.data);\n          break;\n        }\n        case \"err\": {\n          this.stderr.emit(\"data\", data.payload.data);\n          break;\n        }\n      }\n    });\n  }\n  async runCommand(command, args, options = {}) {\n    invariant(!this.id, 'Failed to run \"runCommand\" on a ShellProcess: there is already a process running.');\n    const shellInfo = await this.channel.send(\"shell/runCommand\", { command, args, options });\n    invariant(shellInfo, 'Failed to run \"runCommand\" on a ShellProcess: was not able to retrieve a running process.');\n    this.id = shellInfo.id;\n    this.state = \"running\";\n    return shellInfo;\n  }\n  async on(message, listener) {\n    switch (message) {\n      case \"progress\": {\n        this.channel.on(\"worker/progress\", ({ data }) => {\n          listener(data.status);\n        });\n        return;\n      }\n      case \"exit\": {\n        this.channel.on(\"worker/exit\", ({ data }) => {\n          if (data.workerId === this.id) {\n            listener(data.exitCode, data.error);\n          }\n        });\n        return;\n      }\n    }\n  }\n  async kill() {\n    invariant(\n      this.id,\n      'Failed to run \"kill\" on a ShellProcess: there is no process running. Did you forget to run it?'\n    );\n    this.state = \"idle\";\n    await this.channel.send(\"shell/exit\", { id: this.id }).catch((error) => {\n      throw new Error(format('Failed to kill shell with ID \"%s\"', this.id), { cause: error });\n    });\n    this.id = void 0;\n  }\n};\n\n// src/modules/preview.ts\nvar import_deferred_promise2 = __toESM(require_build());\nvar TIMEOUT = 2e4;\nvar PreviewApi = class {\n  constructor(channel) {\n    this.channel = channel;\n  }\n  async waitFor(payload, predicate, timeout = TIMEOUT) {\n    const readyPromise = new import_deferred_promise2.DeferredPromise();\n    const rejectTimeout = setTimeout(() => {\n      readyPromise.reject();\n    }, timeout);\n    const previewInformation = await this.channel.send(\"preview/get/info\", payload).catch((error) => {\n      readyPromise.reject(\n        new Error(\n          format(\n            'Failed to look up preview information for shell ID \"%s\" (port: %d)',\n            payload.sourceShellId,\n            payload.port\n          )\n        )\n      );\n    });\n    const foundPreview = previewInformation && predicate(previewInformation);\n    if (foundPreview) {\n      readyPromise.resolve({\n        url: previewInformation.url,\n        port: previewInformation.port,\n        sourceShellId: previewInformation.sourceShellId\n      });\n    }\n    this.channel.on(\"preview/port/ready\", ({ data }) => {\n      if (!foundPreview && predicate(data)) {\n        readyPromise.resolve({\n          url: data.url,\n          port: data.port,\n          sourceShellId: data.sourceShellId\n        });\n      }\n    });\n    return readyPromise.finally(() => {\n      clearTimeout(rejectTimeout);\n    });\n  }\n  async getByShellId(sourceShellId, timeout) {\n    return this.waitFor({ sourceShellId }, (data) => data.sourceShellId === sourceShellId, timeout).catch((error) => {\n      throw new Error(format('Failed to get shell by ID \"%s\"', sourceShellId), { cause: error });\n    });\n  }\n  async waitForPort(port, timeout) {\n    return this.waitFor({ port }, (data) => data.port === port, timeout).catch((error) => {\n      throw new Error(format(\"Failed to await port %d\", port), { cause: error });\n    });\n  }\n};\n\n// src/Nodebox.ts\nvar DEFAULT_RUNTIME_URL = \"https://nodebox-runtime.codesandbox.io\";\nvar debug2 = createDebug(\"emulator\");\nvar Nodebox = class {\n  constructor(options) {\n    this.options = options;\n    invariant(\n      this.options.iframe,\n      'Failed to create a Nodebox: expected \"iframe\" argument to be a reference to an <iframe> element but got %j',\n      this.options.iframe\n    );\n    this.url = this.options.runtimeUrl || DEFAULT_RUNTIME_URL;\n    this.isConnected = false;\n  }\n  channel = null;\n  isConnected;\n  url;\n  fileSystemApi = null;\n  shellApi = null;\n  previewApi = null;\n  async connect() {\n    const { iframe, cdnUrl } = this.options;\n    debug2(\"[message-sender]: Connecting to node emulator...\");\n    const connectionPromise = new import_deferred_promise3.DeferredPromise();\n    if (!this.url) {\n      connectionPromise.reject(\n        new Error(\"Nodebox URL is missing. Did you forget to provide it when creating this Nodebox instance?\")\n      );\n    }\n    invariant(\n      iframe.contentWindow,\n      \"Failed to create a MessageChannel with the Nodebox iframe: no content window found\"\n    );\n    this.channel = new MessageSender(iframe.contentWindow);\n    const frameLoadPromise = new import_deferred_promise3.DeferredPromise();\n    iframe.setAttribute(\"src\", this.url);\n    iframe.addEventListener(\n      \"load\",\n      () => {\n        frameLoadPromise.resolve();\n      },\n      { once: true }\n    );\n    iframe.addEventListener(\n      \"error\",\n      (event) => {\n        frameLoadPromise.reject(event.error);\n      },\n      { once: true }\n    );\n    await frameLoadPromise;\n    debug2(\"[message-sender]: IFrame loaded...\");\n    await this.channel.handshake();\n    debug2(\"[message-sender]: Handshake completed...\");\n    this.channel.send(\"connect\", {\n      cdnUrl\n    });\n    this.channel.on(\"runtime/ready\", () => {\n      connectionPromise.resolve();\n    });\n    return connectionPromise.then(() => {\n      debug2(\"[message-sender]: Connected to runtime...\");\n      this.isConnected = true;\n    });\n  }\n  get fs() {\n    invariant(\n      this.isConnected,\n      'Failed to access the File System API: consumer is not connected. Did you forget to run \"connect()\"?'\n    );\n    if (this.fileSystemApi) {\n      return this.fileSystemApi;\n    }\n    this.fileSystemApi = new FileSystemApi(this.channel);\n    return this.fileSystemApi;\n  }\n  get shell() {\n    invariant(\n      this.isConnected,\n      'Failed to access the Shell API: consumer is not connected. Did you forget to run \"connect()\"?'\n    );\n    if (this.shellApi) {\n      return this.shellApi;\n    }\n    this.shellApi = new ShellApi(this.channel);\n    return this.shellApi;\n  }\n  get preview() {\n    invariant(\n      this.isConnected,\n      'Failed to access the Preview API: consumer is not connected. Did you forget to run \"connect()\"?'\n    );\n    if (this.previewApi) {\n      return this.previewApi;\n    }\n    this.previewApi = new PreviewApi(this.channel);\n    return this.previewApi;\n  }\n};\n\n// src/runtime-protocol.types.ts\nvar INJECT_MESSAGE_TYPE = \"INJECT_AND_INVOKE\";\nvar PREVIEW_LOADED_MESSAGE_TYPE = \"PREVIEW_LOADED\";\nexport {\n  INJECT_MESSAGE_TYPE,\n  MessageReceiver,\n  MessageSender,\n  Nodebox,\n  PREVIEW_LOADED_MESSAGE_TYPE\n};\n", "import { _ as __awaiter, a as __generator, n as nullthrows, c as createError, g as __extends, h as __assign } from '../../utils-52664384.mjs';\nimport { INJECT_MESSAGE_TYPE, Nodebox, PREVIEW_LOADED_MESSAGE_TYPE } from '@codesandbox/nodebox';\nimport { S as SandpackClient } from '../../base-80a1f760.mjs';\nimport { c as consoleHook, f as fromBundlerFilesToFS, r as readBuffer, w as writeBuffer, g as generateRandomId, E as EventEmitter, a as getMessageFromError, b as findStartScriptPackageJson } from '../../consoleHook-59e792cb.mjs';\nimport 'outvariant';\nimport 'dequal';\n\nfunction loadPreviewIframe(iframe, url) {\n    return __awaiter(this, void 0, void 0, function () {\n        var contentWindow, TIME_OUT, MAX_MANY_TIRES, tries, timeout;\n        return __generator(this, function (_a) {\n            contentWindow = iframe.contentWindow;\n            nullthrows(contentWindow, \"Failed to await preview iframe: no content window found\");\n            TIME_OUT = 90000;\n            MAX_MANY_TIRES = 20;\n            tries = 0;\n            return [2 /*return*/, new Promise(function (resolve, reject) {\n                    var triesToSetUrl = function () {\n                        var onLoadPage = function () {\n                            clearTimeout(timeout);\n                            tries = MAX_MANY_TIRES;\n                            resolve();\n                            iframe.removeEventListener(\"load\", onLoadPage);\n                        };\n                        if (tries >= MAX_MANY_TIRES) {\n                            reject(createError(\"Could not able to connect to preview.\"));\n                            return;\n                        }\n                        iframe.setAttribute(\"src\", url);\n                        timeout = setTimeout(function () {\n                            triesToSetUrl();\n                            iframe.removeEventListener(\"load\", onLoadPage);\n                        }, TIME_OUT);\n                        tries = tries + 1;\n                        iframe.addEventListener(\"load\", onLoadPage);\n                    };\n                    iframe.addEventListener(\"error\", function () { return reject(new Error(\"Iframe error\")); });\n                    iframe.addEventListener(\"abort\", function () { return reject(new Error(\"Aborted\")); });\n                    triesToSetUrl();\n                })];\n        });\n    });\n}\nvar setPreviewIframeProperties = function (iframe, options) {\n    iframe.style.border = \"0\";\n    iframe.style.width = options.width || \"100%\";\n    iframe.style.height = options.height || \"100%\";\n    iframe.style.overflow = \"hidden\";\n    iframe.allow = \"cross-origin-isolated\";\n};\n\n/* eslint-disable @typescript-eslint/ban-ts-comment, @typescript-eslint/explicit-function-return-type, no-restricted-globals, @typescript-eslint/no-explicit-any  */\nfunction setupHistoryListeners(_a) {\n    var scope = _a.scope;\n    // @ts-ignore\n    var origHistoryProto = window.history.__proto__;\n    var historyList = [];\n    var historyPosition = 0;\n    var dispatchMessage = function (url) {\n        parent.postMessage({\n            type: \"urlchange\",\n            url: url,\n            back: historyPosition > 0,\n            forward: historyPosition < historyList.length - 1,\n            channelId: scope.channelId,\n        }, \"*\");\n    };\n    function pushHistory(url, state) {\n        // remove \"future\" locations\n        historyList.splice(historyPosition + 1);\n        historyList.push({ url: url, state: state });\n        historyPosition = historyList.length - 1;\n    }\n    Object.assign(window.history, {\n        go: function (delta) {\n            var newPos = historyPosition + delta;\n            if (newPos >= 0 && newPos <= historyList.length - 1) {\n                historyPosition = newPos;\n                var _a = historyList[historyPosition], url = _a.url, state = _a.state;\n                origHistoryProto.replaceState.call(window.history, state, \"\", url);\n                var newURL = document.location.href;\n                dispatchMessage(newURL);\n                window.dispatchEvent(new PopStateEvent(\"popstate\", { state: state }));\n            }\n        },\n        back: function () {\n            window.history.go(-1);\n        },\n        forward: function () {\n            window.history.go(1);\n        },\n        pushState: function (state, title, url) {\n            origHistoryProto.replaceState.call(window.history, state, title, url);\n            pushHistory(url, state);\n            dispatchMessage(document.location.href);\n        },\n        replaceState: function (state, title, url) {\n            origHistoryProto.replaceState.call(window.history, state, title, url);\n            historyList[historyPosition] = { state: state, url: url };\n            dispatchMessage(document.location.href);\n        },\n    });\n    function handleMessage(_a) {\n        var data = _a.data;\n        if (data.type === \"urlback\") {\n            history.back();\n        }\n        else if (data.type === \"urlforward\") {\n            history.forward();\n        }\n        else if (data.type === \"refresh\") {\n            document.location.reload();\n        }\n    }\n    window.addEventListener(\"message\", handleMessage);\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction watchResize(_a) {\n    var scope = _a.scope;\n    var lastHeight = 0;\n    function getDocumentHeight() {\n        if (typeof window === \"undefined\")\n            return 0;\n        var body = document.body;\n        var html = document.documentElement;\n        return Math.max(body.scrollHeight, body.offsetHeight, html.offsetHeight);\n    }\n    function sendResizeEvent() {\n        var height = getDocumentHeight();\n        if (lastHeight !== height) {\n            window.parent.postMessage({\n                type: \"resize\",\n                height: height,\n                codesandbox: true,\n                channelId: scope.channelId,\n            }, \"*\");\n        }\n        lastHeight = height;\n    }\n    sendResizeEvent();\n    var throttle;\n    var observer = new MutationObserver(function () {\n        if (throttle === undefined) {\n            sendResizeEvent();\n            throttle = setTimeout(function () {\n                throttle = undefined;\n            }, 300);\n        }\n    });\n    observer.observe(document, {\n        attributes: true,\n        childList: true,\n        subtree: true,\n    });\n    /**\n     * Ideally we should only use a `MutationObserver` to trigger a resize event,\n     * however, we noted that it's not 100% reliable, so we went for polling strategy as well\n     */\n    setInterval(sendResizeEvent, 300);\n}\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nvar scripts = [\n    { code: setupHistoryListeners.toString(), id: \"historyListener\" },\n    {\n        code: \"function consoleHook({ scope }) {\" + consoleHook + \"\\n};\",\n        id: \"consoleHook\",\n    },\n    { code: watchResize.toString(), id: \"watchResize\" },\n];\nvar injectScriptToIframe = function (iframe, channelId) {\n    scripts.forEach(function (_a) {\n        var _b;\n        var code = _a.code, id = _a.id;\n        var message = {\n            uid: id,\n            type: INJECT_MESSAGE_TYPE,\n            code: \"exports.activate = \".concat(code),\n            scope: { channelId: channelId },\n        };\n        (_b = iframe.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage(message, \"*\");\n    });\n};\n\n/* eslint-disable no-console,@typescript-eslint/no-explicit-any,prefer-rest-params,@typescript-eslint/explicit-module-boundary-types */\nvar SandpackNode = /** @class */ (function (_super) {\n    __extends(SandpackNode, _super);\n    function SandpackNode(selector, sandboxInfo, options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this, selector, sandboxInfo, __assign(__assign({}, options), { bundlerURL: options.bundlerURL })) || this;\n        _this._modulesCache = new Map();\n        _this.messageChannelId = generateRandomId();\n        _this._initPromise = null;\n        _this.emitter = new EventEmitter();\n        // Assign iframes\n        _this.manageIframes(selector);\n        // Init emulator\n        _this.emulator = new Nodebox({\n            iframe: _this.emulatorIframe,\n            runtimeUrl: _this.options.bundlerURL,\n        });\n        // Trigger initial compile\n        _this.updateSandbox(sandboxInfo);\n        return _this;\n    }\n    // Initialize nodebox, should only ever be called once\n    SandpackNode.prototype._init = function (files) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.emulator.connect()];\n                    case 1:\n                        _a.sent();\n                        // 2. Setup\n                        return [4 /*yield*/, this.emulator.fs.init(files)];\n                    case 2:\n                        // 2. Setup\n                        _a.sent();\n                        // 2.1 Other dependencies\n                        return [4 /*yield*/, this.globalListeners()];\n                    case 3:\n                        // 2.1 Other dependencies\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * It initializes the emulator and provide it with files, template and script to run\n     */\n    SandpackNode.prototype.compile = function (files) {\n        return __awaiter(this, void 0, void 0, function () {\n            var shellId, err_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        _a.trys.push([0, 5, , 6]);\n                        // 1. Init\n                        this.status = \"initializing\";\n                        this.dispatch({ type: \"start\", firstLoad: true });\n                        if (!this._initPromise) {\n                            this._initPromise = this._init(files);\n                        }\n                        return [4 /*yield*/, this._initPromise];\n                    case 1:\n                        _a.sent();\n                        this.dispatch({ type: \"connected\" });\n                        return [4 /*yield*/, this.createShellProcessFromTask(files)];\n                    case 2:\n                        shellId = (_a.sent()).id;\n                        // 4. Launch Preview\n                        return [4 /*yield*/, this.createPreviewURLFromId(shellId)];\n                    case 3:\n                        // 4. Launch Preview\n                        _a.sent();\n                        return [4 /*yield*/, this.setLocationURLIntoIFrame()];\n                    case 4:\n                        _a.sent();\n                        // 5. Returns to consumer\n                        this.dispatchDoneMessage();\n                        return [3 /*break*/, 6];\n                    case 5:\n                        err_1 = _a.sent();\n                        this.dispatch({\n                            type: \"action\",\n                            action: \"notification\",\n                            notificationType: \"error\",\n                            title: getMessageFromError(err_1),\n                        });\n                        this.dispatch({ type: \"done\", compilatonError: true });\n                        return [3 /*break*/, 6];\n                    case 6: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * It creates a new shell and run the starting task\n     */\n    SandpackNode.prototype.createShellProcessFromTask = function (files) {\n        return __awaiter(this, void 0, void 0, function () {\n            var packageJsonContent;\n            var _a;\n            var _this = this;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        packageJsonContent = readBuffer(files[\"/package.json\"]);\n                        this.emulatorCommand = findStartScriptPackageJson(packageJsonContent);\n                        this.emulatorShellProcess = this.emulator.shell.create();\n                        // Shell listeners\n                        return [4 /*yield*/, this.emulatorShellProcess.on(\"exit\", function (exitCode) {\n                                _this.dispatch({\n                                    type: \"action\",\n                                    action: \"notification\",\n                                    notificationType: \"error\",\n                                    title: createError(\"Error: process.exit(\".concat(exitCode, \") called.\")),\n                                });\n                            })];\n                    case 1:\n                        // Shell listeners\n                        _b.sent();\n                        return [4 /*yield*/, this.emulatorShellProcess.on(\"progress\", function (data) {\n                                var _a, _b;\n                                if (data.state === \"command_running\" ||\n                                    data.state === \"starting_command\") {\n                                    _this.dispatch({\n                                        type: \"shell/progress\",\n                                        data: __assign(__assign({}, data), { command: [\n                                                (_a = _this.emulatorCommand) === null || _a === void 0 ? void 0 : _a[0],\n                                                (_b = _this.emulatorCommand) === null || _b === void 0 ? void 0 : _b[1].join(\" \"),\n                                            ].join(\" \") }),\n                                    });\n                                    _this.status = \"installing-dependencies\";\n                                    return;\n                                }\n                                _this.dispatch({ type: \"shell/progress\", data: data });\n                            })];\n                    case 2:\n                        _b.sent();\n                        this.emulatorShellProcess.stdout.on(\"data\", function (data) {\n                            _this.dispatch({ type: \"stdout\", payload: { data: data, type: \"out\" } });\n                        });\n                        this.emulatorShellProcess.stderr.on(\"data\", function (data) {\n                            _this.dispatch({ type: \"stdout\", payload: { data: data, type: \"err\" } });\n                        });\n                        return [4 /*yield*/, (_a = this.emulatorShellProcess).runCommand.apply(_a, this.emulatorCommand)];\n                    case 3: return [2 /*return*/, _b.sent()];\n                }\n            });\n        });\n    };\n    SandpackNode.prototype.createPreviewURLFromId = function (id) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var url;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        this.iframePreviewUrl = undefined;\n                        return [4 /*yield*/, this.emulator.preview.getByShellId(id)];\n                    case 1:\n                        url = (_b.sent()).url;\n                        this.iframePreviewUrl = url + ((_a = this.options.startRoute) !== null && _a !== void 0 ? _a : \"\");\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Nodebox needs to handle two types of iframes at the same time:\n     *\n     * 1. Runtime iframe: where the emulator process runs, which is responsible\n     *    for creating the other iframes (hidden);\n     * 2. Preview iframes: any other node process that contains a PORT (public);\n     */\n    SandpackNode.prototype.manageIframes = function (selector) {\n        var _a;\n        /**\n         * Pick the preview iframe\n         */\n        if (typeof selector === \"string\") {\n            var element = document.querySelector(selector);\n            nullthrows(element, \"The element '\".concat(selector, \"' was not found\"));\n            this.iframe = document.createElement(\"iframe\");\n            element === null || element === void 0 ? void 0 : element.appendChild(this.iframe);\n        }\n        else {\n            this.iframe = selector;\n        }\n        // Set preview iframe styles\n        setPreviewIframeProperties(this.iframe, this.options);\n        nullthrows(this.iframe.parentNode, \"The given iframe does not have a parent.\");\n        /**\n         * Create the runtime iframe, which is hidden sibling\n         * from the preview one\n         */\n        this.emulatorIframe = document.createElement(\"iframe\");\n        this.emulatorIframe.classList.add(\"sp-bridge-frame\");\n        (_a = this.iframe.parentNode) === null || _a === void 0 ? void 0 : _a.appendChild(this.emulatorIframe);\n    };\n    SandpackNode.prototype.setLocationURLIntoIFrame = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.iframePreviewUrl) return [3 /*break*/, 2];\n                        return [4 /*yield*/, loadPreviewIframe(this.iframe, this.iframePreviewUrl)];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Send all messages and events to tell to the\n     * consumer that the bundler is ready without any error\n     */\n    SandpackNode.prototype.dispatchDoneMessage = function () {\n        this.status = \"done\";\n        this.dispatch({ type: \"done\", compilatonError: false });\n        if (this.iframePreviewUrl) {\n            this.dispatch({\n                type: \"urlchange\",\n                url: this.iframePreviewUrl,\n                back: false,\n                forward: false,\n            });\n        }\n    };\n    SandpackNode.prototype.globalListeners = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        window.addEventListener(\"message\", function (event) {\n                            if (event.data.type === PREVIEW_LOADED_MESSAGE_TYPE) {\n                                injectScriptToIframe(_this.iframe, _this.messageChannelId);\n                            }\n                            if (event.data.type === \"urlchange\" &&\n                                event.data.channelId === _this.messageChannelId) {\n                                _this.dispatch({\n                                    type: \"urlchange\",\n                                    url: event.data.url,\n                                    back: event.data.back,\n                                    forward: event.data.forward,\n                                });\n                            }\n                            else if (event.data.channelId === _this.messageChannelId) {\n                                _this.dispatch(event.data);\n                            }\n                        });\n                        return [4 /*yield*/, this.emulator.fs.watch([\"*\"], [\n                                \".next\",\n                                \"node_modules\",\n                                \"build\",\n                                \"dist\",\n                                \"vendor\",\n                                \".config\",\n                                \".vuepress\",\n                            ], function (message) { return __awaiter(_this, void 0, void 0, function () {\n                                var event, path, type, _a, content, newContent, err_2;\n                                return __generator(this, function (_b) {\n                                    switch (_b.label) {\n                                        case 0:\n                                            if (!message)\n                                                return [2 /*return*/];\n                                            event = message;\n                                            path = \"newPath\" in event\n                                                ? event.newPath\n                                                : \"path\" in event\n                                                    ? event.path\n                                                    : \"\";\n                                            return [4 /*yield*/, this.emulator.fs.stat(path)];\n                                        case 1:\n                                            type = (_b.sent()).type;\n                                            if (type !== \"file\")\n                                                return [2 /*return*/, null];\n                                            _b.label = 2;\n                                        case 2:\n                                            _b.trys.push([2, 10, , 11]);\n                                            _a = event.type;\n                                            switch (_a) {\n                                                case \"change\": return [3 /*break*/, 3];\n                                                case \"create\": return [3 /*break*/, 3];\n                                                case \"remove\": return [3 /*break*/, 5];\n                                                case \"rename\": return [3 /*break*/, 6];\n                                                case \"close\": return [3 /*break*/, 8];\n                                            }\n                                            return [3 /*break*/, 9];\n                                        case 3: return [4 /*yield*/, this.emulator.fs.readFile(event.path, \"utf8\")];\n                                        case 4:\n                                            content = _b.sent();\n                                            this.dispatch({\n                                                type: \"fs/change\",\n                                                path: event.path,\n                                                content: content,\n                                            });\n                                            this._modulesCache.set(event.path, writeBuffer(content));\n                                            return [3 /*break*/, 9];\n                                        case 5:\n                                            this.dispatch({\n                                                type: \"fs/remove\",\n                                                path: event.path,\n                                            });\n                                            this._modulesCache.delete(event.path);\n                                            return [3 /*break*/, 9];\n                                        case 6:\n                                            this.dispatch({\n                                                type: \"fs/remove\",\n                                                path: event.oldPath,\n                                            });\n                                            this._modulesCache.delete(event.oldPath);\n                                            return [4 /*yield*/, this.emulator.fs.readFile(event.newPath, \"utf8\")];\n                                        case 7:\n                                            newContent = _b.sent();\n                                            this.dispatch({\n                                                type: \"fs/change\",\n                                                path: event.newPath,\n                                                content: newContent,\n                                            });\n                                            this._modulesCache.set(event.newPath, writeBuffer(newContent));\n                                            return [3 /*break*/, 9];\n                                        case 8: return [3 /*break*/, 9];\n                                        case 9: return [3 /*break*/, 11];\n                                        case 10:\n                                            err_2 = _b.sent();\n                                            this.dispatch({\n                                                type: \"action\",\n                                                action: \"notification\",\n                                                notificationType: \"error\",\n                                                title: getMessageFromError(err_2),\n                                            });\n                                            return [3 /*break*/, 11];\n                                        case 11: return [2 /*return*/];\n                                    }\n                                });\n                            }); })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * PUBLIC Methods\n     */\n    SandpackNode.prototype.restartShellProcess = function () {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!(this.emulatorShellProcess && this.emulatorCommand)) return [3 /*break*/, 3];\n                        // 1. Set the loading state and clean the URL\n                        this.dispatch({ type: \"start\", firstLoad: true });\n                        this.status = \"initializing\";\n                        // 2. Exit shell\n                        return [4 /*yield*/, this.emulatorShellProcess.kill()];\n                    case 1:\n                        // 2. Exit shell\n                        _b.sent();\n                        (_a = this.iframe) === null || _a === void 0 ? void 0 : _a.removeAttribute(\"attr\");\n                        this.emulator.fs.rm(\"/node_modules/.vite\", {\n                            recursive: true,\n                            force: true,\n                        });\n                        // 3 Run command again\n                        return [4 /*yield*/, this.compile(Object.fromEntries(this._modulesCache))];\n                    case 2:\n                        // 3 Run command again\n                        _b.sent();\n                        _b.label = 3;\n                    case 3: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SandpackNode.prototype.updateSandbox = function (setup) {\n        var _this = this;\n        var _a;\n        var modules = fromBundlerFilesToFS(setup.files);\n        /**\n         * Update file changes\n         */\n        if (((_a = this.emulatorShellProcess) === null || _a === void 0 ? void 0 : _a.state) === \"running\") {\n            Object.entries(modules).forEach(function (_a) {\n                var key = _a[0], value = _a[1];\n                if (!_this._modulesCache.get(key) ||\n                    readBuffer(value) !== readBuffer(_this._modulesCache.get(key))) {\n                    _this.emulator.fs.writeFile(key, value, { recursive: true });\n                }\n            });\n            return;\n        }\n        /**\n         * Pass init files to the bundler\n         */\n        this.dispatch({\n            codesandbox: true,\n            modules: modules,\n            template: setup.template,\n            type: \"compile\",\n        });\n        /**\n         * Add modules to cache, this will ensure uniqueness changes\n         *\n         * Keep it after the compile action, in order to update the cache at the right moment\n         */\n        Object.entries(modules).forEach(function (_a) {\n            var key = _a[0], value = _a[1];\n            _this._modulesCache.set(key, writeBuffer(value));\n        });\n    };\n    SandpackNode.prototype.dispatch = function (message) {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function () {\n            var _c;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        _c = message.type;\n                        switch (_c) {\n                            case \"compile\": return [3 /*break*/, 1];\n                            case \"refresh\": return [3 /*break*/, 2];\n                            case \"urlback\": return [3 /*break*/, 4];\n                            case \"urlforward\": return [3 /*break*/, 4];\n                            case \"shell/restart\": return [3 /*break*/, 5];\n                            case \"shell/openPreview\": return [3 /*break*/, 6];\n                        }\n                        return [3 /*break*/, 7];\n                    case 1:\n                        this.compile(message.modules);\n                        return [3 /*break*/, 8];\n                    case 2: return [4 /*yield*/, this.setLocationURLIntoIFrame()];\n                    case 3:\n                        _d.sent();\n                        return [3 /*break*/, 8];\n                    case 4:\n                        (_b = (_a = this.iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage(message, \"*\");\n                        return [3 /*break*/, 8];\n                    case 5:\n                        this.restartShellProcess();\n                        return [3 /*break*/, 8];\n                    case 6:\n                        window.open(this.iframePreviewUrl, \"_blank\");\n                        return [3 /*break*/, 8];\n                    case 7:\n                        this.emitter.dispatch(message);\n                        _d.label = 8;\n                    case 8: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SandpackNode.prototype.listen = function (listener) {\n        return this.emitter.listener(listener);\n    };\n    SandpackNode.prototype.destroy = function () {\n        this.emulatorIframe.remove();\n        this.emitter.cleanup();\n    };\n    return SandpackNode;\n}(SandpackClient));\n\nexport { SandpackNode };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,WAAW,OAAO;AACtB,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO;AAC1B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,aAAa,CAAC,IAAI,QAAQ,SAAS,YAAY;AACjD,SAAO,QAAQ,GAAG,GAAG,kBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,GAAG,IAAI;AAC7F;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA,EACnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,EACzG;AACF;AACA,IAAIA,iBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,kBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,SAAO;AACT;AACA,IAAI,gBAAgB,CAAC,KAAK,QAAQ,QAAQ;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,UAAU,YAAY,GAAG;AACnC;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,WAAW;AAC1C,gBAAc,KAAK,QAAQ,yBAAyB;AACpD,SAAO,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AACnD;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,UAAU;AACzC,MAAI,OAAO,IAAI,GAAG;AAChB,UAAM,UAAU,mDAAmD;AACrE,oBAAkB,UAAU,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK;AACrE;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,OAAO,WAAW;AACjD,gBAAc,KAAK,QAAQ,wBAAwB;AACnD,WAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK;AACxD,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,KAAK,QAAQ,WAAW;AAC7C,gBAAc,KAAK,QAAQ,uBAAuB;AAClD,SAAO;AACT;AAGA,IAAI,cAAc,WAAW;AAAA,EAC3B,mEAAmE,SAAS,QAAQ;AAClF,WAAO,UAAU,SAAS,IAAI,KAAK,MAAM;AACvC,UAAI,IAAI,cAAc;AACtB,aAAO,EAAE,OAAO,EAAE,SAAS,IAAI;AAAA,IACjC;AAAA,EACF;AACF,CAAC;AAGD,IAAI,8BAA8B,WAAW;AAAA,EAC3C,mFAAmF,SAAS,QAAQ;AAClG,QAAI,MAAM,YAAY;AACtB,QAAI,MAAM,OAAO,WAAW,WAAW,SAAS;AAChD,QAAI,cAAc,OAAO,KAAK,GAAG,EAAE;AACnC,QAAI,kBAAkB,UAAU,YAAY,UAAU,UAAU,SAAS;AACzE,QAAI,WAAW,KAAK,kBAAkB,UAAU,UAAU,QAAQ,SAAS,EAAE,IAAI,YAAY,SAAS,EAAE,GAAG,CAAC;AAC5G,WAAO,UAAU,SAAS,cAAc;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAGD,IAAI,iCAAiC,WAAW;AAAA,EAC9C,sFAAsF,SAAS,QAAQ;AACrG,QAAI;AACJ,QAAI,SAAS,OAAO,WAAW,gBAAgB,OAAO,UAAU,OAAO,aAAa,OAAO,SAAS,eAAe,KAAK;AACxH,QAAI,QAAQ;AACV,YAAM,KAAK,IAAI,GAAG,EAAE,IAAI;AACxB,uBAAiB,WAAW;AAC1B,eAAO,KAAK,IAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG;AAAA,MACrE;AAAA,IACF,OAAO;AACL,uBAAiB,KAAK;AAAA,IACxB;AACA,QAAI;AACJ,WAAO,UAAU;AAAA,EACnB;AACF,CAAC;AAGD,IAAI,eAAe,WAAW;AAAA,EAC5B,iEAAiE,SAAS,QAAQ;AAChF,QAAI,cAAc,4BAA4B;AAC9C,QAAI,MAAM,YAAY;AACtB,QAAI,iBAAiB,+BAA+B;AACpD,QAAI,IAAI;AACR,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,iBAAiB,KAAK,IAAI,MAAM,SAAS;AAC7C,aAAS,cAAc;AACrB,aAAO,KAAK,eAAe,IAAI,kBAAkB,GAAG,SAAS,IAAI,GAAG,SAAS;AAAA,IAC/E;AACA,aAAS,cAAc;AACrB,UAAI,IAAI,iBAAiB,IAAI;AAC7B;AACA,aAAO,IAAI;AAAA,IACb;AACA,aAAS,QAAQ;AACf,UAAI,SAAS,KAAK,aAAY,oBAAI,KAAK,GAAE,QAAQ,EAAE,SAAS,IAAI,GAAG,UAAU,IAAI,YAAY,EAAE,SAAS,IAAI,GAAG,SAAS,GAAG,QAAQ,YAAY,GAAG,SAAS,YAAY,IAAI,YAAY;AACvL,aAAO,SAAS,YAAY,UAAU,QAAQ;AAAA,IAChD;AACA,UAAM,OAAO,SAAS,OAAO;AAC3B,UAAI,QAAO,oBAAI,KAAK,GAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,UAAU,YAAY,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,QAAQ,YAAY,EAAE,MAAM,GAAG,CAAC,IAAI,YAAY,EAAE,MAAM,EAAE,GAAG,SAAS,YAAY,EAAE,MAAM,EAAE;AAC1L,aAAO,KAAK,MAAM,EAAE,IAAI,UAAU,QAAQ;AAAA,IAC5C;AACA,UAAM,SAAS,SAAS,OAAO,eAAe;AAC5C,UAAI,OAAO,kBAAkB;AAC3B,eAAO;AACT,UAAI,cAAc,WAAW,GAAG;AAC9B,eAAO;AACT,aAAO;AAAA,IACT;AACA,UAAM,SAAS,SAAS,OAAO,eAAe;AAC5C,UAAI,OAAO,kBAAkB;AAC3B,eAAO;AACT,UAAI,eAAe,cAAc;AACjC,UAAI,gBAAgB,KAAK,gBAAgB;AACvC,eAAO;AACT,aAAO;AAAA,IACT;AACA,UAAM,cAAc;AACpB,WAAO,UAAU;AAAA,EACnB;AACF,CAAC;AAGD,IAAI,iCAAiC,WAAW;AAAA,EAC9C,wIAAwI,SAAS;AAC/I;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,yBAAyB;AACjC,aAAS,yBAAyB;AAChC,YAAM,WAAW,CAAC,SAAS,WAAW;AACpC,iBAAS,QAAQ;AACjB,iBAAS,UAAU,CAAC,SAAS;AAC3B,cAAI,SAAS,UAAU,WAAW;AAChC;AAAA,UACF;AACA,mBAAS,SAAS;AAClB,gBAAM,cAAc,CAAC,UAAU;AAC7B,qBAAS,QAAQ;AACjB,mBAAO;AAAA,UACT;AACA,iBAAO,QAAQ,gBAAgB,UAAU,OAAO,QAAQ,QAAQ,IAAI,EAAE,KAAK,WAAW,CAAC;AAAA,QACzF;AACA,iBAAS,SAAS,CAAC,WAAW;AAC5B,cAAI,SAAS,UAAU,WAAW;AAChC;AAAA,UACF;AACA,yBAAe,MAAM;AACnB,qBAAS,QAAQ;AAAA,UACnB,CAAC;AACD,iBAAO,OAAO,SAAS,kBAAkB,MAAM;AAAA,QACjD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,YAAQ,yBAAyB;AAAA,EACnC;AACF,CAAC;AAGD,IAAI,0BAA0B,WAAW;AAAA,EACvC,iIAAiI,SAAS;AACxI;AAhLJ;AAiLI,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB;AAC1B,QAAI,2BAA2B,+BAA+B;AAC9D,QAAI,oBAAmB,mBAAc,QAAQ;AAAA,MAI3C,YAAY,WAAW,MAAM;AAC3B,cAAM,oBAAoB,GAAG,yBAAyB,wBAAwB;AAC9E,cAAM,CAAC,iBAAiB,mBAAmB;AACzC,2BAAiB,iBAAiB,cAAc;AAChD,+CAAW,iBAAiB,SAAS,iBAAiB;AAAA,QACxD,CAAC;AAoBH;AA5BA;AACA;AACA;AAOE,2BAAK,WAAY;AACjB,aAAK,UAAU,mBAAK,WAAU;AAC9B,aAAK,SAAS,mBAAK,WAAU;AAAA,MAC/B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,mBAAK,WAAU;AAAA,MACxB;AAAA,MACA,IAAI,kBAAkB;AACpB,eAAO,mBAAK,WAAU;AAAA,MACxB;AAAA,MACA,KAAK,aAAa,YAAY;AAC5B,eAAO,sBAAK,wBAAL,WAAe,MAAM,KAAK,aAAa,UAAU;AAAA,MAC1D;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,sBAAK,wBAAL,WAAe,MAAM,MAAM,UAAU;AAAA,MAC9C;AAAA,MACA,QAAQ,WAAW;AACjB,eAAO,sBAAK,wBAAL,WAAe,MAAM,QAAQ,SAAS;AAAA,MAC/C;AAAA,IAOF,GAlCE,2BA4BA,yCAAS,SAAC,SAAS;AACjB,aAAO,OAAO,iBAAiB,SAAS;AAAA,QACtC,SAAS,EAAE,cAAc,MAAM,OAAO,KAAK,QAAQ;AAAA,QACnD,QAAQ,EAAE,cAAc,MAAM,OAAO,KAAK,OAAO;AAAA,MACnD,CAAC;AAAA,IACH,GAlCqB;AAoCvB,YAAQ,kBAAkB;AAAA,EAC5B;AACF,CAAC;AAGD,IAAI,gBAAgB,WAAW;AAAA,EAC7B,uHAAuH,SAAS;AAC9H;AACA,QAAI,kBAAkB,WAAW,QAAQ,oBAAoB,OAAO,SAAS,SAAS,GAAG,GAAG,GAAG,IAAI;AACjG,UAAI,OAAO;AACT,aAAK;AACP,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AACzC,iBAAO,EAAE,CAAC;AAAA,QACZ,EAAE;AAAA,MACJ;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACnC,IAAI,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO;AACT,aAAK;AACP,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACb;AACA,QAAI,eAAe,WAAW,QAAQ,gBAAgB,SAAS,GAAG,UAAU;AAC1E,eAAS,KAAK;AACZ,YAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,CAAC;AACtE,0BAAgB,UAAU,GAAG,CAAC;AAAA,IACpC;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,+BAA+B,GAAG,OAAO;AACtD,iBAAa,wBAAwB,GAAG,OAAO;AAAA,EACjD;AACF,CAAC;AAGD,IAAI,0BAA0B,WAAW;AAAA,EACvC,+GAA+G,SAAS;AACtH;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB;AAC1B,QAAI,kBAAkB,cAAc,MAAM;AAAA,MAIxC,YAAY,SAAS,MAAM,OAAO;AAChC,cAAM,+CAA+C,KAAK,IAAI,KAAK,SAAS,CAAC,mEAAmE;AAJlJ;AACA;AACA;AAGE,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,YAAQ,kBAAkB;AAAA,EAC5B;AACF,CAAC;AAGD,IAAI,kBAAkB,WAAW;AAAA,EAC/B,uGAAuG,SAAS;AAC9G;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU;AAClB,QAAI,oBAAoB,wBAAwB;AAChD,QAAI,SAAS,eAAe,qCAAqC,eAAe,iBAAiB,iBAAiB,mBAAmB,mBAAmB,qBAAqB,eAAe;AAC5L,QAAI,WAAW,MAAM;AAAA,MACnB,cAAc;AACZ,QAAAF,cAAa,MAAM,aAAa;AAChC,QAAAA,cAAa,MAAM,eAAe;AAClC,QAAAA,cAAa,MAAM,iBAAiB;AACpC,QAAAA,cAAa,MAAM,aAAa;AAChC,QAAAA,cAAa,MAAM,SAAS,MAAM;AAClC,QAAAA,cAAa,MAAM,eAAe,MAAM;AACxC,QAAAA,cAAa,MAAM,qCAAqC,MAAM;AAC9D,QAAAC,cAAa,MAAM,SAAyB,oBAAI,IAAI,CAAC;AACrD,QAAAA,cAAa,MAAM,eAAe,SAAS,mBAAmB;AAC9D,QAAAA,cAAa,MAAM,qCAAqC,KAAK;AAAA,MAC/D;AAAA,MACA,OAAO,cAAc,SAAS,WAAW;AACvC,eAAO,QAAQ,cAAc,SAAS;AAAA,MACxC;AAAA,MACA,gBAAgB,cAAc;AAC5B,QAAAA,cAAa,MAAM,eAAe,YAAY;AAC9C,eAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAChB,eAAOF,cAAa,MAAM,aAAa;AAAA,MACzC;AAAA,MACA,aAAa;AACX,eAAO,MAAM,KAAKA,cAAa,MAAM,OAAO,EAAE,KAAK,CAAC;AAAA,MACtD;AAAA,MACA,KAAK,cAAc,MAAM;AACvB,cAAM,YAAYG,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS;AAC5F,kBAAU,QAAQ,CAAC,aAAa;AAC9B,mBAAS,MAAM,MAAM,IAAI;AAAA,QAC3B,CAAC;AACD,eAAO,UAAU,SAAS;AAAA,MAC5B;AAAA,MACA,YAAY,WAAW,UAAU;AAC/B,QAAAA,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,eAAe,WAAW,QAAQ;AACnG,cAAM,gBAAgBA,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS,EAAE,OAAO,QAAQ;AACjH,QAAAH,cAAa,MAAM,OAAO,EAAE,IAAI,WAAW,aAAa;AACxD,YAAIA,cAAa,MAAM,aAAa,IAAI,KAAK,KAAK,cAAc,SAAS,IAAIA,cAAa,MAAM,aAAa,KAAK,CAACA,cAAa,MAAM,mCAAmC,GAAG;AAC1K,UAAAE,cAAa,MAAM,qCAAqC,IAAI;AAC5D,gBAAM,oBAAoB,IAAI,kBAAkB,gBAAgB,MAAM,WAAW,KAAK,cAAc,SAAS,CAAC;AAC9G,kBAAQ,KAAK,iBAAiB;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAAA,MACA,GAAG,WAAW,UAAU;AACtB,eAAO,KAAK,YAAY,WAAW,QAAQ;AAAA,MAC7C;AAAA,MACA,KAAK,WAAW,UAAU;AACxB,eAAO,KAAK,YAAY,WAAWC,iBAAgB,MAAM,mBAAmB,mBAAmB,EAAE,KAAK,MAAM,WAAW,QAAQ,CAAC;AAAA,MAClI;AAAA,MACA,gBAAgB,WAAW,UAAU;AACnC,cAAM,YAAYA,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS;AAC5F,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,gBAAgB,CAAC,QAAQ,EAAE,OAAO,SAAS;AACjD,UAAAH,cAAa,MAAM,OAAO,EAAE,IAAI,WAAW,aAAa;AAAA,QAC1D,OAAO;AACL,UAAAA,cAAa,MAAM,OAAO,EAAE,IAAI,WAAW,UAAU,OAAO,QAAQ,CAAC;AAAA,QACvE;AACA,eAAO;AAAA,MACT;AAAA,MACA,oBAAoB,WAAW,UAAU;AACvC,eAAO,KAAK,gBAAgB,WAAWG,iBAAgB,MAAM,mBAAmB,mBAAmB,EAAE,KAAK,MAAM,WAAW,QAAQ,CAAC;AAAA,MACtI;AAAA,MACA,eAAe,WAAW,UAAU;AAClC,cAAM,YAAYA,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS;AAC5F,YAAI,UAAU,SAAS,GAAG;AACxB,UAAAA,iBAAgB,MAAM,iBAAiB,iBAAiB,EAAE,KAAK,MAAM,WAAW,QAAQ;AACxF,UAAAH,cAAa,MAAM,OAAO,EAAE,IAAI,WAAW,SAAS;AACpD,UAAAG,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,kBAAkB,WAAW,QAAQ;AAAA,QACxG;AACA,eAAO;AAAA,MACT;AAAA,MACA,IAAI,WAAW,UAAU;AACvB,eAAO,KAAK,eAAe,WAAW,QAAQ;AAAA,MAChD;AAAA,MACA,mBAAmB,WAAW;AAC5B,YAAI,WAAW;AACb,UAAAH,cAAa,MAAM,OAAO,EAAE,OAAO,SAAS;AAAA,QAC9C,OAAO;AACL,UAAAA,cAAa,MAAM,OAAO,EAAE,MAAM;AAAA,QACpC;AACA,eAAO;AAAA,MACT;AAAA,MACA,UAAU,WAAW;AACnB,eAAO,MAAM,KAAKG,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS,CAAC;AAAA,MAC/F;AAAA,MACA,cAAc,WAAW;AACvB,eAAOA,iBAAgB,MAAM,eAAe,eAAe,EAAE,KAAK,MAAM,SAAS,EAAE;AAAA,MACrF;AAAA,MACA,aAAa,WAAW;AACtB,eAAO,KAAK,UAAU,SAAS;AAAA,MACjC;AAAA,IACF;AACA,QAAI,WAAW;AACf,cAAU,oBAAI,QAAQ;AACtB,oBAAgB,oBAAI,QAAQ;AAC5B,0CAAsC,oBAAI,QAAQ;AAClD,oBAAgB,oBAAI,QAAQ;AAC5B,sBAAkB,SAAS,WAAW;AACpC,aAAOH,cAAa,MAAM,OAAO,EAAE,IAAI,SAAS,KAAK,CAAC;AAAA,IACxD;AACA,sBAAkB,oBAAI,QAAQ;AAC9B,wBAAoB,SAAS,WAAW,UAAU;AAChD,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,QAAQ,IAAI;AACd,kBAAU,OAAO,OAAO,CAAC;AAAA,MAC3B;AACA,aAAO,CAAC;AAAA,IACV;AACA,wBAAoB,oBAAI,QAAQ;AAChC,0BAAsB,SAAS,WAAW,UAAU;AAClD,YAAM,eAAe,IAAI,SAAS;AAChC,aAAK,eAAe,WAAW,YAAY;AAC3C,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,oBAAgB,oBAAI,QAAQ;AAC5B,sBAAkB,SAAS,mBAAmB,WAAW,UAAU;AACjE,WAAK;AAAA,QACH;AAAA,QACA,GAAG,CAAC,WAAW,QAAQ;AAAA,MACzB;AAAA,IACF;AACA,IAAAD,eAAc,UAAU,uBAAuB,EAAE;AACjD,YAAQ,UAAU;AAAA,EACpB;AACF,CAAC;AAGD,IAAI,cAAc,WAAW;AAAA,EAC3B,qGAAqG,SAAS;AAC5G;AACA,QAAI,kBAAkB,WAAW,QAAQ,oBAAoB,OAAO,SAAS,SAAS,GAAG,GAAG,GAAG,IAAI;AACjG,UAAI,OAAO;AACT,aAAK;AACP,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AACzC,iBAAO,EAAE,CAAC;AAAA,QACZ,EAAE;AAAA,MACJ;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACnC,IAAI,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO;AACT,aAAK;AACP,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACb;AACA,QAAI,eAAe,WAAW,QAAQ,gBAAgB,SAAS,GAAG,UAAU;AAC1E,eAAS,KAAK;AACZ,YAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,CAAC;AACtE,0BAAgB,UAAU,GAAG,CAAC;AAAA,IACpC;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,gBAAgB,GAAG,OAAO;AACvC,iBAAa,wBAAwB,GAAG,OAAO;AAAA,EACjD;AACF,CAAC;AAGD,IAAI,cAAc,QAAQ,aAAa,CAAC;AAGxC,IAAI,kBAAkB;AACtB,SAAS,oBAAoB,YAAY,MAAM;AAC7C,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO,OAAO,UAAU;AAAA,IAC1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;AAAA,IAClC,KAAK,KAAK;AACR,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;AAAA,MACT;AACA,YAAM,OAAO,KAAK,UAAU,UAAU;AACtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,OAAO,YAAY,aAAa;AACvC,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,QAAQ;AAAA,IAC7B;AAAA,IACA,CAAC,OAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY,eAAe;AAC9C,YAAM,QAAQ,oBAAoB,YAAY,IAAI;AAClD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG,CAAC;AAAA,EACtE;AACA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAC1D,SAAO;AACT;AACA,IAAI,yBAAyB;AAC7B,SAAS,gBAAgB,OAAO;AAC9B,MAAI,CAAC,MAAM,OAAO;AAChB;AAAA,EACF;AACA,QAAM,YAAY,MAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAG,sBAAsB;AAC1C,QAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AACA,IAAI,iBAAiB,cAAc,MAAM;AAAA,EACvC,YAAY,YAAY,aAAa;AACnC,UAAM,OAAO;AACb,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,UAAU,OAAO,SAAS,GAAG,WAAW;AAC7C,oBAAgB,IAAI;AAAA,EACtB;AACF;AACA,IAAI,YAAY,CAAC,WAAW,YAAY,gBAAgB;AACtD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAe,SAAS,GAAG,WAAW;AAAA,EAClD;AACF;AACA,UAAU,KAAK,CAAC,kBAAkB,WAAW,YAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAM,gBAAgB,iBAAiB,UAAU,QAAQ;AACzD,UAAM,QAAQ,gBAAgB,IAAI,iBAAiB,OAAO,SAAS,WAAW,CAAC,IAAI,iBAAiB,OAAO,SAAS,WAAW,CAAC;AAChI,UAAM;AAAA,EACR;AACF;AAGA,IAAI,0BAA0B,QAAQ,cAAc,CAAC;AAGrD,IAAI,OAAO,OAAO,aAAa,oBAAoB;AACnD,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AACpB;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,OAAO,YAAY,MAAM;AACvC,QAAI,SAAS,QAAQ;AACnB,YAAM,YAAY,MAAM;AACtB,YAAI,QAAQ,SAAS,QAAQ;AAC3B,iBAAO,GAAG,KAAK;AACjB,YAAI,QAAQ,SAAS,UAAU;AAC7B,iBAAO,GAAG,GAAG;AACf,eAAO;AAAA,MACT;AACA,YAAM,eAAe,QAAQ,QAAQ,WAAW,EAAE;AAClD,cAAQ,MAAM,GAAG,YAAY,KAAK,CAAC,GAAG,KAAK,IAAI,UAAU,CAAC,GAAG,OAAO,IAAI,YAAY,IAAI,GAAG,IAAI;AAAA,IACjG;AAAA,EACF;AACF;AAGA,IAAI,QAAQ,YAAY,UAAU;AAgFlC,IAAI,gBAAgB,MAAM;AAAA,EACxB,YAAY,QAAQ;AAyBpB;AACA;AACA;AACA;AA3BE,SAAK,SAAS;AACd,SAAK,UAAU,IAAI,YAAY;AAC/B,SAAK,UAAU,IAAI,eAAe;AAClC,SAAK,eAAe,KAAK,QAAQ;AACjC,UAAM,uBAAuB,IAAI,wBAAwB,gBAAgB;AACzE,UAAM,oBAAoB,CAAC,YAAY;AACrC,UAAI,QAAQ,KAAK,SAAS,kBAAkB;AAC1C,cAAM,oCAAoC;AAC1C,6BAAqB,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,iBAAiB,WAAW,iBAAiB;AACpD,yBAAqB,KAAK,MAAM;AAC9B,aAAO,oBAAoB,WAAW,iBAAiB;AAAA,IACzD,CAAC;AACD,SAAK,uBAAuB;AAC5B,SAAK,aAAa,YAAY,CAAC,QAAQ;AACrC,YAAM,OAAO,IAAI;AACjB,UAAI,KAAK,QAAQ,MAAM;AACrB,cAAM,4CAA4C,KAAK,MAAM,KAAK,OAAO;AACzE,aAAK,QAAQ,cAAc,IAAI,aAAa,KAAK,MAAM,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA,EAKA,MAAM,YAAY;AAChB,UAAM,mBAAmB,IAAI,wBAAwB,gBAAgB;AACrE,UAAM,KAAK;AACX,UAAM,qCAAqC;AAC3C,SAAK,OAAO;AAAA,MACV;AAAA,QACE,MAAM;AAAA,MACR;AAAA,MACA;AAAA,MACA,CAAC,KAAK,QAAQ,KAAK;AAAA,IACrB;AACA,SAAK,GAAG,2BAA2B,MAAM;AACvC,uBAAiB,QAAQ;AACzB,mBAAa,gBAAgB;AAAA,IAC/B,CAAC;AACD,UAAM,mBAAmB,WAAW,MAAM;AACxC,uBAAiB,OAAO,IAAI,MAAM,kCAAkC,CAAC;AAAA,IACvE,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,GAAG,OAAO,UAAU,SAAS;AAC3B,UAAM,uCAAuC,KAAK;AAClD,SAAK,QAAQ;AAAA,MACX;AAAA,MACA,CAAC,YAAY;AACX,YAAI,mBAAmB,cAAc;AACnC,mBAAS,OAAO;AAAA,QAClB;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,OAAO,UAAU,SAAS;AAC5B,SAAK,QAAQ,oBAAoB,OAAO,UAAU,OAAO;AAAA,EAC3D;AAAA,EACA,MAAM,KAAK,UAAU,MAAM;AACzB,UAAM,mBAAmB,IAAI,wBAAwB,gBAAgB;AACrE,UAAM,eAAe,GAAG,YAAY,SAAS;AAC7C,UAAM,UAAU,KAAK,CAAC,KAAK,CAAC;AAC5B,UAAM,oCAAoC,OAAO,aAAa,OAAO;AACrE,SAAK,aAAa,YAAY,EAAE,MAAM,OAAO,SAAS,EAAE,aAAa,QAAQ,EAAE,CAAC;AAChF,UAAM,wDAAwD,OAAO,WAAW;AAChF,UAAM,sBAAsB,CAAC,cAAc;AACzC,YAAM,EAAE,MAAM,MAAM,IAAI;AACxB,UAAI,MAAM,gBAAgB,aAAa;AACrC,cAAM,kBAAkB,MAAM,mBAAmB,CAAC;AAClD,cAAM,iDAAiD,OAAO,WAAW;AACzE,yBAAiB,QAAQ;AAAA,UACvB,GAAG;AAAA,UACH,aAAa,MAAM;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,wBAAwB,CAAC,cAAc;AAC3C,YAAM,EAAE,MAAM,MAAM,IAAI;AACxB,UAAI,MAAM,gBAAgB,aAAa;AACrC,cAAM,iDAAiD,OAAO,WAAW;AACzE,yBAAiB,OAAO,MAAM,KAAK;AAAA,MACrC;AAAA,IACF;AACA,SAAK,GAAG,2BAA2B,mBAAmB;AACtD,SAAK,GAAG,6BAA6B,qBAAqB;AAC1D,WAAO,iBAAiB,QAAQ,MAAM;AACpC,WAAK,QAAQ,oBAAoB,2BAA2B,mBAAmB;AAC/E,WAAK,QAAQ,oBAAoB,6BAA6B,qBAAqB;AAAA,IACrF,CAAC;AAAA,EACH;AACF;AAGA,IAAI,2BAA2B,QAAQ,cAAc,CAAC;AAGtD,IAAI,eAAe,QAAQ,aAAa,CAAC;AACzC,IAAI,gBAAgB,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,KAAK,OAAO;AAChB,UAAM,KAAK,QAAQ,KAAK,WAAW,EAAE,MAAM,CAAC;AAAA,EAC9C;AAAA,EACA,MAAM,SAAS,MAAM,UAAU;AAC7B,UAAM,WAAW,MAAM,KAAK,QAAQ,KAAK,eAAe,EAAE,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC,UAAU;AAC3F,YAAM,IAAI,MAAM,OAAO,oCAAoC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACpF,CAAC;AACD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,MAAM,UAAU,MAAM,SAAS,SAAS;AACtC,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,OAAO,YAAY,UAAU;AAC/B,iBAAW,QAAQ;AACnB,kBAAY,CAAC,CAAC,QAAQ;AAAA,IACxB,WAAW,OAAO,YAAY,UAAU;AACtC,iBAAW;AAAA,IACb;AACA,UAAM,KAAK,QAAQ,KAAK,gBAAgB,EAAE,MAAM,SAAS,UAAU,UAAU,CAAC,EAAE,MAAM,CAAC,UAAU;AAC/F,YAAM,IAAI,MAAM,OAAO,qCAAqC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACrF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,UAAM,WAAW,MAAM,KAAK,QAAQ,KAAK,cAAc,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,UAAU;AAChF,YAAM,IAAI,MAAM,OAAO,yCAAyC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACzF,CAAC;AACD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,MAAM,MAAM,MAAM,SAAS;AACzB,UAAM,YAAY,CAAC,EAAC,mCAAS;AAC7B,UAAM,KAAK,QAAQ,KAAK,YAAY,EAAE,MAAM,UAAU,CAAC,EAAE,MAAM,CAAC,UAAU;AACxE,YAAM,IAAI,MAAM,OAAO,yCAAyC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACzF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,KAAK,MAAM;AACf,UAAM,WAAW,MAAM,KAAK,QAAQ,KAAK,WAAW,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,UAAU;AAC7E,YAAM,IAAI,MAAM,OAAO,oCAAoC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACpF,CAAC;AACD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,MAAM,GAAG,MAAM,SAAS;AACtB,UAAM,EAAE,OAAO,UAAU,IAAI,WAAW,CAAC;AACzC,UAAM,KAAK,QAAQ,KAAK,SAAS,EAAE,MAAM,OAAO,UAAU,CAAC,EAAE,MAAM,CAAC,UAAU;AAC5E,YAAM,IAAI,MAAM,OAAO,sCAAsC,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,MAAM,UAAU,UAAU,UAAU;AACxC,UAAM,aAAa,GAAG,aAAa,SAAS;AAC5C,UAAM,KAAK,QAAQ,KAAK,YAAY,EAAE,WAAW,UAAU,SAAS,CAAC;AACrE,SAAK,QAAQ,GAAG,kBAAkB,CAAC,EAAE,KAAK,MAAM;AAC9C,UAAI,KAAK,cAAc,aAAa,UAAU;AAC5C,cAAM,MAAM,EAAE,GAAG,KAAK;AACtB,eAAO,IAAI;AACX,iBAAS,GAAG;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,SAAS,MAAM,KAAK,QAAQ,KAAK,cAAc,EAAE,UAAU,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAGA,IAAI,8BAA8B,QAAQ,YAAY,CAAC;AACvD,IAAI,WAAW,MAAM;AAAA,EACnB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,WAAO,IAAI,aAAa,KAAK,OAAO;AAAA,EACtC;AACF;AACA,IAAI,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AAerB;AACA;AACA;AACA;AACA;AAlBE,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS,IAAI,4BAA4B,QAAQ;AACtD,SAAK,SAAS,IAAI,4BAA4B,QAAQ;AACtD,SAAK,QAAQ;AAAA,MACX,OAAO,CAAC,SAAS;AACf,YAAI,CAAC,KAAK,IAAI;AACZ,gBAAM,IAAI,MAAM,2DAA2D;AAAA,QAC7E;AACA,eAAO,KAAK,QAAQ,KAAK,eAAe,EAAE,MAAM,UAAU,KAAK,GAAG,CAAC;AAAA,MACrE;AAAA,IACF;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAMA,mBAAmB;AACjB,SAAK,QAAQ,GAAG,cAAc,CAAC,YAAY;AACzC,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,KAAK,aAAa,KAAK,IAAI;AAC7B;AAAA,MACF;AACA,cAAQ,KAAK,QAAQ,MAAM;AAAA,QACzB,KAAK,OAAO;AACV,eAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,IAAI;AAC1C;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,eAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,IAAI;AAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,MAAM,WAAW,SAAS,MAAM,UAAU,CAAC,GAAG;AAC5C,cAAU,CAAC,KAAK,IAAI,mFAAmF;AACvG,UAAM,YAAY,MAAM,KAAK,QAAQ,KAAK,oBAAoB,EAAE,SAAS,MAAM,QAAQ,CAAC;AACxF,cAAU,WAAW,2FAA2F;AAChH,SAAK,KAAK,UAAU;AACpB,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG,SAAS,UAAU;AAC1B,YAAQ,SAAS;AAAA,MACf,KAAK,YAAY;AACf,aAAK,QAAQ,GAAG,mBAAmB,CAAC,EAAE,KAAK,MAAM;AAC/C,mBAAS,KAAK,MAAM;AAAA,QACtB,CAAC;AACD;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAK,QAAQ,GAAG,eAAe,CAAC,EAAE,KAAK,MAAM;AAC3C,cAAI,KAAK,aAAa,KAAK,IAAI;AAC7B,qBAAS,KAAK,UAAU,KAAK,KAAK;AAAA,UACpC;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,SAAK,QAAQ;AACb,UAAM,KAAK,QAAQ,KAAK,cAAc,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,UAAU;AACtE,YAAM,IAAI,MAAM,OAAO,qCAAqC,KAAK,EAAE,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IACxF,CAAC;AACD,SAAK,KAAK;AAAA,EACZ;AACF;AAGA,IAAI,2BAA2B,QAAQ,cAAc,CAAC;AACtD,IAAI,UAAU;AACd,IAAI,aAAa,MAAM;AAAA,EACrB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,QAAQ,SAAS,WAAW,UAAU,SAAS;AACnD,UAAM,eAAe,IAAI,yBAAyB,gBAAgB;AAClE,UAAM,gBAAgB,WAAW,MAAM;AACrC,mBAAa,OAAO;AAAA,IACtB,GAAG,OAAO;AACV,UAAM,qBAAqB,MAAM,KAAK,QAAQ,KAAK,oBAAoB,OAAO,EAAE,MAAM,CAAC,UAAU;AAC/F,mBAAa;AAAA,QACX,IAAI;AAAA,UACF;AAAA,YACE;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,eAAe,sBAAsB,UAAU,kBAAkB;AACvE,QAAI,cAAc;AAChB,mBAAa,QAAQ;AAAA,QACnB,KAAK,mBAAmB;AAAA,QACxB,MAAM,mBAAmB;AAAA,QACzB,eAAe,mBAAmB;AAAA,MACpC,CAAC;AAAA,IACH;AACA,SAAK,QAAQ,GAAG,sBAAsB,CAAC,EAAE,KAAK,MAAM;AAClD,UAAI,CAAC,gBAAgB,UAAU,IAAI,GAAG;AACpC,qBAAa,QAAQ;AAAA,UACnB,KAAK,KAAK;AAAA,UACV,MAAM,KAAK;AAAA,UACX,eAAe,KAAK;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO,aAAa,QAAQ,MAAM;AAChC,mBAAa,aAAa;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,MAAM,aAAa,eAAe,SAAS;AACzC,WAAO,KAAK,QAAQ,EAAE,cAAc,GAAG,CAAC,SAAS,KAAK,kBAAkB,eAAe,OAAO,EAAE,MAAM,CAAC,UAAU;AAC/G,YAAM,IAAI,MAAM,OAAO,kCAAkC,aAAa,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IAC3F,CAAC;AAAA,EACH;AAAA,EACA,MAAM,YAAY,MAAM,SAAS;AAC/B,WAAO,KAAK,QAAQ,EAAE,KAAK,GAAG,CAAC,SAAS,KAAK,SAAS,MAAM,OAAO,EAAE,MAAM,CAAC,UAAU;AACpF,YAAM,IAAI,MAAM,OAAO,2BAA2B,IAAI,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IAC3E,CAAC;AAAA,EACH;AACF;AAGA,IAAI,sBAAsB;AAC1B,IAAI,SAAS,YAAY,UAAU;AACnC,IAAI,UAAU,MAAM;AAAA,EAClB,YAAY,SAAS;AAUrB,mCAAU;AACV;AACA;AACA,yCAAgB;AAChB,oCAAW;AACX,sCAAa;AAdX,SAAK,UAAU;AACf;AAAA,MACE,KAAK,QAAQ;AAAA,MACb;AAAA,MACA,KAAK,QAAQ;AAAA,IACf;AACA,SAAK,MAAM,KAAK,QAAQ,cAAc;AACtC,SAAK,cAAc;AAAA,EACrB;AAAA,EAOA,MAAM,UAAU;AACd,UAAM,EAAE,QAAQ,OAAO,IAAI,KAAK;AAChC,WAAO,kDAAkD;AACzD,UAAM,oBAAoB,IAAI,yBAAyB,gBAAgB;AACvE,QAAI,CAAC,KAAK,KAAK;AACb,wBAAkB;AAAA,QAChB,IAAI,MAAM,2FAA2F;AAAA,MACvG;AAAA,IACF;AACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,IACF;AACA,SAAK,UAAU,IAAI,cAAc,OAAO,aAAa;AACrD,UAAM,mBAAmB,IAAI,yBAAyB,gBAAgB;AACtE,WAAO,aAAa,OAAO,KAAK,GAAG;AACnC,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AACJ,yBAAiB,QAAQ;AAAA,MAC3B;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IACf;AACA,WAAO;AAAA,MACL;AAAA,MACA,CAAC,UAAU;AACT,yBAAiB,OAAO,MAAM,KAAK;AAAA,MACrC;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IACf;AACA,UAAM;AACN,WAAO,oCAAoC;AAC3C,UAAM,KAAK,QAAQ,UAAU;AAC7B,WAAO,0CAA0C;AACjD,SAAK,QAAQ,KAAK,WAAW;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,SAAK,QAAQ,GAAG,iBAAiB,MAAM;AACrC,wBAAkB,QAAQ;AAAA,IAC5B,CAAC;AACD,WAAO,kBAAkB,KAAK,MAAM;AAClC,aAAO,2CAA2C;AAClD,WAAK,cAAc;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd;AACA,SAAK,gBAAgB,IAAI,cAAc,KAAK,OAAO;AACnD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,aAAO,KAAK;AAAA,IACd;AACA,SAAK,WAAW,IAAI,SAAS,KAAK,OAAO;AACzC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK;AAAA,IACd;AACA,SAAK,aAAa,IAAI,WAAW,KAAK,OAAO;AAC7C,WAAO,KAAK;AAAA,EACd;AACF;AAGA,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;;;AC7hClC,SAAS,kBAAkB,QAAQ,KAAK;AACpC,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,eAAe,UAAU,gBAAgB,OAAO;AACpD,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAgB,OAAO;AACvB,iBAAW,eAAe,yDAAyD;AACnF,iBAAW;AACX,uBAAiB;AACjB,cAAQ;AACR,aAAO,CAAC,GAAc,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACrD,YAAI,gBAAgB,WAAY;AAC5B,cAAI,aAAa,WAAY;AACzB,yBAAa,OAAO;AACpB,oBAAQ;AACR,oBAAQ;AACR,mBAAO,oBAAoB,QAAQ,UAAU;AAAA,UACjD;AACA,cAAI,SAAS,gBAAgB;AACzB,mBAAO,YAAY,uCAAuC,CAAC;AAC3D;AAAA,UACJ;AACA,iBAAO,aAAa,OAAO,GAAG;AAC9B,oBAAU,WAAW,WAAY;AAC7B,0BAAc;AACd,mBAAO,oBAAoB,QAAQ,UAAU;AAAA,UACjD,GAAG,QAAQ;AACX,kBAAQ,QAAQ;AAChB,iBAAO,iBAAiB,QAAQ,UAAU;AAAA,QAC9C;AACA,eAAO,iBAAiB,SAAS,WAAY;AAAE,iBAAO,OAAO,IAAI,MAAM,cAAc,CAAC;AAAA,QAAG,CAAC;AAC1F,eAAO,iBAAiB,SAAS,WAAY;AAAE,iBAAO,OAAO,IAAI,MAAM,SAAS,CAAC;AAAA,QAAG,CAAC;AACrF,sBAAc;AAAA,MAClB,CAAC,CAAC;AAAA,IACV,CAAC;AAAA,EACL,CAAC;AACL;AACA,IAAI,6BAA6B,SAAU,QAAQ,SAAS;AACxD,SAAO,MAAM,SAAS;AACtB,SAAO,MAAM,QAAQ,QAAQ,SAAS;AACtC,SAAO,MAAM,SAAS,QAAQ,UAAU;AACxC,SAAO,MAAM,WAAW;AACxB,SAAO,QAAQ;AACnB;AAGA,SAAS,sBAAsB,IAAI;AAC/B,MAAI,QAAQ,GAAG;AAEf,MAAI,mBAAmB,OAAO,QAAQ;AACtC,MAAI,cAAc,CAAC;AACnB,MAAI,kBAAkB;AACtB,MAAI,kBAAkB,SAAU,KAAK;AACjC,WAAO,YAAY;AAAA,MACf,MAAM;AAAA,MACN;AAAA,MACA,MAAM,kBAAkB;AAAA,MACxB,SAAS,kBAAkB,YAAY,SAAS;AAAA,MAChD,WAAW,MAAM;AAAA,IACrB,GAAG,GAAG;AAAA,EACV;AACA,WAAS,YAAY,KAAK,OAAO;AAE7B,gBAAY,OAAO,kBAAkB,CAAC;AACtC,gBAAY,KAAK,EAAE,KAAU,MAAa,CAAC;AAC3C,sBAAkB,YAAY,SAAS;AAAA,EAC3C;AACA,SAAO,OAAO,OAAO,SAAS;AAAA,IAC1B,IAAI,SAAU,OAAO;AACjB,UAAI,SAAS,kBAAkB;AAC/B,UAAI,UAAU,KAAK,UAAU,YAAY,SAAS,GAAG;AACjD,0BAAkB;AAClB,YAAIK,MAAK,YAAY,eAAe,GAAG,MAAMA,IAAG,KAAK,QAAQA,IAAG;AAChE,yBAAiB,aAAa,KAAK,OAAO,SAAS,OAAO,IAAI,GAAG;AACjE,YAAI,SAAS,SAAS,SAAS;AAC/B,wBAAgB,MAAM;AACtB,eAAO,cAAc,IAAI,cAAc,YAAY,EAAE,MAAa,CAAC,CAAC;AAAA,MACxE;AAAA,IACJ;AAAA,IACA,MAAM,WAAY;AACd,aAAO,QAAQ,GAAG,EAAE;AAAA,IACxB;AAAA,IACA,SAAS,WAAY;AACjB,aAAO,QAAQ,GAAG,CAAC;AAAA,IACvB;AAAA,IACA,WAAW,SAAU,OAAO,OAAO,KAAK;AACpC,uBAAiB,aAAa,KAAK,OAAO,SAAS,OAAO,OAAO,GAAG;AACpE,kBAAY,KAAK,KAAK;AACtB,sBAAgB,SAAS,SAAS,IAAI;AAAA,IAC1C;AAAA,IACA,cAAc,SAAU,OAAO,OAAO,KAAK;AACvC,uBAAiB,aAAa,KAAK,OAAO,SAAS,OAAO,OAAO,GAAG;AACpE,kBAAY,eAAe,IAAI,EAAE,OAAc,IAAS;AACxD,sBAAgB,SAAS,SAAS,IAAI;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,WAAS,cAAcA,KAAI;AACvB,QAAI,OAAOA,IAAG;AACd,QAAI,KAAK,SAAS,WAAW;AACzB,cAAQ,KAAK;AAAA,IACjB,WACS,KAAK,SAAS,cAAc;AACjC,cAAQ,QAAQ;AAAA,IACpB,WACS,KAAK,SAAS,WAAW;AAC9B,eAAS,SAAS,OAAO;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO,iBAAiB,WAAW,aAAa;AACpD;AAGA,SAAS,YAAY,IAAI;AACrB,MAAI,QAAQ,GAAG;AACf,MAAI,aAAa;AACjB,WAAS,oBAAoB;AACzB,QAAI,OAAO,WAAW;AAClB,aAAO;AACX,QAAI,OAAO,SAAS;AACpB,QAAI,OAAO,SAAS;AACpB,WAAO,KAAK,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAAA,EAC3E;AACA,WAAS,kBAAkB;AACvB,QAAI,SAAS,kBAAkB;AAC/B,QAAI,eAAe,QAAQ;AACvB,aAAO,OAAO,YAAY;AAAA,QACtB,MAAM;AAAA,QACN;AAAA,QACA,aAAa;AAAA,QACb,WAAW,MAAM;AAAA,MACrB,GAAG,GAAG;AAAA,IACV;AACA,iBAAa;AAAA,EACjB;AACA,kBAAgB;AAChB,MAAI;AACJ,MAAI,WAAW,IAAI,iBAAiB,WAAY;AAC5C,QAAI,aAAa,QAAW;AACxB,sBAAgB;AAChB,iBAAW,WAAW,WAAY;AAC9B,mBAAW;AAAA,MACf,GAAG,GAAG;AAAA,IACV;AAAA,EACJ,CAAC;AACD,WAAS,QAAQ,UAAU;AAAA,IACvB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,EACb,CAAC;AAKD,cAAY,iBAAiB,GAAG;AACpC;AAGA,IAAI,UAAU;AAAA,EACV,EAAE,MAAM,sBAAsB,SAAS,GAAG,IAAI,kBAAkB;AAAA,EAChE;AAAA,IACI,MAAM,sCAAsC,cAAc;AAAA,IAC1D,IAAI;AAAA,EACR;AAAA,EACA,EAAE,MAAM,YAAY,SAAS,GAAG,IAAI,cAAc;AACtD;AACA,IAAI,uBAAuB,SAAU,QAAQ,WAAW;AACpD,UAAQ,QAAQ,SAAU,IAAI;AAC1B,QAAI;AACJ,QAAI,OAAO,GAAG,MAAM,KAAK,GAAG;AAC5B,QAAI,UAAU;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM,sBAAsB,OAAO,IAAI;AAAA,MACvC,OAAO,EAAE,UAAqB;AAAA,IAClC;AACA,KAAC,KAAK,OAAO,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,SAAS,GAAG;AAAA,EAChG,CAAC;AACL;AAGA,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,UAAU,aAAa,SAAS;AAClD,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,UAAI,QAAQ,OAAO,KAAK,MAAM,UAAU,aAAa,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,EAAE,YAAY,QAAQ,WAAW,CAAC,CAAC,KAAK;AAC7H,YAAM,gBAAgB,oBAAI,IAAI;AAC9B,YAAM,mBAAmB,iBAAiB;AAC1C,YAAM,eAAe;AACrB,YAAM,UAAU,IAAI,aAAa;AAEjC,YAAM,cAAc,QAAQ;AAE5B,YAAM,WAAW,IAAI,QAAQ;AAAA,QACzB,QAAQ,MAAM;AAAA,QACd,YAAY,MAAM,QAAQ;AAAA,MAC9B,CAAC;AAED,YAAM,cAAc,WAAW;AAC/B,aAAO;AAAA,IACX;AAEA,IAAAA,cAAa,UAAU,QAAQ,SAAU,OAAO;AAC5C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,YACpD,KAAK;AACD,iBAAG,KAAK;AAER,qBAAO,CAAC,GAAa,KAAK,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,YACrD,KAAK;AAED,iBAAG,KAAK;AAER,qBAAO,CAAC,GAAa,KAAK,gBAAgB,CAAC;AAAA,YAC/C,KAAK;AAED,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAIA,IAAAA,cAAa,UAAU,UAAU,SAAU,OAAO;AAC9C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,SAAS;AACb,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AAExB,mBAAK,SAAS;AACd,mBAAK,SAAS,EAAE,MAAM,SAAS,WAAW,KAAK,CAAC;AAChD,kBAAI,CAAC,KAAK,cAAc;AACpB,qBAAK,eAAe,KAAK,MAAM,KAAK;AAAA,cACxC;AACA,qBAAO,CAAC,GAAa,KAAK,YAAY;AAAA,YAC1C,KAAK;AACD,iBAAG,KAAK;AACR,mBAAK,SAAS,EAAE,MAAM,YAAY,CAAC;AACnC,qBAAO,CAAC,GAAa,KAAK,2BAA2B,KAAK,CAAC;AAAA,YAC/D,KAAK;AACD,wBAAW,GAAG,KAAK,EAAG;AAEtB,qBAAO,CAAC,GAAa,KAAK,uBAAuB,OAAO,CAAC;AAAA,YAC7D,KAAK;AAED,iBAAG,KAAK;AACR,qBAAO,CAAC,GAAa,KAAK,yBAAyB,CAAC;AAAA,YACxD,KAAK;AACD,iBAAG,KAAK;AAER,mBAAK,oBAAoB;AACzB,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,mBAAK,SAAS;AAAA,gBACV,MAAM;AAAA,gBACN,QAAQ;AAAA,gBACR,kBAAkB;AAAA,gBAClB,OAAO,oBAAoB,KAAK;AAAA,cACpC,CAAC;AACD,mBAAK,SAAS,EAAE,MAAM,QAAQ,iBAAiB,KAAK,CAAC;AACrD,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAIA,IAAAA,cAAa,UAAU,6BAA6B,SAAU,OAAO;AACjE,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ;AACZ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mCAAqB,WAAW,MAAM,eAAe,CAAC;AACtD,mBAAK,kBAAkB,2BAA2B,kBAAkB;AACpE,mBAAK,uBAAuB,KAAK,SAAS,MAAM,OAAO;AAEvD,qBAAO,CAAC,GAAa,KAAK,qBAAqB,GAAG,QAAQ,SAAU,UAAU;AACtE,sBAAM,SAAS;AAAA,kBACX,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,kBAAkB;AAAA,kBAClB,OAAO,YAAY,uBAAuB,OAAO,UAAU,WAAW,CAAC;AAAA,gBAC3E,CAAC;AAAA,cACL,CAAC,CAAC;AAAA,YACV,KAAK;AAED,iBAAG,KAAK;AACR,qBAAO,CAAC,GAAa,KAAK,qBAAqB,GAAG,YAAY,SAAU,MAAM;AACtE,oBAAID,KAAIE;AACR,oBAAI,KAAK,UAAU,qBACf,KAAK,UAAU,oBAAoB;AACnC,wBAAM,SAAS;AAAA,oBACX,MAAM;AAAA,oBACN,MAAM,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,SAAS;AAAA,uBACrCF,MAAK,MAAM,qBAAqB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC;AAAA,uBACrEE,MAAK,MAAM,qBAAqB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,EAAE,KAAK,GAAG;AAAA,oBACpF,EAAE,KAAK,GAAG,EAAE,CAAC;AAAA,kBACrB,CAAC;AACD,wBAAM,SAAS;AACf;AAAA,gBACJ;AACA,sBAAM,SAAS,EAAE,MAAM,kBAAkB,KAAW,CAAC;AAAA,cACzD,CAAC,CAAC;AAAA,YACV,KAAK;AACD,iBAAG,KAAK;AACR,mBAAK,qBAAqB,OAAO,GAAG,QAAQ,SAAU,MAAM;AACxD,sBAAM,SAAS,EAAE,MAAM,UAAU,SAAS,EAAE,MAAY,MAAM,MAAM,EAAE,CAAC;AAAA,cAC3E,CAAC;AACD,mBAAK,qBAAqB,OAAO,GAAG,QAAQ,SAAU,MAAM;AACxD,sBAAM,SAAS,EAAE,MAAM,UAAU,SAAS,EAAE,MAAY,MAAM,MAAM,EAAE,CAAC;AAAA,cAC3E,CAAC;AACD,qBAAO,CAAC,IAAc,KAAK,KAAK,sBAAsB,WAAW,MAAM,IAAI,KAAK,eAAe,CAAC;AAAA,YACpG,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAD,cAAa,UAAU,yBAAyB,SAAU,IAAI;AAC1D,UAAI;AACJ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,mBAAmB;AACxB,qBAAO,CAAC,GAAa,KAAK,SAAS,QAAQ,aAAa,EAAE,CAAC;AAAA,YAC/D,KAAK;AACD,oBAAO,GAAG,KAAK,EAAG;AAClB,mBAAK,mBAAmB,QAAQ,KAAK,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAC/F,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAQA,IAAAA,cAAa,UAAU,gBAAgB,SAAU,UAAU;AACvD,UAAI;AAIJ,UAAI,OAAO,aAAa,UAAU;AAC9B,YAAI,UAAU,SAAS,cAAc,QAAQ;AAC7C,mBAAW,SAAS,gBAAgB,OAAO,UAAU,iBAAiB,CAAC;AACvE,aAAK,SAAS,SAAS,cAAc,QAAQ;AAC7C,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,KAAK,MAAM;AAAA,MACrF,OACK;AACD,aAAK,SAAS;AAAA,MAClB;AAEA,iCAA2B,KAAK,QAAQ,KAAK,OAAO;AACpD,iBAAW,KAAK,OAAO,YAAY,0CAA0C;AAK7E,WAAK,iBAAiB,SAAS,cAAc,QAAQ;AACrD,WAAK,eAAe,UAAU,IAAI,iBAAiB;AACnD,OAAC,KAAK,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,KAAK,cAAc;AAAA,IACzG;AACA,IAAAA,cAAa,UAAU,2BAA2B,WAAY;AAC1D,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK;AAAkB,uBAAO,CAAC,GAAa,CAAC;AAClD,qBAAO,CAAC,GAAa,kBAAkB,KAAK,QAAQ,KAAK,gBAAgB,CAAC;AAAA,YAC9E,KAAK;AACD,iBAAG,KAAK;AACR,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAKA,IAAAA,cAAa,UAAU,sBAAsB,WAAY;AACrD,WAAK,SAAS;AACd,WAAK,SAAS,EAAE,MAAM,QAAQ,iBAAiB,MAAM,CAAC;AACtD,UAAI,KAAK,kBAAkB;AACvB,aAAK,SAAS;AAAA,UACV,MAAM;AAAA,UACN,KAAK,KAAK;AAAA,UACV,MAAM;AAAA,UACN,SAAS;AAAA,QACb,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,kBAAkB,WAAY;AACjD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,qBAAO,iBAAiB,WAAW,SAAU,OAAO;AAChD,oBAAI,MAAM,KAAK,SAAS,6BAA6B;AACjD,uCAAqB,MAAM,QAAQ,MAAM,gBAAgB;AAAA,gBAC7D;AACA,oBAAI,MAAM,KAAK,SAAS,eACpB,MAAM,KAAK,cAAc,MAAM,kBAAkB;AACjD,wBAAM,SAAS;AAAA,oBACX,MAAM;AAAA,oBACN,KAAK,MAAM,KAAK;AAAA,oBAChB,MAAM,MAAM,KAAK;AAAA,oBACjB,SAAS,MAAM,KAAK;AAAA,kBACxB,CAAC;AAAA,gBACL,WACS,MAAM,KAAK,cAAc,MAAM,kBAAkB;AACtD,wBAAM,SAAS,MAAM,IAAI;AAAA,gBAC7B;AAAA,cACJ,CAAC;AACD,qBAAO,CAAC,GAAa,KAAK,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG;AAAA,gBAC3C;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ,GAAG,SAAU,SAAS;AAAE,uBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACxE,sBAAI,OAAO,MAAM,MAAMD,KAAI,SAAS,YAAY;AAChD,yBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,4BAAQ,GAAG,OAAO;AAAA,sBACd,KAAK;AACD,4BAAI,CAAC;AACD,iCAAO;AAAA,4BAAC;AAAA;AAAA,0BAAY;AACxB,gCAAQ;AACR,+BAAO,aAAa,QACd,MAAM,UACN,UAAU,QACN,MAAM,OACN;AACV,+BAAO,CAAC,GAAa,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AAAA,sBACpD,KAAK;AACD,+BAAQ,GAAG,KAAK,EAAG;AACnB,4BAAI,SAAS;AACT,iCAAO,CAAC,GAAc,IAAI;AAC9B,2BAAG,QAAQ;AAAA,sBACf,KAAK;AACD,2BAAG,KAAK,KAAK,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC;AAC1B,wBAAAA,MAAK,MAAM;AACX,gCAAQA,KAAI;AAAA,0BACR,KAAK;AAAU,mCAAO,CAAC,GAAa,CAAC;AAAA,0BACrC,KAAK;AAAU,mCAAO,CAAC,GAAa,CAAC;AAAA,0BACrC,KAAK;AAAU,mCAAO,CAAC,GAAa,CAAC;AAAA,0BACrC,KAAK;AAAU,mCAAO,CAAC,GAAa,CAAC;AAAA,0BACrC,KAAK;AAAS,mCAAO,CAAC,GAAa,CAAC;AAAA,wBACxC;AACA,+BAAO,CAAC,GAAa,CAAC;AAAA,sBAC1B,KAAK;AAAG,+BAAO,CAAC,GAAa,KAAK,SAAS,GAAG,SAAS,MAAM,MAAM,MAAM,CAAC;AAAA,sBAC1E,KAAK;AACD,kCAAU,GAAG,KAAK;AAClB,6BAAK,SAAS;AAAA,0BACV,MAAM;AAAA,0BACN,MAAM,MAAM;AAAA,0BACZ;AAAA,wBACJ,CAAC;AACD,6BAAK,cAAc,IAAI,MAAM,MAAM,YAAY,OAAO,CAAC;AACvD,+BAAO,CAAC,GAAa,CAAC;AAAA,sBAC1B,KAAK;AACD,6BAAK,SAAS;AAAA,0BACV,MAAM;AAAA,0BACN,MAAM,MAAM;AAAA,wBAChB,CAAC;AACD,6BAAK,cAAc,OAAO,MAAM,IAAI;AACpC,+BAAO,CAAC,GAAa,CAAC;AAAA,sBAC1B,KAAK;AACD,6BAAK,SAAS;AAAA,0BACV,MAAM;AAAA,0BACN,MAAM,MAAM;AAAA,wBAChB,CAAC;AACD,6BAAK,cAAc,OAAO,MAAM,OAAO;AACvC,+BAAO,CAAC,GAAa,KAAK,SAAS,GAAG,SAAS,MAAM,SAAS,MAAM,CAAC;AAAA,sBACzE,KAAK;AACD,qCAAa,GAAG,KAAK;AACrB,6BAAK,SAAS;AAAA,0BACV,MAAM;AAAA,0BACN,MAAM,MAAM;AAAA,0BACZ,SAAS;AAAA,wBACb,CAAC;AACD,6BAAK,cAAc,IAAI,MAAM,SAAS,YAAY,UAAU,CAAC;AAC7D,+BAAO,CAAC,GAAa,CAAC;AAAA,sBAC1B,KAAK;AAAG,+BAAO,CAAC,GAAa,CAAC;AAAA,sBAC9B,KAAK;AAAG,+BAAO,CAAC,GAAa,EAAE;AAAA,sBAC/B,KAAK;AACD,gCAAQ,GAAG,KAAK;AAChB,6BAAK,SAAS;AAAA,0BACV,MAAM;AAAA,0BACN,QAAQ;AAAA,0BACR,kBAAkB;AAAA,0BAClB,OAAO,oBAAoB,KAAK;AAAA,wBACpC,CAAC;AACD,+BAAO,CAAC,GAAa,EAAE;AAAA,sBAC3B,KAAK;AAAI,+BAAO;AAAA,0BAAC;AAAA;AAAA,wBAAY;AAAA,oBACjC;AAAA,kBACJ,CAAC;AAAA,gBACL,CAAC;AAAA,cAAG,CAAC,CAAC;AAAA,YACd,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAIA,IAAAC,cAAa,UAAU,sBAAsB,WAAY;AACrD,UAAI;AACJ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,EAAE,KAAK,wBAAwB,KAAK;AAAkB,uBAAO,CAAC,GAAa,CAAC;AAEhF,mBAAK,SAAS,EAAE,MAAM,SAAS,WAAW,KAAK,CAAC;AAChD,mBAAK,SAAS;AAEd,qBAAO,CAAC,GAAa,KAAK,qBAAqB,KAAK,CAAC;AAAA,YACzD,KAAK;AAED,iBAAG,KAAK;AACR,eAAC,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,MAAM;AACjF,mBAAK,SAAS,GAAG,GAAG,uBAAuB;AAAA,gBACvC,WAAW;AAAA,gBACX,OAAO;AAAA,cACX,CAAC;AAED,qBAAO,CAAC,GAAa,KAAK,QAAQ,OAAO,YAAY,KAAK,aAAa,CAAC,CAAC;AAAA,YAC7E,KAAK;AAED,iBAAG,KAAK;AACR,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,cAAa,UAAU,gBAAgB,SAAU,OAAO;AACpD,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,UAAU,qBAAqB,MAAM,KAAK;AAI9C,YAAM,KAAK,KAAK,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,WAAW;AAChG,eAAO,QAAQ,OAAO,EAAE,QAAQ,SAAUD,KAAI;AAC1C,cAAI,MAAMA,IAAG,CAAC,GAAG,QAAQA,IAAG,CAAC;AAC7B,cAAI,CAAC,MAAM,cAAc,IAAI,GAAG,KAC5B,WAAW,KAAK,MAAM,WAAW,MAAM,cAAc,IAAI,GAAG,CAAC,GAAG;AAChE,kBAAM,SAAS,GAAG,UAAU,KAAK,OAAO,EAAE,WAAW,KAAK,CAAC;AAAA,UAC/D;AAAA,QACJ,CAAC;AACD;AAAA,MACJ;AAIA,WAAK,SAAS;AAAA,QACV,aAAa;AAAA,QACb;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,MAAM;AAAA,MACV,CAAC;AAMD,aAAO,QAAQ,OAAO,EAAE,QAAQ,SAAUA,KAAI;AAC1C,YAAI,MAAMA,IAAG,CAAC,GAAG,QAAQA,IAAG,CAAC;AAC7B,cAAM,cAAc,IAAI,KAAK,YAAY,KAAK,CAAC;AAAA,MACnD,CAAC;AAAA,IACL;AACA,IAAAC,cAAa,UAAU,WAAW,SAAU,SAAS;AACjD,UAAI,IAAI;AACR,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,QAAQ;AACb,sBAAQ,IAAI;AAAA,gBACR,KAAK;AAAW,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACtC,KAAK;AAAW,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACtC,KAAK;AAAW,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACtC,KAAK;AAAc,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACzC,KAAK;AAAiB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC5C,KAAK;AAAqB,yBAAO,CAAC,GAAa,CAAC;AAAA,cACpD;AACA,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,mBAAK,QAAQ,QAAQ,OAAO;AAC5B,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,yBAAyB,CAAC;AAAA,YAC5D,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,eAAC,MAAM,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,SAAS,GAAG;AAChJ,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,mBAAK,oBAAoB;AACzB,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,qBAAO,KAAK,KAAK,kBAAkB,QAAQ;AAC3C,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,mBAAK,QAAQ,SAAS,OAAO;AAC7B,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,cAAa,UAAU,SAAS,SAAU,UAAU;AAChD,aAAO,KAAK,QAAQ,SAAS,QAAQ;AAAA,IACzC;AACA,IAAAA,cAAa,UAAU,UAAU,WAAY;AACzC,WAAK,eAAe,OAAO;AAC3B,WAAK,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAOA;AAAA,EACX,EAAE,cAAc;AAAA;", "names": ["__publicField", "__privateGet", "__privateAdd", "__privateSet", "__privateMethod", "_a", "SandpackNode", "_b"]}