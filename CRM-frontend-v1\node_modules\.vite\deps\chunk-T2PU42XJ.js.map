{"version": 3, "sources": ["../../@mui/material/zero-styled/index.js"], "sourcesContent": ["import useThemeProps from '../styles/useThemeProps';\nexport { default as styled } from '../styles/styled';\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function createUseThemeProps(name) {\n  return useThemeProps;\n}"], "mappings": ";;;;;AAIO,SAAS,oBAAoB,MAAM;AACxC,SAAO;AACT;", "names": []}