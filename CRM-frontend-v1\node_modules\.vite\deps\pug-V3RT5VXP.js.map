{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/pug.js"], "sourcesContent": ["import {javascript} from \"./javascript.js\"\n\nvar ATTRS_NEST = {\n  '{': '}',\n  '(': ')',\n  '[': ']'\n}\n\nfunction defaultCopyState(state) {\n  if (typeof state != \"object\") return state\n  let newState = {}\n  for (let prop in state) {\n    let val = state[prop]\n    newState[prop] = val instanceof Array ? val.slice() : val\n  }\n  return newState\n}\n\nclass State {\n  constructor(indentUnit) {\n    this.indentUnit = indentUnit\n\n    this.javaScriptLine = false\n    this.javaScriptLineExcludesColon = false\n\n    this.javaScriptArguments = false\n    this.javaScriptArgumentsDepth = 0\n\n    this.isInterpolating = false\n    this.interpolationNesting = 0\n\n    this.jsState = javascript.startState(indentUnit)\n\n    this.restOfLine = ''\n\n    this.isIncludeFiltered = false\n    this.isEach = false\n\n    this.lastTag = ''\n\n    // Attributes Mode\n    this.isAttrs = false\n    this.attrsNest = []\n    this.inAttributeName = true\n    this.attributeIsType = false\n    this.attrValue = ''\n\n    // Indented Mode\n    this.indentOf = Infinity\n    this.indentToken = ''\n  }\n\n  copy() {\n    var res = new State(this.indentUnit)\n    res.javaScriptLine = this.javaScriptLine\n    res.javaScriptLineExcludesColon = this.javaScriptLineExcludesColon\n    res.javaScriptArguments = this.javaScriptArguments\n    res.javaScriptArgumentsDepth = this.javaScriptArgumentsDepth\n    res.isInterpolating = this.isInterpolating\n    res.interpolationNesting = this.interpolationNesting\n\n    res.jsState = (javascript.copyState || defaultCopyState)(this.jsState)\n\n    res.restOfLine = this.restOfLine\n\n    res.isIncludeFiltered = this.isIncludeFiltered\n    res.isEach = this.isEach\n    res.lastTag = this.lastTag\n    res.isAttrs = this.isAttrs\n    res.attrsNest = this.attrsNest.slice()\n    res.inAttributeName = this.inAttributeName\n    res.attributeIsType = this.attributeIsType\n    res.attrValue = this.attrValue\n    res.indentOf = this.indentOf\n    res.indentToken = this.indentToken\n\n    return res\n  }\n}\n\nfunction javaScript(stream, state) {\n  if (stream.sol()) {\n    // if javaScriptLine was set at end of line, ignore it\n    state.javaScriptLine = false\n    state.javaScriptLineExcludesColon = false\n  }\n  if (state.javaScriptLine) {\n    if (state.javaScriptLineExcludesColon && stream.peek() === ':') {\n      state.javaScriptLine = false\n      state.javaScriptLineExcludesColon = false\n      return\n    }\n    var tok = javascript.token(stream, state.jsState)\n    if (stream.eol()) state.javaScriptLine = false\n    return tok || true\n  }\n}\nfunction javaScriptArguments(stream, state) {\n  if (state.javaScriptArguments) {\n    if (state.javaScriptArgumentsDepth === 0 && stream.peek() !== '(') {\n      state.javaScriptArguments = false\n      return\n    }\n    if (stream.peek() === '(') {\n      state.javaScriptArgumentsDepth++\n    } else if (stream.peek() === ')') {\n      state.javaScriptArgumentsDepth--\n    }\n    if (state.javaScriptArgumentsDepth === 0) {\n      state.javaScriptArguments = false\n      return\n    }\n\n    var tok = javascript.token(stream, state.jsState)\n    return tok || true\n  }\n}\n\nfunction yieldStatement(stream) {\n  if (stream.match(/^yield\\b/)) {\n    return 'keyword'\n  }\n}\n\nfunction doctype(stream) {\n  if (stream.match(/^(?:doctype) *([^\\n]+)?/)) return 'meta'\n}\n\nfunction interpolation(stream, state) {\n  if (stream.match('#{')) {\n    state.isInterpolating = true\n    state.interpolationNesting = 0\n    return 'punctuation'\n  }\n}\n\nfunction interpolationContinued(stream, state) {\n  if (state.isInterpolating) {\n    if (stream.peek() === '}') {\n      state.interpolationNesting--\n      if (state.interpolationNesting < 0) {\n        stream.next()\n        state.isInterpolating = false\n        return 'punctuation'\n      }\n    } else if (stream.peek() === '{') {\n      state.interpolationNesting++\n    }\n    return javascript.token(stream, state.jsState) || true\n  }\n}\n\nfunction caseStatement(stream, state) {\n  if (stream.match(/^case\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction when(stream, state) {\n  if (stream.match(/^when\\b/)) {\n    state.javaScriptLine = true\n    state.javaScriptLineExcludesColon = true\n    return 'keyword'\n  }\n}\n\nfunction defaultStatement(stream) {\n  if (stream.match(/^default\\b/)) {\n    return 'keyword'\n  }\n}\n\nfunction extendsStatement(stream, state) {\n  if (stream.match(/^extends?\\b/)) {\n    state.restOfLine = 'string'\n    return 'keyword'\n  }\n}\n\nfunction append(stream, state) {\n  if (stream.match(/^append\\b/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\nfunction prepend(stream, state) {\n  if (stream.match(/^prepend\\b/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\nfunction block(stream, state) {\n  if (stream.match(/^block\\b *(?:(prepend|append)\\b)?/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\n\nfunction include(stream, state) {\n  if (stream.match(/^include\\b/)) {\n    state.restOfLine = 'string'\n    return 'keyword'\n  }\n}\n\nfunction includeFiltered(stream, state) {\n  if (stream.match(/^include:([a-zA-Z0-9\\-]+)/, false) && stream.match('include')) {\n    state.isIncludeFiltered = true\n    return 'keyword'\n  }\n}\n\nfunction includeFilteredContinued(stream, state) {\n  if (state.isIncludeFiltered) {\n    var tok = filter(stream, state)\n    state.isIncludeFiltered = false\n    state.restOfLine = 'string'\n    return tok\n  }\n}\n\nfunction mixin(stream, state) {\n  if (stream.match(/^mixin\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction call(stream, state) {\n  if (stream.match(/^\\+([-\\w]+)/)) {\n    if (!stream.match(/^\\( *[-\\w]+ *=/, false)) {\n      state.javaScriptArguments = true\n      state.javaScriptArgumentsDepth = 0\n    }\n    return 'variable'\n  }\n  if (stream.match('+#{', false)) {\n    stream.next()\n    state.mixinCallAfter = true\n    return interpolation(stream, state)\n  }\n}\nfunction callArguments(stream, state) {\n  if (state.mixinCallAfter) {\n    state.mixinCallAfter = false\n    if (!stream.match(/^\\( *[-\\w]+ *=/, false)) {\n      state.javaScriptArguments = true\n      state.javaScriptArgumentsDepth = 0\n    }\n    return true\n  }\n}\n\nfunction conditional(stream, state) {\n  if (stream.match(/^(if|unless|else if|else)\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction each(stream, state) {\n  if (stream.match(/^(- *)?(each|for)\\b/)) {\n    state.isEach = true\n    return 'keyword'\n  }\n}\nfunction eachContinued(stream, state) {\n  if (state.isEach) {\n    if (stream.match(/^ in\\b/)) {\n      state.javaScriptLine = true\n      state.isEach = false\n      return 'keyword'\n    } else if (stream.sol() || stream.eol()) {\n      state.isEach = false\n    } else if (stream.next()) {\n      while (!stream.match(/^ in\\b/, false) && stream.next()) {}\n      return 'variable'\n    }\n  }\n}\n\nfunction whileStatement(stream, state) {\n  if (stream.match(/^while\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction tag(stream, state) {\n  var captures\n  if (captures = stream.match(/^(\\w(?:[-:\\w]*\\w)?)\\/?/)) {\n    state.lastTag = captures[1].toLowerCase()\n    return 'tag'\n  }\n}\n\nfunction filter(stream, state) {\n  if (stream.match(/^:([\\w\\-]+)/)) {\n    setStringMode(stream, state)\n    return 'atom'\n  }\n}\n\nfunction code(stream, state) {\n  if (stream.match(/^(!?=|-)/)) {\n    state.javaScriptLine = true\n    return 'punctuation'\n  }\n}\n\nfunction id(stream) {\n  if (stream.match(/^#([\\w-]+)/)) {\n    return 'builtin'\n  }\n}\n\nfunction className(stream) {\n  if (stream.match(/^\\.([\\w-]+)/)) {\n    return 'className'\n  }\n}\n\nfunction attrs(stream, state) {\n  if (stream.peek() == '(') {\n    stream.next()\n    state.isAttrs = true\n    state.attrsNest = []\n    state.inAttributeName = true\n    state.attrValue = ''\n    state.attributeIsType = false\n    return 'punctuation'\n  }\n}\n\nfunction attrsContinued(stream, state) {\n  if (state.isAttrs) {\n    if (ATTRS_NEST[stream.peek()]) {\n      state.attrsNest.push(ATTRS_NEST[stream.peek()])\n    }\n    if (state.attrsNest[state.attrsNest.length - 1] === stream.peek()) {\n      state.attrsNest.pop()\n    } else if (stream.eat(')')) {\n      state.isAttrs = false\n      return 'punctuation'\n    }\n    if (state.inAttributeName && stream.match(/^[^=,\\)!]+/)) {\n      if (stream.peek() === '=' || stream.peek() === '!') {\n        state.inAttributeName = false\n        state.jsState = javascript.startState(2)\n        if (state.lastTag === 'script' && stream.current().trim().toLowerCase() === 'type') {\n          state.attributeIsType = true\n        } else {\n          state.attributeIsType = false\n        }\n      }\n      return 'attribute'\n    }\n\n    var tok = javascript.token(stream, state.jsState)\n    if (state.attrsNest.length === 0 && (tok === 'string' || tok === 'variable' || tok === 'keyword')) {\n      try {\n        Function('', 'var x ' + state.attrValue.replace(/,\\s*$/, '').replace(/^!/, ''))\n        state.inAttributeName = true\n        state.attrValue = ''\n        stream.backUp(stream.current().length)\n        return attrsContinued(stream, state)\n      } catch (ex) {\n        //not the end of an attribute\n      }\n    }\n    state.attrValue += stream.current()\n    return tok || true\n  }\n}\n\nfunction attributesBlock(stream, state) {\n  if (stream.match(/^&attributes\\b/)) {\n    state.javaScriptArguments = true\n    state.javaScriptArgumentsDepth = 0\n    return 'keyword'\n  }\n}\n\nfunction indent(stream) {\n  if (stream.sol() && stream.eatSpace()) {\n    return 'indent'\n  }\n}\n\nfunction comment(stream, state) {\n  if (stream.match(/^ *\\/\\/(-)?([^\\n]*)/)) {\n    state.indentOf = stream.indentation()\n    state.indentToken = 'comment'\n    return 'comment'\n  }\n}\n\nfunction colon(stream) {\n  if (stream.match(/^: */)) {\n    return 'colon'\n  }\n}\n\nfunction text(stream, state) {\n  if (stream.match(/^(?:\\| ?| )([^\\n]+)/)) {\n    return 'string'\n  }\n  if (stream.match(/^(<[^\\n]*)/, false)) {\n    // html string\n    setStringMode(stream, state)\n    stream.skipToEnd()\n    return state.indentToken\n  }\n}\n\nfunction dot(stream, state) {\n  if (stream.eat('.')) {\n    setStringMode(stream, state)\n    return 'dot'\n  }\n}\n\nfunction fail(stream) {\n  stream.next()\n  return null\n}\n\n\nfunction setStringMode(stream, state) {\n  state.indentOf = stream.indentation()\n  state.indentToken = 'string'\n}\nfunction restOfLine(stream, state) {\n  if (stream.sol()) {\n    // if restOfLine was set at end of line, ignore it\n    state.restOfLine = ''\n  }\n  if (state.restOfLine) {\n    stream.skipToEnd()\n    var tok = state.restOfLine\n    state.restOfLine = ''\n    return tok\n  }\n}\n\n\nfunction startState(indentUnit) {\n  return new State(indentUnit)\n}\nfunction copyState(state) {\n  return state.copy()\n}\nfunction nextToken(stream, state) {\n  var tok = restOfLine(stream, state)\n      || interpolationContinued(stream, state)\n      || includeFilteredContinued(stream, state)\n      || eachContinued(stream, state)\n      || attrsContinued(stream, state)\n      || javaScript(stream, state)\n      || javaScriptArguments(stream, state)\n      || callArguments(stream, state)\n\n      || yieldStatement(stream)\n      || doctype(stream)\n      || interpolation(stream, state)\n      || caseStatement(stream, state)\n      || when(stream, state)\n      || defaultStatement(stream)\n      || extendsStatement(stream, state)\n      || append(stream, state)\n      || prepend(stream, state)\n      || block(stream, state)\n      || include(stream, state)\n      || includeFiltered(stream, state)\n      || mixin(stream, state)\n      || call(stream, state)\n      || conditional(stream, state)\n      || each(stream, state)\n      || whileStatement(stream, state)\n      || tag(stream, state)\n      || filter(stream, state)\n      || code(stream, state)\n      || id(stream)\n      || className(stream)\n      || attrs(stream, state)\n      || attributesBlock(stream, state)\n      || indent(stream)\n      || text(stream, state)\n      || comment(stream, state)\n      || colon(stream)\n      || dot(stream, state)\n      || fail(stream)\n\n  return tok === true ? null : tok\n}\n\nexport const pug = {\n  startState: startState,\n  copyState: copyState,\n  token: nextToken\n}\n"], "mappings": ";;;;;;AAEA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,SAAS;AAAU,WAAO;AACrC,MAAI,WAAW,CAAC;AAChB,WAAS,QAAQ,OAAO;AACtB,QAAI,MAAM,MAAM,IAAI;AACpB,aAAS,IAAI,IAAI,eAAe,QAAQ,IAAI,MAAM,IAAI;AAAA,EACxD;AACA,SAAO;AACT;AAEA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV,YAAY,YAAY;AACtB,SAAK,aAAa;AAElB,SAAK,iBAAiB;AACtB,SAAK,8BAA8B;AAEnC,SAAK,sBAAsB;AAC3B,SAAK,2BAA2B;AAEhC,SAAK,kBAAkB;AACvB,SAAK,uBAAuB;AAE5B,SAAK,UAAU,WAAW,WAAW,UAAU;AAE/C,SAAK,aAAa;AAElB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AAEd,SAAK,UAAU;AAGf,SAAK,UAAU;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AAGjB,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,OAAO;AACL,QAAI,MAAM,IAAI,OAAM,KAAK,UAAU;AACnC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,8BAA8B,KAAK;AACvC,QAAI,sBAAsB,KAAK;AAC/B,QAAI,2BAA2B,KAAK;AACpC,QAAI,kBAAkB,KAAK;AAC3B,QAAI,uBAAuB,KAAK;AAEhC,QAAI,WAAW,WAAW,aAAa,kBAAkB,KAAK,OAAO;AAErE,QAAI,aAAa,KAAK;AAEtB,QAAI,oBAAoB,KAAK;AAC7B,QAAI,SAAS,KAAK;AAClB,QAAI,UAAU,KAAK;AACnB,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,KAAK,UAAU,MAAM;AACrC,QAAI,kBAAkB,KAAK;AAC3B,QAAI,kBAAkB,KAAK;AAC3B,QAAI,YAAY,KAAK;AACrB,QAAI,WAAW,KAAK;AACpB,QAAI,cAAc,KAAK;AAEvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,OAAO,IAAI,GAAG;AAEhB,UAAM,iBAAiB;AACvB,UAAM,8BAA8B;AAAA,EACtC;AACA,MAAI,MAAM,gBAAgB;AACxB,QAAI,MAAM,+BAA+B,OAAO,KAAK,MAAM,KAAK;AAC9D,YAAM,iBAAiB;AACvB,YAAM,8BAA8B;AACpC;AAAA,IACF;AACA,QAAI,MAAM,WAAW,MAAM,QAAQ,MAAM,OAAO;AAChD,QAAI,OAAO,IAAI;AAAG,YAAM,iBAAiB;AACzC,WAAO,OAAO;AAAA,EAChB;AACF;AACA,SAAS,oBAAoB,QAAQ,OAAO;AAC1C,MAAI,MAAM,qBAAqB;AAC7B,QAAI,MAAM,6BAA6B,KAAK,OAAO,KAAK,MAAM,KAAK;AACjE,YAAM,sBAAsB;AAC5B;AAAA,IACF;AACA,QAAI,OAAO,KAAK,MAAM,KAAK;AACzB,YAAM;AAAA,IACR,WAAW,OAAO,KAAK,MAAM,KAAK;AAChC,YAAM;AAAA,IACR;AACA,QAAI,MAAM,6BAA6B,GAAG;AACxC,YAAM,sBAAsB;AAC5B;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,MAAM,QAAQ,MAAM,OAAO;AAChD,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,SAAS,eAAe,QAAQ;AAC9B,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,QAAQ,QAAQ;AACvB,MAAI,OAAO,MAAM,yBAAyB;AAAG,WAAO;AACtD;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM,kBAAkB;AACxB,UAAM,uBAAuB;AAC7B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,MAAI,MAAM,iBAAiB;AACzB,QAAI,OAAO,KAAK,MAAM,KAAK;AACzB,YAAM;AACN,UAAI,MAAM,uBAAuB,GAAG;AAClC,eAAO,KAAK;AACZ,cAAM,kBAAkB;AACxB,eAAO;AAAA,MACT;AAAA,IACF,WAAW,OAAO,KAAK,MAAM,KAAK;AAChC,YAAM;AAAA,IACR;AACA,WAAO,WAAW,MAAM,QAAQ,MAAM,OAAO,KAAK;AAAA,EACpD;AACF;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,UAAM,iBAAiB;AACvB,UAAM,8BAA8B;AACpC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,MAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AACA,SAAS,MAAM,QAAQ,OAAO;AAC5B,MAAI,OAAO,MAAM,mCAAmC,GAAG;AACrD,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,MAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI,OAAO,MAAM,6BAA6B,KAAK,KAAK,OAAO,MAAM,SAAS,GAAG;AAC/E,UAAM,oBAAoB;AAC1B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,yBAAyB,QAAQ,OAAO;AAC/C,MAAI,MAAM,mBAAmB;AAC3B,QAAI,MAAM,OAAO,QAAQ,KAAK;AAC9B,UAAM,oBAAoB;AAC1B,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAM,QAAQ,OAAO;AAC5B,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,QAAI,CAAC,OAAO,MAAM,kBAAkB,KAAK,GAAG;AAC1C,YAAM,sBAAsB;AAC5B,YAAM,2BAA2B;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,KAAK,GAAG;AAC9B,WAAO,KAAK;AACZ,UAAM,iBAAiB;AACvB,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AACF;AACA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,MAAM,gBAAgB;AACxB,UAAM,iBAAiB;AACvB,QAAI,CAAC,OAAO,MAAM,kBAAkB,KAAK,GAAG;AAC1C,YAAM,sBAAsB;AAC5B,YAAM,2BAA2B;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,OAAO,MAAM,6BAA6B,GAAG;AAC/C,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,MAAM,qBAAqB,GAAG;AACvC,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,MAAM,QAAQ;AAChB,QAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,YAAM,iBAAiB;AACvB,YAAM,SAAS;AACf,aAAO;AAAA,IACT,WAAW,OAAO,IAAI,KAAK,OAAO,IAAI,GAAG;AACvC,YAAM,SAAS;AAAA,IACjB,WAAW,OAAO,KAAK,GAAG;AACxB,aAAO,CAAC,OAAO,MAAM,UAAU,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,MAAC;AACzD,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,IAAI,QAAQ,OAAO;AAC1B,MAAI;AACJ,MAAI,WAAW,OAAO,MAAM,wBAAwB,GAAG;AACrD,UAAM,UAAU,SAAS,CAAC,EAAE,YAAY;AACxC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,kBAAc,QAAQ,KAAK;AAC3B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,GAAG,QAAQ;AAClB,MAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAU,QAAQ;AACzB,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAM,QAAQ,OAAO;AAC5B,MAAI,OAAO,KAAK,KAAK,KAAK;AACxB,WAAO,KAAK;AACZ,UAAM,UAAU;AAChB,UAAM,YAAY,CAAC;AACnB,UAAM,kBAAkB;AACxB,UAAM,YAAY;AAClB,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,MAAM,SAAS;AACjB,QAAI,WAAW,OAAO,KAAK,CAAC,GAAG;AAC7B,YAAM,UAAU,KAAK,WAAW,OAAO,KAAK,CAAC,CAAC;AAAA,IAChD;AACA,QAAI,MAAM,UAAU,MAAM,UAAU,SAAS,CAAC,MAAM,OAAO,KAAK,GAAG;AACjE,YAAM,UAAU,IAAI;AAAA,IACtB,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,YAAM,UAAU;AAChB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,mBAAmB,OAAO,MAAM,YAAY,GAAG;AACvD,UAAI,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK;AAClD,cAAM,kBAAkB;AACxB,cAAM,UAAU,WAAW,WAAW,CAAC;AACvC,YAAI,MAAM,YAAY,YAAY,OAAO,QAAQ,EAAE,KAAK,EAAE,YAAY,MAAM,QAAQ;AAClF,gBAAM,kBAAkB;AAAA,QAC1B,OAAO;AACL,gBAAM,kBAAkB;AAAA,QAC1B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,WAAW,MAAM,QAAQ,MAAM,OAAO;AAChD,QAAI,MAAM,UAAU,WAAW,MAAM,QAAQ,YAAY,QAAQ,cAAc,QAAQ,YAAY;AACjG,UAAI;AACF,iBAAS,IAAI,WAAW,MAAM,UAAU,QAAQ,SAAS,EAAE,EAAE,QAAQ,MAAM,EAAE,CAAC;AAC9E,cAAM,kBAAkB;AACxB,cAAM,YAAY;AAClB,eAAO,OAAO,OAAO,QAAQ,EAAE,MAAM;AACrC,eAAO,eAAe,QAAQ,KAAK;AAAA,MACrC,SAAS,IAAI;AAAA,MAEb;AAAA,IACF;AACA,UAAM,aAAa,OAAO,QAAQ;AAClC,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,UAAM,sBAAsB;AAC5B,UAAM,2BAA2B;AACjC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,QAAQ;AACtB,MAAI,OAAO,IAAI,KAAK,OAAO,SAAS,GAAG;AACrC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,MAAI,OAAO,MAAM,qBAAqB,GAAG;AACvC,UAAM,WAAW,OAAO,YAAY;AACpC,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAM,QAAQ;AACrB,MAAI,OAAO,MAAM,MAAM,GAAG;AACxB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,MAAM,qBAAqB,GAAG;AACvC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AAErC,kBAAc,QAAQ,KAAK;AAC3B,WAAO,UAAU;AACjB,WAAO,MAAM;AAAA,EACf;AACF;AAEA,SAAS,IAAI,QAAQ,OAAO;AAC1B,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,kBAAc,QAAQ,KAAK;AAC3B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ;AACpB,SAAO,KAAK;AACZ,SAAO;AACT;AAGA,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,WAAW,OAAO,YAAY;AACpC,QAAM,cAAc;AACtB;AACA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,OAAO,IAAI,GAAG;AAEhB,UAAM,aAAa;AAAA,EACrB;AACA,MAAI,MAAM,YAAY;AACpB,WAAO,UAAU;AACjB,QAAI,MAAM,MAAM;AAChB,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AACF;AAGA,SAAS,WAAW,YAAY;AAC9B,SAAO,IAAI,MAAM,UAAU;AAC7B;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,KAAK;AACpB;AACA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,MAAM,WAAW,QAAQ,KAAK,KAC3B,uBAAuB,QAAQ,KAAK,KACpC,yBAAyB,QAAQ,KAAK,KACtC,cAAc,QAAQ,KAAK,KAC3B,eAAe,QAAQ,KAAK,KAC5B,WAAW,QAAQ,KAAK,KACxB,oBAAoB,QAAQ,KAAK,KACjC,cAAc,QAAQ,KAAK,KAE3B,eAAe,MAAM,KACrB,QAAQ,MAAM,KACd,cAAc,QAAQ,KAAK,KAC3B,cAAc,QAAQ,KAAK,KAC3B,KAAK,QAAQ,KAAK,KAClB,iBAAiB,MAAM,KACvB,iBAAiB,QAAQ,KAAK,KAC9B,OAAO,QAAQ,KAAK,KACpB,QAAQ,QAAQ,KAAK,KACrB,MAAM,QAAQ,KAAK,KACnB,QAAQ,QAAQ,KAAK,KACrB,gBAAgB,QAAQ,KAAK,KAC7B,MAAM,QAAQ,KAAK,KACnB,KAAK,QAAQ,KAAK,KAClB,YAAY,QAAQ,KAAK,KACzB,KAAK,QAAQ,KAAK,KAClB,eAAe,QAAQ,KAAK,KAC5B,IAAI,QAAQ,KAAK,KACjB,OAAO,QAAQ,KAAK,KACpB,KAAK,QAAQ,KAAK,KAClB,GAAG,MAAM,KACT,UAAU,MAAM,KAChB,MAAM,QAAQ,KAAK,KACnB,gBAAgB,QAAQ,KAAK,KAC7B,OAAO,MAAM,KACb,KAAK,QAAQ,KAAK,KAClB,QAAQ,QAAQ,KAAK,KACrB,MAAM,MAAM,KACZ,IAAI,QAAQ,KAAK,KACjB,KAAK,MAAM;AAElB,SAAO,QAAQ,OAAO,OAAO;AAC/B;AAEO,IAAM,MAAM;AAAA,EACjB;AAAA,EACA;AAAA,EACA,OAAO;AACT;", "names": []}