{"version": 3, "sources": ["../../turndown/lib/turndown.browser.es.js"], "sourcesContent": ["function extend (destination) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (source.hasOwnProperty(key)) destination[key] = source[key];\n    }\n  }\n  return destination\n}\n\nfunction repeat (character, count) {\n  return Array(count + 1).join(character)\n}\n\nfunction trimLeadingNewlines (string) {\n  return string.replace(/^\\n*/, '')\n}\n\nfunction trimTrailingNewlines (string) {\n  // avoid match-at-end regexp bottleneck, see #370\n  var indexEnd = string.length;\n  while (indexEnd > 0 && string[indexEnd - 1] === '\\n') indexEnd--;\n  return string.substring(0, indexEnd)\n}\n\nvar blockElements = [\n  'ADDRESS', 'ARTICLE', 'ASIDE', 'AUDIO', 'BLOCKQUOTE', 'BODY', 'CANVAS',\n  'CENTER', 'DD', 'DIR', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE',\n  'FOOTER', 'FORM', 'FRAMESET', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER',\n  'HGROUP', 'HR', 'HTML', 'ISINDEX', 'LI', 'MAIN', 'MENU', 'NAV', 'NOFRAMES',\n  'NOSCRIPT', 'OL', 'OUTPUT', 'P', 'PRE', 'SECTION', 'TABLE', 'TBODY', 'TD',\n  'TFOOT', 'TH', 'THEAD', 'TR', 'UL'\n];\n\nfunction isBlock (node) {\n  return is(node, blockElements)\n}\n\nvar voidElements = [\n  'AREA', 'BASE', 'BR', 'COL', 'COMMAND', 'EMBED', 'HR', 'IMG', 'INPUT',\n  'KEYGEN', 'LINK', 'META', 'PARAM', 'SOURCE', 'TRACK', 'WBR'\n];\n\nfunction isVoid (node) {\n  return is(node, voidElements)\n}\n\nfunction hasVoid (node) {\n  return has(node, voidElements)\n}\n\nvar meaningfulWhenBlankElements = [\n  'A', 'TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TH', 'TD', 'IFRAME', 'SCRIPT',\n  'AUDIO', 'VIDEO'\n];\n\nfunction isMeaningfulWhenBlank (node) {\n  return is(node, meaningfulWhenBlankElements)\n}\n\nfunction hasMeaningfulWhenBlank (node) {\n  return has(node, meaningfulWhenBlankElements)\n}\n\nfunction is (node, tagNames) {\n  return tagNames.indexOf(node.nodeName) >= 0\n}\n\nfunction has (node, tagNames) {\n  return (\n    node.getElementsByTagName &&\n    tagNames.some(function (tagName) {\n      return node.getElementsByTagName(tagName).length\n    })\n  )\n}\n\nvar rules = {};\n\nrules.paragraph = {\n  filter: 'p',\n\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.lineBreak = {\n  filter: 'br',\n\n  replacement: function (content, node, options) {\n    return options.br + '\\n'\n  }\n};\n\nrules.heading = {\n  filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n  replacement: function (content, node, options) {\n    var hLevel = Number(node.nodeName.charAt(1));\n\n    if (options.headingStyle === 'setext' && hLevel < 3) {\n      var underline = repeat((hLevel === 1 ? '=' : '-'), content.length);\n      return (\n        '\\n\\n' + content + '\\n' + underline + '\\n\\n'\n      )\n    } else {\n      return '\\n\\n' + repeat('#', hLevel) + ' ' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.blockquote = {\n  filter: 'blockquote',\n\n  replacement: function (content) {\n    content = content.replace(/^\\n+|\\n+$/g, '');\n    content = content.replace(/^/gm, '> ');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.list = {\n  filter: ['ul', 'ol'],\n\n  replacement: function (content, node) {\n    var parent = node.parentNode;\n    if (parent.nodeName === 'LI' && parent.lastElementChild === node) {\n      return '\\n' + content\n    } else {\n      return '\\n\\n' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.listItem = {\n  filter: 'li',\n\n  replacement: function (content, node, options) {\n    content = content\n      .replace(/^\\n+/, '') // remove leading newlines\n      .replace(/\\n+$/, '\\n') // replace trailing newlines with just a single one\n      .replace(/\\n/gm, '\\n    '); // indent\n    var prefix = options.bulletListMarker + '   ';\n    var parent = node.parentNode;\n    if (parent.nodeName === 'OL') {\n      var start = parent.getAttribute('start');\n      var index = Array.prototype.indexOf.call(parent.children, node);\n      prefix = (start ? Number(start) + index : index + 1) + '.  ';\n    }\n    return (\n      prefix + content + (node.nextSibling && !/\\n$/.test(content) ? '\\n' : '')\n    )\n  }\n};\n\nrules.indentedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'indented' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    return (\n      '\\n\\n    ' +\n      node.firstChild.textContent.replace(/\\n/g, '\\n    ') +\n      '\\n\\n'\n    )\n  }\n};\n\nrules.fencedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'fenced' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var className = node.firstChild.getAttribute('class') || '';\n    var language = (className.match(/language-(\\S+)/) || [null, ''])[1];\n    var code = node.firstChild.textContent;\n\n    var fenceChar = options.fence.charAt(0);\n    var fenceSize = 3;\n    var fenceInCodeRegex = new RegExp('^' + fenceChar + '{3,}', 'gm');\n\n    var match;\n    while ((match = fenceInCodeRegex.exec(code))) {\n      if (match[0].length >= fenceSize) {\n        fenceSize = match[0].length + 1;\n      }\n    }\n\n    var fence = repeat(fenceChar, fenceSize);\n\n    return (\n      '\\n\\n' + fence + language + '\\n' +\n      code.replace(/\\n$/, '') +\n      '\\n' + fence + '\\n\\n'\n    )\n  }\n};\n\nrules.horizontalRule = {\n  filter: 'hr',\n\n  replacement: function (content, node, options) {\n    return '\\n\\n' + options.hr + '\\n\\n'\n  }\n};\n\nrules.inlineLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'inlined' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node) {\n    var href = node.getAttribute('href');\n    if (href) href = href.replace(/([()])/g, '\\\\$1');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title.replace(/\"/g, '\\\\\"') + '\"';\n    return '[' + content + '](' + href + title + ')'\n  }\n};\n\nrules.referenceLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'referenced' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var href = node.getAttribute('href');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title + '\"';\n    var replacement;\n    var reference;\n\n    switch (options.linkReferenceStyle) {\n      case 'collapsed':\n        replacement = '[' + content + '][]';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      case 'shortcut':\n        replacement = '[' + content + ']';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      default:\n        var id = this.references.length + 1;\n        replacement = '[' + content + '][' + id + ']';\n        reference = '[' + id + ']: ' + href + title;\n    }\n\n    this.references.push(reference);\n    return replacement\n  },\n\n  references: [],\n\n  append: function (options) {\n    var references = '';\n    if (this.references.length) {\n      references = '\\n\\n' + this.references.join('\\n') + '\\n\\n';\n      this.references = []; // Reset references\n    }\n    return references\n  }\n};\n\nrules.emphasis = {\n  filter: ['em', 'i'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.emDelimiter + content + options.emDelimiter\n  }\n};\n\nrules.strong = {\n  filter: ['strong', 'b'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.strongDelimiter + content + options.strongDelimiter\n  }\n};\n\nrules.code = {\n  filter: function (node) {\n    var hasSiblings = node.previousSibling || node.nextSibling;\n    var isCodeBlock = node.parentNode.nodeName === 'PRE' && !hasSiblings;\n\n    return node.nodeName === 'CODE' && !isCodeBlock\n  },\n\n  replacement: function (content) {\n    if (!content) return ''\n    content = content.replace(/\\r?\\n|\\r/g, ' ');\n\n    var extraSpace = /^`|^ .*?[^ ].* $|`$/.test(content) ? ' ' : '';\n    var delimiter = '`';\n    var matches = content.match(/`+/gm) || [];\n    while (matches.indexOf(delimiter) !== -1) delimiter = delimiter + '`';\n\n    return delimiter + extraSpace + content + extraSpace + delimiter\n  }\n};\n\nrules.image = {\n  filter: 'img',\n\n  replacement: function (content, node) {\n    var alt = cleanAttribute(node.getAttribute('alt'));\n    var src = node.getAttribute('src') || '';\n    var title = cleanAttribute(node.getAttribute('title'));\n    var titlePart = title ? ' \"' + title + '\"' : '';\n    return src ? '![' + alt + ']' + '(' + src + titlePart + ')' : ''\n  }\n};\n\nfunction cleanAttribute (attribute) {\n  return attribute ? attribute.replace(/(\\n+\\s*)+/g, '\\n') : ''\n}\n\n/**\n * Manages a collection of rules used to convert HTML to Markdown\n */\n\nfunction Rules (options) {\n  this.options = options;\n  this._keep = [];\n  this._remove = [];\n\n  this.blankRule = {\n    replacement: options.blankReplacement\n  };\n\n  this.keepReplacement = options.keepReplacement;\n\n  this.defaultRule = {\n    replacement: options.defaultReplacement\n  };\n\n  this.array = [];\n  for (var key in options.rules) this.array.push(options.rules[key]);\n}\n\nRules.prototype = {\n  add: function (key, rule) {\n    this.array.unshift(rule);\n  },\n\n  keep: function (filter) {\n    this._keep.unshift({\n      filter: filter,\n      replacement: this.keepReplacement\n    });\n  },\n\n  remove: function (filter) {\n    this._remove.unshift({\n      filter: filter,\n      replacement: function () {\n        return ''\n      }\n    });\n  },\n\n  forNode: function (node) {\n    if (node.isBlank) return this.blankRule\n    var rule;\n\n    if ((rule = findRule(this.array, node, this.options))) return rule\n    if ((rule = findRule(this._keep, node, this.options))) return rule\n    if ((rule = findRule(this._remove, node, this.options))) return rule\n\n    return this.defaultRule\n  },\n\n  forEach: function (fn) {\n    for (var i = 0; i < this.array.length; i++) fn(this.array[i], i);\n  }\n};\n\nfunction findRule (rules, node, options) {\n  for (var i = 0; i < rules.length; i++) {\n    var rule = rules[i];\n    if (filterValue(rule, node, options)) return rule\n  }\n  return void 0\n}\n\nfunction filterValue (rule, node, options) {\n  var filter = rule.filter;\n  if (typeof filter === 'string') {\n    if (filter === node.nodeName.toLowerCase()) return true\n  } else if (Array.isArray(filter)) {\n    if (filter.indexOf(node.nodeName.toLowerCase()) > -1) return true\n  } else if (typeof filter === 'function') {\n    if (filter.call(rule, node, options)) return true\n  } else {\n    throw new TypeError('`filter` needs to be a string, array, or function')\n  }\n}\n\n/**\n * The collapseWhitespace function is adapted from collapse-whitespace\n * by Luc Thevenard.\n *\n * The MIT License (MIT)\n *\n * Copyright (c) 2014 Luc Thevenard <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * collapseWhitespace(options) removes extraneous whitespace from an the given element.\n *\n * @param {Object} options\n */\nfunction collapseWhitespace (options) {\n  var element = options.element;\n  var isBlock = options.isBlock;\n  var isVoid = options.isVoid;\n  var isPre = options.isPre || function (node) {\n    return node.nodeName === 'PRE'\n  };\n\n  if (!element.firstChild || isPre(element)) return\n\n  var prevText = null;\n  var keepLeadingWs = false;\n\n  var prev = null;\n  var node = next(prev, element, isPre);\n\n  while (node !== element) {\n    if (node.nodeType === 3 || node.nodeType === 4) { // Node.TEXT_NODE or Node.CDATA_SECTION_NODE\n      var text = node.data.replace(/[ \\r\\n\\t]+/g, ' ');\n\n      if ((!prevText || / $/.test(prevText.data)) &&\n          !keepLeadingWs && text[0] === ' ') {\n        text = text.substr(1);\n      }\n\n      // `text` might be empty at this point.\n      if (!text) {\n        node = remove(node);\n        continue\n      }\n\n      node.data = text;\n\n      prevText = node;\n    } else if (node.nodeType === 1) { // Node.ELEMENT_NODE\n      if (isBlock(node) || node.nodeName === 'BR') {\n        if (prevText) {\n          prevText.data = prevText.data.replace(/ $/, '');\n        }\n\n        prevText = null;\n        keepLeadingWs = false;\n      } else if (isVoid(node) || isPre(node)) {\n        // Avoid trimming space around non-block, non-BR void elements and inline PRE.\n        prevText = null;\n        keepLeadingWs = true;\n      } else if (prevText) {\n        // Drop protection if set previously.\n        keepLeadingWs = false;\n      }\n    } else {\n      node = remove(node);\n      continue\n    }\n\n    var nextNode = next(prev, node, isPre);\n    prev = node;\n    node = nextNode;\n  }\n\n  if (prevText) {\n    prevText.data = prevText.data.replace(/ $/, '');\n    if (!prevText.data) {\n      remove(prevText);\n    }\n  }\n}\n\n/**\n * remove(node) removes the given node from the DOM and returns the\n * next node in the sequence.\n *\n * @param {Node} node\n * @return {Node} node\n */\nfunction remove (node) {\n  var next = node.nextSibling || node.parentNode;\n\n  node.parentNode.removeChild(node);\n\n  return next\n}\n\n/**\n * next(prev, current, isPre) returns the next node in the sequence, given the\n * current and previous nodes.\n *\n * @param {Node} prev\n * @param {Node} current\n * @param {Function} isPre\n * @return {Node}\n */\nfunction next (prev, current, isPre) {\n  if ((prev && prev.parentNode === current) || isPre(current)) {\n    return current.nextSibling || current.parentNode\n  }\n\n  return current.firstChild || current.nextSibling || current.parentNode\n}\n\n/*\n * Set up window for Node.js\n */\n\nvar root = (typeof window !== 'undefined' ? window : {});\n\n/*\n * Parsing HTML strings\n */\n\nfunction canParseHTMLNatively () {\n  var Parser = root.DOMParser;\n  var canParse = false;\n\n  // Adapted from https://gist.github.com/1129031\n  // Firefox/Opera/IE throw errors on unsupported types\n  try {\n    // WebKit returns null on unsupported types\n    if (new Parser().parseFromString('', 'text/html')) {\n      canParse = true;\n    }\n  } catch (e) {}\n\n  return canParse\n}\n\nfunction createHTMLParser () {\n  var Parser = function () {};\n\n  {\n    if (shouldUseActiveX()) {\n      Parser.prototype.parseFromString = function (string) {\n        var doc = new window.ActiveXObject('htmlfile');\n        doc.designMode = 'on'; // disable on-page scripts\n        doc.open();\n        doc.write(string);\n        doc.close();\n        return doc\n      };\n    } else {\n      Parser.prototype.parseFromString = function (string) {\n        var doc = document.implementation.createHTMLDocument('');\n        doc.open();\n        doc.write(string);\n        doc.close();\n        return doc\n      };\n    }\n  }\n  return Parser\n}\n\nfunction shouldUseActiveX () {\n  var useActiveX = false;\n  try {\n    document.implementation.createHTMLDocument('').open();\n  } catch (e) {\n    if (root.ActiveXObject) useActiveX = true;\n  }\n  return useActiveX\n}\n\nvar HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();\n\nfunction RootNode (input, options) {\n  var root;\n  if (typeof input === 'string') {\n    var doc = htmlParser().parseFromString(\n      // DOM parsers arrange elements in the <head> and <body>.\n      // Wrapping in a custom element ensures elements are reliably arranged in\n      // a single element.\n      '<x-turndown id=\"turndown-root\">' + input + '</x-turndown>',\n      'text/html'\n    );\n    root = doc.getElementById('turndown-root');\n  } else {\n    root = input.cloneNode(true);\n  }\n  collapseWhitespace({\n    element: root,\n    isBlock: isBlock,\n    isVoid: isVoid,\n    isPre: options.preformattedCode ? isPreOrCode : null\n  });\n\n  return root\n}\n\nvar _htmlParser;\nfunction htmlParser () {\n  _htmlParser = _htmlParser || new HTMLParser();\n  return _htmlParser\n}\n\nfunction isPreOrCode (node) {\n  return node.nodeName === 'PRE' || node.nodeName === 'CODE'\n}\n\nfunction Node (node, options) {\n  node.isBlock = isBlock(node);\n  node.isCode = node.nodeName === 'CODE' || node.parentNode.isCode;\n  node.isBlank = isBlank(node);\n  node.flankingWhitespace = flankingWhitespace(node, options);\n  return node\n}\n\nfunction isBlank (node) {\n  return (\n    !isVoid(node) &&\n    !isMeaningfulWhenBlank(node) &&\n    /^\\s*$/i.test(node.textContent) &&\n    !hasVoid(node) &&\n    !hasMeaningfulWhenBlank(node)\n  )\n}\n\nfunction flankingWhitespace (node, options) {\n  if (node.isBlock || (options.preformattedCode && node.isCode)) {\n    return { leading: '', trailing: '' }\n  }\n\n  var edges = edgeWhitespace(node.textContent);\n\n  // abandon leading ASCII WS if left-flanked by ASCII WS\n  if (edges.leadingAscii && isFlankedByWhitespace('left', node, options)) {\n    edges.leading = edges.leadingNonAscii;\n  }\n\n  // abandon trailing ASCII WS if right-flanked by ASCII WS\n  if (edges.trailingAscii && isFlankedByWhitespace('right', node, options)) {\n    edges.trailing = edges.trailingNonAscii;\n  }\n\n  return { leading: edges.leading, trailing: edges.trailing }\n}\n\nfunction edgeWhitespace (string) {\n  var m = string.match(/^(([ \\t\\r\\n]*)(\\s*))(?:(?=\\S)[\\s\\S]*\\S)?((\\s*?)([ \\t\\r\\n]*))$/);\n  return {\n    leading: m[1], // whole string for whitespace-only strings\n    leadingAscii: m[2],\n    leadingNonAscii: m[3],\n    trailing: m[4], // empty for whitespace-only strings\n    trailingNonAscii: m[5],\n    trailingAscii: m[6]\n  }\n}\n\nfunction isFlankedByWhitespace (side, node, options) {\n  var sibling;\n  var regExp;\n  var isFlanked;\n\n  if (side === 'left') {\n    sibling = node.previousSibling;\n    regExp = / $/;\n  } else {\n    sibling = node.nextSibling;\n    regExp = /^ /;\n  }\n\n  if (sibling) {\n    if (sibling.nodeType === 3) {\n      isFlanked = regExp.test(sibling.nodeValue);\n    } else if (options.preformattedCode && sibling.nodeName === 'CODE') {\n      isFlanked = false;\n    } else if (sibling.nodeType === 1 && !isBlock(sibling)) {\n      isFlanked = regExp.test(sibling.textContent);\n    }\n  }\n  return isFlanked\n}\n\nvar reduce = Array.prototype.reduce;\nvar escapes = [\n  [/\\\\/g, '\\\\\\\\'],\n  [/\\*/g, '\\\\*'],\n  [/^-/g, '\\\\-'],\n  [/^\\+ /g, '\\\\+ '],\n  [/^(=+)/g, '\\\\$1'],\n  [/^(#{1,6}) /g, '\\\\$1 '],\n  [/`/g, '\\\\`'],\n  [/^~~~/g, '\\\\~~~'],\n  [/\\[/g, '\\\\['],\n  [/\\]/g, '\\\\]'],\n  [/^>/g, '\\\\>'],\n  [/_/g, '\\\\_'],\n  [/^(\\d+)\\. /g, '$1\\\\. ']\n];\n\nfunction TurndownService (options) {\n  if (!(this instanceof TurndownService)) return new TurndownService(options)\n\n  var defaults = {\n    rules: rules,\n    headingStyle: 'setext',\n    hr: '* * *',\n    bulletListMarker: '*',\n    codeBlockStyle: 'indented',\n    fence: '```',\n    emDelimiter: '_',\n    strongDelimiter: '**',\n    linkStyle: 'inlined',\n    linkReferenceStyle: 'full',\n    br: '  ',\n    preformattedCode: false,\n    blankReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' : ''\n    },\n    keepReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + node.outerHTML + '\\n\\n' : node.outerHTML\n    },\n    defaultReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + content + '\\n\\n' : content\n    }\n  };\n  this.options = extend({}, defaults, options);\n  this.rules = new Rules(this.options);\n}\n\nTurndownService.prototype = {\n  /**\n   * The entry point for converting a string or DOM node to Markdown\n   * @public\n   * @param {String|HTMLElement} input The string or DOM node to convert\n   * @returns A Markdown representation of the input\n   * @type String\n   */\n\n  turndown: function (input) {\n    if (!canConvert(input)) {\n      throw new TypeError(\n        input + ' is not a string, or an element/document/fragment node.'\n      )\n    }\n\n    if (input === '') return ''\n\n    var output = process.call(this, new RootNode(input, this.options));\n    return postProcess.call(this, output)\n  },\n\n  /**\n   * Add one or more plugins\n   * @public\n   * @param {Function|Array} plugin The plugin or array of plugins to add\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  use: function (plugin) {\n    if (Array.isArray(plugin)) {\n      for (var i = 0; i < plugin.length; i++) this.use(plugin[i]);\n    } else if (typeof plugin === 'function') {\n      plugin(this);\n    } else {\n      throw new TypeError('plugin must be a Function or an Array of Functions')\n    }\n    return this\n  },\n\n  /**\n   * Adds a rule\n   * @public\n   * @param {String} key The unique key of the rule\n   * @param {Object} rule The rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  addRule: function (key, rule) {\n    this.rules.add(key, rule);\n    return this\n  },\n\n  /**\n   * Keep a node (as HTML) that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  keep: function (filter) {\n    this.rules.keep(filter);\n    return this\n  },\n\n  /**\n   * Remove a node that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  remove: function (filter) {\n    this.rules.remove(filter);\n    return this\n  },\n\n  /**\n   * Escapes Markdown syntax\n   * @public\n   * @param {String} string The string to escape\n   * @returns A string with Markdown syntax escaped\n   * @type String\n   */\n\n  escape: function (string) {\n    return escapes.reduce(function (accumulator, escape) {\n      return accumulator.replace(escape[0], escape[1])\n    }, string)\n  }\n};\n\n/**\n * Reduces a DOM node down to its Markdown string equivalent\n * @private\n * @param {HTMLElement} parentNode The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction process (parentNode) {\n  var self = this;\n  return reduce.call(parentNode.childNodes, function (output, node) {\n    node = new Node(node, self.options);\n\n    var replacement = '';\n    if (node.nodeType === 3) {\n      replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);\n    } else if (node.nodeType === 1) {\n      replacement = replacementForNode.call(self, node);\n    }\n\n    return join(output, replacement)\n  }, '')\n}\n\n/**\n * Appends strings as each rule requires and trims the output\n * @private\n * @param {String} output The conversion output\n * @returns A trimmed version of the ouput\n * @type String\n */\n\nfunction postProcess (output) {\n  var self = this;\n  this.rules.forEach(function (rule) {\n    if (typeof rule.append === 'function') {\n      output = join(output, rule.append(self.options));\n    }\n  });\n\n  return output.replace(/^[\\t\\r\\n]+/, '').replace(/[\\t\\r\\n\\s]+$/, '')\n}\n\n/**\n * Converts an element node to its Markdown equivalent\n * @private\n * @param {HTMLElement} node The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction replacementForNode (node) {\n  var rule = this.rules.forNode(node);\n  var content = process.call(this, node);\n  var whitespace = node.flankingWhitespace;\n  if (whitespace.leading || whitespace.trailing) content = content.trim();\n  return (\n    whitespace.leading +\n    rule.replacement(content, node, this.options) +\n    whitespace.trailing\n  )\n}\n\n/**\n * Joins replacement to the current output with appropriate number of new lines\n * @private\n * @param {String} output The current conversion output\n * @param {String} replacement The string to append to the output\n * @returns Joined output\n * @type String\n */\n\nfunction join (output, replacement) {\n  var s1 = trimTrailingNewlines(output);\n  var s2 = trimLeadingNewlines(replacement);\n  var nls = Math.max(output.length - s1.length, replacement.length - s2.length);\n  var separator = '\\n\\n'.substring(0, nls);\n\n  return s1 + separator + s2\n}\n\n/**\n * Determines whether an input can be converted\n * @private\n * @param {String|HTMLElement} input Describe this parameter\n * @returns Describe what it returns\n * @type String|Object|Array|Boolean|Number\n */\n\nfunction canConvert (input) {\n  return (\n    input != null && (\n      typeof input === 'string' ||\n      (input.nodeType && (\n        input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11\n      ))\n    )\n  )\n}\n\nexport default TurndownService;\n"], "mappings": ";;;AAAA,SAAS,OAAQ,aAAa;AAC5B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC;AACxB,aAAS,OAAO,QAAQ;AACtB,UAAI,OAAO,eAAe,GAAG;AAAG,oBAAY,GAAG,IAAI,OAAO,GAAG;AAAA,IAC/D;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,OAAQ,WAAW,OAAO;AACjC,SAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,SAAS;AACxC;AAEA,SAAS,oBAAqB,QAAQ;AACpC,SAAO,OAAO,QAAQ,QAAQ,EAAE;AAClC;AAEA,SAAS,qBAAsB,QAAQ;AAErC,MAAI,WAAW,OAAO;AACtB,SAAO,WAAW,KAAK,OAAO,WAAW,CAAC,MAAM;AAAM;AACtD,SAAO,OAAO,UAAU,GAAG,QAAQ;AACrC;AAEA,IAAI,gBAAgB;AAAA,EAClB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAC9D;AAAA,EAAU;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAY;AAAA,EAAc;AAAA,EACpE;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAClE;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAChE;AAAA,EAAY;AAAA,EAAM;AAAA,EAAU;AAAA,EAAK;AAAA,EAAO;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EACrE;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAAM;AAChC;AAEA,SAAS,QAAS,MAAM;AACtB,SAAO,GAAG,MAAM,aAAa;AAC/B;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAO;AAAA,EAAW;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAC9D;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AACxD;AAEA,SAAS,OAAQ,MAAM;AACrB,SAAO,GAAG,MAAM,YAAY;AAC9B;AAEA,SAAS,QAAS,MAAM;AACtB,SAAO,IAAI,MAAM,YAAY;AAC/B;AAEA,IAAI,8BAA8B;AAAA,EAChC;AAAA,EAAK;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAM;AAAA,EAAU;AAAA,EAC/D;AAAA,EAAS;AACX;AAEA,SAAS,sBAAuB,MAAM;AACpC,SAAO,GAAG,MAAM,2BAA2B;AAC7C;AAEA,SAAS,uBAAwB,MAAM;AACrC,SAAO,IAAI,MAAM,2BAA2B;AAC9C;AAEA,SAAS,GAAI,MAAM,UAAU;AAC3B,SAAO,SAAS,QAAQ,KAAK,QAAQ,KAAK;AAC5C;AAEA,SAAS,IAAK,MAAM,UAAU;AAC5B,SACE,KAAK,wBACL,SAAS,KAAK,SAAU,SAAS;AAC/B,WAAO,KAAK,qBAAqB,OAAO,EAAE;AAAA,EAC5C,CAAC;AAEL;AAEA,IAAI,QAAQ,CAAC;AAEb,MAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS;AAC9B,WAAO,SAAS,UAAU;AAAA,EAC5B;AACF;AAEA,MAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,WAAO,QAAQ,KAAK;AAAA,EACtB;AACF;AAEA,MAAM,UAAU;AAAA,EACd,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAE3C,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,QAAI,SAAS,OAAO,KAAK,SAAS,OAAO,CAAC,CAAC;AAE3C,QAAI,QAAQ,iBAAiB,YAAY,SAAS,GAAG;AACnD,UAAI,YAAY,OAAQ,WAAW,IAAI,MAAM,KAAM,QAAQ,MAAM;AACjE,aACE,SAAS,UAAU,OAAO,YAAY;AAAA,IAE1C,OAAO;AACL,aAAO,SAAS,OAAO,KAAK,MAAM,IAAI,MAAM,UAAU;AAAA,IACxD;AAAA,EACF;AACF;AAEA,MAAM,aAAa;AAAA,EACjB,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS;AAC9B,cAAU,QAAQ,QAAQ,cAAc,EAAE;AAC1C,cAAU,QAAQ,QAAQ,OAAO,IAAI;AACrC,WAAO,SAAS,UAAU;AAAA,EAC5B;AACF;AAEA,MAAM,OAAO;AAAA,EACX,QAAQ,CAAC,MAAM,IAAI;AAAA,EAEnB,aAAa,SAAU,SAAS,MAAM;AACpC,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,aAAa,QAAQ,OAAO,qBAAqB,MAAM;AAChE,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,SAAS,UAAU;AAAA,IAC5B;AAAA,EACF;AACF;AAEA,MAAM,WAAW;AAAA,EACf,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,cAAU,QACP,QAAQ,QAAQ,EAAE,EAClB,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,QAAQ;AAC3B,QAAI,SAAS,QAAQ,mBAAmB;AACxC,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,aAAa,MAAM;AAC5B,UAAI,QAAQ,OAAO,aAAa,OAAO;AACvC,UAAI,QAAQ,MAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,IAAI;AAC9D,gBAAU,QAAQ,OAAO,KAAK,IAAI,QAAQ,QAAQ,KAAK;AAAA,IACzD;AACA,WACE,SAAS,WAAW,KAAK,eAAe,CAAC,MAAM,KAAK,OAAO,IAAI,OAAO;AAAA,EAE1E;AACF;AAEA,MAAM,oBAAoB;AAAA,EACxB,QAAQ,SAAU,MAAM,SAAS;AAC/B,WACE,QAAQ,mBAAmB,cAC3B,KAAK,aAAa,SAClB,KAAK,cACL,KAAK,WAAW,aAAa;AAAA,EAEjC;AAAA,EAEA,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,WACE,aACA,KAAK,WAAW,YAAY,QAAQ,OAAO,QAAQ,IACnD;AAAA,EAEJ;AACF;AAEA,MAAM,kBAAkB;AAAA,EACtB,QAAQ,SAAU,MAAM,SAAS;AAC/B,WACE,QAAQ,mBAAmB,YAC3B,KAAK,aAAa,SAClB,KAAK,cACL,KAAK,WAAW,aAAa;AAAA,EAEjC;AAAA,EAEA,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,QAAI,YAAY,KAAK,WAAW,aAAa,OAAO,KAAK;AACzD,QAAI,YAAY,UAAU,MAAM,gBAAgB,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;AAClE,QAAI,OAAO,KAAK,WAAW;AAE3B,QAAI,YAAY,QAAQ,MAAM,OAAO,CAAC;AACtC,QAAI,YAAY;AAChB,QAAI,mBAAmB,IAAI,OAAO,MAAM,YAAY,QAAQ,IAAI;AAEhE,QAAI;AACJ,WAAQ,QAAQ,iBAAiB,KAAK,IAAI,GAAI;AAC5C,UAAI,MAAM,CAAC,EAAE,UAAU,WAAW;AAChC,oBAAY,MAAM,CAAC,EAAE,SAAS;AAAA,MAChC;AAAA,IACF;AAEA,QAAI,QAAQ,OAAO,WAAW,SAAS;AAEvC,WACE,SAAS,QAAQ,WAAW,OAC5B,KAAK,QAAQ,OAAO,EAAE,IACtB,OAAO,QAAQ;AAAA,EAEnB;AACF;AAEA,MAAM,iBAAiB;AAAA,EACrB,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,MAAM,aAAa;AAAA,EACjB,QAAQ,SAAU,MAAM,SAAS;AAC/B,WACE,QAAQ,cAAc,aACtB,KAAK,aAAa,OAClB,KAAK,aAAa,MAAM;AAAA,EAE5B;AAAA,EAEA,aAAa,SAAU,SAAS,MAAM;AACpC,QAAI,OAAO,KAAK,aAAa,MAAM;AACnC,QAAI;AAAM,aAAO,KAAK,QAAQ,WAAW,MAAM;AAC/C,QAAI,QAAQ,eAAe,KAAK,aAAa,OAAO,CAAC;AACrD,QAAI;AAAO,cAAQ,OAAO,MAAM,QAAQ,MAAM,KAAK,IAAI;AACvD,WAAO,MAAM,UAAU,OAAO,OAAO,QAAQ;AAAA,EAC/C;AACF;AAEA,MAAM,gBAAgB;AAAA,EACpB,QAAQ,SAAU,MAAM,SAAS;AAC/B,WACE,QAAQ,cAAc,gBACtB,KAAK,aAAa,OAClB,KAAK,aAAa,MAAM;AAAA,EAE5B;AAAA,EAEA,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,QAAI,OAAO,KAAK,aAAa,MAAM;AACnC,QAAI,QAAQ,eAAe,KAAK,aAAa,OAAO,CAAC;AACrD,QAAI;AAAO,cAAQ,OAAO,QAAQ;AAClC,QAAI;AACJ,QAAI;AAEJ,YAAQ,QAAQ,oBAAoB;AAAA,MAClC,KAAK;AACH,sBAAc,MAAM,UAAU;AAC9B,oBAAY,MAAM,UAAU,QAAQ,OAAO;AAC3C;AAAA,MACF,KAAK;AACH,sBAAc,MAAM,UAAU;AAC9B,oBAAY,MAAM,UAAU,QAAQ,OAAO;AAC3C;AAAA,MACF;AACE,YAAI,KAAK,KAAK,WAAW,SAAS;AAClC,sBAAc,MAAM,UAAU,OAAO,KAAK;AAC1C,oBAAY,MAAM,KAAK,QAAQ,OAAO;AAAA,IAC1C;AAEA,SAAK,WAAW,KAAK,SAAS;AAC9B,WAAO;AAAA,EACT;AAAA,EAEA,YAAY,CAAC;AAAA,EAEb,QAAQ,SAAU,SAAS;AACzB,QAAI,aAAa;AACjB,QAAI,KAAK,WAAW,QAAQ;AAC1B,mBAAa,SAAS,KAAK,WAAW,KAAK,IAAI,IAAI;AACnD,WAAK,aAAa,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACF;AAEA,MAAM,WAAW;AAAA,EACf,QAAQ,CAAC,MAAM,GAAG;AAAA,EAElB,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,QAAI,CAAC,QAAQ,KAAK;AAAG,aAAO;AAC5B,WAAO,QAAQ,cAAc,UAAU,QAAQ;AAAA,EACjD;AACF;AAEA,MAAM,SAAS;AAAA,EACb,QAAQ,CAAC,UAAU,GAAG;AAAA,EAEtB,aAAa,SAAU,SAAS,MAAM,SAAS;AAC7C,QAAI,CAAC,QAAQ,KAAK;AAAG,aAAO;AAC5B,WAAO,QAAQ,kBAAkB,UAAU,QAAQ;AAAA,EACrD;AACF;AAEA,MAAM,OAAO;AAAA,EACX,QAAQ,SAAU,MAAM;AACtB,QAAI,cAAc,KAAK,mBAAmB,KAAK;AAC/C,QAAI,cAAc,KAAK,WAAW,aAAa,SAAS,CAAC;AAEzD,WAAO,KAAK,aAAa,UAAU,CAAC;AAAA,EACtC;AAAA,EAEA,aAAa,SAAU,SAAS;AAC9B,QAAI,CAAC;AAAS,aAAO;AACrB,cAAU,QAAQ,QAAQ,aAAa,GAAG;AAE1C,QAAI,aAAa,sBAAsB,KAAK,OAAO,IAAI,MAAM;AAC7D,QAAI,YAAY;AAChB,QAAI,UAAU,QAAQ,MAAM,MAAM,KAAK,CAAC;AACxC,WAAO,QAAQ,QAAQ,SAAS,MAAM;AAAI,kBAAY,YAAY;AAElE,WAAO,YAAY,aAAa,UAAU,aAAa;AAAA,EACzD;AACF;AAEA,MAAM,QAAQ;AAAA,EACZ,QAAQ;AAAA,EAER,aAAa,SAAU,SAAS,MAAM;AACpC,QAAI,MAAM,eAAe,KAAK,aAAa,KAAK,CAAC;AACjD,QAAI,MAAM,KAAK,aAAa,KAAK,KAAK;AACtC,QAAI,QAAQ,eAAe,KAAK,aAAa,OAAO,CAAC;AACrD,QAAI,YAAY,QAAQ,OAAO,QAAQ,MAAM;AAC7C,WAAO,MAAM,OAAO,MAAM,OAAY,MAAM,YAAY,MAAM;AAAA,EAChE;AACF;AAEA,SAAS,eAAgB,WAAW;AAClC,SAAO,YAAY,UAAU,QAAQ,cAAc,IAAI,IAAI;AAC7D;AAMA,SAAS,MAAO,SAAS;AACvB,OAAK,UAAU;AACf,OAAK,QAAQ,CAAC;AACd,OAAK,UAAU,CAAC;AAEhB,OAAK,YAAY;AAAA,IACf,aAAa,QAAQ;AAAA,EACvB;AAEA,OAAK,kBAAkB,QAAQ;AAE/B,OAAK,cAAc;AAAA,IACjB,aAAa,QAAQ;AAAA,EACvB;AAEA,OAAK,QAAQ,CAAC;AACd,WAAS,OAAO,QAAQ;AAAO,SAAK,MAAM,KAAK,QAAQ,MAAM,GAAG,CAAC;AACnE;AAEA,MAAM,YAAY;AAAA,EAChB,KAAK,SAAU,KAAK,MAAM;AACxB,SAAK,MAAM,QAAQ,IAAI;AAAA,EACzB;AAAA,EAEA,MAAM,SAAU,QAAQ;AACtB,SAAK,MAAM,QAAQ;AAAA,MACjB;AAAA,MACA,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,QAAQ,SAAU,QAAQ;AACxB,SAAK,QAAQ,QAAQ;AAAA,MACnB;AAAA,MACA,aAAa,WAAY;AACvB,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,SAAS,SAAU,MAAM;AACvB,QAAI,KAAK;AAAS,aAAO,KAAK;AAC9B,QAAI;AAEJ,QAAK,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK,OAAO;AAAI,aAAO;AAC9D,QAAK,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK,OAAO;AAAI,aAAO;AAC9D,QAAK,OAAO,SAAS,KAAK,SAAS,MAAM,KAAK,OAAO;AAAI,aAAO;AAEhE,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,SAAS,SAAU,IAAI;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AAAK,SAAG,KAAK,MAAM,CAAC,GAAG,CAAC;AAAA,EACjE;AACF;AAEA,SAAS,SAAUA,QAAO,MAAM,SAAS;AACvC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,QAAI,OAAOA,OAAM,CAAC;AAClB,QAAI,YAAY,MAAM,MAAM,OAAO;AAAG,aAAO;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,SAAS,YAAa,MAAM,MAAM,SAAS;AACzC,MAAI,SAAS,KAAK;AAClB,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,WAAW,KAAK,SAAS,YAAY;AAAG,aAAO;AAAA,EACrD,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,QAAI,OAAO,QAAQ,KAAK,SAAS,YAAY,CAAC,IAAI;AAAI,aAAO;AAAA,EAC/D,WAAW,OAAO,WAAW,YAAY;AACvC,QAAI,OAAO,KAAK,MAAM,MAAM,OAAO;AAAG,aAAO;AAAA,EAC/C,OAAO;AACL,UAAM,IAAI,UAAU,mDAAmD;AAAA,EACzE;AACF;AAkCA,SAAS,mBAAoB,SAAS;AACpC,MAAI,UAAU,QAAQ;AACtB,MAAIC,WAAU,QAAQ;AACtB,MAAIC,UAAS,QAAQ;AACrB,MAAI,QAAQ,QAAQ,SAAS,SAAUC,OAAM;AAC3C,WAAOA,MAAK,aAAa;AAAA,EAC3B;AAEA,MAAI,CAAC,QAAQ,cAAc,MAAM,OAAO;AAAG;AAE3C,MAAI,WAAW;AACf,MAAI,gBAAgB;AAEpB,MAAI,OAAO;AACX,MAAI,OAAO,KAAK,MAAM,SAAS,KAAK;AAEpC,SAAO,SAAS,SAAS;AACvB,QAAI,KAAK,aAAa,KAAK,KAAK,aAAa,GAAG;AAC9C,UAAI,OAAO,KAAK,KAAK,QAAQ,eAAe,GAAG;AAE/C,WAAK,CAAC,YAAY,KAAK,KAAK,SAAS,IAAI,MACrC,CAAC,iBAAiB,KAAK,CAAC,MAAM,KAAK;AACrC,eAAO,KAAK,OAAO,CAAC;AAAA,MACtB;AAGA,UAAI,CAAC,MAAM;AACT,eAAO,OAAO,IAAI;AAClB;AAAA,MACF;AAEA,WAAK,OAAO;AAEZ,iBAAW;AAAA,IACb,WAAW,KAAK,aAAa,GAAG;AAC9B,UAAIF,SAAQ,IAAI,KAAK,KAAK,aAAa,MAAM;AAC3C,YAAI,UAAU;AACZ,mBAAS,OAAO,SAAS,KAAK,QAAQ,MAAM,EAAE;AAAA,QAChD;AAEA,mBAAW;AACX,wBAAgB;AAAA,MAClB,WAAWC,QAAO,IAAI,KAAK,MAAM,IAAI,GAAG;AAEtC,mBAAW;AACX,wBAAgB;AAAA,MAClB,WAAW,UAAU;AAEnB,wBAAgB;AAAA,MAClB;AAAA,IACF,OAAO;AACL,aAAO,OAAO,IAAI;AAClB;AAAA,IACF;AAEA,QAAI,WAAW,KAAK,MAAM,MAAM,KAAK;AACrC,WAAO;AACP,WAAO;AAAA,EACT;AAEA,MAAI,UAAU;AACZ,aAAS,OAAO,SAAS,KAAK,QAAQ,MAAM,EAAE;AAC9C,QAAI,CAAC,SAAS,MAAM;AAClB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AASA,SAAS,OAAQ,MAAM;AACrB,MAAIE,QAAO,KAAK,eAAe,KAAK;AAEpC,OAAK,WAAW,YAAY,IAAI;AAEhC,SAAOA;AACT;AAWA,SAAS,KAAM,MAAM,SAAS,OAAO;AACnC,MAAK,QAAQ,KAAK,eAAe,WAAY,MAAM,OAAO,GAAG;AAC3D,WAAO,QAAQ,eAAe,QAAQ;AAAA,EACxC;AAEA,SAAO,QAAQ,cAAc,QAAQ,eAAe,QAAQ;AAC9D;AAMA,IAAI,OAAQ,OAAO,WAAW,cAAc,SAAS,CAAC;AAMtD,SAAS,uBAAwB;AAC/B,MAAI,SAAS,KAAK;AAClB,MAAI,WAAW;AAIf,MAAI;AAEF,QAAI,IAAI,OAAO,EAAE,gBAAgB,IAAI,WAAW,GAAG;AACjD,iBAAW;AAAA,IACb;AAAA,EACF,SAAS,GAAG;AAAA,EAAC;AAEb,SAAO;AACT;AAEA,SAAS,mBAAoB;AAC3B,MAAI,SAAS,WAAY;AAAA,EAAC;AAE1B;AACE,QAAI,iBAAiB,GAAG;AACtB,aAAO,UAAU,kBAAkB,SAAU,QAAQ;AACnD,YAAI,MAAM,IAAI,OAAO,cAAc,UAAU;AAC7C,YAAI,aAAa;AACjB,YAAI,KAAK;AACT,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM;AACV,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO,UAAU,kBAAkB,SAAU,QAAQ;AACnD,YAAI,MAAM,SAAS,eAAe,mBAAmB,EAAE;AACvD,YAAI,KAAK;AACT,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,mBAAoB;AAC3B,MAAI,aAAa;AACjB,MAAI;AACF,aAAS,eAAe,mBAAmB,EAAE,EAAE,KAAK;AAAA,EACtD,SAAS,GAAG;AACV,QAAI,KAAK;AAAe,mBAAa;AAAA,EACvC;AACA,SAAO;AACT;AAEA,IAAI,aAAa,qBAAqB,IAAI,KAAK,YAAY,iBAAiB;AAE5E,SAAS,SAAU,OAAO,SAAS;AACjC,MAAIC;AACJ,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,WAAW,EAAE;AAAA;AAAA;AAAA;AAAA,MAIrB,oCAAoC,QAAQ;AAAA,MAC5C;AAAA,IACF;AACA,IAAAA,QAAO,IAAI,eAAe,eAAe;AAAA,EAC3C,OAAO;AACL,IAAAA,QAAO,MAAM,UAAU,IAAI;AAAA,EAC7B;AACA,qBAAmB;AAAA,IACjB,SAASA;AAAA,IACT;AAAA,IACA;AAAA,IACA,OAAO,QAAQ,mBAAmB,cAAc;AAAA,EAClD,CAAC;AAED,SAAOA;AACT;AAEA,IAAI;AACJ,SAAS,aAAc;AACrB,gBAAc,eAAe,IAAI,WAAW;AAC5C,SAAO;AACT;AAEA,SAAS,YAAa,MAAM;AAC1B,SAAO,KAAK,aAAa,SAAS,KAAK,aAAa;AACtD;AAEA,SAAS,KAAM,MAAM,SAAS;AAC5B,OAAK,UAAU,QAAQ,IAAI;AAC3B,OAAK,SAAS,KAAK,aAAa,UAAU,KAAK,WAAW;AAC1D,OAAK,UAAU,QAAQ,IAAI;AAC3B,OAAK,qBAAqB,mBAAmB,MAAM,OAAO;AAC1D,SAAO;AACT;AAEA,SAAS,QAAS,MAAM;AACtB,SACE,CAAC,OAAO,IAAI,KACZ,CAAC,sBAAsB,IAAI,KAC3B,SAAS,KAAK,KAAK,WAAW,KAC9B,CAAC,QAAQ,IAAI,KACb,CAAC,uBAAuB,IAAI;AAEhC;AAEA,SAAS,mBAAoB,MAAM,SAAS;AAC1C,MAAI,KAAK,WAAY,QAAQ,oBAAoB,KAAK,QAAS;AAC7D,WAAO,EAAE,SAAS,IAAI,UAAU,GAAG;AAAA,EACrC;AAEA,MAAI,QAAQ,eAAe,KAAK,WAAW;AAG3C,MAAI,MAAM,gBAAgB,sBAAsB,QAAQ,MAAM,OAAO,GAAG;AACtE,UAAM,UAAU,MAAM;AAAA,EACxB;AAGA,MAAI,MAAM,iBAAiB,sBAAsB,SAAS,MAAM,OAAO,GAAG;AACxE,UAAM,WAAW,MAAM;AAAA,EACzB;AAEA,SAAO,EAAE,SAAS,MAAM,SAAS,UAAU,MAAM,SAAS;AAC5D;AAEA,SAAS,eAAgB,QAAQ;AAC/B,MAAI,IAAI,OAAO,MAAM,+DAA+D;AACpF,SAAO;AAAA,IACL,SAAS,EAAE,CAAC;AAAA;AAAA,IACZ,cAAc,EAAE,CAAC;AAAA,IACjB,iBAAiB,EAAE,CAAC;AAAA,IACpB,UAAU,EAAE,CAAC;AAAA;AAAA,IACb,kBAAkB,EAAE,CAAC;AAAA,IACrB,eAAe,EAAE,CAAC;AAAA,EACpB;AACF;AAEA,SAAS,sBAAuB,MAAM,MAAM,SAAS;AACnD,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,cAAU,KAAK;AACf,aAAS;AAAA,EACX,OAAO;AACL,cAAU,KAAK;AACf,aAAS;AAAA,EACX;AAEA,MAAI,SAAS;AACX,QAAI,QAAQ,aAAa,GAAG;AAC1B,kBAAY,OAAO,KAAK,QAAQ,SAAS;AAAA,IAC3C,WAAW,QAAQ,oBAAoB,QAAQ,aAAa,QAAQ;AAClE,kBAAY;AAAA,IACd,WAAW,QAAQ,aAAa,KAAK,CAAC,QAAQ,OAAO,GAAG;AACtD,kBAAY,OAAO,KAAK,QAAQ,WAAW;AAAA,IAC7C;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,SAAS,MAAM,UAAU;AAC7B,IAAI,UAAU;AAAA,EACZ,CAAC,OAAO,MAAM;AAAA,EACd,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,SAAS,MAAM;AAAA,EAChB,CAAC,UAAU,MAAM;AAAA,EACjB,CAAC,eAAe,OAAO;AAAA,EACvB,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,SAAS,OAAO;AAAA,EACjB,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,MAAM,KAAK;AAAA,EACZ,CAAC,cAAc,QAAQ;AACzB;AAEA,SAAS,gBAAiB,SAAS;AACjC,MAAI,EAAE,gBAAgB;AAAkB,WAAO,IAAI,gBAAgB,OAAO;AAE1E,MAAI,WAAW;AAAA,IACb;AAAA,IACA,cAAc;AAAA,IACd,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,kBAAkB,SAAU,SAAS,MAAM;AACzC,aAAO,KAAK,UAAU,SAAS;AAAA,IACjC;AAAA,IACA,iBAAiB,SAAU,SAAS,MAAM;AACxC,aAAO,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS,KAAK;AAAA,IAChE;AAAA,IACA,oBAAoB,SAAU,SAAS,MAAM;AAC3C,aAAO,KAAK,UAAU,SAAS,UAAU,SAAS;AAAA,IACpD;AAAA,EACF;AACA,OAAK,UAAU,OAAO,CAAC,GAAG,UAAU,OAAO;AAC3C,OAAK,QAAQ,IAAI,MAAM,KAAK,OAAO;AACrC;AAEA,gBAAgB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS1B,UAAU,SAAU,OAAO;AACzB,QAAI,CAAC,WAAW,KAAK,GAAG;AACtB,YAAM,IAAI;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,QAAI,UAAU;AAAI,aAAO;AAEzB,QAAI,SAAS,QAAQ,KAAK,MAAM,IAAI,SAAS,OAAO,KAAK,OAAO,CAAC;AACjE,WAAO,YAAY,KAAK,MAAM,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,SAAU,QAAQ;AACrB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAAK,aAAK,IAAI,OAAO,CAAC,CAAC;AAAA,IAC5D,WAAW,OAAO,WAAW,YAAY;AACvC,aAAO,IAAI;AAAA,IACb,OAAO;AACL,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC1E;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,SAAU,KAAK,MAAM;AAC5B,SAAK,MAAM,IAAI,KAAK,IAAI;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,SAAU,QAAQ;AACtB,SAAK,MAAM,KAAK,MAAM;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,SAAU,QAAQ;AACxB,SAAK,MAAM,OAAO,MAAM;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,SAAU,QAAQ;AACxB,WAAO,QAAQ,OAAO,SAAU,aAAa,QAAQ;AACnD,aAAO,YAAY,QAAQ,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACjD,GAAG,MAAM;AAAA,EACX;AACF;AAUA,SAAS,QAAS,YAAY;AAC5B,MAAI,OAAO;AACX,SAAO,OAAO,KAAK,WAAW,YAAY,SAAU,QAAQ,MAAM;AAChE,WAAO,IAAI,KAAK,MAAM,KAAK,OAAO;AAElC,QAAI,cAAc;AAClB,QAAI,KAAK,aAAa,GAAG;AACvB,oBAAc,KAAK,SAAS,KAAK,YAAY,KAAK,OAAO,KAAK,SAAS;AAAA,IACzE,WAAW,KAAK,aAAa,GAAG;AAC9B,oBAAc,mBAAmB,KAAK,MAAM,IAAI;AAAA,IAClD;AAEA,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC,GAAG,EAAE;AACP;AAUA,SAAS,YAAa,QAAQ;AAC5B,MAAI,OAAO;AACX,OAAK,MAAM,QAAQ,SAAU,MAAM;AACjC,QAAI,OAAO,KAAK,WAAW,YAAY;AACrC,eAAS,KAAK,QAAQ,KAAK,OAAO,KAAK,OAAO,CAAC;AAAA,IACjD;AAAA,EACF,CAAC;AAED,SAAO,OAAO,QAAQ,cAAc,EAAE,EAAE,QAAQ,gBAAgB,EAAE;AACpE;AAUA,SAAS,mBAAoB,MAAM;AACjC,MAAI,OAAO,KAAK,MAAM,QAAQ,IAAI;AAClC,MAAI,UAAU,QAAQ,KAAK,MAAM,IAAI;AACrC,MAAI,aAAa,KAAK;AACtB,MAAI,WAAW,WAAW,WAAW;AAAU,cAAU,QAAQ,KAAK;AACtE,SACE,WAAW,UACX,KAAK,YAAY,SAAS,MAAM,KAAK,OAAO,IAC5C,WAAW;AAEf;AAWA,SAAS,KAAM,QAAQ,aAAa;AAClC,MAAI,KAAK,qBAAqB,MAAM;AACpC,MAAI,KAAK,oBAAoB,WAAW;AACxC,MAAI,MAAM,KAAK,IAAI,OAAO,SAAS,GAAG,QAAQ,YAAY,SAAS,GAAG,MAAM;AAC5E,MAAI,YAAY,OAAO,UAAU,GAAG,GAAG;AAEvC,SAAO,KAAK,YAAY;AAC1B;AAUA,SAAS,WAAY,OAAO;AAC1B,SACE,SAAS,SACP,OAAO,UAAU,YAChB,MAAM,aACL,MAAM,aAAa,KAAK,MAAM,aAAa,KAAK,MAAM,aAAa;AAI3E;AAEA,IAAO,8BAAQ;", "names": ["rules", "isBlock", "isVoid", "node", "next", "root"]}