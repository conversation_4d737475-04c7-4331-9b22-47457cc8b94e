{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/julia.js"], "sourcesContent": ["function wordRegexp(words, end, pre) {\n  if (typeof pre === \"undefined\") pre = \"\";\n  if (typeof end === \"undefined\") { end = \"\\\\b\"; }\n  return new RegExp(\"^\" + pre + \"((\" + words.join(\")|(\") + \"))\" + end);\n}\n\nvar octChar = \"\\\\\\\\[0-7]{1,3}\";\nvar hexChar = \"\\\\\\\\x[A-Fa-f0-9]{1,2}\";\nvar sChar = \"\\\\\\\\[abefnrtv0%?'\\\"\\\\\\\\]\";\nvar uChar = \"([^\\\\u0027\\\\u005C\\\\uD800-\\\\uDFFF]|[\\\\uD800-\\\\uDFFF][\\\\uDC00-\\\\uDFFF])\";\n\nvar asciiOperatorsList = [\n  \"[<>]:\", \"[<>=]=\", \"<<=?\", \">>>?=?\", \"=>\", \"--?>\", \"<--[->]?\", \"\\\\/\\\\/\",\n  \"\\\\.{2,3}\", \"[\\\\.\\\\\\\\%*+\\\\-<>!\\\\/^|&]=?\", \"\\\\?\", \"\\\\$\", \"~\", \":\"\n];\nvar operators = wordRegexp([\n  \"[<>]:\", \"[<>=]=\", \"[!=]==\", \"<<=?\", \">>>?=?\", \"=>?\", \"--?>\", \"<--[->]?\", \"\\\\/\\\\/\",\n  \"[\\\\\\\\%*+\\\\-<>!\\\\/^|&\\\\u00F7\\\\u22BB]=?\", \"\\\\?\", \"\\\\$\", \"~\", \":\",\n  \"\\\\u00D7\", \"\\\\u2208\", \"\\\\u2209\", \"\\\\u220B\", \"\\\\u220C\", \"\\\\u2218\",\n  \"\\\\u221A\", \"\\\\u221B\", \"\\\\u2229\", \"\\\\u222A\", \"\\\\u2260\", \"\\\\u2264\",\n  \"\\\\u2265\", \"\\\\u2286\", \"\\\\u2288\", \"\\\\u228A\", \"\\\\u22C5\",\n  \"\\\\b(in|isa)\\\\b(?!\\.?\\\\()\"\n], \"\");\nvar delimiters = /^[;,()[\\]{}]/;\nvar identifiers = /^[_A-Za-z\\u00A1-\\u2217\\u2219-\\uFFFF][\\w\\u00A1-\\u2217\\u2219-\\uFFFF]*!*/;\n\nvar chars = wordRegexp([octChar, hexChar, sChar, uChar], \"'\");\n\nvar openersList = [\"begin\", \"function\", \"type\", \"struct\", \"immutable\", \"let\",\n                   \"macro\", \"for\", \"while\", \"quote\", \"if\", \"else\", \"elseif\", \"try\",\n                   \"finally\", \"catch\", \"do\"];\n\nvar closersList = [\"end\", \"else\", \"elseif\", \"catch\", \"finally\"];\n\nvar keywordsList = [\"if\", \"else\", \"elseif\", \"while\", \"for\", \"begin\", \"let\",\n                    \"end\", \"do\", \"try\", \"catch\", \"finally\", \"return\", \"break\", \"continue\",\n                    \"global\", \"local\", \"const\", \"export\", \"import\", \"importall\", \"using\",\n                    \"function\", \"where\", \"macro\", \"module\", \"baremodule\", \"struct\", \"type\",\n                    \"mutable\", \"immutable\", \"quote\", \"typealias\", \"abstract\", \"primitive\",\n                    \"bitstype\"];\n\nvar builtinsList = [\"true\", \"false\", \"nothing\", \"NaN\", \"Inf\"];\n\nvar openers = wordRegexp(openersList);\nvar closers = wordRegexp(closersList);\nvar keywords = wordRegexp(keywordsList);\nvar builtins = wordRegexp(builtinsList);\n\nvar macro = /^@[_A-Za-z\\u00A1-\\uFFFF][\\w\\u00A1-\\uFFFF]*!*/;\nvar symbol = /^:[_A-Za-z\\u00A1-\\uFFFF][\\w\\u00A1-\\uFFFF]*!*/;\nvar stringPrefixes = /^(`|([_A-Za-z\\u00A1-\\uFFFF]*\"(\"\")?))/;\n\nvar macroOperators = wordRegexp(asciiOperatorsList, \"\", \"@\");\nvar symbolOperators = wordRegexp(asciiOperatorsList, \"\", \":\");\n\nfunction inArray(state) {\n  return (state.nestedArrays > 0);\n}\n\nfunction inGenerator(state) {\n  return (state.nestedGenerators > 0);\n}\n\nfunction currentScope(state, n) {\n  if (typeof(n) === \"undefined\") { n = 0; }\n  if (state.scopes.length <= n) {\n    return null;\n  }\n  return state.scopes[state.scopes.length - (n + 1)];\n}\n\n// tokenizers\nfunction tokenBase(stream, state) {\n  // Handle multiline comments\n  if (stream.match('#=', false)) {\n    state.tokenize = tokenComment;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle scope changes\n  var leavingExpr = state.leavingExpr;\n  if (stream.sol()) {\n    leavingExpr = false;\n  }\n  state.leavingExpr = false;\n\n  if (leavingExpr) {\n    if (stream.match(/^'+/)) {\n      return \"operator\";\n    }\n  }\n\n  if (stream.match(/\\.{4,}/)) {\n    return \"error\";\n  } else if (stream.match(/\\.{1,3}/)) {\n    return \"operator\";\n  }\n\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var ch = stream.peek();\n\n  // Handle single line comments\n  if (ch === '#') {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  if (ch === '[') {\n    state.scopes.push('[');\n    state.nestedArrays++;\n  }\n\n  if (ch === '(') {\n    state.scopes.push('(');\n    state.nestedGenerators++;\n  }\n\n  if (inArray(state) && ch === ']') {\n    while (state.scopes.length && currentScope(state) !== \"[\") { state.scopes.pop(); }\n    state.scopes.pop();\n    state.nestedArrays--;\n    state.leavingExpr = true;\n  }\n\n  if (inGenerator(state) && ch === ')') {\n    while (state.scopes.length && currentScope(state) !== \"(\") { state.scopes.pop(); }\n    state.scopes.pop();\n    state.nestedGenerators--;\n    state.leavingExpr = true;\n  }\n\n  if (inArray(state)) {\n    if (state.lastToken == \"end\" && stream.match(':')) {\n      return \"operator\";\n    }\n    if (stream.match('end')) {\n      return \"number\";\n    }\n  }\n\n  var match;\n  if (match = stream.match(openers, false)) {\n    state.scopes.push(match[0]);\n  }\n\n  if (stream.match(closers, false)) {\n    state.scopes.pop();\n  }\n\n  // Handle type annotations\n  if (stream.match(/^::(?![:\\$])/)) {\n    state.tokenize = tokenAnnotation;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle symbols\n  if (!leavingExpr && (stream.match(symbol) || stream.match(symbolOperators))) {\n    return \"builtin\";\n  }\n\n  // Handle parametric types\n  //if (stream.match(/^{[^}]*}(?=\\()/)) {\n  //  return \"builtin\";\n  //}\n\n  // Handle operators and Delimiters\n  if (stream.match(operators)) {\n    return \"operator\";\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^\\.?\\d/, false)) {\n    var imMatcher = RegExp(/^im\\b/);\n    var numberLiteral = false;\n    if (stream.match(/^0x\\.[0-9a-f_]+p[\\+\\-]?[_\\d]+/i)) { numberLiteral = true; }\n    // Integers\n    if (stream.match(/^0x[0-9a-f_]+/i)) { numberLiteral = true; } // Hex\n    if (stream.match(/^0b[01_]+/i)) { numberLiteral = true; } // Binary\n    if (stream.match(/^0o[0-7_]+/i)) { numberLiteral = true; } // Octal\n    // Floats\n    if (stream.match(/^(?:(?:\\d[_\\d]*)?\\.(?!\\.)(?:\\d[_\\d]*)?|\\d[_\\d]*\\.(?!\\.)(?:\\d[_\\d]*))?([Eef][\\+\\-]?[_\\d]+)?/i)) { numberLiteral = true; }\n    if (stream.match(/^\\d[_\\d]*(e[\\+\\-]?\\d+)?/i)) { numberLiteral = true; } // Decimal\n    if (numberLiteral) {\n      // Integer literals may be \"long\"\n      stream.match(imMatcher);\n      state.leavingExpr = true;\n      return \"number\";\n    }\n  }\n\n  // Handle Chars\n  if (stream.match(\"'\")) {\n    state.tokenize = tokenChar;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle Strings\n  if (stream.match(stringPrefixes)) {\n    state.tokenize = tokenStringFactory(stream.current());\n    return state.tokenize(stream, state);\n  }\n\n  if (stream.match(macro) || stream.match(macroOperators)) {\n    return \"meta\";\n  }\n\n  if (stream.match(delimiters)) {\n    return null;\n  }\n\n  if (stream.match(keywords)) {\n    return \"keyword\";\n  }\n\n  if (stream.match(builtins)) {\n    return \"builtin\";\n  }\n\n  var isDefinition = state.isDefinition || state.lastToken == \"function\" ||\n      state.lastToken == \"macro\" || state.lastToken == \"type\" ||\n      state.lastToken == \"struct\" || state.lastToken == \"immutable\";\n\n  if (stream.match(identifiers)) {\n    if (isDefinition) {\n      if (stream.peek() === '.') {\n        state.isDefinition = true;\n        return \"variable\";\n      }\n      state.isDefinition = false;\n      return \"def\";\n    }\n    state.leavingExpr = true;\n    return \"variable\";\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return \"error\";\n}\n\nfunction tokenAnnotation(stream, state) {\n  stream.match(/.*?(?=[,;{}()=\\s]|$)/);\n  if (stream.match('{')) {\n    state.nestedParameters++;\n  } else if (stream.match('}') && state.nestedParameters > 0) {\n    state.nestedParameters--;\n  }\n  if (state.nestedParameters > 0) {\n    stream.match(/.*?(?={|})/) || stream.next();\n  } else if (state.nestedParameters == 0) {\n    state.tokenize = tokenBase;\n  }\n  return \"builtin\";\n}\n\nfunction tokenComment(stream, state) {\n  if (stream.match('#=')) {\n    state.nestedComments++;\n  }\n  if (!stream.match(/.*?(?=(#=|=#))/)) {\n    stream.skipToEnd();\n  }\n  if (stream.match('=#')) {\n    state.nestedComments--;\n    if (state.nestedComments == 0)\n      state.tokenize = tokenBase;\n  }\n  return \"comment\";\n}\n\nfunction tokenChar(stream, state) {\n  var isChar = false, match;\n  if (stream.match(chars)) {\n    isChar = true;\n  } else if (match = stream.match(/\\\\u([a-f0-9]{1,4})(?=')/i)) {\n    var value = parseInt(match[1], 16);\n    if (value <= 55295 || value >= 57344) { // (U+0,U+D7FF), (U+E000,U+FFFF)\n      isChar = true;\n      stream.next();\n    }\n  } else if (match = stream.match(/\\\\U([A-Fa-f0-9]{5,8})(?=')/)) {\n    var value = parseInt(match[1], 16);\n    if (value <= 1114111) { // U+10FFFF\n      isChar = true;\n      stream.next();\n    }\n  }\n  if (isChar) {\n    state.leavingExpr = true;\n    state.tokenize = tokenBase;\n    return \"string\";\n  }\n  if (!stream.match(/^[^']+(?=')/)) { stream.skipToEnd(); }\n  if (stream.match(\"'\")) { state.tokenize = tokenBase; }\n  return \"error\";\n}\n\nfunction tokenStringFactory(delimiter) {\n  if (delimiter.substr(-3) === '\"\"\"') {\n    delimiter = '\"\"\"';\n  } else if (delimiter.substr(-1) === '\"') {\n    delimiter = '\"';\n  }\n  function tokenString(stream, state) {\n    if (stream.eat('\\\\')) {\n      stream.next();\n    } else if (stream.match(delimiter)) {\n      state.tokenize = tokenBase;\n      state.leavingExpr = true;\n      return \"string\";\n    } else {\n      stream.eat(/[`\"]/);\n    }\n    stream.eatWhile(/[^\\\\`\"]/);\n    return \"string\";\n  }\n  return tokenString;\n}\n\nexport const julia = {\n  name: \"julia\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scopes: [],\n      lastToken: null,\n      leavingExpr: false,\n      isDefinition: false,\n      nestedArrays: 0,\n      nestedComments: 0,\n      nestedGenerators: 0,\n      nestedParameters: 0,\n      firstParenPos: -1\n    };\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    var current = stream.current();\n\n    if (current && style) {\n      state.lastToken = current;\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var delta = 0;\n    if ( textAfter === ']' || textAfter === ')' || /^end\\b/.test(textAfter) ||\n         /^else/.test(textAfter) || /^catch\\b/.test(textAfter) || /^elseif\\b/.test(textAfter) ||\n         /^finally/.test(textAfter) ) {\n      delta = -1;\n    }\n    return (state.scopes.length + delta) * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(end|else|catch|finally)\\b$/,\n    commentTokens: {line: \"#\", block: {open: \"#=\", close: \"=#\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    autocomplete: keywordsList.concat(builtinsList)\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ;AAAa,UAAM;AACtC,MAAI,OAAO,QAAQ,aAAa;AAAE,UAAM;AAAA,EAAO;AAC/C,SAAO,IAAI,OAAO,MAAM,MAAM,OAAO,MAAM,KAAK,KAAK,IAAI,OAAO,GAAG;AACrE;AAEA,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,QAAQ;AAEZ,IAAI,qBAAqB;AAAA,EACvB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAY;AAAA,EAC/D;AAAA,EAAY;AAAA,EAA8B;AAAA,EAAO;AAAA,EAAO;AAAA,EAAK;AAC/D;AACA,IAAI,YAAY,WAAW;AAAA,EACzB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAY;AAAA,EAC1E;AAAA,EAAyC;AAAA,EAAO;AAAA,EAAO;AAAA,EAAK;AAAA,EAC5D;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EACvD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EACvD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAC5C;AACF,GAAG,EAAE;AACL,IAAI,aAAa;AACjB,IAAI,cAAc;AAElB,IAAI,QAAQ,WAAW,CAAC,SAAS,SAAS,OAAO,KAAK,GAAG,GAAG;AAE5D,IAAI,cAAc;AAAA,EAAC;AAAA,EAAS;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAa;AAAA,EACpD;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAU;AAAA,EAC1D;AAAA,EAAW;AAAA,EAAS;AAAI;AAE3C,IAAI,cAAc,CAAC,OAAO,QAAQ,UAAU,SAAS,SAAS;AAE9D,IAAI,eAAe;AAAA,EAAC;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EACjD;AAAA,EAAO;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAC3D;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAa;AAAA,EAC7D;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAc;AAAA,EAAU;AAAA,EAChE;AAAA,EAAW;AAAA,EAAa;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAC1D;AAAU;AAE9B,IAAI,eAAe,CAAC,QAAQ,SAAS,WAAW,OAAO,KAAK;AAE5D,IAAI,UAAU,WAAW,WAAW;AACpC,IAAI,UAAU,WAAW,WAAW;AACpC,IAAI,WAAW,WAAW,YAAY;AACtC,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,iBAAiB;AAErB,IAAI,iBAAiB,WAAW,oBAAoB,IAAI,GAAG;AAC3D,IAAI,kBAAkB,WAAW,oBAAoB,IAAI,GAAG;AAE5D,SAAS,QAAQ,OAAO;AACtB,SAAQ,MAAM,eAAe;AAC/B;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAQ,MAAM,mBAAmB;AACnC;AAEA,SAAS,aAAa,OAAO,GAAG;AAC9B,MAAI,OAAO,MAAO,aAAa;AAAE,QAAI;AAAA,EAAG;AACxC,MAAI,MAAM,OAAO,UAAU,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,MAAM,OAAO,MAAM,OAAO,UAAU,IAAI,EAAE;AACnD;AAGA,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,OAAO,MAAM,MAAM,KAAK,GAAG;AAC7B,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,cAAc,MAAM;AACxB,MAAI,OAAO,IAAI,GAAG;AAChB,kBAAc;AAAA,EAChB;AACA,QAAM,cAAc;AAEpB,MAAI,aAAa;AACf,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,KAAK;AACd,UAAM,OAAO,KAAK,GAAG;AACrB,UAAM;AAAA,EACR;AAEA,MAAI,OAAO,KAAK;AACd,UAAM,OAAO,KAAK,GAAG;AACrB,UAAM;AAAA,EACR;AAEA,MAAI,QAAQ,KAAK,KAAK,OAAO,KAAK;AAChC,WAAO,MAAM,OAAO,UAAU,aAAa,KAAK,MAAM,KAAK;AAAE,YAAM,OAAO,IAAI;AAAA,IAAG;AACjF,UAAM,OAAO,IAAI;AACjB,UAAM;AACN,UAAM,cAAc;AAAA,EACtB;AAEA,MAAI,YAAY,KAAK,KAAK,OAAO,KAAK;AACpC,WAAO,MAAM,OAAO,UAAU,aAAa,KAAK,MAAM,KAAK;AAAE,YAAM,OAAO,IAAI;AAAA,IAAG;AACjF,UAAM,OAAO,IAAI;AACjB,UAAM;AACN,UAAM,cAAc;AAAA,EACtB;AAEA,MAAI,QAAQ,KAAK,GAAG;AAClB,QAAI,MAAM,aAAa,SAAS,OAAO,MAAM,GAAG,GAAG;AACjD,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,QAAQ,OAAO,MAAM,SAAS,KAAK,GAAG;AACxC,UAAM,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,EAC5B;AAEA,MAAI,OAAO,MAAM,SAAS,KAAK,GAAG;AAChC,UAAM,OAAO,IAAI;AAAA,EACnB;AAGA,MAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,CAAC,gBAAgB,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,eAAe,IAAI;AAC3E,WAAO;AAAA,EACT;AAQA,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,UAAU,KAAK,GAAG;AACjC,QAAI,YAAY,OAAO,OAAO;AAC9B,QAAI,gBAAgB;AACpB,QAAI,OAAO,MAAM,gCAAgC,GAAG;AAAE,sBAAgB;AAAA,IAAM;AAE5E,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAAE,sBAAgB;AAAA,IAAM;AAC5D,QAAI,OAAO,MAAM,YAAY,GAAG;AAAE,sBAAgB;AAAA,IAAM;AACxD,QAAI,OAAO,MAAM,aAAa,GAAG;AAAE,sBAAgB;AAAA,IAAM;AAEzD,QAAI,OAAO,MAAM,6FAA6F,GAAG;AAAE,sBAAgB;AAAA,IAAM;AACzI,QAAI,OAAO,MAAM,0BAA0B,GAAG;AAAE,sBAAgB;AAAA,IAAM;AACtE,QAAI,eAAe;AAEjB,aAAO,MAAM,SAAS;AACtB,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,OAAO,MAAM,GAAG,GAAG;AACrB,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAM,WAAW,mBAAmB,OAAO,QAAQ,CAAC;AACpD,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAEA,MAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,cAAc,GAAG;AACvD,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,MAAM,gBAAgB,MAAM,aAAa,cACxD,MAAM,aAAa,WAAW,MAAM,aAAa,UACjD,MAAM,aAAa,YAAY,MAAM,aAAa;AAEtD,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,QAAI,cAAc;AAChB,UAAI,OAAO,KAAK,MAAM,KAAK;AACzB,cAAM,eAAe;AACrB,eAAO;AAAA,MACT;AACA,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AACA,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AAGA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,SAAO,MAAM,sBAAsB;AACnC,MAAI,OAAO,MAAM,GAAG,GAAG;AACrB,UAAM;AAAA,EACR,WAAW,OAAO,MAAM,GAAG,KAAK,MAAM,mBAAmB,GAAG;AAC1D,UAAM;AAAA,EACR;AACA,MAAI,MAAM,mBAAmB,GAAG;AAC9B,WAAO,MAAM,YAAY,KAAK,OAAO,KAAK;AAAA,EAC5C,WAAW,MAAM,oBAAoB,GAAG;AACtC,UAAM,WAAW;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM;AAAA,EACR;AACA,MAAI,CAAC,OAAO,MAAM,gBAAgB,GAAG;AACnC,WAAO,UAAU;AAAA,EACnB;AACA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM;AACN,QAAI,MAAM,kBAAkB;AAC1B,YAAM,WAAW;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,SAAS,OAAO;AACpB,MAAI,OAAO,MAAM,KAAK,GAAG;AACvB,aAAS;AAAA,EACX,WAAW,QAAQ,OAAO,MAAM,0BAA0B,GAAG;AAC3D,QAAI,QAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,QAAI,SAAS,SAAS,SAAS,OAAO;AACpC,eAAS;AACT,aAAO,KAAK;AAAA,IACd;AAAA,EACF,WAAW,QAAQ,OAAO,MAAM,4BAA4B,GAAG;AAC7D,QAAI,QAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,QAAI,SAAS,SAAS;AACpB,eAAS;AACT,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,MAAI,QAAQ;AACV,UAAM,cAAc;AACpB,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO,MAAM,aAAa,GAAG;AAAE,WAAO,UAAU;AAAA,EAAG;AACxD,MAAI,OAAO,MAAM,GAAG,GAAG;AAAE,UAAM,WAAW;AAAA,EAAW;AACrD,SAAO;AACT;AAEA,SAAS,mBAAmB,WAAW;AACrC,MAAI,UAAU,OAAO,EAAE,MAAM,OAAO;AAClC,gBAAY;AAAA,EACd,WAAW,UAAU,OAAO,EAAE,MAAM,KAAK;AACvC,gBAAY;AAAA,EACd;AACA,WAAS,YAAY,QAAQ,OAAO;AAClC,QAAI,OAAO,IAAI,IAAI,GAAG;AACpB,aAAO,KAAK;AAAA,IACd,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,YAAM,WAAW;AACjB,YAAM,cAAc;AACpB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,IAAI,MAAM;AAAA,IACnB;AACA,WAAO,SAAS,SAAS;AACzB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,OAAO,QAAQ;AAE7B,QAAI,WAAW,OAAO;AACpB,YAAM,YAAY;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,QAAQ;AACZ,QAAK,cAAc,OAAO,cAAc,OAAO,SAAS,KAAK,SAAS,KACjE,QAAQ,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,KACnF,WAAW,KAAK,SAAS,GAAI;AAChC,cAAQ;AAAA,IACV;AACA,YAAQ,MAAM,OAAO,SAAS,SAAS,GAAG;AAAA,EAC5C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC3D,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,IAC9C,cAAc,aAAa,OAAO,YAAY;AAAA,EAChD;AACF;", "names": []}