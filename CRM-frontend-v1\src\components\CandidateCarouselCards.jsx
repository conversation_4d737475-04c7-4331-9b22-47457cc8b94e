import React, { useState } from 'react';
import { avatarBgColors, avatarTextColors } from 'constants/avatarColors';
import { Icon } from '@iconify/react';
import { candidateStatusBgColor, candidateStatusTextColor } from 'constants/candidateStatusConstants';
import { Badge } from '@mui/material';

function CandidateCarouselCards({
  title,
  candidates,
  color,
  showActions,
  onWhatsappClick,
  onVoiceAppClick,
  onSMSClick,
  onEmailClick,
  showSubmissionActions,
  onUpdateCandidateStatusClick,
  count
}) {
  const [selectedCandidates, setSelectedCandidates] = useState([]);
  const isAllSelected = selectedCandidates.length === candidates.length;

  const handleSelectAll = () => {
    setSelectedCandidates(isAllSelected ? [] : candidates?.map((c) => c.id));
  };

  const handleSelectCandidate = (id) => {
    setSelectedCandidates((prev) => (prev?.includes(id) ? prev.filter((c) => c !== id) : [...prev, id]));
  };

  return (
    <div style={{ backgroundColor: 'white', borderTop: `3px solid ${color}`, width: '100%' }}>
      {/* Header Row */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '15px 25px',
          borderBottom: '2px solid #CED0DA',
          position: 'sticky',
          top: '0',
          zIndex: '1',
          backgroundColor: 'white'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          {/* <input type="checkbox" checked={isAllSelected} onChange={handleSelectAll} /> */}
          <h4 style={{ margin: 0, color: '#676C7E', fontWeight: 600, fontFamily: 'Poppins' }}>{title}</h4>
        </div>
        <span>{count}</span>
      </div>
      {candidates?.map((candidate, index) => {
        const bgColor = avatarBgColors[index % avatarBgColors.length];
        const textColor = avatarTextColors[index % avatarTextColors.length];

        return (
          <div
            key={candidate.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                {isAllSelected && (
                  <input
                    type="checkbox"
                    checked={selectedCandidates?.includes(candidate.id)}
                    onChange={() => handleSelectCandidate(candidate.id)}
                    style={{ position: 'absolute', marginLeft: '15px' }}
                  />
                )}
                {!selectedCandidates?.includes(candidate.id) && (
                  <div
                    style={{
                      backgroundColor: bgColor,
                      color: textColor,
                      width: '50px',
                      height: '50px',
                      fontWeight: 500,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: '50%',
                      marginLeft: '-5px',
                      fontFamily: 'Poppins'
                    }}
                  >
                    <p style={{ fontSize: '18px', margin: 0 }}>
                      {candidate.name
                        ?.split(' ')
                        ?.map((word) => word.charAt(0).toUpperCase())
                        .slice(0, 2)
                        .join('')}
                    </p>
                  </div>
                )}
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>{candidate.name}</p>

              <p style={{ color: '#676C7E', fontWeight: 400, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>Ultimate Outsourcing</p>
              {showActions && (
                <>
                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center', marginTop: '5px' }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                      onClick={() => {
                        onEmailClick();
                      }}
                    >
                      <Badge
                        badgeContent={candidate?.response_count ? candidate?.response_count : null}
                        color="warning"
                        invisible={!candidate?.response_count}
                        sx={{
                          '& .MuiBadge-badge': {
                            fontSize: '0.55rem',
                            minWidth: '14px',
                            height: '14px',
                            padding: '0 4px'
                          }
                        }}
                      >
                        <Icon icon="material-symbols:mail" width="18" height="18" style={{ color: '#5DA9E9' }} />
                      </Badge>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                      onClick={() => {
                        onWhatsappClick(candidate);
                      }}
                    >
                      <Badge
                        badgeContent={candidate?.response_count ? candidate?.response_count : null}
                        color="warning"
                        invisible={!candidate?.response_count}
                        sx={{
                          '& .MuiBadge-badge': {
                            fontSize: '0.45rem',
                            minWidth: '14px',
                            height: '14px',
                            padding: '0 4px'
                          }
                        }}
                      >
                        <Icon icon="mingcute:whatsapp-fill" width="18" height="18" style={{ color: '#5DA9E9' }} />
                      </Badge>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                      onClick={() => {
                        onSMSClick(candidate);
                      }}
                    >
                      <Icon icon="ic:sharp-message" width="18" height="18" style={{ color: '#5DA9E9' }} />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                    >
                      <Icon
                        icon="material-symbols:call"
                        width="14"
                        height="14"
                        style={{ color: '#5DA9E9' }}
                        onClick={() => {
                          onVoiceAppClick(candidate);
                        }}
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                    >
                      <Icon icon="bxl:linkedin" width="18" height="18" style={{ color: '#5DA9E9' }} />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: '#DDFFD9',
                        width: '25px',
                        height: '25px',
                        borderRadius: '50%',
                        justifyContent: 'center'
                      }}
                    >
                      <Icon icon="mdi:linkedin" width="18" height="18" style={{ color: '#5DA9E9' }} />
                    </div>
                  </div>
                </>
              )}
              <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
                {candidate?.stage && (
                  <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                    <Icon icon="fluent:radio-button-16-regular" width="14" height="14" style={{ color: color }} />
                    <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                      {candidate?.stage}
                    </p>
                  </div>
                )}
                {showSubmissionActions && (
                  <>
                    <div style={{ display: 'flex', gap: '4px', alignItems: 'center', marginTop: '5px' }}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          backgroundColor: '#DDFFD9',
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          justifyContent: 'center'
                        }}
                        onClick={() => {
                          onUpdateCandidateStatusClick();
                        }}
                      >
                        <Icon icon="qlementine-icons:update-24" width="14" height="14" style={{ color: '#5DA9E9' }} />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
            {candidate?.status && (
              <div style={{ width: '30%', textAlign: 'center' }}>
                <p
                  style={{
                    backgroundColor: candidateStatusBgColor[candidate?.status],
                    color: candidateStatusTextColor[candidate?.status],
                    margin: 0,
                    borderRadius: '2px',
                    display: 'inline-block',
                    padding: '5px 4px',
                    fontWeight: 500,
                    fontSize: '10px',
                    width: '100px',
                    fontFamily: 'Poppins'
                  }}
                >
                  {candidate?.status || 'N/A'}
                </p>
                <div style={{ display: 'flex', gap: '5px', alignItems: 'flex-start', justifyContent: 'center', marginTop: '5px' }}>
                  <Icon icon="mingcute:time-line" width="14" height="14" style={{ color: '#5DA9E9' }} />
                  <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                    {candidate.replyTime}
                  </p>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

export default CandidateCarouselCards;
