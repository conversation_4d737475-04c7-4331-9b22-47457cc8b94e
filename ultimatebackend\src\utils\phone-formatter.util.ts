/**
 * Utility functions for phone number formatting and validation
 */

/**
 * Convert phone numbers to E.164 format
 * @param phoneNumber - The phone number to format
 * @param defaultCountryCode - Default country code to use if none provided (default: '+44' for UK)
 * @returns Formatted phone number in E.164 format
 * 
 * @example
 * formatToE164('07123456789') // returns '+447123456789'
 * formatToE164('447123456789') // returns '+447123456789'
 * formatToE164('+447123456789') // returns '+447123456789'
 * formatToE164('0044123456789') // returns '+44123456789'
 * formatToE164('123456789', '+1') // returns '+1123456789'
 */
export function formatToE164(phoneNumber: string, defaultCountryCode: string = '+44'): string {
  if (!phoneNumber) {
    throw new Error('Phone number is required');
  }

  // Remove all non-digit characters except +
  let cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If already starts with +, validate and return
  if (cleaned.startsWith('+')) {
    if (cleaned.length < 10) {
      throw new Error('Phone number too short');
    }
    return cleaned;
  }
  
  // If starts with 00, replace with +
  if (cleaned.startsWith('00')) {
    const formatted = '+' + cleaned.substring(2);
    if (formatted.length < 10) {
      throw new Error('Phone number too short');
    }
    return formatted;
  }
  
  // If starts with 0 (UK format), replace with +44
  if (cleaned.startsWith('0')) {
    const formatted = '+44' + cleaned.substring(1);
    if (formatted.length < 12) {
      throw new Error('UK phone number too short');
    }
    return formatted;
  }
  
  // If it's just digits without country code
  if (/^\d+$/.test(cleaned)) {
    // For UK mobile numbers starting with 7 and having 11 digits total
    if (cleaned.startsWith('7') && cleaned.length === 11) {
      return '+44' + cleaned;
    }
    
    // For other cases, use the default country code
    const formatted = defaultCountryCode + cleaned;
    if (formatted.length < 10) {
      throw new Error('Phone number too short');
    }
    return formatted;
  }
  
  // If none of the above patterns match, try with default country code
  const formatted = defaultCountryCode + cleaned;
  if (formatted.length < 10) {
    throw new Error('Invalid phone number format');
  }
  return formatted;
}

/**
 * Validate if a phone number is in E.164 format
 * @param phoneNumber - The phone number to validate
 * @returns true if valid E.164 format, false otherwise
 */
export function isValidE164(phoneNumber: string): boolean {
  if (!phoneNumber) return false;

  // E.164 format: + followed by 1-15 digits, minimum 7 digits total (including country code)
  const e164Regex = /^\+[1-9]\d{6,14}$/;
  return e164Regex.test(phoneNumber);
}

/**
 * Format phone number for WhatsApp (adds whatsapp: prefix)
 * @param phoneNumber - The phone number to format
 * @param defaultCountryCode - Default country code if needed
 * @returns WhatsApp formatted phone number
 */
export function formatForWhatsApp(phoneNumber: string, defaultCountryCode: string = '+44'): string {
  // Remove whatsapp: prefix if present
  let cleaned = phoneNumber.replace(/^whatsapp:/, '');
  
  // Format to E.164
  const e164Number = formatToE164(cleaned, defaultCountryCode);
  
  // Add whatsapp: prefix
  return `whatsapp:${e164Number}`;
}

/**
 * Extract phone number from WhatsApp format
 * @param whatsappNumber - WhatsApp formatted number (whatsapp:+1234567890)
 * @returns E.164 formatted phone number
 */
export function extractFromWhatsApp(whatsappNumber: string): string {
  return whatsappNumber.replace(/^whatsapp:/, '');
}

/**
 * Normalize phone number by removing common prefixes and formatting
 * @param phoneNumber - The phone number to normalize
 * @returns Normalized phone number without prefixes
 */
export function normalizePhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return '';
  
  return phoneNumber
    .replace(/^whatsapp:/, '')
    .replace(/^sms:/, '')
    .trim();
}

/**
 * Get country code from E.164 phone number
 * @param phoneNumber - E.164 formatted phone number
 * @returns Country code (e.g., '+44', '+1')
 */
export function getCountryCode(phoneNumber: string): string {
  if (!phoneNumber.startsWith('+')) {
    throw new Error('Phone number must be in E.164 format');
  }
  
  // Common country codes (this is a simplified list)
  const countryCodes = ['+1', '+7', '+20', '+27', '+30', '+31', '+32', '+33', '+34', '+36', '+39', '+40', '+41', '+43', '+44', '+45', '+46', '+47', '+48', '+49', '+51', '+52', '+53', '+54', '+55', '+56', '+57', '+58', '+60', '+61', '+62', '+63', '+64', '+65', '+66', '+81', '+82', '+84', '+86', '+90', '+91', '+92', '+93', '+94', '+95', '+98', '+212', '+213', '+216', '+218', '+220', '+221', '+222', '+223', '+224', '+225', '+226', '+227', '+228', '+229', '+230', '+231', '+232', '+233', '+234', '+235', '+236', '+237', '+238', '+239', '+240', '+241', '+242', '+243', '+244', '+245', '+246', '+248', '+249', '+250', '+251', '+252', '+253', '+254', '+255', '+256', '+257', '+258', '+260', '+261', '+262', '+263', '+264', '+265', '+266', '+267', '+268', '+269', '+290', '+291', '+297', '+298', '+299', '+350', '+351', '+352', '+353', '+354', '+355', '+356', '+357', '+358', '+359', '+370', '+371', '+372', '+373', '+374', '+375', '+376', '+377', '+378', '+380', '+381', '+382', '+383', '+385', '+386', '+387', '+389', '+420', '+421', '+423', '+500', '+501', '+502', '+503', '+504', '+505', '+506', '+507', '+508', '+509', '+590', '+591', '+592', '+593', '+594', '+595', '+596', '+597', '+598', '+599', '+670', '+672', '+673', '+674', '+675', '+676', '+677', '+678', '+679', '+680', '+681', '+682', '+683', '+684', '+685', '+686', '+687', '+688', '+689', '+690', '+691', '+692', '+850', '+852', '+853', '+855', '+856', '+880', '+886', '+960', '+961', '+962', '+963', '+964', '+965', '+966', '+967', '+968', '+970', '+971', '+972', '+973', '+974', '+975', '+976', '+977', '+992', '+993', '+994', '+995', '+996', '+998'];
  
  // Find the longest matching country code
  for (const code of countryCodes.sort((a, b) => b.length - a.length)) {
    if (phoneNumber.startsWith(code)) {
      return code;
    }
  }
  
  // If no match found, assume single digit country code
  return phoneNumber.substring(0, 2);
}

/**
 * Format phone number for display (adds spaces for readability)
 * @param phoneNumber - E.164 formatted phone number
 * @returns Formatted phone number for display
 */
export function formatForDisplay(phoneNumber: string): string {
  if (!phoneNumber.startsWith('+')) {
    return phoneNumber;
  }
  
  const countryCode = getCountryCode(phoneNumber);
  const number = phoneNumber.substring(countryCode.length);
  
  // Format based on country code
  if (countryCode === '+44') {
    // UK format: +44 7123 456789
    if (number.length === 10) {
      return `${countryCode} ${number.substring(0, 4)} ${number.substring(4, 7)} ${number.substring(7)}`;
    }
  } else if (countryCode === '+1') {
    // US format: +****************
    if (number.length === 10) {
      return `${countryCode} (${number.substring(0, 3)}) ${number.substring(3, 6)}-${number.substring(6)}`;
    }
  }
  
  // Default format: +XX XXXX XXXX
  if (number.length >= 8) {
    const firstPart = number.substring(0, 4);
    const secondPart = number.substring(4);
    return `${countryCode} ${firstPart} ${secondPart}`;
  }
  
  return phoneNumber;
}
