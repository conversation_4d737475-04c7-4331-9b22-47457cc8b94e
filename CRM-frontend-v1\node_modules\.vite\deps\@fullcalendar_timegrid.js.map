{"version": 3, "sources": ["../../@fullcalendar/timegrid/internal.js", "../../@fullcalendar/timegrid/index.js"], "sourcesContent": ["import { Splitter, hasBgRendering, createFormatter, ViewContextType, ContentContainer, BaseComponent, DateComponent, diffDays, buildNavLinkAttrs, WeekNumberContainer, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, NowTimer, NowIndicatorContainer, renderScrollShim, rangeContainsMarker, startOfDay, asRoughMs, createDuration, RefMap, PositionCache, MoreLinkContainer, SegHierarchy, groupIntersectingEntries, binarySearch, getEntrySpanEnd, buildEntryKey, StandardEvent, memoize, sortEventSegs, DayCellContainer, hasCustomDayCellContent, getSegMeta, buildIsoString, computeEarliestSegStart, buildEventRangeKey, BgEvent, renderFill, addDurations, multiplyDuration, wholeDivideDurations, Slicer, intersectRanges, formatIsoTimeString, DayHeader, DaySeriesModel, DayTableModel, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, createRef, Fragment } from '@fullcalendar/core/preact.js';\nimport { DayTable } from '@fullcalendar/daygrid/internal.js';\n\nclass AllDaySplitter extends Splitter {\n    getKeyInfo() {\n        return {\n            allDay: {},\n            timed: {},\n        };\n    }\n    getKeysForDateSpan(dateSpan) {\n        if (dateSpan.allDay) {\n            return ['allDay'];\n        }\n        return ['timed'];\n    }\n    getKeysForEventDef(eventDef) {\n        if (!eventDef.allDay) {\n            return ['timed'];\n        }\n        if (hasBgRendering(eventDef)) {\n            return ['timed', 'allDay'];\n        }\n        return ['allDay'];\n    }\n}\n\nconst DEFAULT_SLAT_LABEL_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    omitZeroMinute: true,\n    meridiem: 'short',\n});\nfunction TimeColsAxisCell(props) {\n    let classNames = [\n        'fc-timegrid-slot',\n        'fc-timegrid-slot-label',\n        props.isLabeled ? 'fc-scrollgrid-shrink' : 'fc-timegrid-slot-minor',\n    ];\n    return (createElement(ViewContextType.Consumer, null, (context) => {\n        if (!props.isLabeled) {\n            return (createElement(\"td\", { className: classNames.join(' '), \"data-time\": props.isoTimeStr }));\n        }\n        let { dateEnv, options, viewApi } = context;\n        let labelFormat = // TODO: fully pre-parse\n         options.slotLabelFormat == null ? DEFAULT_SLAT_LABEL_FORMAT :\n            Array.isArray(options.slotLabelFormat) ? createFormatter(options.slotLabelFormat[0]) :\n                createFormatter(options.slotLabelFormat);\n        let renderProps = {\n            level: 0,\n            time: props.time,\n            date: dateEnv.toDate(props.date),\n            view: viewApi,\n            text: dateEnv.format(props.date, labelFormat),\n        };\n        return (createElement(ContentContainer, { elTag: \"td\", elClasses: classNames, elAttrs: {\n                'data-time': props.isoTimeStr,\n            }, renderProps: renderProps, generatorName: \"slotLabelContent\", customGenerator: options.slotLabelContent, defaultGenerator: renderInnerContent, classNameGenerator: options.slotLabelClassNames, didMount: options.slotLabelDidMount, willUnmount: options.slotLabelWillUnmount }, (InnerContent) => (createElement(\"div\", { className: \"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame\" },\n            createElement(InnerContent, { elTag: \"div\", elClasses: [\n                    'fc-timegrid-slot-label-cushion',\n                    'fc-scrollgrid-shrink-cushion',\n                ] })))));\n    }));\n}\nfunction renderInnerContent(props) {\n    return props.text;\n}\n\nclass TimeBodyAxis extends BaseComponent {\n    render() {\n        return this.props.slatMetas.map((slatMeta) => (createElement(\"tr\", { key: slatMeta.key },\n            createElement(TimeColsAxisCell, Object.assign({}, slatMeta)))));\n    }\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'short' });\nconst AUTO_ALL_DAY_MAX_EVENT_ROWS = 5;\nclass TimeColsView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.allDaySplitter = new AllDaySplitter(); // for use by subclasses\n        this.headerElRef = createRef();\n        this.rootElRef = createRef();\n        this.scrollerElRef = createRef();\n        this.state = {\n            slatCoords: null,\n        };\n        this.handleScrollTopRequest = (scrollTop) => {\n            let scrollerEl = this.scrollerElRef.current;\n            if (scrollerEl) { // TODO: not sure how this could ever be null. weirdness with the reducer\n                scrollerEl.scrollTop = scrollTop;\n            }\n        };\n        /* Header Render Methods\n        ------------------------------------------------------------------------------------------------------------------*/\n        this.renderHeadAxis = (rowKey, frameHeight = '') => {\n            let { options } = this.context;\n            let { dateProfile } = this.props;\n            let range = dateProfile.renderRange;\n            let dayCnt = diffDays(range.start, range.end);\n            // only do in day views (to avoid doing in week views that dont need it)\n            let navLinkAttrs = (dayCnt === 1)\n                ? buildNavLinkAttrs(this.context, range.start, 'week')\n                : {};\n            if (options.weekNumbers && rowKey === 'day') {\n                return (createElement(WeekNumberContainer, { elTag: \"th\", elClasses: [\n                        'fc-timegrid-axis',\n                        'fc-scrollgrid-shrink',\n                    ], elAttrs: {\n                        'aria-hidden': true,\n                    }, date: range.start, defaultFormat: DEFAULT_WEEK_NUM_FORMAT }, (InnerContent) => (createElement(\"div\", { className: [\n                        'fc-timegrid-axis-frame',\n                        'fc-scrollgrid-shrink-frame',\n                        'fc-timegrid-axis-frame-liquid',\n                    ].join(' '), style: { height: frameHeight } },\n                    createElement(InnerContent, { elTag: \"a\", elClasses: [\n                            'fc-timegrid-axis-cushion',\n                            'fc-scrollgrid-shrink-cushion',\n                            'fc-scrollgrid-sync-inner',\n                        ], elAttrs: navLinkAttrs })))));\n            }\n            return (createElement(\"th\", { \"aria-hidden\": true, className: \"fc-timegrid-axis\" },\n                createElement(\"div\", { className: \"fc-timegrid-axis-frame\", style: { height: frameHeight } })));\n        };\n        /* Table Component Render Methods\n        ------------------------------------------------------------------------------------------------------------------*/\n        // only a one-way height sync. we don't send the axis inner-content height to the DayGrid,\n        // but DayGrid still needs to have classNames on inner elements in order to measure.\n        this.renderTableRowAxis = (rowHeight) => {\n            let { options, viewApi } = this.context;\n            let renderProps = {\n                text: options.allDayText,\n                view: viewApi,\n            };\n            return (\n            // TODO: make reusable hook. used in list view too\n            createElement(ContentContainer, { elTag: \"td\", elClasses: [\n                    'fc-timegrid-axis',\n                    'fc-scrollgrid-shrink',\n                ], elAttrs: {\n                    'aria-hidden': true,\n                }, renderProps: renderProps, generatorName: \"allDayContent\", customGenerator: options.allDayContent, defaultGenerator: renderAllDayInner, classNameGenerator: options.allDayClassNames, didMount: options.allDayDidMount, willUnmount: options.allDayWillUnmount }, (InnerContent) => (createElement(\"div\", { className: [\n                    'fc-timegrid-axis-frame',\n                    'fc-scrollgrid-shrink-frame',\n                    rowHeight == null ? ' fc-timegrid-axis-frame-liquid' : '',\n                ].join(' '), style: { height: rowHeight } },\n                createElement(InnerContent, { elTag: \"span\", elClasses: [\n                        'fc-timegrid-axis-cushion',\n                        'fc-scrollgrid-shrink-cushion',\n                        'fc-scrollgrid-sync-inner',\n                    ] })))));\n        };\n        this.handleSlatCoords = (slatCoords) => {\n            this.setState({ slatCoords });\n        };\n    }\n    // rendering\n    // ----------------------------------------------------------------------------------------------------\n    renderSimpleLayout(headerRowContent, allDayContent, timeContent) {\n        let { context, props } = this;\n        let sections = [];\n        let stickyHeaderDates = getStickyHeaderDates(context.options);\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunk: {\n                    elRef: this.headerElRef,\n                    tableClassName: 'fc-col-header',\n                    rowContent: headerRowContent,\n                },\n            });\n        }\n        if (allDayContent) {\n            sections.push({\n                type: 'body',\n                key: 'all-day',\n                chunk: { content: allDayContent },\n            });\n            sections.push({\n                type: 'body',\n                key: 'all-day-divider',\n                outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n                createElement(\"tr\", { role: \"presentation\", className: \"fc-scrollgrid-section\" },\n                    createElement(\"td\", { className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded') }))),\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            expandRows: Boolean(context.options.expandRows),\n            chunk: {\n                scrollerElRef: this.scrollerElRef,\n                content: timeContent,\n            },\n        });\n        return (createElement(ViewContainer, { elRef: this.rootElRef, elClasses: ['fc-timegrid'], viewSpec: context.viewSpec },\n            createElement(SimpleScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, collapsibleWidth: props.forPrint, cols: [{ width: 'shrink' }], sections: sections })));\n    }\n    renderHScrollLayout(headerRowContent, allDayContent, timeContent, colCnt, dayMinWidth, slatMetas, slatCoords) {\n        let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n        if (!ScrollGrid) {\n            throw new Error('No ScrollGrid implementation');\n        }\n        let { context, props } = this;\n        let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n        let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n        let sections = [];\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                syncRowHeights: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        rowContent: (arg) => (createElement(\"tr\", { role: \"presentation\" }, this.renderHeadAxis('day', arg.rowSyncHeights[0]))),\n                    },\n                    {\n                        key: 'cols',\n                        elRef: this.headerElRef,\n                        tableClassName: 'fc-col-header',\n                        rowContent: headerRowContent,\n                    },\n                ],\n            });\n        }\n        if (allDayContent) {\n            sections.push({\n                type: 'body',\n                key: 'all-day',\n                syncRowHeights: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        rowContent: (contentArg) => (createElement(\"tr\", { role: \"presentation\" }, this.renderTableRowAxis(contentArg.rowSyncHeights[0]))),\n                    },\n                    {\n                        key: 'cols',\n                        content: allDayContent,\n                    },\n                ],\n            });\n            sections.push({\n                key: 'all-day-divider',\n                type: 'body',\n                outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n                createElement(\"tr\", { role: \"presentation\", className: \"fc-scrollgrid-section\" },\n                    createElement(\"td\", { colSpan: 2, className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded') }))),\n            });\n        }\n        let isNowIndicator = context.options.nowIndicator;\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            expandRows: Boolean(context.options.expandRows),\n            chunks: [\n                {\n                    key: 'axis',\n                    content: (arg) => (\n                    // TODO: make this now-indicator arrow more DRY with TimeColsContent\n                    createElement(\"div\", { className: \"fc-timegrid-axis-chunk\" },\n                        createElement(\"table\", { \"aria-hidden\": true, style: { height: arg.expandRows ? arg.clientHeight : '' } },\n                            arg.tableColGroupNode,\n                            createElement(\"tbody\", null,\n                                createElement(TimeBodyAxis, { slatMetas: slatMetas }))),\n                        createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" },\n                            createElement(NowTimer, { unit: isNowIndicator ? 'minute' : 'day' /* hacky */ }, (nowDate) => {\n                                let nowIndicatorTop = isNowIndicator &&\n                                    slatCoords &&\n                                    slatCoords.safeComputeTop(nowDate); // might return void\n                                if (typeof nowIndicatorTop === 'number') {\n                                    return (createElement(NowIndicatorContainer, { elClasses: ['fc-timegrid-now-indicator-arrow'], elStyle: { top: nowIndicatorTop }, isAxis: true, date: nowDate }));\n                                }\n                                return null;\n                            })))),\n                },\n                {\n                    key: 'cols',\n                    scrollerElRef: this.scrollerElRef,\n                    content: timeContent,\n                },\n            ],\n        });\n        if (stickyFooterScrollbar) {\n            sections.push({\n                key: 'footer',\n                type: 'footer',\n                isSticky: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        content: renderScrollShim,\n                    },\n                    {\n                        key: 'cols',\n                        content: renderScrollShim,\n                    },\n                ],\n            });\n        }\n        return (createElement(ViewContainer, { elRef: this.rootElRef, elClasses: ['fc-timegrid'], viewSpec: context.viewSpec },\n            createElement(ScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, forPrint: props.forPrint, collapsibleWidth: false, colGroups: [\n                    { width: 'shrink', cols: [{ width: 'shrink' }] },\n                    { cols: [{ span: colCnt, minWidth: dayMinWidth }] },\n                ], sections: sections })));\n    }\n    /* Dimensions\n    ------------------------------------------------------------------------------------------------------------------*/\n    getAllDayMaxEventProps() {\n        let { dayMaxEvents, dayMaxEventRows } = this.context.options;\n        if (dayMaxEvents === true || dayMaxEventRows === true) { // is auto?\n            dayMaxEvents = undefined;\n            dayMaxEventRows = AUTO_ALL_DAY_MAX_EVENT_ROWS; // make sure \"auto\" goes to a real number\n        }\n        return { dayMaxEvents, dayMaxEventRows };\n    }\n}\nfunction renderAllDayInner(renderProps) {\n    return renderProps.text;\n}\n\nclass TimeColsSlatsCoords {\n    constructor(positions, dateProfile, slotDuration) {\n        this.positions = positions;\n        this.dateProfile = dateProfile;\n        this.slotDuration = slotDuration;\n    }\n    safeComputeTop(date) {\n        let { dateProfile } = this;\n        if (rangeContainsMarker(dateProfile.currentRange, date)) {\n            let startOfDayDate = startOfDay(date);\n            let timeMs = date.valueOf() - startOfDayDate.valueOf();\n            if (timeMs >= asRoughMs(dateProfile.slotMinTime) &&\n                timeMs < asRoughMs(dateProfile.slotMaxTime)) {\n                return this.computeTimeTop(createDuration(timeMs));\n            }\n        }\n        return null;\n    }\n    // Computes the top coordinate, relative to the bounds of the grid, of the given date.\n    // A `startOfDayDate` must be given for avoiding ambiguity over how to treat midnight.\n    computeDateTop(when, startOfDayDate) {\n        if (!startOfDayDate) {\n            startOfDayDate = startOfDay(when);\n        }\n        return this.computeTimeTop(createDuration(when.valueOf() - startOfDayDate.valueOf()));\n    }\n    // Computes the top coordinate, relative to the bounds of the grid, of the given time (a Duration).\n    // This is a makeshify way to compute the time-top. Assumes all slatMetas dates are uniform.\n    // Eventually allow computation with arbirary slat dates.\n    computeTimeTop(duration) {\n        let { positions, dateProfile } = this;\n        let len = positions.els.length;\n        // floating-point value of # of slots covered\n        let slatCoverage = (duration.milliseconds - asRoughMs(dateProfile.slotMinTime)) / asRoughMs(this.slotDuration);\n        let slatIndex;\n        let slatRemainder;\n        // compute a floating-point number for how many slats should be progressed through.\n        // from 0 to number of slats (inclusive)\n        // constrained because slotMinTime/slotMaxTime might be customized.\n        slatCoverage = Math.max(0, slatCoverage);\n        slatCoverage = Math.min(len, slatCoverage);\n        // an integer index of the furthest whole slat\n        // from 0 to number slats (*exclusive*, so len-1)\n        slatIndex = Math.floor(slatCoverage);\n        slatIndex = Math.min(slatIndex, len - 1);\n        // how much further through the slatIndex slat (from 0.0-1.0) must be covered in addition.\n        // could be 1.0 if slatCoverage is covering *all* the slots\n        slatRemainder = slatCoverage - slatIndex;\n        return positions.tops[slatIndex] +\n            positions.getHeight(slatIndex) * slatRemainder;\n    }\n}\n\nclass TimeColsSlatsBody extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { slatElRefs } = props;\n        return (createElement(\"tbody\", null, props.slatMetas.map((slatMeta, i) => {\n            let renderProps = {\n                time: slatMeta.time,\n                date: context.dateEnv.toDate(slatMeta.date),\n                view: context.viewApi,\n            };\n            return (createElement(\"tr\", { key: slatMeta.key, ref: slatElRefs.createRef(slatMeta.key) },\n                props.axis && (createElement(TimeColsAxisCell, Object.assign({}, slatMeta))),\n                createElement(ContentContainer, { elTag: \"td\", elClasses: [\n                        'fc-timegrid-slot',\n                        'fc-timegrid-slot-lane',\n                        !slatMeta.isLabeled && 'fc-timegrid-slot-minor',\n                    ], elAttrs: {\n                        'data-time': slatMeta.isoTimeStr,\n                    }, renderProps: renderProps, generatorName: \"slotLaneContent\", customGenerator: options.slotLaneContent, classNameGenerator: options.slotLaneClassNames, didMount: options.slotLaneDidMount, willUnmount: options.slotLaneWillUnmount })));\n        })));\n    }\n}\n\n/*\nfor the horizontal \"slats\" that run width-wise. Has a time axis on a side. Depends on RTL.\n*/\nclass TimeColsSlats extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.rootElRef = createRef();\n        this.slatElRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        return (createElement(\"div\", { ref: this.rootElRef, className: \"fc-timegrid-slots\" },\n            createElement(\"table\", { \"aria-hidden\": true, className: context.theme.getClass('table'), style: {\n                    minWidth: props.tableMinWidth,\n                    width: props.clientWidth,\n                    height: props.minHeight,\n                } },\n                props.tableColGroupNode /* relies on there only being a single <col> for the axis */,\n                createElement(TimeColsSlatsBody, { slatElRefs: this.slatElRefs, axis: props.axis, slatMetas: props.slatMetas }))));\n    }\n    componentDidMount() {\n        this.updateSizing();\n    }\n    componentDidUpdate() {\n        this.updateSizing();\n    }\n    componentWillUnmount() {\n        if (this.props.onCoords) {\n            this.props.onCoords(null);\n        }\n    }\n    updateSizing() {\n        let { context, props } = this;\n        if (props.onCoords &&\n            props.clientWidth !== null // means sizing has stabilized\n        ) {\n            let rootEl = this.rootElRef.current;\n            if (rootEl.offsetHeight) { // not hidden by css\n                props.onCoords(new TimeColsSlatsCoords(new PositionCache(this.rootElRef.current, collectSlatEls(this.slatElRefs.currentMap, props.slatMetas), false, true), this.props.dateProfile, context.options.slotDuration));\n            }\n        }\n    }\n}\nfunction collectSlatEls(elMap, slatMetas) {\n    return slatMetas.map((slatMeta) => elMap[slatMeta.key]);\n}\n\nfunction splitSegsByCol(segs, colCnt) {\n    let segsByCol = [];\n    let i;\n    for (i = 0; i < colCnt; i += 1) {\n        segsByCol.push([]);\n    }\n    if (segs) {\n        for (i = 0; i < segs.length; i += 1) {\n            segsByCol[segs[i].col].push(segs[i]);\n        }\n    }\n    return segsByCol;\n}\nfunction splitInteractionByCol(ui, colCnt) {\n    let byRow = [];\n    if (!ui) {\n        for (let i = 0; i < colCnt; i += 1) {\n            byRow[i] = null;\n        }\n    }\n    else {\n        for (let i = 0; i < colCnt; i += 1) {\n            byRow[i] = {\n                affectedInstances: ui.affectedInstances,\n                isEvent: ui.isEvent,\n                segs: [],\n            };\n        }\n        for (let seg of ui.segs) {\n            byRow[seg.col].segs.push(seg);\n        }\n    }\n    return byRow;\n}\n\nclass TimeColMoreLink extends BaseComponent {\n    render() {\n        let { props } = this;\n        return (createElement(MoreLinkContainer, { elClasses: ['fc-timegrid-more-link'], elStyle: {\n                top: props.top,\n                bottom: props.bottom,\n            }, allDayDate: null, moreCnt: props.hiddenSegs.length, allSegs: props.hiddenSegs, hiddenSegs: props.hiddenSegs, extraDateSpan: props.extraDateSpan, dateProfile: props.dateProfile, todayRange: props.todayRange, popoverContent: () => renderPlainFgSegs(props.hiddenSegs, props), defaultGenerator: renderMoreLinkInner, forceTimed: true }, (InnerContent) => (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-timegrid-more-link-inner', 'fc-sticky'] }))));\n    }\n}\nfunction renderMoreLinkInner(props) {\n    return props.shortText;\n}\n\n// segInputs assumed sorted\nfunction buildPositioning(segInputs, strictOrder, maxStackCnt) {\n    let hierarchy = new SegHierarchy();\n    if (strictOrder != null) {\n        hierarchy.strictOrder = strictOrder;\n    }\n    if (maxStackCnt != null) {\n        hierarchy.maxStackCnt = maxStackCnt;\n    }\n    let hiddenEntries = hierarchy.addSegs(segInputs);\n    let hiddenGroups = groupIntersectingEntries(hiddenEntries);\n    let web = buildWeb(hierarchy);\n    web = stretchWeb(web, 1); // all levelCoords/thickness will have 0.0-1.0\n    let segRects = webToRects(web);\n    return { segRects, hiddenGroups };\n}\nfunction buildWeb(hierarchy) {\n    const { entriesByLevel } = hierarchy;\n    const buildNode = cacheable((level, lateral) => level + ':' + lateral, (level, lateral) => {\n        let siblingRange = findNextLevelSegs(hierarchy, level, lateral);\n        let nextLevelRes = buildNodes(siblingRange, buildNode);\n        let entry = entriesByLevel[level][lateral];\n        return [\n            Object.assign(Object.assign({}, entry), { nextLevelNodes: nextLevelRes[0] }),\n            entry.thickness + nextLevelRes[1], // the pressure builds\n        ];\n    });\n    return buildNodes(entriesByLevel.length\n        ? { level: 0, lateralStart: 0, lateralEnd: entriesByLevel[0].length }\n        : null, buildNode)[0];\n}\nfunction buildNodes(siblingRange, buildNode) {\n    if (!siblingRange) {\n        return [[], 0];\n    }\n    let { level, lateralStart, lateralEnd } = siblingRange;\n    let lateral = lateralStart;\n    let pairs = [];\n    while (lateral < lateralEnd) {\n        pairs.push(buildNode(level, lateral));\n        lateral += 1;\n    }\n    pairs.sort(cmpDescPressures);\n    return [\n        pairs.map(extractNode),\n        pairs[0][1], // first item's pressure\n    ];\n}\nfunction cmpDescPressures(a, b) {\n    return b[1] - a[1];\n}\nfunction extractNode(a) {\n    return a[0];\n}\nfunction findNextLevelSegs(hierarchy, subjectLevel, subjectLateral) {\n    let { levelCoords, entriesByLevel } = hierarchy;\n    let subjectEntry = entriesByLevel[subjectLevel][subjectLateral];\n    let afterSubject = levelCoords[subjectLevel] + subjectEntry.thickness;\n    let levelCnt = levelCoords.length;\n    let level = subjectLevel;\n    // skip past levels that are too high up\n    for (; level < levelCnt && levelCoords[level] < afterSubject; level += 1)\n        ; // do nothing\n    for (; level < levelCnt; level += 1) {\n        let entries = entriesByLevel[level];\n        let entry;\n        let searchIndex = binarySearch(entries, subjectEntry.span.start, getEntrySpanEnd);\n        let lateralStart = searchIndex[0] + searchIndex[1]; // if exact match (which doesn't collide), go to next one\n        let lateralEnd = lateralStart;\n        while ( // loop through entries that horizontally intersect\n        (entry = entries[lateralEnd]) && // but not past the whole seg list\n            entry.span.start < subjectEntry.span.end) {\n            lateralEnd += 1;\n        }\n        if (lateralStart < lateralEnd) {\n            return { level, lateralStart, lateralEnd };\n        }\n    }\n    return null;\n}\nfunction stretchWeb(topLevelNodes, totalThickness) {\n    const stretchNode = cacheable((node, startCoord, prevThickness) => buildEntryKey(node), (node, startCoord, prevThickness) => {\n        let { nextLevelNodes, thickness } = node;\n        let allThickness = thickness + prevThickness;\n        let thicknessFraction = thickness / allThickness;\n        let endCoord;\n        let newChildren = [];\n        if (!nextLevelNodes.length) {\n            endCoord = totalThickness;\n        }\n        else {\n            for (let childNode of nextLevelNodes) {\n                if (endCoord === undefined) {\n                    let res = stretchNode(childNode, startCoord, allThickness);\n                    endCoord = res[0];\n                    newChildren.push(res[1]);\n                }\n                else {\n                    let res = stretchNode(childNode, endCoord, 0);\n                    newChildren.push(res[1]);\n                }\n            }\n        }\n        let newThickness = (endCoord - startCoord) * thicknessFraction;\n        return [endCoord - newThickness, Object.assign(Object.assign({}, node), { thickness: newThickness, nextLevelNodes: newChildren })];\n    });\n    return topLevelNodes.map((node) => stretchNode(node, 0, 0)[1]);\n}\n// not sorted in any particular order\nfunction webToRects(topLevelNodes) {\n    let rects = [];\n    const processNode = cacheable((node, levelCoord, stackDepth) => buildEntryKey(node), (node, levelCoord, stackDepth) => {\n        let rect = Object.assign(Object.assign({}, node), { levelCoord,\n            stackDepth, stackForward: 0 });\n        rects.push(rect);\n        return (rect.stackForward = processNodes(node.nextLevelNodes, levelCoord + node.thickness, stackDepth + 1) + 1);\n    });\n    function processNodes(nodes, levelCoord, stackDepth) {\n        let stackForward = 0;\n        for (let node of nodes) {\n            stackForward = Math.max(processNode(node, levelCoord, stackDepth), stackForward);\n        }\n        return stackForward;\n    }\n    processNodes(topLevelNodes, 0, 0);\n    return rects; // TODO: sort rects by levelCoord to be consistent with toRects?\n}\n// TODO: move to general util\nfunction cacheable(keyFunc, workFunc) {\n    const cache = {};\n    return (...args) => {\n        let key = keyFunc(...args);\n        return (key in cache)\n            ? cache[key]\n            : (cache[key] = workFunc(...args));\n    };\n}\n\nfunction computeSegVCoords(segs, colDate, slatCoords = null, eventMinHeight = 0) {\n    let vcoords = [];\n    if (slatCoords) {\n        for (let i = 0; i < segs.length; i += 1) {\n            let seg = segs[i];\n            let spanStart = slatCoords.computeDateTop(seg.start, colDate);\n            let spanEnd = Math.max(spanStart + (eventMinHeight || 0), // :(\n            slatCoords.computeDateTop(seg.end, colDate));\n            vcoords.push({\n                start: Math.round(spanStart),\n                end: Math.round(spanEnd), //\n            });\n        }\n    }\n    return vcoords;\n}\nfunction computeFgSegPlacements(segs, segVCoords, // might not have for every seg\neventOrderStrict, eventMaxStack) {\n    let segInputs = [];\n    let dumbSegs = []; // segs without coords\n    for (let i = 0; i < segs.length; i += 1) {\n        let vcoords = segVCoords[i];\n        if (vcoords) {\n            segInputs.push({\n                index: i,\n                thickness: 1,\n                span: vcoords,\n            });\n        }\n        else {\n            dumbSegs.push(segs[i]);\n        }\n    }\n    let { segRects, hiddenGroups } = buildPositioning(segInputs, eventOrderStrict, eventMaxStack);\n    let segPlacements = [];\n    for (let segRect of segRects) {\n        segPlacements.push({\n            seg: segs[segRect.index],\n            rect: segRect,\n        });\n    }\n    for (let dumbSeg of dumbSegs) {\n        segPlacements.push({ seg: dumbSeg, rect: null });\n    }\n    return { segPlacements, hiddenGroups };\n}\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    meridiem: false,\n});\nclass TimeColEvent extends BaseComponent {\n    render() {\n        return (createElement(StandardEvent, Object.assign({}, this.props, { elClasses: [\n                'fc-timegrid-event',\n                'fc-v-event',\n                this.props.isShort && 'fc-timegrid-event-short',\n            ], defaultTimeFormat: DEFAULT_TIME_FORMAT })));\n    }\n}\n\nclass TimeCol extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.sortEventSegs = memoize(sortEventSegs);\n    }\n    // TODO: memoize event-placement?\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let isSelectMirror = options.selectMirror;\n        let mirrorSegs = // yuck\n         (props.eventDrag && props.eventDrag.segs) ||\n            (props.eventResize && props.eventResize.segs) ||\n            (isSelectMirror && props.dateSelectionSegs) ||\n            [];\n        let interactionAffectedInstances = // TODO: messy way to compute this\n         (props.eventDrag && props.eventDrag.affectedInstances) ||\n            (props.eventResize && props.eventResize.affectedInstances) ||\n            {};\n        let sortedFgSegs = this.sortEventSegs(props.fgEventSegs, options.eventOrder);\n        return (createElement(DayCellContainer, { elTag: \"td\", elRef: props.elRef, elClasses: [\n                'fc-timegrid-col',\n                ...(props.extraClassNames || []),\n            ], elAttrs: Object.assign({ role: 'gridcell' }, props.extraDataAttrs), date: props.date, dateProfile: props.dateProfile, todayRange: props.todayRange, extraRenderProps: props.extraRenderProps }, (InnerContent) => (createElement(\"div\", { className: \"fc-timegrid-col-frame\" },\n            createElement(\"div\", { className: \"fc-timegrid-col-bg\" },\n                this.renderFillSegs(props.businessHourSegs, 'non-business'),\n                this.renderFillSegs(props.bgEventSegs, 'bg-event'),\n                this.renderFillSegs(props.dateSelectionSegs, 'highlight')),\n            createElement(\"div\", { className: \"fc-timegrid-col-events\" }, this.renderFgSegs(sortedFgSegs, interactionAffectedInstances, false, false, false)),\n            createElement(\"div\", { className: \"fc-timegrid-col-events\" }, this.renderFgSegs(mirrorSegs, {}, Boolean(props.eventDrag), Boolean(props.eventResize), Boolean(isSelectMirror), 'mirror')),\n            createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" }, this.renderNowIndicator(props.nowIndicatorSegs)),\n            hasCustomDayCellContent(options) && (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-timegrid-col-misc'] }))))));\n    }\n    renderFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n        let { props } = this;\n        if (props.forPrint) {\n            return renderPlainFgSegs(sortedFgSegs, props);\n        }\n        return this.renderPositionedFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey);\n    }\n    renderPositionedFgSegs(segs, // if not mirror, needs to be sorted\n    segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n        let { eventMaxStack, eventShortHeight, eventOrderStrict, eventMinHeight } = this.context.options;\n        let { date, slatCoords, eventSelection, todayRange, nowDate } = this.props;\n        let isMirror = isDragging || isResizing || isDateSelecting;\n        let segVCoords = computeSegVCoords(segs, date, slatCoords, eventMinHeight);\n        let { segPlacements, hiddenGroups } = computeFgSegPlacements(segs, segVCoords, eventOrderStrict, eventMaxStack);\n        return (createElement(Fragment, null,\n            this.renderHiddenGroups(hiddenGroups, segs),\n            segPlacements.map((segPlacement) => {\n                let { seg, rect } = segPlacement;\n                let instanceId = seg.eventRange.instance.instanceId;\n                let isVisible = isMirror || Boolean(!segIsInvisible[instanceId] && rect);\n                let vStyle = computeSegVStyle(rect && rect.span);\n                let hStyle = (!isMirror && rect) ? this.computeSegHStyle(rect) : { left: 0, right: 0 };\n                let isInset = Boolean(rect) && rect.stackForward > 0;\n                let isShort = Boolean(rect) && (rect.span.end - rect.span.start) < eventShortHeight; // look at other places for this problem\n                return (createElement(\"div\", { className: 'fc-timegrid-event-harness' +\n                        (isInset ? ' fc-timegrid-event-harness-inset' : ''), key: forcedKey || instanceId, style: Object.assign(Object.assign({ visibility: isVisible ? '' : 'hidden' }, vStyle), hStyle) },\n                    createElement(TimeColEvent, Object.assign({ seg: seg, isDragging: isDragging, isResizing: isResizing, isDateSelecting: isDateSelecting, isSelected: instanceId === eventSelection, isShort: isShort }, getSegMeta(seg, todayRange, nowDate)))));\n            })));\n    }\n    // will already have eventMinHeight applied because segInputs already had it\n    renderHiddenGroups(hiddenGroups, segs) {\n        let { extraDateSpan, dateProfile, todayRange, nowDate, eventSelection, eventDrag, eventResize } = this.props;\n        return (createElement(Fragment, null, hiddenGroups.map((hiddenGroup) => {\n            let positionCss = computeSegVStyle(hiddenGroup.span);\n            let hiddenSegs = compileSegsFromEntries(hiddenGroup.entries, segs);\n            return (createElement(TimeColMoreLink, { key: buildIsoString(computeEarliestSegStart(hiddenSegs)), hiddenSegs: hiddenSegs, top: positionCss.top, bottom: positionCss.bottom, extraDateSpan: extraDateSpan, dateProfile: dateProfile, todayRange: todayRange, nowDate: nowDate, eventSelection: eventSelection, eventDrag: eventDrag, eventResize: eventResize }));\n        })));\n    }\n    renderFillSegs(segs, fillType) {\n        let { props, context } = this;\n        let segVCoords = computeSegVCoords(segs, props.date, props.slatCoords, context.options.eventMinHeight); // don't assume all populated\n        let children = segVCoords.map((vcoords, i) => {\n            let seg = segs[i];\n            return (createElement(\"div\", { key: buildEventRangeKey(seg.eventRange), className: \"fc-timegrid-bg-harness\", style: computeSegVStyle(vcoords) }, fillType === 'bg-event' ?\n                createElement(BgEvent, Object.assign({ seg: seg }, getSegMeta(seg, props.todayRange, props.nowDate))) :\n                renderFill(fillType)));\n        });\n        return createElement(Fragment, null, children);\n    }\n    renderNowIndicator(segs) {\n        let { slatCoords, date } = this.props;\n        if (!slatCoords) {\n            return null;\n        }\n        return segs.map((seg, i) => (createElement(NowIndicatorContainer\n        // key doesn't matter. will only ever be one\n        , { \n            // key doesn't matter. will only ever be one\n            key: i, elClasses: ['fc-timegrid-now-indicator-line'], elStyle: {\n                top: slatCoords.computeDateTop(seg.start, date),\n            }, isAxis: false, date: date })));\n    }\n    computeSegHStyle(segHCoords) {\n        let { isRtl, options } = this.context;\n        let shouldOverlap = options.slotEventOverlap;\n        let nearCoord = segHCoords.levelCoord; // the left side if LTR. the right side if RTL. floating-point\n        let farCoord = segHCoords.levelCoord + segHCoords.thickness; // the right side if LTR. the left side if RTL. floating-point\n        let left; // amount of space from left edge, a fraction of the total width\n        let right; // amount of space from right edge, a fraction of the total width\n        if (shouldOverlap) {\n            // double the width, but don't go beyond the maximum forward coordinate (1.0)\n            farCoord = Math.min(1, nearCoord + (farCoord - nearCoord) * 2);\n        }\n        if (isRtl) {\n            left = 1 - farCoord;\n            right = nearCoord;\n        }\n        else {\n            left = nearCoord;\n            right = 1 - farCoord;\n        }\n        let props = {\n            zIndex: segHCoords.stackDepth + 1,\n            left: left * 100 + '%',\n            right: right * 100 + '%',\n        };\n        if (shouldOverlap && !segHCoords.stackForward) {\n            // add padding to the edge so that forward stacked events don't cover the resizer's icon\n            props[isRtl ? 'marginLeft' : 'marginRight'] = 10 * 2; // 10 is a guesstimate of the icon's width\n        }\n        return props;\n    }\n}\nfunction renderPlainFgSegs(sortedFgSegs, { todayRange, nowDate, eventSelection, eventDrag, eventResize }) {\n    let hiddenInstances = (eventDrag ? eventDrag.affectedInstances : null) ||\n        (eventResize ? eventResize.affectedInstances : null) ||\n        {};\n    return (createElement(Fragment, null, sortedFgSegs.map((seg) => {\n        let instanceId = seg.eventRange.instance.instanceId;\n        return (createElement(\"div\", { key: instanceId, style: { visibility: hiddenInstances[instanceId] ? 'hidden' : '' } },\n            createElement(TimeColEvent, Object.assign({ seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: instanceId === eventSelection, isShort: false }, getSegMeta(seg, todayRange, nowDate)))));\n    })));\n}\nfunction computeSegVStyle(segVCoords) {\n    if (!segVCoords) {\n        return { top: '', bottom: '' };\n    }\n    return {\n        top: segVCoords.start,\n        bottom: -segVCoords.end,\n    };\n}\nfunction compileSegsFromEntries(segEntries, allSegs) {\n    return segEntries.map((segEntry) => allSegs[segEntry.index]);\n}\n\nclass TimeColsContent extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.splitFgEventSegs = memoize(splitSegsByCol);\n        this.splitBgEventSegs = memoize(splitSegsByCol);\n        this.splitBusinessHourSegs = memoize(splitSegsByCol);\n        this.splitNowIndicatorSegs = memoize(splitSegsByCol);\n        this.splitDateSelectionSegs = memoize(splitSegsByCol);\n        this.splitEventDrag = memoize(splitInteractionByCol);\n        this.splitEventResize = memoize(splitInteractionByCol);\n        this.rootElRef = createRef();\n        this.cellElRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        let nowIndicatorTop = context.options.nowIndicator &&\n            props.slatCoords &&\n            props.slatCoords.safeComputeTop(props.nowDate); // might return void\n        let colCnt = props.cells.length;\n        let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, colCnt);\n        let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, colCnt);\n        let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, colCnt);\n        let nowIndicatorSegsByRow = this.splitNowIndicatorSegs(props.nowIndicatorSegs, colCnt);\n        let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, colCnt);\n        let eventDragByRow = this.splitEventDrag(props.eventDrag, colCnt);\n        let eventResizeByRow = this.splitEventResize(props.eventResize, colCnt);\n        return (createElement(\"div\", { className: \"fc-timegrid-cols\", ref: this.rootElRef },\n            createElement(\"table\", { role: \"presentation\", style: {\n                    minWidth: props.tableMinWidth,\n                    width: props.clientWidth,\n                } },\n                props.tableColGroupNode,\n                createElement(\"tbody\", { role: \"presentation\" },\n                    createElement(\"tr\", { role: \"row\" },\n                        props.axis && (createElement(\"td\", { \"aria-hidden\": true, className: \"fc-timegrid-col fc-timegrid-axis\" },\n                            createElement(\"div\", { className: \"fc-timegrid-col-frame\" },\n                                createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" }, typeof nowIndicatorTop === 'number' && (createElement(NowIndicatorContainer, { elClasses: ['fc-timegrid-now-indicator-arrow'], elStyle: { top: nowIndicatorTop }, isAxis: true, date: props.nowDate })))))),\n                        props.cells.map((cell, i) => (createElement(TimeCol, { key: cell.key, elRef: this.cellElRefs.createRef(cell.key), dateProfile: props.dateProfile, date: cell.date, nowDate: props.nowDate, todayRange: props.todayRange, extraRenderProps: cell.extraRenderProps, extraDataAttrs: cell.extraDataAttrs, extraClassNames: cell.extraClassNames, extraDateSpan: cell.extraDateSpan, fgEventSegs: fgEventSegsByRow[i], bgEventSegs: bgEventSegsByRow[i], businessHourSegs: businessHourSegsByRow[i], nowIndicatorSegs: nowIndicatorSegsByRow[i], dateSelectionSegs: dateSelectionSegsByRow[i], eventDrag: eventDragByRow[i], eventResize: eventResizeByRow[i], slatCoords: props.slatCoords, eventSelection: props.eventSelection, forPrint: props.forPrint }))))))));\n    }\n    componentDidMount() {\n        this.updateCoords();\n    }\n    componentDidUpdate() {\n        this.updateCoords();\n    }\n    updateCoords() {\n        let { props } = this;\n        if (props.onColCoords &&\n            props.clientWidth !== null // means sizing has stabilized\n        ) {\n            props.onColCoords(new PositionCache(this.rootElRef.current, collectCellEls(this.cellElRefs.currentMap, props.cells), true, // horizontal\n            false));\n        }\n    }\n}\nfunction collectCellEls(elMap, cells) {\n    return cells.map((cell) => elMap[cell.key]);\n}\n\n/* A component that renders one or more columns of vertical time slots\n----------------------------------------------------------------------------------------------------------------------*/\nclass TimeCols extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.processSlotOptions = memoize(processSlotOptions);\n        this.state = {\n            slatCoords: null,\n        };\n        this.handleRootEl = (el) => {\n            if (el) {\n                this.context.registerInteractiveComponent(this, {\n                    el,\n                    isHitComboAllowed: this.props.isHitComboAllowed,\n                });\n            }\n            else {\n                this.context.unregisterInteractiveComponent(this);\n            }\n        };\n        this.handleScrollRequest = (request) => {\n            let { onScrollTopRequest } = this.props;\n            let { slatCoords } = this.state;\n            if (onScrollTopRequest && slatCoords) {\n                if (request.time) {\n                    let top = slatCoords.computeTimeTop(request.time);\n                    top = Math.ceil(top); // zoom can give weird floating-point values. rather scroll a little bit further\n                    if (top) {\n                        top += 1; // to overcome top border that slots beyond the first have. looks better\n                    }\n                    onScrollTopRequest(top);\n                }\n                return true;\n            }\n            return false;\n        };\n        this.handleColCoords = (colCoords) => {\n            this.colCoords = colCoords;\n        };\n        this.handleSlatCoords = (slatCoords) => {\n            this.setState({ slatCoords });\n            if (this.props.onSlatCoords) {\n                this.props.onSlatCoords(slatCoords);\n            }\n        };\n    }\n    render() {\n        let { props, state } = this;\n        return (createElement(\"div\", { className: \"fc-timegrid-body\", ref: this.handleRootEl, style: {\n                // these props are important to give this wrapper correct dimensions for interactions\n                // TODO: if we set it here, can we avoid giving to inner tables?\n                width: props.clientWidth,\n                minWidth: props.tableMinWidth,\n            } },\n            createElement(TimeColsSlats, { axis: props.axis, dateProfile: props.dateProfile, slatMetas: props.slatMetas, clientWidth: props.clientWidth, minHeight: props.expandRows ? props.clientHeight : '', tableMinWidth: props.tableMinWidth, tableColGroupNode: props.axis ? props.tableColGroupNode : null /* axis depends on the colgroup's shrinking */, onCoords: this.handleSlatCoords }),\n            createElement(TimeColsContent, { cells: props.cells, axis: props.axis, dateProfile: props.dateProfile, businessHourSegs: props.businessHourSegs, bgEventSegs: props.bgEventSegs, fgEventSegs: props.fgEventSegs, dateSelectionSegs: props.dateSelectionSegs, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, todayRange: props.todayRange, nowDate: props.nowDate, nowIndicatorSegs: props.nowIndicatorSegs, clientWidth: props.clientWidth, tableMinWidth: props.tableMinWidth, tableColGroupNode: props.tableColGroupNode, slatCoords: state.slatCoords, onColCoords: this.handleColCoords, forPrint: props.forPrint })));\n    }\n    componentDidMount() {\n        this.scrollResponder = this.context.createScrollResponder(this.handleScrollRequest);\n    }\n    componentDidUpdate(prevProps) {\n        this.scrollResponder.update(prevProps.dateProfile !== this.props.dateProfile);\n    }\n    componentWillUnmount() {\n        this.scrollResponder.detach();\n    }\n    queryHit(positionLeft, positionTop) {\n        let { dateEnv, options } = this.context;\n        let { colCoords } = this;\n        let { dateProfile } = this.props;\n        let { slatCoords } = this.state;\n        let { snapDuration, snapsPerSlot } = this.processSlotOptions(this.props.slotDuration, options.snapDuration);\n        let colIndex = colCoords.leftToIndex(positionLeft);\n        let slatIndex = slatCoords.positions.topToIndex(positionTop);\n        if (colIndex != null && slatIndex != null) {\n            let cell = this.props.cells[colIndex];\n            let slatTop = slatCoords.positions.tops[slatIndex];\n            let slatHeight = slatCoords.positions.getHeight(slatIndex);\n            let partial = (positionTop - slatTop) / slatHeight; // floating point number between 0 and 1\n            let localSnapIndex = Math.floor(partial * snapsPerSlot); // the snap # relative to start of slat\n            let snapIndex = slatIndex * snapsPerSlot + localSnapIndex;\n            let dayDate = this.props.cells[colIndex].date;\n            let time = addDurations(dateProfile.slotMinTime, multiplyDuration(snapDuration, snapIndex));\n            let start = dateEnv.add(dayDate, time);\n            let end = dateEnv.add(start, snapDuration);\n            return {\n                dateProfile,\n                dateSpan: Object.assign({ range: { start, end }, allDay: false }, cell.extraDateSpan),\n                dayEl: colCoords.els[colIndex],\n                rect: {\n                    left: colCoords.lefts[colIndex],\n                    right: colCoords.rights[colIndex],\n                    top: slatTop,\n                    bottom: slatTop + slatHeight,\n                },\n                layer: 0,\n            };\n        }\n        return null;\n    }\n}\nfunction processSlotOptions(slotDuration, snapDurationOverride) {\n    let snapDuration = snapDurationOverride || slotDuration;\n    let snapsPerSlot = wholeDivideDurations(slotDuration, snapDuration);\n    if (snapsPerSlot === null) {\n        snapDuration = slotDuration;\n        snapsPerSlot = 1;\n        // TODO: say warning?\n    }\n    return { snapDuration, snapsPerSlot };\n}\n\nclass DayTimeColsSlicer extends Slicer {\n    sliceRange(range, dayRanges) {\n        let segs = [];\n        for (let col = 0; col < dayRanges.length; col += 1) {\n            let segRange = intersectRanges(range, dayRanges[col]);\n            if (segRange) {\n                segs.push({\n                    start: segRange.start,\n                    end: segRange.end,\n                    isStart: segRange.start.valueOf() === range.start.valueOf(),\n                    isEnd: segRange.end.valueOf() === range.end.valueOf(),\n                    col,\n                });\n            }\n        }\n        return segs;\n    }\n}\n\nclass DayTimeCols extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.buildDayRanges = memoize(buildDayRanges);\n        this.slicer = new DayTimeColsSlicer();\n        this.timeColsRef = createRef();\n    }\n    render() {\n        let { props, context } = this;\n        let { dateProfile, dayTableModel } = props;\n        let { nowIndicator, nextDayThreshold } = context.options;\n        let dayRanges = this.buildDayRanges(dayTableModel, dateProfile, context.dateEnv);\n        // give it the first row of cells\n        // TODO: would move this further down hierarchy, but sliceNowDate needs it\n        return (createElement(NowTimer, { unit: nowIndicator ? 'minute' : 'day' }, (nowDate, todayRange) => (createElement(TimeCols, Object.assign({ ref: this.timeColsRef }, this.slicer.sliceProps(props, dateProfile, null, context, dayRanges), { forPrint: props.forPrint, axis: props.axis, dateProfile: dateProfile, slatMetas: props.slatMetas, slotDuration: props.slotDuration, cells: dayTableModel.cells[0], tableColGroupNode: props.tableColGroupNode, tableMinWidth: props.tableMinWidth, clientWidth: props.clientWidth, clientHeight: props.clientHeight, expandRows: props.expandRows, nowDate: nowDate, nowIndicatorSegs: nowIndicator && this.slicer.sliceNowDate(nowDate, dateProfile, nextDayThreshold, context, dayRanges), todayRange: todayRange, onScrollTopRequest: props.onScrollTopRequest, onSlatCoords: props.onSlatCoords })))));\n    }\n}\nfunction buildDayRanges(dayTableModel, dateProfile, dateEnv) {\n    let ranges = [];\n    for (let date of dayTableModel.headerDates) {\n        ranges.push({\n            start: dateEnv.add(date, dateProfile.slotMinTime),\n            end: dateEnv.add(date, dateProfile.slotMaxTime),\n        });\n    }\n    return ranges;\n}\n\n// potential nice values for the slot-duration and interval-duration\n// from largest to smallest\nconst STOCK_SUB_DURATIONS = [\n    { hours: 1 },\n    { minutes: 30 },\n    { minutes: 15 },\n    { seconds: 30 },\n    { seconds: 15 },\n];\nfunction buildSlatMetas(slotMinTime, slotMaxTime, explicitLabelInterval, slotDuration, dateEnv) {\n    let dayStart = new Date(0);\n    let slatTime = slotMinTime;\n    let slatIterator = createDuration(0);\n    let labelInterval = explicitLabelInterval || computeLabelInterval(slotDuration);\n    let metas = [];\n    while (asRoughMs(slatTime) < asRoughMs(slotMaxTime)) {\n        let date = dateEnv.add(dayStart, slatTime);\n        let isLabeled = wholeDivideDurations(slatIterator, labelInterval) !== null;\n        metas.push({\n            date,\n            time: slatTime,\n            key: date.toISOString(),\n            isoTimeStr: formatIsoTimeString(date),\n            isLabeled,\n        });\n        slatTime = addDurations(slatTime, slotDuration);\n        slatIterator = addDurations(slatIterator, slotDuration);\n    }\n    return metas;\n}\n// Computes an automatic value for slotLabelInterval\nfunction computeLabelInterval(slotDuration) {\n    let i;\n    let labelInterval;\n    let slotsPerLabel;\n    // find the smallest stock label interval that results in more than one slots-per-label\n    for (i = STOCK_SUB_DURATIONS.length - 1; i >= 0; i -= 1) {\n        labelInterval = createDuration(STOCK_SUB_DURATIONS[i]);\n        slotsPerLabel = wholeDivideDurations(labelInterval, slotDuration);\n        if (slotsPerLabel !== null && slotsPerLabel > 1) {\n            return labelInterval;\n        }\n    }\n    return slotDuration; // fall back\n}\n\nclass DayTimeColsView extends TimeColsView {\n    constructor() {\n        super(...arguments);\n        this.buildTimeColsModel = memoize(buildTimeColsModel);\n        this.buildSlatMetas = memoize(buildSlatMetas);\n    }\n    render() {\n        let { options, dateEnv, dateProfileGenerator } = this.context;\n        let { props } = this;\n        let { dateProfile } = props;\n        let dayTableModel = this.buildTimeColsModel(dateProfile, dateProfileGenerator);\n        let splitProps = this.allDaySplitter.splitProps(props);\n        let slatMetas = this.buildSlatMetas(dateProfile.slotMinTime, dateProfile.slotMaxTime, options.slotLabelInterval, options.slotDuration, dateEnv);\n        let { dayMinWidth } = options;\n        let hasAttachedAxis = !dayMinWidth;\n        let hasDetachedAxis = dayMinWidth;\n        let headerContent = options.dayHeaders && (createElement(DayHeader, { dates: dayTableModel.headerDates, dateProfile: dateProfile, datesRepDistinctDays: true, renderIntro: hasAttachedAxis ? this.renderHeadAxis : null }));\n        let allDayContent = (options.allDaySlot !== false) && ((contentArg) => (createElement(DayTable, Object.assign({}, splitProps.allDay, { dateProfile: dateProfile, dayTableModel: dayTableModel, nextDayThreshold: options.nextDayThreshold, tableMinWidth: contentArg.tableMinWidth, colGroupNode: contentArg.tableColGroupNode, renderRowIntro: hasAttachedAxis ? this.renderTableRowAxis : null, showWeekNumbers: false, expandRows: false, headerAlignElRef: this.headerElRef, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, forPrint: props.forPrint }, this.getAllDayMaxEventProps()))));\n        let timeGridContent = (contentArg) => (createElement(DayTimeCols, Object.assign({}, splitProps.timed, { dayTableModel: dayTableModel, dateProfile: dateProfile, axis: hasAttachedAxis, slotDuration: options.slotDuration, slatMetas: slatMetas, forPrint: props.forPrint, tableColGroupNode: contentArg.tableColGroupNode, tableMinWidth: contentArg.tableMinWidth, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, onSlatCoords: this.handleSlatCoords, expandRows: contentArg.expandRows, onScrollTopRequest: this.handleScrollTopRequest })));\n        return hasDetachedAxis\n            ? this.renderHScrollLayout(headerContent, allDayContent, timeGridContent, dayTableModel.colCnt, dayMinWidth, slatMetas, this.state.slatCoords)\n            : this.renderSimpleLayout(headerContent, allDayContent, timeGridContent);\n    }\n}\nfunction buildTimeColsModel(dateProfile, dateProfileGenerator) {\n    let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n    return new DayTableModel(daySeries, false);\n}\n\nvar css_248z = \".fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:\\\"\\\\00a0\\\"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:\\\"\\\\00a0-\\\\00a0\\\"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-arrow,.fc .fc-timegrid-now-indicator-line{pointer-events:none}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}\";\ninjectStyles(css_248z);\n\nexport { DayTimeCols, DayTimeColsSlicer, DayTimeColsView, TimeCols, TimeColsSlatsCoords, TimeColsView, buildDayRanges, buildSlatMetas, buildTimeColsModel };\n", "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayTimeColsView } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nimport '@fullcalendar/daygrid/internal.js';\n\nconst OPTION_REFINERS = {\n    allDaySlot: Boolean,\n};\n\nvar index = createPlugin({\n    name: '@fullcalendar/timegrid',\n    initialView: 'timeGridWeek',\n    optionRefiners: OPTION_REFINERS,\n    views: {\n        timeGrid: {\n            component: DayTimeColsView,\n            usesMinMaxTime: true,\n            allDaySlot: true,\n            slotDuration: '00:30:00',\n            slotEventOverlap: true, // a bad name. confused with overlap/constraint system\n        },\n        timeGridDay: {\n            type: 'timeGrid',\n            duration: { days: 1 },\n        },\n        timeGridWeek: {\n            type: 'timeGrid',\n            duration: { weeks: 1 },\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,iBAAN,cAA6B,SAAS;AAAA,EAClC,aAAa;AACT,WAAO;AAAA,MACH,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,mBAAmB,UAAU;AACzB,QAAI,SAAS,QAAQ;AACjB,aAAO,CAAC,QAAQ;AAAA,IACpB;AACA,WAAO,CAAC,OAAO;AAAA,EACnB;AAAA,EACA,mBAAmB,UAAU;AACzB,QAAI,CAAC,SAAS,QAAQ;AAClB,aAAO,CAAC,OAAO;AAAA,IACnB;AACA,QAAI,eAAe,QAAQ,GAAG;AAC1B,aAAO,CAAC,SAAS,QAAQ;AAAA,IAC7B;AACA,WAAO,CAAC,QAAQ;AAAA,EACpB;AACJ;AAEA,IAAM,4BAA4B,gBAAgB;AAAA,EAC9C,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU;AACd,CAAC;AACD,SAAS,iBAAiB,OAAO;AAC7B,MAAI,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,MAAM,YAAY,yBAAyB;AAAA,EAC/C;AACA,SAAQ,EAAc,gBAAgB,UAAU,MAAM,CAAC,YAAY;AAC/D,QAAI,CAAC,MAAM,WAAW;AAClB,aAAQ,EAAc,MAAM,EAAE,WAAW,WAAW,KAAK,GAAG,GAAG,aAAa,MAAM,WAAW,CAAC;AAAA,IAClG;AACA,QAAI,EAAE,SAAS,SAAS,QAAQ,IAAI;AACpC,QAAI;AAAA;AAAA,MACH,QAAQ,mBAAmB,OAAO,4BAC/B,MAAM,QAAQ,QAAQ,eAAe,IAAI,gBAAgB,QAAQ,gBAAgB,CAAC,CAAC,IAC/E,gBAAgB,QAAQ,eAAe;AAAA;AAC/C,QAAI,cAAc;AAAA,MACd,OAAO;AAAA,MACP,MAAM,MAAM;AAAA,MACZ,MAAM,QAAQ,OAAO,MAAM,IAAI;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,QAAQ,OAAO,MAAM,MAAM,WAAW;AAAA,IAChD;AACA,WAAQ,EAAc,kBAAkB,EAAE,OAAO,MAAM,WAAW,YAAY,SAAS;AAAA,MAC/E,aAAa,MAAM;AAAA,IACvB,GAAG,aAA0B,eAAe,oBAAoB,iBAAiB,QAAQ,kBAAkB,kBAAkB,oBAAoB,oBAAoB,QAAQ,qBAAqB,UAAU,QAAQ,mBAAmB,aAAa,QAAQ,qBAAqB,GAAG,CAAC,iBAAkB;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,0DAA0D;AAAA,MACnY,EAAc,cAAc,EAAE,OAAO,OAAO,WAAW;AAAA,QAC/C;AAAA,QACA;AAAA,MACJ,EAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAClB,CAAC;AACL;AACA,SAAS,mBAAmB,OAAO;AAC/B,SAAO,MAAM;AACjB;AAEA,IAAM,eAAN,cAA2B,cAAc;AAAA,EACrC,SAAS;AACL,WAAO,KAAK,MAAM,UAAU,IAAI,CAAC,aAAc;AAAA,MAAc;AAAA,MAAM,EAAE,KAAK,SAAS,IAAI;AAAA,MACnF,EAAc,kBAAkB,OAAO,OAAO,CAAC,GAAG,QAAQ,CAAC;AAAA,IAAC,CAAE;AAAA,EACtE;AACJ;AAEA,IAAM,0BAA0B,gBAAgB,EAAE,MAAM,QAAQ,CAAC;AACjE,IAAM,8BAA8B;AACpC,IAAM,eAAN,cAA2B,cAAc;AAAA,EACrC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,iBAAiB,IAAI,eAAe;AACzC,SAAK,cAAc,EAAU;AAC7B,SAAK,YAAY,EAAU;AAC3B,SAAK,gBAAgB,EAAU;AAC/B,SAAK,QAAQ;AAAA,MACT,YAAY;AAAA,IAChB;AACA,SAAK,yBAAyB,CAAC,cAAc;AACzC,UAAI,aAAa,KAAK,cAAc;AACpC,UAAI,YAAY;AACZ,mBAAW,YAAY;AAAA,MAC3B;AAAA,IACJ;AAGA,SAAK,iBAAiB,CAAC,QAAQ,cAAc,OAAO;AAChD,UAAI,EAAE,QAAQ,IAAI,KAAK;AACvB,UAAI,EAAE,YAAY,IAAI,KAAK;AAC3B,UAAI,QAAQ,YAAY;AACxB,UAAI,SAAS,SAAS,MAAM,OAAO,MAAM,GAAG;AAE5C,UAAI,eAAgB,WAAW,IACzB,kBAAkB,KAAK,SAAS,MAAM,OAAO,MAAM,IACnD,CAAC;AACP,UAAI,QAAQ,eAAe,WAAW,OAAO;AACzC,eAAQ,EAAc,qBAAqB,EAAE,OAAO,MAAM,WAAW;AAAA,UAC7D;AAAA,UACA;AAAA,QACJ,GAAG,SAAS;AAAA,UACR,eAAe;AAAA,QACnB,GAAG,MAAM,MAAM,OAAO,eAAe,wBAAwB,GAAG,CAAC,iBAAkB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW;AAAA,YACjH;AAAA,YACA;AAAA,YACA;AAAA,UACJ,EAAE,KAAK,GAAG,GAAG,OAAO,EAAE,QAAQ,YAAY,EAAE;AAAA,UAC5C,EAAc,cAAc,EAAE,OAAO,KAAK,WAAW;AAAA,YAC7C;AAAA,YACA;AAAA,YACA;AAAA,UACJ,GAAG,SAAS,aAAa,CAAC;AAAA,QAAC,CAAE;AAAA,MACzC;AACA,aAAQ;AAAA,QAAc;AAAA,QAAM,EAAE,eAAe,MAAM,WAAW,mBAAmB;AAAA,QAC7E,EAAc,OAAO,EAAE,WAAW,0BAA0B,OAAO,EAAE,QAAQ,YAAY,EAAE,CAAC;AAAA,MAAC;AAAA,IACrG;AAKA,SAAK,qBAAqB,CAAC,cAAc;AACrC,UAAI,EAAE,SAAS,QAAQ,IAAI,KAAK;AAChC,UAAI,cAAc;AAAA,QACd,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,MACV;AACA;AAAA;AAAA,QAEA,EAAc,kBAAkB,EAAE,OAAO,MAAM,WAAW;AAAA,UAClD;AAAA,UACA;AAAA,QACJ,GAAG,SAAS;AAAA,UACR,eAAe;AAAA,QACnB,GAAG,aAA0B,eAAe,iBAAiB,iBAAiB,QAAQ,eAAe,kBAAkB,mBAAmB,oBAAoB,QAAQ,kBAAkB,UAAU,QAAQ,gBAAgB,aAAa,QAAQ,kBAAkB,GAAG,CAAC,iBAAkB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW;AAAA,YACrT;AAAA,YACA;AAAA,YACA,aAAa,OAAO,mCAAmC;AAAA,UAC3D,EAAE,KAAK,GAAG,GAAG,OAAO,EAAE,QAAQ,UAAU,EAAE;AAAA,UAC1C,EAAc,cAAc,EAAE,OAAO,QAAQ,WAAW;AAAA,YAChD;AAAA,YACA;AAAA,YACA;AAAA,UACJ,EAAE,CAAC;AAAA,QAAC,CAAE;AAAA;AAAA,IAClB;AACA,SAAK,mBAAmB,CAAC,eAAe;AACpC,WAAK,SAAS,EAAE,WAAW,CAAC;AAAA,IAChC;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,mBAAmB,kBAAkB,eAAe,aAAa;AAC7D,QAAI,EAAE,SAAS,MAAM,IAAI;AACzB,QAAI,WAAW,CAAC;AAChB,QAAI,oBAAoB,qBAAqB,QAAQ,OAAO;AAC5D,QAAI,kBAAkB;AAClB,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO;AAAA,UACH,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,UAChB,YAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,eAAe;AACf,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO,EAAE,SAAS,cAAc;AAAA,MACpC,CAAC;AACD,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL;AAAA;AAAA,UACA;AAAA,YAAc;AAAA,YAAM,EAAE,MAAM,gBAAgB,WAAW,wBAAwB;AAAA,YAC3E,EAAc,MAAM,EAAE,WAAW,yBAAyB,QAAQ,MAAM,SAAS,iBAAiB,EAAE,CAAC;AAAA,UAAC;AAAA;AAAA,MAC9G,CAAC;AAAA,IACL;AACA,aAAS,KAAK;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,YAAY,QAAQ,QAAQ,QAAQ,UAAU;AAAA,MAC9C,OAAO;AAAA,QACH,eAAe,KAAK;AAAA,QACpB,SAAS;AAAA,MACb;AAAA,IACJ,CAAC;AACD,WAAQ;AAAA,MAAc;AAAA,MAAe,EAAE,OAAO,KAAK,WAAW,WAAW,CAAC,aAAa,GAAG,UAAU,QAAQ,SAAS;AAAA,MACjH,EAAc,kBAAkB,EAAE,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM,UAAU,kBAAkB,MAAM,UAAU,MAAM,CAAC,EAAE,OAAO,SAAS,CAAC,GAAG,SAAmB,CAAC;AAAA,IAAC;AAAA,EAC9K;AAAA,EACA,oBAAoB,kBAAkB,eAAe,aAAa,QAAQ,aAAa,WAAW,YAAY;AAC1G,QAAI,aAAa,KAAK,QAAQ,YAAY;AAC1C,QAAI,CAAC,YAAY;AACb,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAClD;AACA,QAAI,EAAE,SAAS,MAAM,IAAI;AACzB,QAAI,oBAAoB,CAAC,MAAM,YAAY,qBAAqB,QAAQ,OAAO;AAC/E,QAAI,wBAAwB,CAAC,MAAM,YAAY,yBAAyB,QAAQ,OAAO;AACvF,QAAI,WAAW,CAAC;AAChB,QAAI,kBAAkB;AAClB,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,QAAQ;AAAA,UACJ;AAAA,YACI,KAAK;AAAA,YACL,YAAY,CAAC,QAAS,EAAc,MAAM,EAAE,MAAM,eAAe,GAAG,KAAK,eAAe,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC;AAAA,UACzH;AAAA,UACA;AAAA,YACI,KAAK;AAAA,YACL,OAAO,KAAK;AAAA,YACZ,gBAAgB;AAAA,YAChB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,eAAe;AACf,eAAS,KAAK;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,QAAQ;AAAA,UACJ;AAAA,YACI,KAAK;AAAA,YACL,YAAY,CAAC,eAAgB,EAAc,MAAM,EAAE,MAAM,eAAe,GAAG,KAAK,mBAAmB,WAAW,eAAe,CAAC,CAAC,CAAC;AAAA,UACpI;AAAA,UACA;AAAA,YACI,KAAK;AAAA,YACL,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,eAAS,KAAK;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN;AAAA;AAAA,UACA;AAAA,YAAc;AAAA,YAAM,EAAE,MAAM,gBAAgB,WAAW,wBAAwB;AAAA,YAC3E,EAAc,MAAM,EAAE,SAAS,GAAG,WAAW,yBAAyB,QAAQ,MAAM,SAAS,iBAAiB,EAAE,CAAC;AAAA,UAAC;AAAA;AAAA,MAC1H,CAAC;AAAA,IACL;AACA,QAAI,iBAAiB,QAAQ,QAAQ;AACrC,aAAS,KAAK;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,YAAY,QAAQ,QAAQ,QAAQ,UAAU;AAAA,MAC9C,QAAQ;AAAA,QACJ;AAAA,UACI,KAAK;AAAA,UACL,SAAS,CAAC;AAAA;AAAA,YAEV;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,yBAAyB;AAAA,cACvD;AAAA,gBAAc;AAAA,gBAAS,EAAE,eAAe,MAAM,OAAO,EAAE,QAAQ,IAAI,aAAa,IAAI,eAAe,GAAG,EAAE;AAAA,gBACpG,IAAI;AAAA,gBACJ;AAAA,kBAAc;AAAA,kBAAS;AAAA,kBACnB,EAAc,cAAc,EAAE,UAAqB,CAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,cAC9D;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,sCAAsC;AAAA,gBACpE,EAAc,UAAU;AAAA,kBAAE,MAAM,iBAAiB,WAAW;AAAA;AAAA,gBAAkB,GAAG,CAAC,YAAY;AAC1F,sBAAI,kBAAkB,kBAClB,cACA,WAAW,eAAe,OAAO;AACrC,sBAAI,OAAO,oBAAoB,UAAU;AACrC,2BAAQ,EAAc,uBAAuB,EAAE,WAAW,CAAC,iCAAiC,GAAG,SAAS,EAAE,KAAK,gBAAgB,GAAG,QAAQ,MAAM,MAAM,QAAQ,CAAC;AAAA,kBACnK;AACA,yBAAO;AAAA,gBACX,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA;AAAA,QACf;AAAA,QACA;AAAA,UACI,KAAK;AAAA,UACL,eAAe,KAAK;AAAA,UACpB,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,uBAAuB;AACvB,eAAS,KAAK;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,UACJ;AAAA,YACI,KAAK;AAAA,YACL,SAAS;AAAA,UACb;AAAA,UACA;AAAA,YACI,KAAK;AAAA,YACL,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAQ;AAAA,MAAc;AAAA,MAAe,EAAE,OAAO,KAAK,WAAW,WAAW,CAAC,aAAa,GAAG,UAAU,QAAQ,SAAS;AAAA,MACjH,EAAc,YAAY,EAAE,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM,UAAU,UAAU,MAAM,UAAU,kBAAkB,OAAO,WAAW;AAAA,QAClI,EAAE,OAAO,UAAU,MAAM,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;AAAA,QAC/C,EAAE,MAAM,CAAC,EAAE,MAAM,QAAQ,UAAU,YAAY,CAAC,EAAE;AAAA,MACtD,GAAG,SAAmB,CAAC;AAAA,IAAC;AAAA,EACpC;AAAA;AAAA;AAAA,EAGA,yBAAyB;AACrB,QAAI,EAAE,cAAc,gBAAgB,IAAI,KAAK,QAAQ;AACrD,QAAI,iBAAiB,QAAQ,oBAAoB,MAAM;AACnD,qBAAe;AACf,wBAAkB;AAAA,IACtB;AACA,WAAO,EAAE,cAAc,gBAAgB;AAAA,EAC3C;AACJ;AACA,SAAS,kBAAkB,aAAa;AACpC,SAAO,YAAY;AACvB;AAEA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,WAAW,aAAa,cAAc;AAC9C,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,eAAe,MAAM;AACjB,QAAI,EAAE,YAAY,IAAI;AACtB,QAAI,oBAAoB,YAAY,cAAc,IAAI,GAAG;AACrD,UAAI,iBAAiB,WAAW,IAAI;AACpC,UAAI,SAAS,KAAK,QAAQ,IAAI,eAAe,QAAQ;AACrD,UAAI,UAAU,UAAU,YAAY,WAAW,KAC3C,SAAS,UAAU,YAAY,WAAW,GAAG;AAC7C,eAAO,KAAK,eAAe,eAAe,MAAM,CAAC;AAAA,MACrD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,eAAe,MAAM,gBAAgB;AACjC,QAAI,CAAC,gBAAgB;AACjB,uBAAiB,WAAW,IAAI;AAAA,IACpC;AACA,WAAO,KAAK,eAAe,eAAe,KAAK,QAAQ,IAAI,eAAe,QAAQ,CAAC,CAAC;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,UAAU;AACrB,QAAI,EAAE,WAAW,YAAY,IAAI;AACjC,QAAI,MAAM,UAAU,IAAI;AAExB,QAAI,gBAAgB,SAAS,eAAe,UAAU,YAAY,WAAW,KAAK,UAAU,KAAK,YAAY;AAC7G,QAAI;AACJ,QAAI;AAIJ,mBAAe,KAAK,IAAI,GAAG,YAAY;AACvC,mBAAe,KAAK,IAAI,KAAK,YAAY;AAGzC,gBAAY,KAAK,MAAM,YAAY;AACnC,gBAAY,KAAK,IAAI,WAAW,MAAM,CAAC;AAGvC,oBAAgB,eAAe;AAC/B,WAAO,UAAU,KAAK,SAAS,IAC3B,UAAU,UAAU,SAAS,IAAI;AAAA,EACzC;AACJ;AAEA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC1C,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,EAAE,WAAW,IAAI;AACrB,WAAQ,EAAc,SAAS,MAAM,MAAM,UAAU,IAAI,CAAC,UAAU,MAAM;AACtE,UAAI,cAAc;AAAA,QACd,MAAM,SAAS;AAAA,QACf,MAAM,QAAQ,QAAQ,OAAO,SAAS,IAAI;AAAA,QAC1C,MAAM,QAAQ;AAAA,MAClB;AACA,aAAQ;AAAA,QAAc;AAAA,QAAM,EAAE,KAAK,SAAS,KAAK,KAAK,WAAW,UAAU,SAAS,GAAG,EAAE;AAAA,QACrF,MAAM,QAAS,EAAc,kBAAkB,OAAO,OAAO,CAAC,GAAG,QAAQ,CAAC;AAAA,QAC1E,EAAc,kBAAkB,EAAE,OAAO,MAAM,WAAW;AAAA,UAClD;AAAA,UACA;AAAA,UACA,CAAC,SAAS,aAAa;AAAA,QAC3B,GAAG,SAAS;AAAA,UACR,aAAa,SAAS;AAAA,QAC1B,GAAG,aAA0B,eAAe,mBAAmB,iBAAiB,QAAQ,iBAAiB,oBAAoB,QAAQ,oBAAoB,UAAU,QAAQ,kBAAkB,aAAa,QAAQ,oBAAoB,CAAC;AAAA,MAAC;AAAA,IACpP,CAAC,CAAC;AAAA,EACN;AACJ;AAKA,IAAM,gBAAN,cAA4B,cAAc;AAAA,EACtC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,YAAY,EAAU;AAC3B,SAAK,aAAa,IAAI,OAAO;AAAA,EACjC;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,WAAQ;AAAA,MAAc;AAAA,MAAO,EAAE,KAAK,KAAK,WAAW,WAAW,oBAAoB;AAAA,MAC/E;AAAA,QAAc;AAAA,QAAS,EAAE,eAAe,MAAM,WAAW,QAAQ,MAAM,SAAS,OAAO,GAAG,OAAO;AAAA,UACzF,UAAU,MAAM;AAAA,UAChB,OAAO,MAAM;AAAA,UACb,QAAQ,MAAM;AAAA,QAClB,EAAE;AAAA,QACF,MAAM;AAAA,QACN,EAAc,mBAAmB,EAAE,YAAY,KAAK,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,UAAU,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAC5H;AAAA,EACA,oBAAoB;AAChB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,qBAAqB;AACjB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,MAAM,UAAU;AACrB,WAAK,MAAM,SAAS,IAAI;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,eAAe;AACX,QAAI,EAAE,SAAS,MAAM,IAAI;AACzB,QAAI,MAAM,YACN,MAAM,gBAAgB,MACxB;AACE,UAAI,SAAS,KAAK,UAAU;AAC5B,UAAI,OAAO,cAAc;AACrB,cAAM,SAAS,IAAI,oBAAoB,IAAI,cAAc,KAAK,UAAU,SAAS,eAAe,KAAK,WAAW,YAAY,MAAM,SAAS,GAAG,OAAO,IAAI,GAAG,KAAK,MAAM,aAAa,QAAQ,QAAQ,YAAY,CAAC;AAAA,MACrN;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,OAAO,WAAW;AACtC,SAAO,UAAU,IAAI,CAAC,aAAa,MAAM,SAAS,GAAG,CAAC;AAC1D;AAEA,SAAS,eAAe,MAAM,QAAQ;AAClC,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,cAAU,KAAK,CAAC,CAAC;AAAA,EACrB;AACA,MAAI,MAAM;AACN,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACjC,gBAAU,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,IACvC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,IAAI,QAAQ;AACvC,MAAI,QAAQ,CAAC;AACb,MAAI,CAAC,IAAI;AACL,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,YAAM,CAAC,IAAI;AAAA,IACf;AAAA,EACJ,OACK;AACD,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,YAAM,CAAC,IAAI;AAAA,QACP,mBAAmB,GAAG;AAAA,QACtB,SAAS,GAAG;AAAA,QACZ,MAAM,CAAC;AAAA,MACX;AAAA,IACJ;AACA,aAAS,OAAO,GAAG,MAAM;AACrB,YAAM,IAAI,GAAG,EAAE,KAAK,KAAK,GAAG;AAAA,IAChC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EACxC,SAAS;AACL,QAAI,EAAE,MAAM,IAAI;AAChB,WAAQ,EAAc,mBAAmB,EAAE,WAAW,CAAC,uBAAuB,GAAG,SAAS;AAAA,MAClF,KAAK,MAAM;AAAA,MACX,QAAQ,MAAM;AAAA,IAClB,GAAG,YAAY,MAAM,SAAS,MAAM,WAAW,QAAQ,SAAS,MAAM,YAAY,YAAY,MAAM,YAAY,eAAe,MAAM,eAAe,aAAa,MAAM,aAAa,YAAY,MAAM,YAAY,gBAAgB,MAAM,kBAAkB,MAAM,YAAY,KAAK,GAAG,kBAAkB,qBAAqB,YAAY,KAAK,GAAG,CAAC,iBAAkB,EAAc,cAAc,EAAE,OAAO,OAAO,WAAW,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAE;AAAA,EACjd;AACJ;AACA,SAAS,oBAAoB,OAAO;AAChC,SAAO,MAAM;AACjB;AAGA,SAAS,iBAAiB,WAAW,aAAa,aAAa;AAC3D,MAAI,YAAY,IAAI,aAAa;AACjC,MAAI,eAAe,MAAM;AACrB,cAAU,cAAc;AAAA,EAC5B;AACA,MAAI,eAAe,MAAM;AACrB,cAAU,cAAc;AAAA,EAC5B;AACA,MAAI,gBAAgB,UAAU,QAAQ,SAAS;AAC/C,MAAI,eAAe,yBAAyB,aAAa;AACzD,MAAI,MAAM,SAAS,SAAS;AAC5B,QAAM,WAAW,KAAK,CAAC;AACvB,MAAI,WAAW,WAAW,GAAG;AAC7B,SAAO,EAAE,UAAU,aAAa;AACpC;AACA,SAAS,SAAS,WAAW;AACzB,QAAM,EAAE,eAAe,IAAI;AAC3B,QAAM,YAAY,UAAU,CAAC,OAAO,YAAY,QAAQ,MAAM,SAAS,CAAC,OAAO,YAAY;AACvF,QAAI,eAAe,kBAAkB,WAAW,OAAO,OAAO;AAC9D,QAAI,eAAe,WAAW,cAAc,SAAS;AACrD,QAAI,QAAQ,eAAe,KAAK,EAAE,OAAO;AACzC,WAAO;AAAA,MACH,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,gBAAgB,aAAa,CAAC,EAAE,CAAC;AAAA,MAC3E,MAAM,YAAY,aAAa,CAAC;AAAA;AAAA,IACpC;AAAA,EACJ,CAAC;AACD,SAAO,WAAW,eAAe,SAC3B,EAAE,OAAO,GAAG,cAAc,GAAG,YAAY,eAAe,CAAC,EAAE,OAAO,IAClE,MAAM,SAAS,EAAE,CAAC;AAC5B;AACA,SAAS,WAAW,cAAc,WAAW;AACzC,MAAI,CAAC,cAAc;AACf,WAAO,CAAC,CAAC,GAAG,CAAC;AAAA,EACjB;AACA,MAAI,EAAE,OAAO,cAAc,WAAW,IAAI;AAC1C,MAAI,UAAU;AACd,MAAI,QAAQ,CAAC;AACb,SAAO,UAAU,YAAY;AACzB,UAAM,KAAK,UAAU,OAAO,OAAO,CAAC;AACpC,eAAW;AAAA,EACf;AACA,QAAM,KAAK,gBAAgB;AAC3B,SAAO;AAAA,IACH,MAAM,IAAI,WAAW;AAAA,IACrB,MAAM,CAAC,EAAE,CAAC;AAAA;AAAA,EACd;AACJ;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACrB;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;AACA,SAAS,kBAAkB,WAAW,cAAc,gBAAgB;AAChE,MAAI,EAAE,aAAa,eAAe,IAAI;AACtC,MAAI,eAAe,eAAe,YAAY,EAAE,cAAc;AAC9D,MAAI,eAAe,YAAY,YAAY,IAAI,aAAa;AAC5D,MAAI,WAAW,YAAY;AAC3B,MAAI,QAAQ;AAEZ,SAAO,QAAQ,YAAY,YAAY,KAAK,IAAI,cAAc,SAAS;AACnE;AACJ,SAAO,QAAQ,UAAU,SAAS,GAAG;AACjC,QAAI,UAAU,eAAe,KAAK;AAClC,QAAI;AACJ,QAAI,cAAc,aAAa,SAAS,aAAa,KAAK,OAAO,eAAe;AAChF,QAAI,eAAe,YAAY,CAAC,IAAI,YAAY,CAAC;AACjD,QAAI,aAAa;AACjB;AAAA;AAAA,OACC,QAAQ,QAAQ,UAAU;AAAA,MACvB,MAAM,KAAK,QAAQ,aAAa,KAAK;AAAA,MAAK;AAC1C,oBAAc;AAAA,IAClB;AACA,QAAI,eAAe,YAAY;AAC3B,aAAO,EAAE,OAAO,cAAc,WAAW;AAAA,IAC7C;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,eAAe,gBAAgB;AAC/C,QAAM,cAAc,UAAU,CAAC,MAAM,YAAY,kBAAkB,cAAc,IAAI,GAAG,CAAC,MAAM,YAAY,kBAAkB;AACzH,QAAI,EAAE,gBAAgB,UAAU,IAAI;AACpC,QAAI,eAAe,YAAY;AAC/B,QAAI,oBAAoB,YAAY;AACpC,QAAI;AACJ,QAAI,cAAc,CAAC;AACnB,QAAI,CAAC,eAAe,QAAQ;AACxB,iBAAW;AAAA,IACf,OACK;AACD,eAAS,aAAa,gBAAgB;AAClC,YAAI,aAAa,QAAW;AACxB,cAAI,MAAM,YAAY,WAAW,YAAY,YAAY;AACzD,qBAAW,IAAI,CAAC;AAChB,sBAAY,KAAK,IAAI,CAAC,CAAC;AAAA,QAC3B,OACK;AACD,cAAI,MAAM,YAAY,WAAW,UAAU,CAAC;AAC5C,sBAAY,KAAK,IAAI,CAAC,CAAC;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,gBAAgB,WAAW,cAAc;AAC7C,WAAO,CAAC,WAAW,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,WAAW,cAAc,gBAAgB,YAAY,CAAC,CAAC;AAAA,EACrI,CAAC;AACD,SAAO,cAAc,IAAI,CAAC,SAAS,YAAY,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;AACjE;AAEA,SAAS,WAAW,eAAe;AAC/B,MAAI,QAAQ,CAAC;AACb,QAAM,cAAc,UAAU,CAAC,MAAM,YAAY,eAAe,cAAc,IAAI,GAAG,CAAC,MAAM,YAAY,eAAe;AACnH,QAAI,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,MAAE;AAAA,MAChD;AAAA,MAAY,cAAc;AAAA,IAAE,CAAC;AACjC,UAAM,KAAK,IAAI;AACf,WAAQ,KAAK,eAAe,aAAa,KAAK,gBAAgB,aAAa,KAAK,WAAW,aAAa,CAAC,IAAI;AAAA,EACjH,CAAC;AACD,WAAS,aAAa,OAAO,YAAY,YAAY;AACjD,QAAI,eAAe;AACnB,aAAS,QAAQ,OAAO;AACpB,qBAAe,KAAK,IAAI,YAAY,MAAM,YAAY,UAAU,GAAG,YAAY;AAAA,IACnF;AACA,WAAO;AAAA,EACX;AACA,eAAa,eAAe,GAAG,CAAC;AAChC,SAAO;AACX;AAEA,SAAS,UAAU,SAAS,UAAU;AAClC,QAAM,QAAQ,CAAC;AACf,SAAO,IAAI,SAAS;AAChB,QAAI,MAAM,QAAQ,GAAG,IAAI;AACzB,WAAQ,OAAO,QACT,MAAM,GAAG,IACR,MAAM,GAAG,IAAI,SAAS,GAAG,IAAI;AAAA,EACxC;AACJ;AAEA,SAAS,kBAAkB,MAAM,SAAS,aAAa,MAAM,iBAAiB,GAAG;AAC7E,MAAI,UAAU,CAAC;AACf,MAAI,YAAY;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,UAAI,MAAM,KAAK,CAAC;AAChB,UAAI,YAAY,WAAW,eAAe,IAAI,OAAO,OAAO;AAC5D,UAAI,UAAU,KAAK;AAAA,QAAI,aAAa,kBAAkB;AAAA;AAAA,QACtD,WAAW,eAAe,IAAI,KAAK,OAAO;AAAA,MAAC;AAC3C,cAAQ,KAAK;AAAA,QACT,OAAO,KAAK,MAAM,SAAS;AAAA,QAC3B,KAAK,KAAK,MAAM,OAAO;AAAA;AAAA,MAC3B,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,MAAM,YACtC,kBAAkB,eAAe;AAC7B,MAAI,YAAY,CAAC;AACjB,MAAI,WAAW,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,QAAI,UAAU,WAAW,CAAC;AAC1B,QAAI,SAAS;AACT,gBAAU,KAAK;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,MAAM;AAAA,MACV,CAAC;AAAA,IACL,OACK;AACD,eAAS,KAAK,KAAK,CAAC,CAAC;AAAA,IACzB;AAAA,EACJ;AACA,MAAI,EAAE,UAAU,aAAa,IAAI,iBAAiB,WAAW,kBAAkB,aAAa;AAC5F,MAAI,gBAAgB,CAAC;AACrB,WAAS,WAAW,UAAU;AAC1B,kBAAc,KAAK;AAAA,MACf,KAAK,KAAK,QAAQ,KAAK;AAAA,MACvB,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AACA,WAAS,WAAW,UAAU;AAC1B,kBAAc,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,EACnD;AACA,SAAO,EAAE,eAAe,aAAa;AACzC;AAEA,IAAM,sBAAsB,gBAAgB;AAAA,EACxC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACd,CAAC;AACD,IAAM,eAAN,cAA2B,cAAc;AAAA,EACrC,SAAS;AACL,WAAQ,EAAc,eAAe,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,EAAE,WAAW;AAAA,MACxE;AAAA,MACA;AAAA,MACA,KAAK,MAAM,WAAW;AAAA,IAC1B,GAAG,mBAAmB,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACJ;AAEA,IAAM,UAAN,cAAsB,cAAc;AAAA,EAChC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,gBAAgB,QAAQ,aAAa;AAAA,EAC9C;AAAA;AAAA,EAEA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,QAAQ,IAAI;AAClB,QAAI,iBAAiB,QAAQ;AAC7B,QAAI;AAAA;AAAA,MACF,MAAM,aAAa,MAAM,UAAU,QAChC,MAAM,eAAe,MAAM,YAAY,QACvC,kBAAkB,MAAM,qBACzB,CAAC;AAAA;AACL,QAAI;AAAA;AAAA,MACF,MAAM,aAAa,MAAM,UAAU,qBAChC,MAAM,eAAe,MAAM,YAAY,qBACxC,CAAC;AAAA;AACL,QAAI,eAAe,KAAK,cAAc,MAAM,aAAa,QAAQ,UAAU;AAC3E,WAAQ,EAAc,kBAAkB,EAAE,OAAO,MAAM,OAAO,MAAM,OAAO,WAAW;AAAA,MAC9E;AAAA,MACA,GAAI,MAAM,mBAAmB,CAAC;AAAA,IAClC,GAAG,SAAS,OAAO,OAAO,EAAE,MAAM,WAAW,GAAG,MAAM,cAAc,GAAG,MAAM,MAAM,MAAM,aAAa,MAAM,aAAa,YAAY,MAAM,YAAY,kBAAkB,MAAM,iBAAiB,GAAG,CAAC,iBAAkB;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,wBAAwB;AAAA,MAChR;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,qBAAqB;AAAA,QACnD,KAAK,eAAe,MAAM,kBAAkB,cAAc;AAAA,QAC1D,KAAK,eAAe,MAAM,aAAa,UAAU;AAAA,QACjD,KAAK,eAAe,MAAM,mBAAmB,WAAW;AAAA,MAAC;AAAA,MAC7D,EAAc,OAAO,EAAE,WAAW,yBAAyB,GAAG,KAAK,aAAa,cAAc,8BAA8B,OAAO,OAAO,KAAK,CAAC;AAAA,MAChJ,EAAc,OAAO,EAAE,WAAW,yBAAyB,GAAG,KAAK,aAAa,YAAY,CAAC,GAAG,QAAQ,MAAM,SAAS,GAAG,QAAQ,MAAM,WAAW,GAAG,QAAQ,cAAc,GAAG,QAAQ,CAAC;AAAA,MACxL,EAAc,OAAO,EAAE,WAAW,sCAAsC,GAAG,KAAK,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,MAC1H,wBAAwB,OAAO,KAAM,EAAc,cAAc,EAAE,OAAO,OAAO,WAAW,CAAC,sBAAsB,EAAE,CAAC;AAAA,IAAE,CAAE;AAAA,EAClI;AAAA,EACA,aAAa,cAAc,gBAAgB,YAAY,YAAY,iBAAiB,WAAW;AAC3F,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,MAAM,UAAU;AAChB,aAAO,kBAAkB,cAAc,KAAK;AAAA,IAChD;AACA,WAAO,KAAK,uBAAuB,cAAc,gBAAgB,YAAY,YAAY,iBAAiB,SAAS;AAAA,EACvH;AAAA,EACA,uBAAuB,MACvB,gBAAgB,YAAY,YAAY,iBAAiB,WAAW;AAChE,QAAI,EAAE,eAAe,kBAAkB,kBAAkB,eAAe,IAAI,KAAK,QAAQ;AACzF,QAAI,EAAE,MAAM,YAAY,gBAAgB,YAAY,QAAQ,IAAI,KAAK;AACrE,QAAI,WAAW,cAAc,cAAc;AAC3C,QAAI,aAAa,kBAAkB,MAAM,MAAM,YAAY,cAAc;AACzE,QAAI,EAAE,eAAe,aAAa,IAAI,uBAAuB,MAAM,YAAY,kBAAkB,aAAa;AAC9G,WAAQ;AAAA,MAAc;AAAA,MAAU;AAAA,MAC5B,KAAK,mBAAmB,cAAc,IAAI;AAAA,MAC1C,cAAc,IAAI,CAAC,iBAAiB;AAChC,YAAI,EAAE,KAAK,KAAK,IAAI;AACpB,YAAI,aAAa,IAAI,WAAW,SAAS;AACzC,YAAI,YAAY,YAAY,QAAQ,CAAC,eAAe,UAAU,KAAK,IAAI;AACvE,YAAI,SAAS,iBAAiB,QAAQ,KAAK,IAAI;AAC/C,YAAI,SAAU,CAAC,YAAY,OAAQ,KAAK,iBAAiB,IAAI,IAAI,EAAE,MAAM,GAAG,OAAO,EAAE;AACrF,YAAI,UAAU,QAAQ,IAAI,KAAK,KAAK,eAAe;AACnD,YAAI,UAAU,QAAQ,IAAI,KAAM,KAAK,KAAK,MAAM,KAAK,KAAK,QAAS;AACnE,eAAQ;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,+BACjC,UAAU,qCAAqC,KAAK,KAAK,aAAa,YAAY,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,YAAY,YAAY,KAAK,SAAS,GAAG,MAAM,GAAG,MAAM,EAAE;AAAA,UACtL,EAAc,cAAc,OAAO,OAAO,EAAE,KAAU,YAAwB,YAAwB,iBAAkC,YAAY,eAAe,gBAAgB,QAAiB,GAAG,WAAW,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,QAAC;AAAA,MACrP,CAAC;AAAA,IAAC;AAAA,EACV;AAAA;AAAA,EAEA,mBAAmB,cAAc,MAAM;AACnC,QAAI,EAAE,eAAe,aAAa,YAAY,SAAS,gBAAgB,WAAW,YAAY,IAAI,KAAK;AACvG,WAAQ,EAAc,GAAU,MAAM,aAAa,IAAI,CAAC,gBAAgB;AACpE,UAAI,cAAc,iBAAiB,YAAY,IAAI;AACnD,UAAI,aAAa,uBAAuB,YAAY,SAAS,IAAI;AACjE,aAAQ,EAAc,iBAAiB,EAAE,KAAK,eAAe,wBAAwB,UAAU,CAAC,GAAG,YAAwB,KAAK,YAAY,KAAK,QAAQ,YAAY,QAAQ,eAA8B,aAA0B,YAAwB,SAAkB,gBAAgC,WAAsB,YAAyB,CAAC;AAAA,IACnW,CAAC,CAAC;AAAA,EACN;AAAA,EACA,eAAe,MAAM,UAAU;AAC3B,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,aAAa,kBAAkB,MAAM,MAAM,MAAM,MAAM,YAAY,QAAQ,QAAQ,cAAc;AACrG,QAAI,WAAW,WAAW,IAAI,CAAC,SAAS,MAAM;AAC1C,UAAI,MAAM,KAAK,CAAC;AAChB,aAAQ,EAAc,OAAO,EAAE,KAAK,mBAAmB,IAAI,UAAU,GAAG,WAAW,0BAA0B,OAAO,iBAAiB,OAAO,EAAE,GAAG,aAAa,aAC1J,EAAc,SAAS,OAAO,OAAO,EAAE,IAAS,GAAG,WAAW,KAAK,MAAM,YAAY,MAAM,OAAO,CAAC,CAAC,IACpG,WAAW,QAAQ,CAAC;AAAA,IAC5B,CAAC;AACD,WAAO,EAAc,GAAU,MAAM,QAAQ;AAAA,EACjD;AAAA,EACA,mBAAmB,MAAM;AACrB,QAAI,EAAE,YAAY,KAAK,IAAI,KAAK;AAChC,QAAI,CAAC,YAAY;AACb,aAAO;AAAA,IACX;AACA,WAAO,KAAK,IAAI,CAAC,KAAK,MAAO;AAAA,MAAc;AAAA,MAEzC;AAAA;AAAA,QAEE,KAAK;AAAA,QAAG,WAAW,CAAC,gCAAgC;AAAA,QAAG,SAAS;AAAA,UAC5D,KAAK,WAAW,eAAe,IAAI,OAAO,IAAI;AAAA,QAClD;AAAA,QAAG,QAAQ;AAAA,QAAO;AAAA,MAAW;AAAA,IAAC,CAAE;AAAA,EACxC;AAAA,EACA,iBAAiB,YAAY;AACzB,QAAI,EAAE,OAAO,QAAQ,IAAI,KAAK;AAC9B,QAAI,gBAAgB,QAAQ;AAC5B,QAAI,YAAY,WAAW;AAC3B,QAAI,WAAW,WAAW,aAAa,WAAW;AAClD,QAAI;AACJ,QAAI;AACJ,QAAI,eAAe;AAEf,iBAAW,KAAK,IAAI,GAAG,aAAa,WAAW,aAAa,CAAC;AAAA,IACjE;AACA,QAAI,OAAO;AACP,aAAO,IAAI;AACX,cAAQ;AAAA,IACZ,OACK;AACD,aAAO;AACP,cAAQ,IAAI;AAAA,IAChB;AACA,QAAI,QAAQ;AAAA,MACR,QAAQ,WAAW,aAAa;AAAA,MAChC,MAAM,OAAO,MAAM;AAAA,MACnB,OAAO,QAAQ,MAAM;AAAA,IACzB;AACA,QAAI,iBAAiB,CAAC,WAAW,cAAc;AAE3C,YAAM,QAAQ,eAAe,aAAa,IAAI,KAAK;AAAA,IACvD;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,kBAAkB,cAAc,EAAE,YAAY,SAAS,gBAAgB,WAAW,YAAY,GAAG;AACtG,MAAI,mBAAmB,YAAY,UAAU,oBAAoB,UAC5D,cAAc,YAAY,oBAAoB,SAC/C,CAAC;AACL,SAAQ,EAAc,GAAU,MAAM,aAAa,IAAI,CAAC,QAAQ;AAC5D,QAAI,aAAa,IAAI,WAAW,SAAS;AACzC,WAAQ;AAAA,MAAc;AAAA,MAAO,EAAE,KAAK,YAAY,OAAO,EAAE,YAAY,gBAAgB,UAAU,IAAI,WAAW,GAAG,EAAE;AAAA,MAC/G,EAAc,cAAc,OAAO,OAAO,EAAE,KAAU,YAAY,OAAO,YAAY,OAAO,iBAAiB,OAAO,YAAY,eAAe,gBAAgB,SAAS,MAAM,GAAG,WAAW,KAAK,YAAY,OAAO,CAAC,CAAC;AAAA,IAAC;AAAA,EAC/N,CAAC,CAAC;AACN;AACA,SAAS,iBAAiB,YAAY;AAClC,MAAI,CAAC,YAAY;AACb,WAAO,EAAE,KAAK,IAAI,QAAQ,GAAG;AAAA,EACjC;AACA,SAAO;AAAA,IACH,KAAK,WAAW;AAAA,IAChB,QAAQ,CAAC,WAAW;AAAA,EACxB;AACJ;AACA,SAAS,uBAAuB,YAAY,SAAS;AACjD,SAAO,WAAW,IAAI,CAAC,aAAa,QAAQ,SAAS,KAAK,CAAC;AAC/D;AAEA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EACxC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,wBAAwB,QAAQ,cAAc;AACnD,SAAK,wBAAwB,QAAQ,cAAc;AACnD,SAAK,yBAAyB,QAAQ,cAAc;AACpD,SAAK,iBAAiB,QAAQ,qBAAqB;AACnD,SAAK,mBAAmB,QAAQ,qBAAqB;AACrD,SAAK,YAAY,EAAU;AAC3B,SAAK,aAAa,IAAI,OAAO;AAAA,EACjC;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,kBAAkB,QAAQ,QAAQ,gBAClC,MAAM,cACN,MAAM,WAAW,eAAe,MAAM,OAAO;AACjD,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,wBAAwB,KAAK,sBAAsB,MAAM,kBAAkB,MAAM;AACrF,QAAI,wBAAwB,KAAK,sBAAsB,MAAM,kBAAkB,MAAM;AACrF,QAAI,yBAAyB,KAAK,uBAAuB,MAAM,mBAAmB,MAAM;AACxF,QAAI,iBAAiB,KAAK,eAAe,MAAM,WAAW,MAAM;AAChE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,WAAQ;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,oBAAoB,KAAK,KAAK,UAAU;AAAA,MAC9E;AAAA,QAAc;AAAA,QAAS,EAAE,MAAM,gBAAgB,OAAO;AAAA,UAC9C,UAAU,MAAM;AAAA,UAChB,OAAO,MAAM;AAAA,QACjB,EAAE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,UAAc;AAAA,UAAS,EAAE,MAAM,eAAe;AAAA,UAC1C;AAAA,YAAc;AAAA,YAAM,EAAE,MAAM,MAAM;AAAA,YAC9B,MAAM,QAAS;AAAA,cAAc;AAAA,cAAM,EAAE,eAAe,MAAM,WAAW,mCAAmC;AAAA,cACpG;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,wBAAwB;AAAA,gBACtD,EAAc,OAAO,EAAE,WAAW,sCAAsC,GAAG,OAAO,oBAAoB,YAAa,EAAc,uBAAuB,EAAE,WAAW,CAAC,iCAAiC,GAAG,SAAS,EAAE,KAAK,gBAAgB,GAAG,QAAQ,MAAM,MAAM,MAAM,QAAQ,CAAC,CAAE;AAAA,cAAC;AAAA,YAAC;AAAA,YAC5R,MAAM,MAAM,IAAI,CAAC,MAAM,MAAO,EAAc,SAAS,EAAE,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW,UAAU,KAAK,GAAG,GAAG,aAAa,MAAM,aAAa,MAAM,KAAK,MAAM,SAAS,MAAM,SAAS,YAAY,MAAM,YAAY,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,iBAAiB,KAAK,iBAAiB,eAAe,KAAK,eAAe,aAAa,iBAAiB,CAAC,GAAG,aAAa,iBAAiB,CAAC,GAAG,kBAAkB,sBAAsB,CAAC,GAAG,kBAAkB,sBAAsB,CAAC,GAAG,mBAAmB,uBAAuB,CAAC,GAAG,WAAW,eAAe,CAAC,GAAG,aAAa,iBAAiB,CAAC,GAAG,YAAY,MAAM,YAAY,gBAAgB,MAAM,gBAAgB,UAAU,MAAM,SAAS,CAAC,CAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EACnvB;AAAA,EACA,oBAAoB;AAChB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,qBAAqB;AACjB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,eAAe;AACX,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,MAAM,eACN,MAAM,gBAAgB,MACxB;AACE,YAAM,YAAY,IAAI;AAAA,QAAc,KAAK,UAAU;AAAA,QAAS,eAAe,KAAK,WAAW,YAAY,MAAM,KAAK;AAAA,QAAG;AAAA;AAAA,QACrH;AAAA,MAAK,CAAC;AAAA,IACV;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,OAAO,OAAO;AAClC,SAAO,MAAM,IAAI,CAAC,SAAS,MAAM,KAAK,GAAG,CAAC;AAC9C;AAIA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACjC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB,QAAQ,kBAAkB;AACpD,SAAK,QAAQ;AAAA,MACT,YAAY;AAAA,IAChB;AACA,SAAK,eAAe,CAAC,OAAO;AACxB,UAAI,IAAI;AACJ,aAAK,QAAQ,6BAA6B,MAAM;AAAA,UAC5C;AAAA,UACA,mBAAmB,KAAK,MAAM;AAAA,QAClC,CAAC;AAAA,MACL,OACK;AACD,aAAK,QAAQ,+BAA+B,IAAI;AAAA,MACpD;AAAA,IACJ;AACA,SAAK,sBAAsB,CAAC,YAAY;AACpC,UAAI,EAAE,mBAAmB,IAAI,KAAK;AAClC,UAAI,EAAE,WAAW,IAAI,KAAK;AAC1B,UAAI,sBAAsB,YAAY;AAClC,YAAI,QAAQ,MAAM;AACd,cAAI,MAAM,WAAW,eAAe,QAAQ,IAAI;AAChD,gBAAM,KAAK,KAAK,GAAG;AACnB,cAAI,KAAK;AACL,mBAAO;AAAA,UACX;AACA,6BAAmB,GAAG;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,SAAK,kBAAkB,CAAC,cAAc;AAClC,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,mBAAmB,CAAC,eAAe;AACpC,WAAK,SAAS,EAAE,WAAW,CAAC;AAC5B,UAAI,KAAK,MAAM,cAAc;AACzB,aAAK,MAAM,aAAa,UAAU;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,MAAM,IAAI;AACvB,WAAQ;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,oBAAoB,KAAK,KAAK,cAAc,OAAO;AAAA;AAAA;AAAA,QAGrF,OAAO,MAAM;AAAA,QACb,UAAU,MAAM;AAAA,MACpB,EAAE;AAAA,MACF,EAAc,eAAe,EAAE,MAAM,MAAM,MAAM,aAAa,MAAM,aAAa,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,WAAW,MAAM,aAAa,MAAM,eAAe,IAAI,eAAe,MAAM,eAAe,mBAAmB,MAAM,OAAO,MAAM,oBAAoB,MAAqD,UAAU,KAAK,iBAAiB,CAAC;AAAA,MACxX,EAAc,iBAAiB,EAAE,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,aAAa,MAAM,aAAa,kBAAkB,MAAM,kBAAkB,aAAa,MAAM,aAAa,aAAa,MAAM,aAAa,mBAAmB,MAAM,mBAAmB,gBAAgB,MAAM,gBAAgB,WAAW,MAAM,WAAW,aAAa,MAAM,aAAa,YAAY,MAAM,YAAY,SAAS,MAAM,SAAS,kBAAkB,MAAM,kBAAkB,aAAa,MAAM,aAAa,eAAe,MAAM,eAAe,mBAAmB,MAAM,mBAAmB,YAAY,MAAM,YAAY,aAAa,KAAK,iBAAiB,UAAU,MAAM,SAAS,CAAC;AAAA,IAAC;AAAA,EACnpB;AAAA,EACA,oBAAoB;AAChB,SAAK,kBAAkB,KAAK,QAAQ,sBAAsB,KAAK,mBAAmB;AAAA,EACtF;AAAA,EACA,mBAAmB,WAAW;AAC1B,SAAK,gBAAgB,OAAO,UAAU,gBAAgB,KAAK,MAAM,WAAW;AAAA,EAChF;AAAA,EACA,uBAAuB;AACnB,SAAK,gBAAgB,OAAO;AAAA,EAChC;AAAA,EACA,SAAS,cAAc,aAAa;AAChC,QAAI,EAAE,SAAS,QAAQ,IAAI,KAAK;AAChC,QAAI,EAAE,UAAU,IAAI;AACpB,QAAI,EAAE,YAAY,IAAI,KAAK;AAC3B,QAAI,EAAE,WAAW,IAAI,KAAK;AAC1B,QAAI,EAAE,cAAc,aAAa,IAAI,KAAK,mBAAmB,KAAK,MAAM,cAAc,QAAQ,YAAY;AAC1G,QAAI,WAAW,UAAU,YAAY,YAAY;AACjD,QAAI,YAAY,WAAW,UAAU,WAAW,WAAW;AAC3D,QAAI,YAAY,QAAQ,aAAa,MAAM;AACvC,UAAI,OAAO,KAAK,MAAM,MAAM,QAAQ;AACpC,UAAI,UAAU,WAAW,UAAU,KAAK,SAAS;AACjD,UAAI,aAAa,WAAW,UAAU,UAAU,SAAS;AACzD,UAAI,WAAW,cAAc,WAAW;AACxC,UAAI,iBAAiB,KAAK,MAAM,UAAU,YAAY;AACtD,UAAI,YAAY,YAAY,eAAe;AAC3C,UAAI,UAAU,KAAK,MAAM,MAAM,QAAQ,EAAE;AACzC,UAAI,OAAO,aAAa,YAAY,aAAa,iBAAiB,cAAc,SAAS,CAAC;AAC1F,UAAI,QAAQ,QAAQ,IAAI,SAAS,IAAI;AACrC,UAAI,MAAM,QAAQ,IAAI,OAAO,YAAY;AACzC,aAAO;AAAA,QACH;AAAA,QACA,UAAU,OAAO,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,GAAG,QAAQ,MAAM,GAAG,KAAK,aAAa;AAAA,QACpF,OAAO,UAAU,IAAI,QAAQ;AAAA,QAC7B,MAAM;AAAA,UACF,MAAM,UAAU,MAAM,QAAQ;AAAA,UAC9B,OAAO,UAAU,OAAO,QAAQ;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ,UAAU;AAAA,QACtB;AAAA,QACA,OAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,mBAAmB,cAAc,sBAAsB;AAC5D,MAAI,eAAe,wBAAwB;AAC3C,MAAI,eAAe,qBAAqB,cAAc,YAAY;AAClE,MAAI,iBAAiB,MAAM;AACvB,mBAAe;AACf,mBAAe;AAAA,EAEnB;AACA,SAAO,EAAE,cAAc,aAAa;AACxC;AAEA,IAAM,oBAAN,cAAgC,OAAO;AAAA,EACnC,WAAW,OAAO,WAAW;AACzB,QAAI,OAAO,CAAC;AACZ,aAAS,MAAM,GAAG,MAAM,UAAU,QAAQ,OAAO,GAAG;AAChD,UAAI,WAAW,gBAAgB,OAAO,UAAU,GAAG,CAAC;AACpD,UAAI,UAAU;AACV,aAAK,KAAK;AAAA,UACN,OAAO,SAAS;AAAA,UAChB,KAAK,SAAS;AAAA,UACd,SAAS,SAAS,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ;AAAA,UAC1D,OAAO,SAAS,IAAI,QAAQ,MAAM,MAAM,IAAI,QAAQ;AAAA,UACpD;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,cAAN,cAA0B,cAAc;AAAA,EACpC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,iBAAiB,QAAQ,cAAc;AAC5C,SAAK,SAAS,IAAI,kBAAkB;AACpC,SAAK,cAAc,EAAU;AAAA,EACjC;AAAA,EACA,SAAS;AACL,QAAI,EAAE,OAAO,QAAQ,IAAI;AACzB,QAAI,EAAE,aAAa,cAAc,IAAI;AACrC,QAAI,EAAE,cAAc,iBAAiB,IAAI,QAAQ;AACjD,QAAI,YAAY,KAAK,eAAe,eAAe,aAAa,QAAQ,OAAO;AAG/E,WAAQ,EAAc,UAAU,EAAE,MAAM,eAAe,WAAW,MAAM,GAAG,CAAC,SAAS,eAAgB,EAAc,UAAU,OAAO,OAAO,EAAE,KAAK,KAAK,YAAY,GAAG,KAAK,OAAO,WAAW,OAAO,aAAa,MAAM,SAAS,SAAS,GAAG,EAAE,UAAU,MAAM,UAAU,MAAM,MAAM,MAAM,aAA0B,WAAW,MAAM,WAAW,cAAc,MAAM,cAAc,OAAO,cAAc,MAAM,CAAC,GAAG,mBAAmB,MAAM,mBAAmB,eAAe,MAAM,eAAe,aAAa,MAAM,aAAa,cAAc,MAAM,cAAc,YAAY,MAAM,YAAY,SAAkB,kBAAkB,gBAAgB,KAAK,OAAO,aAAa,SAAS,aAAa,kBAAkB,SAAS,SAAS,GAAG,YAAwB,oBAAoB,MAAM,oBAAoB,cAAc,MAAM,aAAa,CAAC,CAAC,CAAE;AAAA,EAC1zB;AACJ;AACA,SAAS,eAAe,eAAe,aAAa,SAAS;AACzD,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,cAAc,aAAa;AACxC,WAAO,KAAK;AAAA,MACR,OAAO,QAAQ,IAAI,MAAM,YAAY,WAAW;AAAA,MAChD,KAAK,QAAQ,IAAI,MAAM,YAAY,WAAW;AAAA,IAClD,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAIA,IAAM,sBAAsB;AAAA,EACxB,EAAE,OAAO,EAAE;AAAA,EACX,EAAE,SAAS,GAAG;AAAA,EACd,EAAE,SAAS,GAAG;AAAA,EACd,EAAE,SAAS,GAAG;AAAA,EACd,EAAE,SAAS,GAAG;AAClB;AACA,SAAS,eAAe,aAAa,aAAa,uBAAuB,cAAc,SAAS;AAC5F,MAAI,WAAW,oBAAI,KAAK,CAAC;AACzB,MAAI,WAAW;AACf,MAAI,eAAe,eAAe,CAAC;AACnC,MAAI,gBAAgB,yBAAyB,qBAAqB,YAAY;AAC9E,MAAI,QAAQ,CAAC;AACb,SAAO,UAAU,QAAQ,IAAI,UAAU,WAAW,GAAG;AACjD,QAAI,OAAO,QAAQ,IAAI,UAAU,QAAQ;AACzC,QAAI,YAAY,qBAAqB,cAAc,aAAa,MAAM;AACtE,UAAM,KAAK;AAAA,MACP;AAAA,MACA,MAAM;AAAA,MACN,KAAK,KAAK,YAAY;AAAA,MACtB,YAAY,oBAAoB,IAAI;AAAA,MACpC;AAAA,IACJ,CAAC;AACD,eAAW,aAAa,UAAU,YAAY;AAC9C,mBAAe,aAAa,cAAc,YAAY;AAAA,EAC1D;AACA,SAAO;AACX;AAEA,SAAS,qBAAqB,cAAc;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,OAAK,IAAI,oBAAoB,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACrD,oBAAgB,eAAe,oBAAoB,CAAC,CAAC;AACrD,oBAAgB,qBAAqB,eAAe,YAAY;AAChE,QAAI,kBAAkB,QAAQ,gBAAgB,GAAG;AAC7C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,kBAAN,cAA8B,aAAa;AAAA,EACvC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB,QAAQ,kBAAkB;AACpD,SAAK,iBAAiB,QAAQ,cAAc;AAAA,EAChD;AAAA,EACA,SAAS;AACL,QAAI,EAAE,SAAS,SAAS,qBAAqB,IAAI,KAAK;AACtD,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,EAAE,YAAY,IAAI;AACtB,QAAI,gBAAgB,KAAK,mBAAmB,aAAa,oBAAoB;AAC7E,QAAI,aAAa,KAAK,eAAe,WAAW,KAAK;AACrD,QAAI,YAAY,KAAK,eAAe,YAAY,aAAa,YAAY,aAAa,QAAQ,mBAAmB,QAAQ,cAAc,OAAO;AAC9I,QAAI,EAAE,YAAY,IAAI;AACtB,QAAI,kBAAkB,CAAC;AACvB,QAAI,kBAAkB;AACtB,QAAI,gBAAgB,QAAQ,cAAe,EAAc,WAAW,EAAE,OAAO,cAAc,aAAa,aAA0B,sBAAsB,MAAM,aAAa,kBAAkB,KAAK,iBAAiB,KAAK,CAAC;AACzN,QAAI,gBAAiB,QAAQ,eAAe,UAAW,CAAC,eAAgB,EAAc,UAAU,OAAO,OAAO,CAAC,GAAG,WAAW,QAAQ,EAAE,aAA0B,eAA8B,kBAAkB,QAAQ,kBAAkB,eAAe,WAAW,eAAe,cAAc,WAAW,mBAAmB,gBAAgB,kBAAkB,KAAK,qBAAqB,MAAM,iBAAiB,OAAO,YAAY,OAAO,kBAAkB,KAAK,aAAa,aAAa,WAAW,aAAa,cAAc,WAAW,cAAc,UAAU,MAAM,SAAS,GAAG,KAAK,uBAAuB,CAAC,CAAC;AACvlB,QAAI,kBAAkB,CAAC,eAAgB,EAAc,aAAa,OAAO,OAAO,CAAC,GAAG,WAAW,OAAO,EAAE,eAA8B,aAA0B,MAAM,iBAAiB,cAAc,QAAQ,cAAc,WAAsB,UAAU,MAAM,UAAU,mBAAmB,WAAW,mBAAmB,eAAe,WAAW,eAAe,aAAa,WAAW,aAAa,cAAc,WAAW,cAAc,cAAc,KAAK,kBAAkB,YAAY,WAAW,YAAY,oBAAoB,KAAK,uBAAuB,CAAC,CAAC;AAC3iB,WAAO,kBACD,KAAK,oBAAoB,eAAe,eAAe,iBAAiB,cAAc,QAAQ,aAAa,WAAW,KAAK,MAAM,UAAU,IAC3I,KAAK,mBAAmB,eAAe,eAAe,eAAe;AAAA,EAC/E;AACJ;AACA,SAAS,mBAAmB,aAAa,sBAAsB;AAC3D,MAAI,YAAY,IAAI,eAAe,YAAY,aAAa,oBAAoB;AAChF,SAAO,IAAI,cAAc,WAAW,KAAK;AAC7C;AAEA,IAAI,WAAW;AACf,aAAa,QAAQ;;;AChnCrB,IAAM,kBAAkB;AAAA,EACpB,YAAY;AAChB;AAEA,IAAI,QAAQ,aAAa;AAAA,EACrB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,OAAO;AAAA,IACH,UAAU;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB;AAAA;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACT,MAAM;AAAA,MACN,UAAU,EAAE,MAAM,EAAE;AAAA,IACxB;AAAA,IACA,cAAc;AAAA,MACV,MAAM;AAAA,MACN,UAAU,EAAE,OAAO,EAAE;AAAA,IACzB;AAAA,EACJ;AACJ,CAAC;", "names": []}