{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/q.js"], "sourcesContent": ["var curPunc,\n    keywords=buildRE([\"abs\",\"acos\",\"aj\",\"aj0\",\"all\",\"and\",\"any\",\"asc\",\"asin\",\"asof\",\"atan\",\"attr\",\"avg\",\"avgs\",\"bin\",\"by\",\"ceiling\",\"cols\",\"cor\",\"cos\",\"count\",\"cov\",\"cross\",\"csv\",\"cut\",\"delete\",\"deltas\",\"desc\",\"dev\",\"differ\",\"distinct\",\"div\",\"do\",\"each\",\"ej\",\"enlist\",\"eval\",\"except\",\"exec\",\"exit\",\"exp\",\"fby\",\"fills\",\"first\",\"fkeys\",\"flip\",\"floor\",\"from\",\"get\",\"getenv\",\"group\",\"gtime\",\"hclose\",\"hcount\",\"hdel\",\"hopen\",\"hsym\",\"iasc\",\"idesc\",\"if\",\"ij\",\"in\",\"insert\",\"inter\",\"inv\",\"key\",\"keys\",\"last\",\"like\",\"list\",\"lj\",\"load\",\"log\",\"lower\",\"lsq\",\"ltime\",\"ltrim\",\"mavg\",\"max\",\"maxs\",\"mcount\",\"md5\",\"mdev\",\"med\",\"meta\",\"min\",\"mins\",\"mmax\",\"mmin\",\"mmu\",\"mod\",\"msum\",\"neg\",\"next\",\"not\",\"null\",\"or\",\"over\",\"parse\",\"peach\",\"pj\",\"plist\",\"prd\",\"prds\",\"prev\",\"prior\",\"rand\",\"rank\",\"ratios\",\"raze\",\"read0\",\"read1\",\"reciprocal\",\"reverse\",\"rload\",\"rotate\",\"rsave\",\"rtrim\",\"save\",\"scan\",\"select\",\"set\",\"setenv\",\"show\",\"signum\",\"sin\",\"sqrt\",\"ss\",\"ssr\",\"string\",\"sublist\",\"sum\",\"sums\",\"sv\",\"system\",\"tables\",\"tan\",\"til\",\"trim\",\"txf\",\"type\",\"uj\",\"ungroup\",\"union\",\"update\",\"upper\",\"upsert\",\"value\",\"var\",\"view\",\"views\",\"vs\",\"wavg\",\"where\",\"where\",\"while\",\"within\",\"wj\",\"wj1\",\"wsum\",\"xasc\",\"xbar\",\"xcol\",\"xcols\",\"xdesc\",\"xexp\",\"xgroup\",\"xkey\",\"xlog\",\"xprev\",\"xrank\"]),\n    E=/[|/&^!+:\\\\\\-*%$=~#;@><,?_\\'\\\"\\[\\(\\]\\)\\s{}]/;\nfunction buildRE(w){return new RegExp(\"^(\"+w.join(\"|\")+\")$\");}\nfunction tokenBase(stream,state){\n  var sol=stream.sol(),c=stream.next();\n  curPunc=null;\n  if(sol)\n    if(c==\"/\")\n      return(state.tokenize=tokenLineComment)(stream,state);\n  else if(c==\"\\\\\"){\n    if(stream.eol()||/\\s/.test(stream.peek()))\n      return stream.skipToEnd(),/^\\\\\\s*$/.test(stream.current())?(state.tokenize=tokenCommentToEOF)(stream):state.tokenize=tokenBase,\"comment\";\n    else\n      return state.tokenize=tokenBase,\"builtin\";\n  }\n  if(/\\s/.test(c))\n    return stream.peek()==\"/\"?(stream.skipToEnd(),\"comment\"):\"null\";\n  if(c=='\"')\n    return(state.tokenize=tokenString)(stream,state);\n  if(c=='`')\n    return stream.eatWhile(/[A-Za-z\\d_:\\/.]/),\"macroName\";\n  if((\".\"==c&&/\\d/.test(stream.peek()))||/\\d/.test(c)){\n    var t=null;\n    stream.backUp(1);\n    if(stream.match(/^\\d{4}\\.\\d{2}(m|\\.\\d{2}([DT](\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)?)?)/)\n       || stream.match(/^\\d+D(\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)/)\n       || stream.match(/^\\d{2}:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?/)\n       || stream.match(/^\\d+[ptuv]{1}/))\n      t=\"temporal\";\n    else if(stream.match(/^0[NwW]{1}/)\n            || stream.match(/^0x[\\da-fA-F]*/)\n            || stream.match(/^[01]+[b]{1}/)\n            || stream.match(/^\\d+[chijn]{1}/)\n            || stream.match(/-?\\d*(\\.\\d*)?(e[+\\-]?\\d+)?(e|f)?/))\n      t=\"number\";\n    return(t&&(!(c=stream.peek())||E.test(c)))?t:(stream.next(),\"error\");\n  }\n  if(/[A-Za-z]|\\./.test(c))\n    return stream.eatWhile(/[A-Za-z._\\d]/),keywords.test(stream.current())?\"keyword\":\"variable\";\n  if(/[|/&^!+:\\\\\\-*%$=~#;@><\\.,?_\\']/.test(c))\n    return null;\n  if(/[{}\\(\\[\\]\\)]/.test(c))\n    return null;\n  return\"error\";\n}\nfunction tokenLineComment(stream,state){\n  return stream.skipToEnd(),/\\/\\s*$/.test(stream.current())?(state.tokenize=tokenBlockComment)(stream,state):(state.tokenize=tokenBase),\"comment\";\n}\nfunction tokenBlockComment(stream,state){\n  var f=stream.sol()&&stream.peek()==\"\\\\\";\n  stream.skipToEnd();\n  if(f&&/^\\\\\\s*$/.test(stream.current()))\n    state.tokenize=tokenBase;\n  return\"comment\";\n}\nfunction tokenCommentToEOF(stream){return stream.skipToEnd(),\"comment\";}\nfunction tokenString(stream,state){\n  var escaped=false,next,end=false;\n  while((next=stream.next())){\n    if(next==\"\\\"\"&&!escaped){end=true;break;}\n    escaped=!escaped&&next==\"\\\\\";\n  }\n  if(end)state.tokenize=tokenBase;\n  return\"string\";\n}\nfunction pushContext(state,type,col){state.context={prev:state.context,indent:state.indent,col:col,type:type};}\nfunction popContext(state){state.indent=state.context.indent;state.context=state.context.prev;}\nexport const q = {\n  name: \"q\",\n  startState:function(){\n    return{tokenize:tokenBase,\n           context:null,\n           indent:0,\n           col:0};\n  },\n  token:function(stream,state){\n    if(stream.sol()){\n      if(state.context&&state.context.align==null)\n        state.context.align=false;\n      state.indent=stream.indentation();\n    }\n    //if (stream.eatSpace()) return null;\n    var style=state.tokenize(stream,state);\n    if(style!=\"comment\"&&state.context&&state.context.align==null&&state.context.type!=\"pattern\"){\n      state.context.align=true;\n    }\n    if(curPunc==\"(\")pushContext(state,\")\",stream.column());\n    else if(curPunc==\"[\")pushContext(state,\"]\",stream.column());\n    else if(curPunc==\"{\")pushContext(state,\"}\",stream.column());\n    else if(/[\\]\\}\\)]/.test(curPunc)){\n      while(state.context&&state.context.type==\"pattern\")popContext(state);\n      if(state.context&&curPunc==state.context.type)popContext(state);\n    }\n    else if(curPunc==\".\"&&state.context&&state.context.type==\"pattern\")popContext(state);\n    else if(/atom|string|variable/.test(style)&&state.context){\n      if(/[\\}\\]]/.test(state.context.type))\n        pushContext(state,\"pattern\",stream.column());\n      else if(state.context.type==\"pattern\"&&!state.context.align){\n        state.context.align=true;\n        state.context.col=stream.column();\n      }\n    }\n    return style;\n  },\n  indent:function(state,textAfter,cx){\n    var firstChar=textAfter&&textAfter.charAt(0);\n    var context=state.context;\n    if(/[\\]\\}]/.test(firstChar))\n      while (context&&context.type==\"pattern\")context=context.prev;\n    var closing=context&&firstChar==context.type;\n    if(!context)\n      return 0;\n    else if(context.type==\"pattern\")\n      return context.col;\n    else if(context.align)\n      return context.col+(closing?0:1);\n    else\n      return context.indent+(closing?0:cx.unit);\n  }\n};\n"], "mappings": ";;;AAAA,IAAI;AAAJ,IACI,WAAS,QAAQ,CAAC,OAAM,QAAO,MAAK,OAAM,OAAM,OAAM,OAAM,OAAM,QAAO,QAAO,QAAO,QAAO,OAAM,QAAO,OAAM,MAAK,WAAU,QAAO,OAAM,OAAM,SAAQ,OAAM,SAAQ,OAAM,OAAM,UAAS,UAAS,QAAO,OAAM,UAAS,YAAW,OAAM,MAAK,QAAO,MAAK,UAAS,QAAO,UAAS,QAAO,QAAO,OAAM,OAAM,SAAQ,SAAQ,SAAQ,QAAO,SAAQ,QAAO,OAAM,UAAS,SAAQ,SAAQ,UAAS,UAAS,QAAO,SAAQ,QAAO,QAAO,SAAQ,MAAK,MAAK,MAAK,UAAS,SAAQ,OAAM,OAAM,QAAO,QAAO,QAAO,QAAO,MAAK,QAAO,OAAM,SAAQ,OAAM,SAAQ,SAAQ,QAAO,OAAM,QAAO,UAAS,OAAM,QAAO,OAAM,QAAO,OAAM,QAAO,QAAO,QAAO,OAAM,OAAM,QAAO,OAAM,QAAO,OAAM,QAAO,MAAK,QAAO,SAAQ,SAAQ,MAAK,SAAQ,OAAM,QAAO,QAAO,SAAQ,QAAO,QAAO,UAAS,QAAO,SAAQ,SAAQ,cAAa,WAAU,SAAQ,UAAS,SAAQ,SAAQ,QAAO,QAAO,UAAS,OAAM,UAAS,QAAO,UAAS,OAAM,QAAO,MAAK,OAAM,UAAS,WAAU,OAAM,QAAO,MAAK,UAAS,UAAS,OAAM,OAAM,QAAO,OAAM,QAAO,MAAK,WAAU,SAAQ,UAAS,SAAQ,UAAS,SAAQ,OAAM,QAAO,SAAQ,MAAK,QAAO,SAAQ,SAAQ,SAAQ,UAAS,MAAK,OAAM,QAAO,QAAO,QAAO,QAAO,SAAQ,SAAQ,QAAO,UAAS,QAAO,QAAO,SAAQ,OAAO,CAAC;AADjuC,IAEI,IAAE;AACN,SAAS,QAAQ,GAAE;AAAC,SAAO,IAAI,OAAO,OAAK,EAAE,KAAK,GAAG,IAAE,IAAI;AAAE;AAC7D,SAAS,UAAU,QAAO,OAAM;AAC9B,MAAI,MAAI,OAAO,IAAI,GAAE,IAAE,OAAO,KAAK;AACnC,YAAQ;AACR,MAAG;AACD,QAAG,KAAG;AACJ,cAAO,MAAM,WAAS,kBAAkB,QAAO,KAAK;AAAA,aAChD,KAAG,MAAK;AACd,UAAG,OAAO,IAAI,KAAG,KAAK,KAAK,OAAO,KAAK,CAAC;AACtC,eAAO,OAAO,UAAU,GAAE,UAAU,KAAK,OAAO,QAAQ,CAAC,KAAG,MAAM,WAAS,mBAAmB,MAAM,IAAE,MAAM,WAAS,WAAU;AAAA;AAE/H,eAAO,MAAM,WAAS,WAAU;AAAA,IACpC;AAAA;AACA,MAAG,KAAK,KAAK,CAAC;AACZ,WAAO,OAAO,KAAK,KAAG,OAAK,OAAO,UAAU,GAAE,aAAW;AAC3D,MAAG,KAAG;AACJ,YAAO,MAAM,WAAS,aAAa,QAAO,KAAK;AACjD,MAAG,KAAG;AACJ,WAAO,OAAO,SAAS,iBAAiB,GAAE;AAC5C,MAAI,OAAK,KAAG,KAAK,KAAK,OAAO,KAAK,CAAC,KAAI,KAAK,KAAK,CAAC,GAAE;AAClD,QAAI,IAAE;AACN,WAAO,OAAO,CAAC;AACf,QAAG,OAAO,MAAM,uEAAuE,KACjF,OAAO,MAAM,4CAA4C,KACzD,OAAO,MAAM,mCAAmC,KAChD,OAAO,MAAM,eAAe;AAChC,UAAE;AAAA,aACI,OAAO,MAAM,YAAY,KACtB,OAAO,MAAM,gBAAgB,KAC7B,OAAO,MAAM,cAAc,KAC3B,OAAO,MAAM,gBAAgB,KAC7B,OAAO,MAAM,kCAAkC;AACxD,UAAE;AACJ,WAAO,MAAI,EAAE,IAAE,OAAO,KAAK,MAAI,EAAE,KAAK,CAAC,KAAI,KAAG,OAAO,KAAK,GAAE;AAAA,EAC9D;AACA,MAAG,cAAc,KAAK,CAAC;AACrB,WAAO,OAAO,SAAS,cAAc,GAAE,SAAS,KAAK,OAAO,QAAQ,CAAC,IAAE,YAAU;AACnF,MAAG,iCAAiC,KAAK,CAAC;AACxC,WAAO;AACT,MAAG,eAAe,KAAK,CAAC;AACtB,WAAO;AACT,SAAM;AACR;AACA,SAAS,iBAAiB,QAAO,OAAM;AACrC,SAAO,OAAO,UAAU,GAAE,SAAS,KAAK,OAAO,QAAQ,CAAC,KAAG,MAAM,WAAS,mBAAmB,QAAO,KAAK,IAAG,MAAM,WAAS,WAAW;AACxI;AACA,SAAS,kBAAkB,QAAO,OAAM;AACtC,MAAI,IAAE,OAAO,IAAI,KAAG,OAAO,KAAK,KAAG;AACnC,SAAO,UAAU;AACjB,MAAG,KAAG,UAAU,KAAK,OAAO,QAAQ,CAAC;AACnC,UAAM,WAAS;AACjB,SAAM;AACR;AACA,SAAS,kBAAkB,QAAO;AAAC,SAAO,OAAO,UAAU,GAAE;AAAU;AACvE,SAAS,YAAY,QAAO,OAAM;AAChC,MAAI,UAAQ,OAAM,MAAK,MAAI;AAC3B,SAAO,OAAK,OAAO,KAAK,GAAG;AACzB,QAAG,QAAM,OAAM,CAAC,SAAQ;AAAC,YAAI;AAAK;AAAA,IAAM;AACxC,cAAQ,CAAC,WAAS,QAAM;AAAA,EAC1B;AACA,MAAG;AAAI,UAAM,WAAS;AACtB,SAAM;AACR;AACA,SAAS,YAAY,OAAM,MAAK,KAAI;AAAC,QAAM,UAAQ,EAAC,MAAK,MAAM,SAAQ,QAAO,MAAM,QAAO,KAAQ,KAAS;AAAE;AAC9G,SAAS,WAAW,OAAM;AAAC,QAAM,SAAO,MAAM,QAAQ;AAAO,QAAM,UAAQ,MAAM,QAAQ;AAAK;AACvF,IAAM,IAAI;AAAA,EACf,MAAM;AAAA,EACN,YAAW,WAAU;AACnB,WAAM;AAAA,MAAC,UAAS;AAAA,MACT,SAAQ;AAAA,MACR,QAAO;AAAA,MACP,KAAI;AAAA,IAAC;AAAA,EACd;AAAA,EACA,OAAM,SAAS,QAAO,OAAM;AAC1B,QAAG,OAAO,IAAI,GAAE;AACd,UAAG,MAAM,WAAS,MAAM,QAAQ,SAAO;AACrC,cAAM,QAAQ,QAAM;AACtB,YAAM,SAAO,OAAO,YAAY;AAAA,IAClC;AAEA,QAAI,QAAM,MAAM,SAAS,QAAO,KAAK;AACrC,QAAG,SAAO,aAAW,MAAM,WAAS,MAAM,QAAQ,SAAO,QAAM,MAAM,QAAQ,QAAM,WAAU;AAC3F,YAAM,QAAQ,QAAM;AAAA,IACtB;AACA,QAAG,WAAS;AAAI,kBAAY,OAAM,KAAI,OAAO,OAAO,CAAC;AAAA,aAC7C,WAAS;AAAI,kBAAY,OAAM,KAAI,OAAO,OAAO,CAAC;AAAA,aAClD,WAAS;AAAI,kBAAY,OAAM,KAAI,OAAO,OAAO,CAAC;AAAA,aAClD,WAAW,KAAK,OAAO,GAAE;AAC/B,aAAM,MAAM,WAAS,MAAM,QAAQ,QAAM;AAAU,mBAAW,KAAK;AACnE,UAAG,MAAM,WAAS,WAAS,MAAM,QAAQ;AAAK,mBAAW,KAAK;AAAA,IAChE,WACQ,WAAS,OAAK,MAAM,WAAS,MAAM,QAAQ,QAAM;AAAU,iBAAW,KAAK;AAAA,aAC3E,uBAAuB,KAAK,KAAK,KAAG,MAAM,SAAQ;AACxD,UAAG,SAAS,KAAK,MAAM,QAAQ,IAAI;AACjC,oBAAY,OAAM,WAAU,OAAO,OAAO,CAAC;AAAA,eACrC,MAAM,QAAQ,QAAM,aAAW,CAAC,MAAM,QAAQ,OAAM;AAC1D,cAAM,QAAQ,QAAM;AACpB,cAAM,QAAQ,MAAI,OAAO,OAAO;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAO,SAAS,OAAM,WAAU,IAAG;AACjC,QAAI,YAAU,aAAW,UAAU,OAAO,CAAC;AAC3C,QAAI,UAAQ,MAAM;AAClB,QAAG,SAAS,KAAK,SAAS;AACxB,aAAO,WAAS,QAAQ,QAAM;AAAU,kBAAQ,QAAQ;AAC1D,QAAI,UAAQ,WAAS,aAAW,QAAQ;AACxC,QAAG,CAAC;AACF,aAAO;AAAA,aACD,QAAQ,QAAM;AACpB,aAAO,QAAQ;AAAA,aACT,QAAQ;AACd,aAAO,QAAQ,OAAK,UAAQ,IAAE;AAAA;AAE9B,aAAO,QAAQ,UAAQ,UAAQ,IAAE,GAAG;AAAA,EACxC;AACF;", "names": []}