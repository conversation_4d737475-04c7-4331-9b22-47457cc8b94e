{"version": 3, "sources": ["../../react-beautiful-dnd/node_modules/react-is/cjs/react-is.development.js", "../../react-beautiful-dnd/node_modules/react-is/index.js", "../../react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "../../react-beautiful-dnd/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../../react-beautiful-dnd/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../../react-beautiful-dnd/node_modules/@babel/runtime/helpers/esm/extends.js", "../../react-beautiful-dnd/node_modules/react-redux/es/components/Provider.js", "../../react-beautiful-dnd/node_modules/react-redux/es/components/Context.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/batch.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/Subscription.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/useIsomorphicLayoutEffect.js", "../../react-beautiful-dnd/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../react-beautiful-dnd/node_modules/react-redux/es/components/connectAdvanced.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/shallowEqual.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/bindActionCreators.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/isPlainObject.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/warning.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/verifyPlainObject.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/wrapMapToProps.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/mapDispatchToProps.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/mapStateToProps.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/mergeProps.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/verifySubselectors.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/selectorFactory.js", "../../react-beautiful-dnd/node_modules/react-redux/es/connect/connect.js", "../../react-beautiful-dnd/node_modules/react-redux/es/hooks/useStore.js", "../../react-beautiful-dnd/node_modules/react-redux/es/hooks/useReduxContext.js", "../../react-beautiful-dnd/node_modules/react-redux/es/hooks/useDispatch.js", "../../react-beautiful-dnd/node_modules/react-redux/es/hooks/useSelector.js", "../../react-beautiful-dnd/node_modules/react-redux/es/utils/reactBatchedUpdates.js", "../../react-beautiful-dnd/node_modules/react-redux/es/index.js", "../../use-memo-one/dist/use-memo-one.esm.js", "../../tiny-invariant/dist/esm/tiny-invariant.js", "../../css-box-model/dist/css-box-model.esm.js", "../../memoize-one/dist/memoize-one.esm.js", "../../raf-schd/dist/raf-schd.esm.js"], "sourcesContent": ["/** @license React v17.0.2\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_DEBUG_TRACING_MODE_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n}\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false;\nvar hasWarnedAboutDeprecatedIsConcurrentMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isConcurrentMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsConcurrentMode) {\n      hasWarnedAboutDeprecatedIsConcurrentMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isConcurrentMode() alias has been deprecated, ' + 'and will be removed in React 18+.');\n    }\n  }\n\n  return false;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "import React, { useLayoutEffect, useEffect, useRef, useState, useContext } from 'react';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport { createStore as createStore$1, applyMiddleware, compose, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { useMemo, useCallback } from 'use-memo-one';\nimport { getRect, expand, offset, withScroll, getBox, createBox, calculateBox } from 'css-box-model';\nimport memoizeOne from 'memoize-one';\nimport rafSchd from 'raf-schd';\nimport ReactDOM from 'react-dom';\n\nvar isProduction = process.env.NODE_ENV === 'production';\nvar spacesAndTabs = /[ \\t]{2,}/g;\nvar lineStartWithSpaces = /^[ \\t]*/gm;\n\nvar clean = function clean(value) {\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n};\n\nvar getDevMessage = function getDevMessage(message) {\n  return clean(\"\\n  %creact-beautiful-dnd\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development only message. It will be removed in production builds.\\n\");\n};\n\nvar getFormattedMessage = function getFormattedMessage(message) {\n  return [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\n};\nvar isDisabledFlag = '__react-beautiful-dnd-disable-dev-warnings';\nfunction log(type, message) {\n  var _console;\n\n  if (isProduction) {\n    return;\n  }\n\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n\n  (_console = console)[type].apply(_console, getFormattedMessage(message));\n}\nvar warning = log.bind(null, 'warn');\nvar error = log.bind(null, 'error');\n\nfunction noop() {}\n\nfunction getOptions(shared, fromBinding) {\n  return _extends({}, shared, {}, fromBinding);\n}\n\nfunction bindEvents(el, bindings, sharedOptions) {\n  var unbindings = bindings.map(function (binding) {\n    var options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(function (unbind) {\n      unbind();\n    });\n  };\n}\n\nvar isProduction$1 = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction RbdInvariant(message) {\n  this.message = message;\n}\n\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\n\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n\n  if (isProduction$1) {\n    throw new RbdInvariant(prefix);\n  } else {\n    throw new RbdInvariant(prefix + \": \" + (message || ''));\n  }\n}\n\nvar ErrorBoundary = function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n\n  function ErrorBoundary() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.callbacks = null;\n    _this.unbind = noop;\n\n    _this.onWindowError = function (event) {\n      var callbacks = _this.getCallbacks();\n\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(\"\\n        An error was caught by our window 'error' event listener while a drag was occurring.\\n        The active drag has been aborted.\\n      \") : void 0;\n      }\n\n      var err = event.error;\n\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n\n    _this.getCallbacks = function () {\n      if (!_this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n\n      return _this.callbacks;\n    };\n\n    _this.setCallbacks = function (callbacks) {\n      _this.callbacks = callbacks;\n    };\n\n    return _this;\n  }\n\n  var _proto = ErrorBoundary.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  };\n\n  _proto.componentDidCatch = function componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n\n      this.setState({});\n      return;\n    }\n\n    throw err;\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.unbind();\n  };\n\n  _proto.render = function render() {\n    return this.props.children(this.setCallbacks);\n  };\n\n  return ErrorBoundary;\n}(React.Component);\n\nvar dragHandleUsageInstructions = \"\\n  Press space bar to start a drag.\\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\\n  Some screen readers may require you to be in focus mode or to use your pass through key\\n\";\n\nvar position = function position(index) {\n  return index + 1;\n};\n\nvar onDragStart = function onDragStart(start) {\n  return \"\\n  You have lifted an item in position \" + position(start.source.index) + \"\\n\";\n};\n\nvar withLocation = function withLocation(source, destination) {\n  var isInHomeList = source.droppableId === destination.droppableId;\n  var startPosition = position(source.index);\n  var endPosition = position(destination.index);\n\n  if (isInHomeList) {\n    return \"\\n      You have moved the item from position \" + startPosition + \"\\n      to position \" + endPosition + \"\\n    \";\n  }\n\n  return \"\\n    You have moved the item from position \" + startPosition + \"\\n    in list \" + source.droppableId + \"\\n    to list \" + destination.droppableId + \"\\n    in position \" + endPosition + \"\\n  \";\n};\n\nvar withCombine = function withCombine(id, source, combine) {\n  var inHomeList = source.droppableId === combine.droppableId;\n\n  if (inHomeList) {\n    return \"\\n      The item \" + id + \"\\n      has been combined with \" + combine.draggableId;\n  }\n\n  return \"\\n      The item \" + id + \"\\n      in list \" + source.droppableId + \"\\n      has been combined with \" + combine.draggableId + \"\\n      in list \" + combine.droppableId + \"\\n    \";\n};\n\nvar onDragUpdate = function onDragUpdate(update) {\n  var location = update.destination;\n\n  if (location) {\n    return withLocation(update.source, location);\n  }\n\n  var combine = update.combine;\n\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n\n  return 'You are over an area that cannot be dropped on';\n};\n\nvar returnedToStart = function returnedToStart(source) {\n  return \"\\n  The item has returned to its starting position\\n  of \" + position(source.index) + \"\\n\";\n};\n\nvar onDragEnd = function onDragEnd(result) {\n  if (result.reason === 'CANCEL') {\n    return \"\\n      Movement cancelled.\\n      \" + returnedToStart(result.source) + \"\\n    \";\n  }\n\n  var location = result.destination;\n  var combine = result.combine;\n\n  if (location) {\n    return \"\\n      You have dropped the item.\\n      \" + withLocation(result.source, location) + \"\\n    \";\n  }\n\n  if (combine) {\n    return \"\\n      You have dropped the item.\\n      \" + withCombine(result.draggableId, result.source, combine) + \"\\n    \";\n  }\n\n  return \"\\n    The item has been dropped while not over a drop area.\\n    \" + returnedToStart(result.source) + \"\\n  \";\n};\n\nvar preset = {\n  dragHandleUsageInstructions: dragHandleUsageInstructions,\n  onDragStart: onDragStart,\n  onDragUpdate: onDragUpdate,\n  onDragEnd: onDragEnd\n};\n\nvar origin = {\n  x: 0,\n  y: 0\n};\nvar add = function add(point1, point2) {\n  return {\n    x: point1.x + point2.x,\n    y: point1.y + point2.y\n  };\n};\nvar subtract = function subtract(point1, point2) {\n  return {\n    x: point1.x - point2.x,\n    y: point1.y - point2.y\n  };\n};\nvar isEqual = function isEqual(point1, point2) {\n  return point1.x === point2.x && point1.y === point2.y;\n};\nvar negate = function negate(point) {\n  return {\n    x: point.x !== 0 ? -point.x : 0,\n    y: point.y !== 0 ? -point.y : 0\n  };\n};\nvar patch = function patch(line, value, otherValue) {\n  var _ref;\n\n  if (otherValue === void 0) {\n    otherValue = 0;\n  }\n\n  return _ref = {}, _ref[line] = value, _ref[line === 'x' ? 'y' : 'x'] = otherValue, _ref;\n};\nvar distance = function distance(point1, point2) {\n  return Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));\n};\nvar closest = function closest(target, points) {\n  return Math.min.apply(Math, points.map(function (point) {\n    return distance(target, point);\n  }));\n};\nvar apply = function apply(fn) {\n  return function (point) {\n    return {\n      x: fn(point.x),\n      y: fn(point.y)\n    };\n  };\n};\n\nvar executeClip = (function (frame, subject) {\n  var result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n\n  return result;\n});\n\nvar offsetByPosition = function offsetByPosition(spacing, point) {\n  return {\n    top: spacing.top + point.y,\n    left: spacing.left + point.x,\n    bottom: spacing.bottom + point.y,\n    right: spacing.right + point.x\n  };\n};\nvar getCorners = function getCorners(spacing) {\n  return [{\n    x: spacing.left,\n    y: spacing.top\n  }, {\n    x: spacing.right,\n    y: spacing.top\n  }, {\n    x: spacing.left,\n    y: spacing.bottom\n  }, {\n    x: spacing.right,\n    y: spacing.bottom\n  }];\n};\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nvar scroll = function scroll(target, frame) {\n  if (!frame) {\n    return target;\n  }\n\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\n\nvar increase = function increase(target, axis, withPlaceholder) {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    var _extends2;\n\n    return _extends({}, target, (_extends2 = {}, _extends2[axis.end] = target[axis.end] + withPlaceholder.increasedBy[axis.line], _extends2));\n  }\n\n  return target;\n};\n\nvar clip = function clip(target, frame) {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n\n  return getRect(target);\n};\n\nvar getSubject = (function (_ref) {\n  var page = _ref.page,\n      withPlaceholder = _ref.withPlaceholder,\n      axis = _ref.axis,\n      frame = _ref.frame;\n  var scrolled = scroll(page.marginBox, frame);\n  var increased = increase(scrolled, axis, withPlaceholder);\n  var clipped = clip(increased, frame);\n  return {\n    page: page,\n    withPlaceholder: withPlaceholder,\n    active: clipped\n  };\n});\n\nvar scrollDroppable = (function (droppable, newScroll) {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var scrollable = droppable.frame;\n  var scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  var scrollDisplacement = negate(scrollDiff);\n\n  var frame = _extends({}, scrollable, {\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  });\n\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame: frame\n  });\n\n  var result = _extends({}, droppable, {\n    frame: frame,\n    subject: subject\n  });\n\n  return result;\n});\n\nfunction isInteger(value) {\n  if (Number.isInteger) {\n    return Number.isInteger(value);\n  }\n\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\nfunction values(map) {\n  if (Object.values) {\n    return Object.values(map);\n  }\n\n  return Object.keys(map).map(function (key) {\n    return map[key];\n  });\n}\nfunction findIndex(list, predicate) {\n  if (list.findIndex) {\n    return list.findIndex(predicate);\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    if (predicate(list[i])) {\n      return i;\n    }\n  }\n\n  return -1;\n}\nfunction find(list, predicate) {\n  if (list.find) {\n    return list.find(predicate);\n  }\n\n  var index = findIndex(list, predicate);\n\n  if (index !== -1) {\n    return list[index];\n  }\n\n  return undefined;\n}\nfunction toArray(list) {\n  return Array.prototype.slice.call(list);\n}\n\nvar toDroppableMap = memoizeOne(function (droppables) {\n  return droppables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDraggableMap = memoizeOne(function (draggables) {\n  return draggables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDroppableList = memoizeOne(function (droppables) {\n  return values(droppables);\n});\nvar toDraggableList = memoizeOne(function (draggables) {\n  return values(draggables);\n});\n\nvar getDraggablesInsideDroppable = memoizeOne(function (droppableId, draggables) {\n  var result = toDraggableList(draggables).filter(function (draggable) {\n    return droppableId === draggable.descriptor.droppableId;\n  }).sort(function (a, b) {\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne(function (remove, list) {\n  return list.filter(function (item) {\n    return item.descriptor.id !== remove.descriptor.id;\n  });\n});\n\nvar moveToNextCombine = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      draggable = _ref.draggable,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      previousImpact = _ref.previousImpact;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var location = tryGetDestination(previousImpact);\n\n  if (!location) {\n    return null;\n  }\n\n  function getImpact(target) {\n    var at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return _extends({}, previousImpact, {\n      at: at\n    });\n  }\n\n  var all = previousImpact.displaced.all;\n  var closestId = all.length ? all[0] : null;\n\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n\n  var withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n\n    var last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n\n  var indexOfClosest = findIndex(withoutDraggable, function (d) {\n    return d.descriptor.id === closestId;\n  });\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant(false) : void 0;\n  var proposedIndex = indexOfClosest - 1;\n\n  if (proposedIndex < 0) {\n    return null;\n  }\n\n  var before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n});\n\nvar isHomeOf = (function (draggable, destination) {\n  return draggable.descriptor.droppableId === destination.descriptor.id;\n});\n\nvar noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nvar emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nvar noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\n\nvar isWithin = (function (lowerBound, upperBound) {\n  return function (value) {\n    return lowerBound <= value && value <= upperBound;\n  };\n});\n\nvar isPartiallyVisibleThroughFrame = (function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n\n    if (isContained) {\n      return true;\n    }\n\n    var isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    var isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    var isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n\n    if (isPartiallyContained) {\n      return true;\n    }\n\n    var isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    var isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    var isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n\n    var isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n});\n\nvar isTotallyVisibleThroughFrame = (function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n});\n\nvar vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nvar horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = (function (axis) {\n  return function (frame) {\n    var isWithinVertical = isWithin(frame.top, frame.bottom);\n    var isWithinHorizontal = isWithin(frame.left, frame.right);\n    return function (subject) {\n      if (axis === vertical) {\n        return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n      }\n\n      return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    };\n  };\n});\n\nvar getDroppableDisplaced = function getDroppableDisplaced(target, destination) {\n  var displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\n\nvar isVisibleInDroppable = function isVisibleInDroppable(target, destination, isVisibleThroughFrameFn) {\n  if (!destination.subject.active) {\n    return false;\n  }\n\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\n\nvar isVisibleInViewport = function isVisibleInViewport(target, viewport, isVisibleThroughFrameFn) {\n  return isVisibleThroughFrameFn(viewport)(target);\n};\n\nvar isVisible = function isVisible(_ref) {\n  var toBeDisplaced = _ref.target,\n      destination = _ref.destination,\n      viewport = _ref.viewport,\n      withDroppableDisplacement = _ref.withDroppableDisplacement,\n      isVisibleThroughFrameFn = _ref.isVisibleThroughFrameFn;\n  var displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\n\nvar isPartiallyVisible = function isPartiallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisible = function isTotallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisibleOnAxis = function isTotallyVisibleOnAxis(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n  }));\n};\n\nvar getShouldAnimate = function getShouldAnimate(id, last, forceShouldAnimate) {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n\n  if (!last) {\n    return true;\n  }\n\n  var invisible = last.invisible,\n      visible = last.visible;\n\n  if (invisible[id]) {\n    return false;\n  }\n\n  var previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\n\nfunction getTarget(draggable, displacedBy) {\n  var marginBox = draggable.page.marginBox;\n  var expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\n\nfunction getDisplacementGroups(_ref) {\n  var afterDragging = _ref.afterDragging,\n      destination = _ref.destination,\n      displacedBy = _ref.displacedBy,\n      viewport = _ref.viewport,\n      forceShouldAnimate = _ref.forceShouldAnimate,\n      last = _ref.last;\n  return afterDragging.reduce(function process(groups, draggable) {\n    var target = getTarget(draggable, displacedBy);\n    var id = draggable.descriptor.id;\n    groups.all.push(id);\n    var isVisible = isPartiallyVisible({\n      target: target,\n      destination: destination,\n      viewport: viewport,\n      withDroppableDisplacement: true\n    });\n\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n\n    var shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    var displacement = {\n      draggableId: id,\n      shouldAnimate: shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n\n  var indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\n\nfunction goAtEnd(_ref) {\n  var insideDestination = _ref.insideDestination,\n      inHomeList = _ref.inHomeList,\n      displacedBy = _ref.displacedBy,\n      destination = _ref.destination;\n  var newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList: inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\n\nfunction calculateReorderImpact(_ref2) {\n  var draggable = _ref2.draggable,\n      insideDestination = _ref2.insideDestination,\n      destination = _ref2.destination,\n      viewport = _ref2.viewport,\n      displacedBy = _ref2.displacedBy,\n      last = _ref2.last,\n      index = _ref2.index,\n      forceShouldAnimate = _ref2.forceShouldAnimate;\n  var inHomeList = isHomeOf(draggable, destination);\n\n  if (index == null) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n\n  var match = find(insideDestination, function (item) {\n    return item.descriptor.index === index;\n  });\n\n  if (!match) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var sliceFrom = insideDestination.indexOf(match);\n  var impacted = withoutDragging.slice(sliceFrom);\n  var displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination: destination,\n    displacedBy: displacedBy,\n    last: last,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate\n  });\n  return {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      combine = _ref.combine,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var combineId = combine.draggableId;\n  var combineWith = draggables[combineId];\n  var combineWithIndex = combineWith.descriptor.index;\n  var didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n\n    return combineWithIndex - 1;\n  }\n\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n\n  return combineWithIndex;\n});\n\nvar fromReorder = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      isInHomeList = _ref.isInHomeList,\n      insideDestination = _ref.insideDestination,\n      location = _ref.location;\n\n  if (!insideDestination.length) {\n    return null;\n  }\n\n  var currentIndex = location.index;\n  var proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  var firstIndex = insideDestination[0].descriptor.index;\n  var lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  var upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n\n  return proposedIndex;\n});\n\nvar moveToNextIndex = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      isInHomeList = _ref.isInHomeList,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant(false) : void 0;\n\n  if (wasAt.type === 'REORDER') {\n    var _newIndex = fromReorder({\n      isMovingForward: isMovingForward,\n      isInHomeList: isInHomeList,\n      location: wasAt.destination,\n      insideDestination: insideDestination\n    });\n\n    if (_newIndex == null) {\n      return null;\n    }\n\n    return calculateReorderImpact({\n      draggable: draggable,\n      insideDestination: insideDestination,\n      destination: destination,\n      viewport: viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: _newIndex\n    });\n  }\n\n  var newIndex = fromCombine({\n    isMovingForward: isMovingForward,\n    destination: destination,\n    displaced: previousImpact.displaced,\n    draggables: draggables,\n    combine: wasAt.combine,\n    afterCritical: afterCritical\n  });\n\n  if (newIndex == null) {\n    return null;\n  }\n\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n});\n\nvar getCombinedItemDisplacement = (function (_ref) {\n  var displaced = _ref.displaced,\n      afterCritical = _ref.afterCritical,\n      combineWith = _ref.combineWith,\n      displacedBy = _ref.displacedBy;\n  var isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n\n  return isDisplaced ? displacedBy.point : origin;\n});\n\nvar whenCombining = (function (_ref) {\n  var afterCritical = _ref.afterCritical,\n      impact = _ref.impact,\n      draggables = _ref.draggables;\n  var combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var combineWith = combine.draggableId;\n  var center = draggables[combineWith].page.borderBox.center;\n  var displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical: afterCritical,\n    combineWith: combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n});\n\nvar distanceFromStartToBorderBoxCenter = function distanceFromStartToBorderBoxCenter(axis, box) {\n  return box.margin[axis.start] + box.borderBox[axis.size] / 2;\n};\n\nvar distanceFromEndToBorderBoxCenter = function distanceFromEndToBorderBoxCenter(axis, box) {\n  return box.margin[axis.end] + box.borderBox[axis.size] / 2;\n};\n\nvar getCrossAxisBorderBoxCenter = function getCrossAxisBorderBoxCenter(axis, target, isMoving) {\n  return target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\n};\n\nvar goAfter = function goAfter(_ref) {\n  var axis = _ref.axis,\n      moveRelativeTo = _ref.moveRelativeTo,\n      isMoving = _ref.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goBefore = function goBefore(_ref2) {\n  var axis = _ref2.axis,\n      moveRelativeTo = _ref2.moveRelativeTo,\n      isMoving = _ref2.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goIntoStart = function goIntoStart(_ref3) {\n  var axis = _ref3.axis,\n      moveInto = _ref3.moveInto,\n      isMoving = _ref3.isMoving;\n  return patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n};\n\nvar whenReordering = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      droppable = _ref.droppable,\n      afterCritical = _ref.afterCritical;\n  var insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var draggablePage = draggable.page;\n  var axis = droppable.axis;\n\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis: axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n\n  var displaced = impact.displaced,\n      displacedBy = impact.displacedBy;\n  var closestAfter = displaced.all[0];\n\n  if (closestAfter) {\n    var closest = draggables[closestAfter];\n\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis: axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n\n    var withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis: axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n\n  var last = insideDestination[insideDestination.length - 1];\n\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    var page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis: axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n\n  return goAfter({\n    axis: axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n});\n\nvar withDroppableDisplacement = (function (droppable, point) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return point;\n  }\n\n  return add(point, frame.scroll.diff.displacement);\n});\n\nvar getResultWithoutDroppableDisplacement = function getResultWithoutDroppableDisplacement(_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      droppable = _ref.droppable,\n      draggables = _ref.draggables,\n      afterCritical = _ref.afterCritical;\n  var original = draggable.page.borderBox.center;\n  var at = impact.at;\n\n  if (!droppable) {\n    return original;\n  }\n\n  if (!at) {\n    return original;\n  }\n\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact: impact,\n      draggable: draggable,\n      draggables: draggables,\n      droppable: droppable,\n      afterCritical: afterCritical\n    });\n  }\n\n  return whenCombining({\n    impact: impact,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n};\n\nvar getPageBorderBoxCenterFromImpact = (function (args) {\n  var withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  var droppable = args.droppable;\n  var withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n});\n\nvar scrollViewport = (function (viewport, newScroll) {\n  var diff = subtract(newScroll, viewport.scroll.initial);\n  var displacement = negate(diff);\n  var frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  var updated = {\n    frame: frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement: displacement\n      }\n    }\n  };\n  return updated;\n});\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\n\nfunction tryGetVisible(id, groups) {\n  for (var i = 0; i < groups.length; i++) {\n    var displacement = groups[i].visible[id];\n\n    if (displacement) {\n      return displacement;\n    }\n  }\n\n  return null;\n}\n\nvar speculativelyIncrease = (function (_ref) {\n  var impact = _ref.impact,\n      viewport = _ref.viewport,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      maxScrollChange = _ref.maxScrollChange;\n  var scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  var scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  var last = impact.displaced;\n  var withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var invisible = {};\n  var visible = {};\n  var groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(function (id) {\n    var displacement = tryGetVisible(id, groups);\n\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n\n    invisible[id] = true;\n  });\n\n  var newImpact = _extends({}, impact, {\n    displaced: {\n      all: last.all,\n      invisible: invisible,\n      visible: visible\n    }\n  });\n\n  return newImpact;\n});\n\nvar withViewportDisplacement = (function (viewport, point) {\n  return add(viewport.scroll.diff.displacement, point);\n});\n\nvar getClientFromPageBorderBoxCenter = (function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      draggable = _ref.draggable,\n      viewport = _ref.viewport;\n  var withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  var offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n});\n\nvar isTotallyVisibleInNewLocation = (function (_ref) {\n  var draggable = _ref.draggable,\n      destination = _ref.destination,\n      newPageBorderBoxCenter = _ref.newPageBorderBoxCenter,\n      viewport = _ref.viewport,\n      withDroppableDisplacement = _ref.withDroppableDisplacement,\n      _ref$onlyOnMainAxis = _ref.onlyOnMainAxis,\n      onlyOnMainAxis = _ref$onlyOnMainAxis === void 0 ? false : _ref$onlyOnMainAxis;\n  var changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  var shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  var args = {\n    target: shifted,\n    destination: destination,\n    withDroppableDisplacement: withDroppableDisplacement,\n    viewport: viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n});\n\nvar moveToNextPlace = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      draggable = _ref.draggable,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      previousClientSelection = _ref.previousClientSelection,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isEnabled) {\n    return null;\n  }\n\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var isInHomeList = isHomeOf(draggable, destination);\n  var impact = moveToNextCombine({\n    isMovingForward: isMovingForward,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact\n  }) || moveToNextIndex({\n    isMovingForward: isMovingForward,\n    isInHomeList: isInHomeList,\n    draggable: draggable,\n    draggables: draggables,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n\n  if (!impact) {\n    return null;\n  }\n\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable: draggable,\n    destination: destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n\n  if (isVisibleInNewLocation) {\n    var clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter: pageBorderBoxCenter,\n      draggable: draggable,\n      viewport: viewport\n    });\n    return {\n      clientSelection: clientSelection,\n      impact: impact,\n      scrollJumpRequest: null\n    };\n  }\n\n  var distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  var cautious = speculativelyIncrease({\n    impact: impact,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n});\n\nvar getKnownActive = function getKnownActive(droppable) {\n  var rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant(false) : void 0;\n  return rect;\n};\n\nvar getBestCrossAxisDroppable = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      source = _ref.source,\n      droppables = _ref.droppables,\n      viewport = _ref.viewport;\n  var active = source.subject.active;\n\n  if (!active) {\n    return null;\n  }\n\n  var axis = source.axis;\n  var isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  var candidates = toDroppableList(droppables).filter(function (droppable) {\n    return droppable !== source;\n  }).filter(function (droppable) {\n    return droppable.isEnabled;\n  }).filter(function (droppable) {\n    return Boolean(droppable.subject.active);\n  }).filter(function (droppable) {\n    return isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable));\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n    var isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort(function (a, b) {\n    var first = getKnownActive(a)[axis.crossAxisStart];\n    var second = getKnownActive(b)[axis.crossAxisStart];\n\n    if (isMovingForward) {\n      return first - second;\n    }\n\n    return second - first;\n  }).filter(function (droppable, index, array) {\n    return getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart];\n  });\n\n  if (!candidates.length) {\n    return null;\n  }\n\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n\n  var contains = candidates.filter(function (droppable) {\n    var isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n\n  if (contains.length === 1) {\n    return contains[0];\n  }\n\n  if (contains.length > 1) {\n    return contains.sort(function (a, b) {\n      return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n    })[0];\n  }\n\n  return candidates.sort(function (a, b) {\n    var first = closest(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    var second = closest(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n\n    if (first !== second) {\n      return first - second;\n    }\n\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n});\n\nvar getCurrentPageBorderBoxCenter = function getCurrentPageBorderBoxCenter(draggable, afterCritical) {\n  var original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nvar getCurrentPageBorderBox = function getCurrentPageBorderBox(draggable, afterCritical) {\n  var original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = (function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      viewport = _ref.viewport,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      afterCritical = _ref.afterCritical;\n  var sorted = insideDestination.filter(function (draggable) {\n    return isTotallyVisible({\n      target: getCurrentPageBorderBox(draggable, afterCritical),\n      destination: destination,\n      viewport: viewport.frame,\n      withDroppableDisplacement: true\n    });\n  }).sort(function (a, b) {\n    var distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    var distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n});\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  var displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nvar getRequiredGrowthForPlaceholder = function getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables) {\n  var axis = droppable.axis;\n\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n\n  var availableSpace = droppable.subject.page.contentBox[axis.size];\n  var insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var spaceUsed = insideDroppable.reduce(function (sum, dimension) {\n    return sum + dimension.client.marginBox[axis.size];\n  }, 0);\n  var requiredSpace = spaceUsed + placeholderSize[axis.line];\n  var needsToGrowBy = requiredSpace - availableSpace;\n\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n\n  return patch(axis.line, needsToGrowBy);\n};\n\nvar withMaxScroll = function withMaxScroll(frame, max) {\n  return _extends({}, frame, {\n    scroll: _extends({}, frame.scroll, {\n      max: max\n    })\n  });\n};\n\nvar addPlaceholder = function addPlaceholder(droppable, draggable, draggables) {\n  var frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant(false) : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant(false) : void 0;\n  var placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  var requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  var added = {\n    placeholderSize: placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n\n  if (!frame) {\n    var _subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n\n    return _extends({}, droppable, {\n      subject: _subject\n    });\n  }\n\n  var maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  var newFrame = withMaxScroll(frame, maxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\nvar removePlaceholder = function removePlaceholder(droppable) {\n  var added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant(false) : void 0;\n  var frame = droppable.frame;\n\n  if (!frame) {\n    var _subject2 = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n\n    return _extends({}, droppable, {\n      subject: _subject2\n    });\n  }\n\n  var oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant(false) : void 0;\n  var newFrame = withMaxScroll(frame, oldMaxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\n\nvar moveToNewDroppable = (function (_ref) {\n  var previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      moveRelativeTo = _ref.moveRelativeTo,\n      insideDestination = _ref.insideDestination,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n\n    var proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    var proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable: draggable,\n      droppable: destination,\n      draggables: draggables,\n      afterCritical: afterCritical\n    });\n    var withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable: draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n\n  var isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n\n  var proposedIndex = function () {\n    var relativeTo = moveRelativeTo.descriptor.index;\n\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n\n    return relativeTo + 1;\n  }();\n\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    displacedBy: displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n});\n\nvar moveCrossAxis = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      draggable = _ref.draggable,\n      isOver = _ref.isOver,\n      draggables = _ref.draggables,\n      droppables = _ref.droppables,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var destination = getBestCrossAxisDroppable({\n    isMovingForward: isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables: droppables,\n    viewport: viewport\n  });\n\n  if (!destination) {\n    return null;\n  }\n\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport: viewport,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  });\n  var impact = moveToNewDroppable({\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    destination: destination,\n    draggable: draggable,\n    draggables: draggables,\n    moveRelativeTo: moveRelativeTo,\n    insideDestination: insideDestination,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n\n  if (!impact) {\n    return null;\n  }\n\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n  return {\n    clientSelection: clientSelection,\n    impact: impact,\n    scrollJumpRequest: null\n  };\n});\n\nvar whatIsDraggedOver = (function (impact) {\n  var at = impact.at;\n\n  if (!at) {\n    return null;\n  }\n\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n\n  return at.combine.droppableId;\n});\n\nvar getDroppableOver = function getDroppableOver(impact, droppables) {\n  var id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\n\nvar moveInDirection = (function (_ref) {\n  var state = _ref.state,\n      type = _ref.type;\n  var isActuallyOver = getDroppableOver(state.impact, state.dimensions.droppables);\n  var isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  var home = state.dimensions.droppables[state.critical.droppable.id];\n  var isOver = isActuallyOver || home;\n  var direction = isOver.axis.direction;\n  var isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n\n  var isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  var _state$dimensions = state.dimensions,\n      draggables = _state$dimensions.draggables,\n      droppables = _state$dimensions.droppables;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    destination: isOver,\n    draggables: draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    isOver: isOver,\n    draggables: draggables,\n    droppables: droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n});\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\n\nfunction getFurthestAway(_ref) {\n  var pageBorderBox = _ref.pageBorderBox,\n      draggable = _ref.draggable,\n      candidates = _ref.candidates;\n  var startCenter = draggable.page.borderBox.center;\n  var sorted = candidates.map(function (candidate) {\n    var axis = candidate.axis;\n    var target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort(function (a, b) {\n    return b.distance - a.distance;\n  });\n  return sorted[0] ? sorted[0].id : null;\n}\n\nfunction getDroppableOver$1(_ref2) {\n  var pageBorderBox = _ref2.pageBorderBox,\n      draggable = _ref2.draggable,\n      droppables = _ref2.droppables;\n  var candidates = toDroppableList(droppables).filter(function (item) {\n    if (!item.isEnabled) {\n      return false;\n    }\n\n    var active = item.subject.active;\n\n    if (!active) {\n      return false;\n    }\n\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n\n    var axis = item.axis;\n    var childCenter = active.center[axis.crossAxisLine];\n    var crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    var crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    var isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    var isStartContained = isContained(crossAxisStart);\n    var isEndContained = isContained(crossAxisEnd);\n\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n\n    return crossAxisEnd > childCenter;\n  });\n\n  if (!candidates.length) {\n    return null;\n  }\n\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n\n  return getFurthestAway({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    candidates: candidates\n  });\n}\n\nvar offsetRectByPosition = function offsetRectByPosition(rect, point) {\n  return getRect(offsetByPosition(rect, point));\n};\n\nvar withDroppableScroll = (function (droppable, area) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return area;\n  }\n\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n});\n\nfunction getIsDisplaced(_ref) {\n  var displaced = _ref.displaced,\n      id = _ref.id;\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex(_ref) {\n  var draggable = _ref.draggable,\n      closest = _ref.closest,\n      inHomeList = _ref.inHomeList;\n\n  if (!closest) {\n    return null;\n  }\n\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n\n  return closest.descriptor.index;\n}\n\nvar getReorderImpact = (function (_ref2) {\n  var targetRect = _ref2.pageBorderBoxWithDroppableScroll,\n      draggable = _ref2.draggable,\n      destination = _ref2.destination,\n      insideDestination = _ref2.insideDestination,\n      last = _ref2.last,\n      viewport = _ref2.viewport,\n      afterCritical = _ref2.afterCritical;\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var closest = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childCenter = child.page.borderBox.center[axis.line];\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: last,\n      id: id\n    });\n\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n\n      return targetStart < childCenter - displacement;\n    }\n\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n\n    return targetStart < childCenter;\n  });\n  var newIndex = atIndex({\n    draggable: draggable,\n    closest: closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: last,\n    displacedBy: displacedBy,\n    index: newIndex\n  });\n});\n\nvar combineThresholdDivisor = 4;\nvar getCombineImpact = (function (_ref) {\n  var draggable = _ref.draggable,\n      targetRect = _ref.pageBorderBoxWithDroppableScroll,\n      previousImpact = _ref.previousImpact,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var combineWith = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childRect = child.page.borderBox;\n    var childSize = childRect[axis.size];\n    var threshold = childSize / combineThresholdDivisor;\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id: id\n    });\n\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n\n  if (!combineWith) {\n    return null;\n  }\n\n  var impact = {\n    displacedBy: displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n});\n\nvar getDragImpact = (function (_ref) {\n  var pageOffset = _ref.pageOffset,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      droppables = _ref.droppables,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  var destinationId = getDroppableOver$1({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    droppables: droppables\n  });\n\n  if (!destinationId) {\n    return noImpact;\n  }\n\n  var destination = droppables[destinationId];\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    previousImpact: previousImpact,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    last: previousImpact.displaced,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n});\n\nvar patchDroppableMap = (function (droppables, updated) {\n  var _extends2;\n\n  return _extends({}, droppables, (_extends2 = {}, _extends2[updated.descriptor.id] = updated, _extends2));\n});\n\nvar clearUnusedPlaceholder = function clearUnusedPlaceholder(_ref) {\n  var previousImpact = _ref.previousImpact,\n      impact = _ref.impact,\n      droppables = _ref.droppables;\n  var last = whatIsDraggedOver(previousImpact);\n  var now = whatIsDraggedOver(impact);\n\n  if (!last) {\n    return droppables;\n  }\n\n  if (last === now) {\n    return droppables;\n  }\n\n  var lastDroppable = droppables[last];\n\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n\n  var updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\n\nvar recomputePlaceholders = (function (_ref2) {\n  var draggable = _ref2.draggable,\n      draggables = _ref2.draggables,\n      droppables = _ref2.droppables,\n      previousImpact = _ref2.previousImpact,\n      impact = _ref2.impact;\n  var cleaned = clearUnusedPlaceholder({\n    previousImpact: previousImpact,\n    impact: impact,\n    droppables: droppables\n  });\n  var isOver = whatIsDraggedOver(impact);\n\n  if (!isOver) {\n    return cleaned;\n  }\n\n  var droppable = droppables[isOver];\n\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n\n  var patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n});\n\nvar update = (function (_ref) {\n  var state = _ref.state,\n      forcedClientSelection = _ref.clientSelection,\n      forcedDimensions = _ref.dimensions,\n      forcedViewport = _ref.viewport,\n      forcedImpact = _ref.impact,\n      scrollJumpRequest = _ref.scrollJumpRequest;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var clientSelection = forcedClientSelection || state.current.client.selection;\n  var offset = subtract(clientSelection, state.initial.client.selection);\n  var client = {\n    offset: offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  var page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  var current = {\n    client: client,\n    page: page\n  };\n\n  if (state.phase === 'COLLECTING') {\n    return _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      dimensions: dimensions,\n      viewport: viewport,\n      current: current\n    });\n  }\n\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable: draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  var withUpdatedPlaceholders = recomputePlaceholders({\n    draggable: draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n\n  var result = _extends({}, state, {\n    current: current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport: viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  });\n\n  return result;\n});\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\n\nvar recompute = (function (_ref) {\n  var impact = _ref.impact,\n      viewport = _ref.viewport,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      forceShouldAnimate = _ref.forceShouldAnimate;\n  var last = impact.displaced;\n  var afterDragging = getDraggables$1(last.all, draggables);\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate,\n    last: last\n  });\n  return _extends({}, impact, {\n    displaced: displaced\n  });\n});\n\nvar getClientBorderBoxCenter = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      droppable = _ref.droppable,\n      draggables = _ref.draggables,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    droppable: droppable,\n    afterCritical: afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n});\n\nvar refreshSnap = (function (_ref) {\n  var state = _ref.state,\n      forcedDimensions = _ref.dimensions,\n      forcedViewport = _ref.viewport;\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var needsVisibilityCheck = state.impact;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var draggables = dimensions.draggables,\n      droppables = dimensions.droppables;\n  var draggable = draggables[state.critical.draggable.id];\n  var isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant(false) : void 0;\n  var destination = droppables[isOver];\n  var impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables\n  });\n  var clientSelection = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact: impact,\n    clientSelection: clientSelection,\n    state: state,\n    dimensions: dimensions,\n    viewport: viewport\n  });\n});\n\nvar getHomeLocation = (function (descriptor) {\n  return {\n    index: descriptor.index,\n    droppableId: descriptor.droppableId\n  };\n});\n\nvar getLiftEffect = (function (_ref) {\n  var draggable = _ref.draggable,\n      home = _ref.home,\n      draggables = _ref.draggables,\n      viewport = _ref.viewport;\n  var displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  var insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  var rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant(false) : void 0;\n  var afterDragging = insideHome.slice(rawIndex + 1);\n  var effected = afterDragging.reduce(function (previous, item) {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  var afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy: displacedBy,\n    effected: effected\n  };\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: home,\n    displacedBy: displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  var impact = {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact: impact,\n    afterCritical: afterCritical\n  };\n});\n\nvar patchDimensionMap = (function (dimensions, updated) {\n  return {\n    draggables: dimensions.draggables,\n    droppables: patchDroppableMap(dimensions.droppables, updated)\n  };\n});\n\nvar start = function start(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nvar finish = function finish(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = (function (_ref) {\n  var draggable = _ref.draggable,\n      offset$1 = _ref.offset,\n      initialWindowScroll = _ref.initialWindowScroll;\n  var client = offset(draggable.client, offset$1);\n  var page = withScroll(client, initialWindowScroll);\n\n  var moved = _extends({}, draggable, {\n    placeholder: _extends({}, draggable.placeholder, {\n      client: client\n    }),\n    client: client,\n    page: page\n  });\n\n  return moved;\n});\n\nvar getFrame = (function (droppable) {\n  var frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant(false) : void 0;\n  return frame;\n});\n\nvar adjustAdditionsForScrollChanges = (function (_ref) {\n  var additions = _ref.additions,\n      updatedDroppables = _ref.updatedDroppables,\n      viewport = _ref.viewport;\n  var windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(function (draggable) {\n    var droppableId = draggable.descriptor.droppableId;\n    var modified = updatedDroppables[droppableId];\n    var frame = getFrame(modified);\n    var droppableScrollChange = frame.scroll.diff.value;\n    var totalChange = add(windowScrollChange, droppableScrollChange);\n    var moved = offsetDraggable({\n      draggable: draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n});\n\nvar publishWhileDraggingInVirtual = (function (_ref) {\n  var state = _ref.state,\n      published = _ref.published;\n  start();\n  var withScrollChange = published.modified.map(function (update) {\n    var existing = state.dimensions.droppables[update.droppableId];\n    var scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n\n  var droppables = _extends({}, state.dimensions.droppables, {}, toDroppableMap(withScrollChange));\n\n  var updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n\n  var draggables = _extends({}, state.dimensions.draggables, {}, updatedAdditions);\n\n  published.removals.forEach(function (id) {\n    delete draggables[id];\n  });\n  var dimensions = {\n    droppables: droppables,\n    draggables: draggables\n  };\n  var wasOverId = whatIsDraggedOver(state.impact);\n  var wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var home = dimensions.droppables[state.critical.droppable.id];\n\n  var _getLiftEffect = getLiftEffect({\n    draggable: draggable,\n    home: home,\n    draggables: draggables,\n    viewport: state.viewport\n  }),\n      onLiftImpact = _getLiftEffect.impact,\n      afterCritical = _getLiftEffect.afterCritical;\n\n  var previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  var impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: previousImpact,\n    viewport: state.viewport,\n    afterCritical: afterCritical\n  });\n  finish();\n\n  var draggingState = _extends({\n    phase: 'DRAGGING'\n  }, state, {\n    phase: 'DRAGGING',\n    impact: impact,\n    onLiftImpact: onLiftImpact,\n    dimensions: dimensions,\n    afterCritical: afterCritical,\n    forceShouldAnimate: false\n  });\n\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n\n  var dropPending = _extends({\n    phase: 'DROP_PENDING'\n  }, draggingState, {\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  });\n\n  return dropPending;\n});\n\nvar isSnapping = function isSnapping(state) {\n  return state.movementMode === 'SNAP';\n};\n\nvar postDroppableChange = function postDroppableChange(state, updated, isEnabledChanging) {\n  var dimensions = patchDimensionMap(state.dimensions, updated);\n\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state: state,\n      dimensions: dimensions\n    });\n  }\n\n  return refreshSnap({\n    state: state,\n    dimensions: dimensions\n  });\n};\n\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      scrollJumpRequest: null\n    });\n  }\n\n  return state;\n}\n\nvar idle = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = (function (state, action) {\n  if (state === void 0) {\n    state = idle;\n  }\n\n  if (action.type === 'FLUSH') {\n    return _extends({}, idle, {\n      shouldFlush: true\n    });\n  }\n\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant(false) : void 0;\n    var _action$payload = action.payload,\n        critical = _action$payload.critical,\n        clientSelection = _action$payload.clientSelection,\n        viewport = _action$payload.viewport,\n        dimensions = _action$payload.dimensions,\n        movementMode = _action$payload.movementMode;\n    var draggable = dimensions.draggables[critical.draggable.id];\n    var home = dimensions.droppables[critical.droppable.id];\n    var client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    var initial = {\n      client: client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    var isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(function (item) {\n      return !item.isFixedOnPage;\n    });\n\n    var _getLiftEffect = getLiftEffect({\n      draggable: draggable,\n      home: home,\n      draggables: dimensions.draggables,\n      viewport: viewport\n    }),\n        impact = _getLiftEffect.impact,\n        afterCritical = _getLiftEffect.afterCritical;\n\n    var result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical: critical,\n      movementMode: movementMode,\n      dimensions: dimensions,\n      initial: initial,\n      current: initial,\n      isWindowScrollAllowed: isWindowScrollAllowed,\n      impact: impact,\n      afterCritical: afterCritical,\n      onLiftImpact: impact,\n      viewport: viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Collection cannot start from phase \" + state.phase) : invariant(false) : void 0;\n\n    var _result = _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      phase: 'COLLECTING'\n    });\n\n    return _result;\n  }\n\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unexpected \" + action.type + \" received in phase \" + state.phase) : invariant(false) : void 0;\n    return publishWhileDraggingInVirtual({\n      state: state,\n      published: action.payload\n    });\n  }\n\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _clientSelection = action.payload.client;\n\n    if (isEqual(_clientSelection, state.current.client.selection)) {\n      return state;\n    }\n\n    return update({\n      state: state,\n      clientSelection: _clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload2 = action.payload,\n        id = _action$payload2.id,\n        newScroll = _action$payload2.newScroll;\n    var target = state.dimensions.droppables[id];\n\n    if (!target) {\n      return state;\n    }\n\n    var scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload3 = action.payload,\n        _id = _action$payload3.id,\n        isEnabled = _action$payload3.isEnabled;\n    var _target = state.dimensions.droppables[_id];\n    !_target ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id + \"] to toggle its enabled state\") : invariant(false) : void 0;\n    !(_target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isEnabled to \" + String(isEnabled) + \"\\n      but it is already \" + String(_target.isEnabled)) : invariant(false) : void 0;\n\n    var updated = _extends({}, _target, {\n      isEnabled: isEnabled\n    });\n\n    return postDroppableChange(state, updated, true);\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload4 = action.payload,\n        _id2 = _action$payload4.id,\n        isCombineEnabled = _action$payload4.isCombineEnabled;\n    var _target2 = state.dimensions.droppables[_id2];\n    !_target2 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id2 + \"] to toggle its isCombineEnabled state\") : invariant(false) : void 0;\n    !(_target2.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isCombineEnabled to \" + String(isCombineEnabled) + \"\\n      but it is already \" + String(_target2.isCombineEnabled)) : invariant(false) : void 0;\n\n    var _updated = _extends({}, _target2, {\n      isCombineEnabled: isCombineEnabled\n    });\n\n    return postDroppableChange(state, _updated, true);\n  }\n\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot move by window in phase \" + state.phase) : invariant(false) : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant(false) : void 0;\n    var _newScroll = action.payload.newScroll;\n\n    if (isEqual(state.viewport.scroll.current, _newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n\n    var _viewport = scrollViewport(state.viewport, _newScroll);\n\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state: state,\n        viewport: _viewport\n      });\n    }\n\n    return update({\n      state: state,\n      viewport: _viewport\n    });\n  }\n\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n\n    var maxScroll = action.payload.maxScroll;\n\n    if (isEqual(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n\n    var withMaxScroll = _extends({}, state.viewport, {\n      scroll: _extends({}, state.viewport.scroll, {\n        max: maxScroll\n      })\n    });\n\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      viewport: withMaxScroll\n    });\n  }\n\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" received while not in DRAGGING phase\") : invariant(false) : void 0;\n\n    var _result2 = moveInDirection({\n      state: state,\n      type: action.type\n    });\n\n    if (!_result2) {\n      return state;\n    }\n\n    return update({\n      state: state,\n      impact: _result2.impact,\n      clientSelection: _result2.clientSelection,\n      scrollJumpRequest: _result2.scrollJumpRequest\n    });\n  }\n\n  if (action.type === 'DROP_PENDING') {\n    var reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant(false) : void 0;\n\n    var newState = _extends({\n      phase: 'DROP_PENDING'\n    }, state, {\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason: reason\n    });\n\n    return newState;\n  }\n\n  if (action.type === 'DROP_ANIMATE') {\n    var _action$payload5 = action.payload,\n        completed = _action$payload5.completed,\n        dropDuration = _action$payload5.dropDuration,\n        newHomeClientOffset = _action$payload5.newHomeClientOffset;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot animate drop from phase \" + state.phase) : invariant(false) : void 0;\n    var _result3 = {\n      phase: 'DROP_ANIMATING',\n      completed: completed,\n      dropDuration: dropDuration,\n      newHomeClientOffset: newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return _result3;\n  }\n\n  if (action.type === 'DROP_COMPLETE') {\n    var _completed = action.payload.completed;\n    return {\n      phase: 'IDLE',\n      completed: _completed,\n      shouldFlush: false\n    };\n  }\n\n  return state;\n});\n\nvar beforeInitialCapture = function beforeInitialCapture(args) {\n  return {\n    type: 'BEFORE_INITIAL_CAPTURE',\n    payload: args\n  };\n};\nvar lift = function lift(args) {\n  return {\n    type: 'LIFT',\n    payload: args\n  };\n};\nvar initialPublish = function initialPublish(args) {\n  return {\n    type: 'INITIAL_PUBLISH',\n    payload: args\n  };\n};\nvar publishWhileDragging = function publishWhileDragging(args) {\n  return {\n    type: 'PUBLISH_WHILE_DRAGGING',\n    payload: args\n  };\n};\nvar collectionStarting = function collectionStarting() {\n  return {\n    type: 'COLLECTION_STARTING',\n    payload: null\n  };\n};\nvar updateDroppableScroll = function updateDroppableScroll(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_SCROLL',\n    payload: args\n  };\n};\nvar updateDroppableIsEnabled = function updateDroppableIsEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_ENABLED',\n    payload: args\n  };\n};\nvar updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n    payload: args\n  };\n};\nvar move = function move(args) {\n  return {\n    type: 'MOVE',\n    payload: args\n  };\n};\nvar moveByWindowScroll = function moveByWindowScroll(args) {\n  return {\n    type: 'MOVE_BY_WINDOW_SCROLL',\n    payload: args\n  };\n};\nvar updateViewportMaxScroll = function updateViewportMaxScroll(args) {\n  return {\n    type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n    payload: args\n  };\n};\nvar moveUp = function moveUp() {\n  return {\n    type: 'MOVE_UP',\n    payload: null\n  };\n};\nvar moveDown = function moveDown() {\n  return {\n    type: 'MOVE_DOWN',\n    payload: null\n  };\n};\nvar moveRight = function moveRight() {\n  return {\n    type: 'MOVE_RIGHT',\n    payload: null\n  };\n};\nvar moveLeft = function moveLeft() {\n  return {\n    type: 'MOVE_LEFT',\n    payload: null\n  };\n};\nvar flush = function flush() {\n  return {\n    type: 'FLUSH',\n    payload: null\n  };\n};\nvar animateDrop = function animateDrop(args) {\n  return {\n    type: 'DROP_ANIMATE',\n    payload: args\n  };\n};\nvar completeDrop = function completeDrop(args) {\n  return {\n    type: 'DROP_COMPLETE',\n    payload: args\n  };\n};\nvar drop = function drop(args) {\n  return {\n    type: 'DROP',\n    payload: args\n  };\n};\nvar dropPending = function dropPending(args) {\n  return {\n    type: 'DROP_PENDING',\n    payload: args\n  };\n};\nvar dropAnimationFinished = function dropAnimationFinished() {\n  return {\n    type: 'DROP_ANIMATION_FINISHED',\n    payload: null\n  };\n};\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n\n  var indexes = insideDestination.map(function (d) {\n    return d.descriptor.index;\n  });\n  var errors = {};\n\n  for (var i = 1; i < indexes.length; i++) {\n    var current = indexes[i];\n    var previous = indexes[i - 1];\n\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n\n  if (!Object.keys(errors).length) {\n    return;\n  }\n\n  var formatted = indexes.map(function (index) {\n    var hasError = Boolean(errors[index]);\n    return hasError ? \"[\\uD83D\\uDD25\" + index + \"]\" : \"\" + index;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Detected non-consecutive <Draggable /> indexes.\\n\\n    (This can cause unexpected bugs)\\n\\n    \" + formatted + \"\\n  \") : void 0;\n}\n\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    var insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift$1 = (function (marshal) {\n  return function (_ref) {\n    var getState = _ref.getState,\n        dispatch = _ref.dispatch;\n    return function (next) {\n      return function (action) {\n        if (action.type !== 'LIFT') {\n          next(action);\n          return;\n        }\n\n        var _action$payload = action.payload,\n            id = _action$payload.id,\n            clientSelection = _action$payload.clientSelection,\n            movementMode = _action$payload.movementMode;\n        var initial = getState();\n\n        if (initial.phase === 'DROP_ANIMATING') {\n          dispatch(completeDrop({\n            completed: initial.completed\n          }));\n        }\n\n        !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant(false) : void 0;\n        dispatch(flush());\n        dispatch(beforeInitialCapture({\n          draggableId: id,\n          movementMode: movementMode\n        }));\n        var scrollOptions = {\n          shouldPublishImmediately: movementMode === 'SNAP'\n        };\n        var request = {\n          draggableId: id,\n          scrollOptions: scrollOptions\n        };\n\n        var _marshal$startPublish = marshal.startPublishing(request),\n            critical = _marshal$startPublish.critical,\n            dimensions = _marshal$startPublish.dimensions,\n            viewport = _marshal$startPublish.viewport;\n\n        validateDimensions(critical, dimensions);\n        dispatch(initialPublish({\n          critical: critical,\n          dimensions: dimensions,\n          clientSelection: clientSelection,\n          movementMode: movementMode,\n          viewport: viewport\n        }));\n      };\n    };\n  };\n});\n\nvar style = (function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          marshal.dragging();\n        }\n\n        if (action.type === 'DROP_ANIMATE') {\n          marshal.dropping(action.payload.completed.result.reason);\n        }\n\n        if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE') {\n          marshal.resting();\n        }\n\n        next(action);\n      };\n    };\n  };\n});\n\nvar curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nvar combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nvar timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nvar outOfTheWayTiming = timings.outOfTheWay + \"s \" + curves.outOfTheWay;\nvar transitions = {\n  fluid: \"opacity \" + outOfTheWayTiming,\n  snap: \"transform \" + outOfTheWayTiming + \", opacity \" + outOfTheWayTiming,\n  drop: function drop(duration) {\n    var timing = duration + \"s \" + curves.drop;\n    return \"transform \" + timing + \", opacity \" + timing;\n  },\n  outOfTheWay: \"transform \" + outOfTheWayTiming,\n  placeholder: \"height \" + outOfTheWayTiming + \", width \" + outOfTheWayTiming + \", margin \" + outOfTheWayTiming\n};\n\nvar moveTo = function moveTo(offset) {\n  return isEqual(offset, origin) ? null : \"translate(\" + offset.x + \"px, \" + offset.y + \"px)\";\n};\n\nvar transforms = {\n  moveTo: moveTo,\n  drop: function drop(offset, isCombining) {\n    var translate = moveTo(offset);\n\n    if (!translate) {\n      return null;\n    }\n\n    if (!isCombining) {\n      return translate;\n    }\n\n    return translate + \" scale(\" + combine.scale.drop + \")\";\n  }\n};\n\nvar minDropTime = timings.minDropTime,\n    maxDropTime = timings.maxDropTime;\nvar dropTimeRange = maxDropTime - minDropTime;\nvar maxDropTimeAtDistance = 1500;\nvar cancelDropModifier = 0.6;\nvar getDropDuration = (function (_ref) {\n  var current = _ref.current,\n      destination = _ref.destination,\n      reason = _ref.reason;\n  var distance$1 = distance(current, destination);\n\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n\n  var percentage = distance$1 / maxDropTimeAtDistance;\n  var duration = minDropTime + dropTimeRange * percentage;\n  var withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n});\n\nvar getNewHomeClientOffset = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      dimensions = _ref.dimensions,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var draggables = dimensions.draggables,\n      droppables = dimensions.droppables;\n  var droppableId = whatIsDraggedOver(impact);\n  var destination = droppableId ? droppables[droppableId] : null;\n  var home = droppables[draggable.descriptor.droppableId];\n  var newClientCenter = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    afterCritical: afterCritical,\n    droppable: destination || home,\n    viewport: viewport\n  });\n  var offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n});\n\nvar getDropImpact = (function (_ref) {\n  var draggables = _ref.draggables,\n      reason = _ref.reason,\n      lastImpact = _ref.lastImpact,\n      home = _ref.home,\n      viewport = _ref.viewport,\n      onLiftImpact = _ref.onLiftImpact;\n\n  if (!lastImpact.at || reason !== 'DROP') {\n    var recomputedHomeImpact = recompute({\n      draggables: draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport: viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n\n  var withoutMovement = _extends({}, lastImpact, {\n    displaced: emptyGroups\n  });\n\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n});\n\nvar drop$1 = (function (_ref) {\n  var getState = _ref.getState,\n      dispatch = _ref.dispatch;\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP') {\n        next(action);\n        return;\n      }\n\n      var state = getState();\n      var reason = action.payload.reason;\n\n      if (state.phase === 'COLLECTING') {\n        dispatch(dropPending({\n          reason: reason\n        }));\n        return;\n      }\n\n      if (state.phase === 'IDLE') {\n        return;\n      }\n\n      var isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n      !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant(false) : void 0;\n      !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot drop in phase: \" + state.phase) : invariant(false) : void 0;\n      var critical = state.critical;\n      var dimensions = state.dimensions;\n      var draggable = dimensions.draggables[state.critical.draggable.id];\n\n      var _getDropImpact = getDropImpact({\n        reason: reason,\n        lastImpact: state.impact,\n        afterCritical: state.afterCritical,\n        onLiftImpact: state.onLiftImpact,\n        home: state.dimensions.droppables[state.critical.droppable.id],\n        viewport: state.viewport,\n        draggables: state.dimensions.draggables\n      }),\n          impact = _getDropImpact.impact,\n          didDropInsideDroppable = _getDropImpact.didDropInsideDroppable;\n\n      var destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n      var combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n      var source = {\n        index: critical.draggable.index,\n        droppableId: critical.droppable.id\n      };\n      var result = {\n        draggableId: draggable.descriptor.id,\n        type: draggable.descriptor.type,\n        source: source,\n        reason: reason,\n        mode: state.movementMode,\n        destination: destination,\n        combine: combine\n      };\n      var newHomeClientOffset = getNewHomeClientOffset({\n        impact: impact,\n        draggable: draggable,\n        dimensions: dimensions,\n        viewport: state.viewport,\n        afterCritical: state.afterCritical\n      });\n      var completed = {\n        critical: state.critical,\n        afterCritical: state.afterCritical,\n        result: result,\n        impact: impact\n      };\n      var isAnimationRequired = !isEqual(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n\n      if (!isAnimationRequired) {\n        dispatch(completeDrop({\n          completed: completed\n        }));\n        return;\n      }\n\n      var dropDuration = getDropDuration({\n        current: state.current.client.offset,\n        destination: newHomeClientOffset,\n        reason: reason\n      });\n      var args = {\n        newHomeClientOffset: newHomeClientOffset,\n        dropDuration: dropDuration,\n        completed: completed\n      };\n      dispatch(animateDrop(args));\n    };\n  };\n});\n\nvar getWindowScroll = (function () {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n});\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn(event) {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n\n      update();\n    }\n  };\n}\n\nfunction getScrollListener(_ref) {\n  var onWindowScroll = _ref.onWindowScroll;\n\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n\n  var scheduled = rafSchd(updateScroll);\n  var binding = getWindowScrollBinding(scheduled);\n  var unbind = noop;\n\n  function isActive() {\n    return unbind !== noop;\n  }\n\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant(false) : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant(false) : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop;\n  }\n\n  return {\n    start: start,\n    stop: stop,\n    isActive: isActive\n  };\n}\n\nvar shouldEnd = function shouldEnd(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\n\nvar scrollListener = (function (store) {\n  var listener = getScrollListener({\n    onWindowScroll: function onWindowScroll(newScroll) {\n      store.dispatch(moveByWindowScroll({\n        newScroll: newScroll\n      }));\n    }\n  });\n  return function (next) {\n    return function (action) {\n      if (!listener.isActive() && action.type === 'INITIAL_PUBLISH') {\n        listener.start();\n      }\n\n      if (listener.isActive() && shouldEnd(action)) {\n        listener.stop();\n      }\n\n      next(action);\n    };\n  };\n});\n\nvar getExpiringAnnounce = (function (announce) {\n  var wasCalled = false;\n  var isExpired = false;\n  var timeoutId = setTimeout(function () {\n    isExpired = true;\n  });\n\n  var result = function result(message) {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Announcements cannot be made asynchronously.\\n        Default message has already been announced.\\n      \") : void 0;\n      return;\n    }\n\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n\n  result.wasCalled = function () {\n    return wasCalled;\n  };\n\n  return result;\n});\n\nvar getAsyncMarshal = (function () {\n  var entries = [];\n\n  var execute = function execute(timerId) {\n    var index = findIndex(entries, function (item) {\n      return item.timerId === timerId;\n    });\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant(false) : void 0;\n\n    var _entries$splice = entries.splice(index, 1),\n        entry = _entries$splice[0];\n\n    entry.callback();\n  };\n\n  var add = function add(fn) {\n    var timerId = setTimeout(function () {\n      return execute(timerId);\n    });\n    var entry = {\n      timerId: timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n\n  var flush = function flush() {\n    if (!entries.length) {\n      return;\n    }\n\n    var shallow = [].concat(entries);\n    entries.length = 0;\n    shallow.forEach(function (entry) {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n\n  return {\n    add: add,\n    flush: flush\n  };\n});\n\nvar areLocationsEqual = function areLocationsEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n\n  if (first == null || second == null) {\n    return false;\n  }\n\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nvar isCombineEqual = function isCombineEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n\n  if (first == null || second == null) {\n    return false;\n  }\n\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nvar isCriticalEqual = function isCriticalEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n\n  var isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  var isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nvar withTimings = function withTimings(key, fn) {\n  start();\n  fn();\n  finish();\n};\n\nvar getDragStart = function getDragStart(critical, mode) {\n  return {\n    draggableId: critical.draggable.id,\n    type: critical.droppable.type,\n    source: {\n      droppableId: critical.droppable.id,\n      index: critical.draggable.index\n    },\n    mode: mode\n  };\n};\n\nvar execute = function execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n\n  var willExpire = getExpiringAnnounce(announce);\n  var provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n};\n\nvar getPublisher = (function (getResponders, announce) {\n  var asyncMarshal = getAsyncMarshal();\n  var dragging = null;\n\n  var beforeCapture = function beforeCapture(draggableId, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeCapture', function () {\n      var fn = getResponders().onBeforeCapture;\n\n      if (fn) {\n        var before = {\n          draggableId: draggableId,\n          mode: mode\n        };\n        fn(before);\n      }\n    });\n  };\n\n  var beforeStart = function beforeStart(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeDragStart', function () {\n      var fn = getResponders().onBeforeDragStart;\n\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n\n  var start = function start(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    var data = getDragStart(critical, mode);\n    dragging = {\n      mode: mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(function () {\n      withTimings('onDragStart', function () {\n        return execute(getResponders().onDragStart, data, announce, preset.onDragStart);\n      });\n    });\n  };\n\n  var update = function update(critical, impact) {\n    var location = tryGetDestination(impact);\n    var combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant(false) : void 0;\n    var hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n\n    var hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n\n    var hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n\n    var data = _extends({}, getDragStart(critical, dragging.mode), {\n      combine: combine,\n      destination: location\n    });\n\n    asyncMarshal.add(function () {\n      withTimings('onDragUpdate', function () {\n        return execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate);\n      });\n    });\n  };\n\n  var flush = function flush() {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant(false) : void 0;\n    asyncMarshal.flush();\n  };\n\n  var drop = function drop(result) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant(false) : void 0;\n    dragging = null;\n    withTimings('onDragEnd', function () {\n      return execute(getResponders().onDragEnd, result, announce, preset.onDragEnd);\n    });\n  };\n\n  var abort = function abort() {\n    if (!dragging) {\n      return;\n    }\n\n    var result = _extends({}, getDragStart(dragging.lastCritical, dragging.mode), {\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    });\n\n    drop(result);\n  };\n\n  return {\n    beforeCapture: beforeCapture,\n    beforeStart: beforeStart,\n    start: start,\n    update: update,\n    flush: flush,\n    drop: drop,\n    abort: abort\n  };\n});\n\nvar responders = (function (getResponders, announce) {\n  var publisher = getPublisher(getResponders, announce);\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'BEFORE_INITIAL_CAPTURE') {\n          publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n          return;\n        }\n\n        if (action.type === 'INITIAL_PUBLISH') {\n          var critical = action.payload.critical;\n          publisher.beforeStart(critical, action.payload.movementMode);\n          next(action);\n          publisher.start(critical, action.payload.movementMode);\n          return;\n        }\n\n        if (action.type === 'DROP_COMPLETE') {\n          var result = action.payload.completed.result;\n          publisher.flush();\n          next(action);\n          publisher.drop(result);\n          return;\n        }\n\n        next(action);\n\n        if (action.type === 'FLUSH') {\n          publisher.abort();\n          return;\n        }\n\n        var state = store.getState();\n\n        if (state.phase === 'DRAGGING') {\n          publisher.update(state.critical, state.impact);\n        }\n      };\n    };\n  };\n});\n\nvar dropAnimationFinish = (function (store) {\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP_ANIMATION_FINISHED') {\n        next(action);\n        return;\n      }\n\n      var state = store.getState();\n      !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant(false) : void 0;\n      store.dispatch(completeDrop({\n        completed: state.completed\n      }));\n    };\n  };\n});\n\nvar dropAnimationFlushOnScroll = (function (store) {\n  var unbind = null;\n  var frameId = null;\n\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n\n  return function (next) {\n    return function (action) {\n      if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATION_FINISHED') {\n        clear();\n      }\n\n      next(action);\n\n      if (action.type !== 'DROP_ANIMATE') {\n        return;\n      }\n\n      var binding = {\n        eventName: 'scroll',\n        options: {\n          capture: true,\n          passive: false,\n          once: true\n        },\n        fn: function flushDropAnimation() {\n          var state = store.getState();\n\n          if (state.phase === 'DROP_ANIMATING') {\n            store.dispatch(dropAnimationFinished());\n          }\n        }\n      };\n      frameId = requestAnimationFrame(function () {\n        frameId = null;\n        unbind = bindEvents(window, [binding]);\n      });\n    };\n  };\n});\n\nvar dimensionMarshalStopper = (function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'DROP_COMPLETE' || action.type === 'FLUSH' || action.type === 'DROP_ANIMATE') {\n          marshal.stopPublishing();\n        }\n\n        next(action);\n      };\n    };\n  };\n});\n\nvar focus = (function (marshal) {\n  var isWatching = false;\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          isWatching = true;\n          marshal.tryRecordFocus(action.payload.critical.draggable.id);\n          next(action);\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n\n        next(action);\n\n        if (!isWatching) {\n          return;\n        }\n\n        if (action.type === 'FLUSH') {\n          isWatching = false;\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n\n        if (action.type === 'DROP_COMPLETE') {\n          isWatching = false;\n          var result = action.payload.completed.result;\n\n          if (result.combine) {\n            marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n          }\n\n          marshal.tryRestoreFocusRecorded();\n        }\n      };\n    };\n  };\n});\n\nvar shouldStop = function shouldStop(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\n\nvar autoScroll = (function (autoScroller) {\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (shouldStop(action)) {\n          autoScroller.stop();\n          next(action);\n          return;\n        }\n\n        if (action.type === 'INITIAL_PUBLISH') {\n          next(action);\n          var state = store.getState();\n          !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant(false) : void 0;\n          autoScroller.start(state);\n          return;\n        }\n\n        next(action);\n        autoScroller.scroll(store.getState());\n      };\n    };\n  };\n});\n\nvar pendingDrop = (function (store) {\n  return function (next) {\n    return function (action) {\n      next(action);\n\n      if (action.type !== 'PUBLISH_WHILE_DRAGGING') {\n        return;\n      }\n\n      var postActionState = store.getState();\n\n      if (postActionState.phase !== 'DROP_PENDING') {\n        return;\n      }\n\n      if (postActionState.isWaiting) {\n        return;\n      }\n\n      store.dispatch(drop({\n        reason: postActionState.reason\n      }));\n    };\n  };\n});\n\nvar composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: 'react-beautiful-dnd'\n}) : compose;\nvar createStore = (function (_ref) {\n  var dimensionMarshal = _ref.dimensionMarshal,\n      focusMarshal = _ref.focusMarshal,\n      styleMarshal = _ref.styleMarshal,\n      getResponders = _ref.getResponders,\n      announce = _ref.announce,\n      autoScroller = _ref.autoScroller;\n  return createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift$1(dimensionMarshal), drop$1, dropAnimationFinish, dropAnimationFlushOnScroll, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n});\n\nvar clean$1 = function clean() {\n  return {\n    additions: {},\n    removals: {},\n    modified: {}\n  };\n};\nfunction createPublisher(_ref) {\n  var registry = _ref.registry,\n      callbacks = _ref.callbacks;\n  var staging = clean$1();\n  var frameId = null;\n\n  var collect = function collect() {\n    if (frameId) {\n      return;\n    }\n\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      start();\n      var _staging = staging,\n          additions = _staging.additions,\n          removals = _staging.removals,\n          modified = _staging.modified;\n      var added = Object.keys(additions).map(function (id) {\n        return registry.draggable.getById(id).getDimension(origin);\n      }).sort(function (a, b) {\n        return a.descriptor.index - b.descriptor.index;\n      });\n      var updated = Object.keys(modified).map(function (id) {\n        var entry = registry.droppable.getById(id);\n        var scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll: scroll\n        };\n      });\n      var result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n\n  var add = function add(entry) {\n    var id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n\n    collect();\n  };\n\n  var remove = function remove(entry) {\n    var descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n\n    collect();\n  };\n\n  var stop = function stop() {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n\n  return {\n    add: add,\n    remove: remove,\n    stop: stop\n  };\n}\n\nvar getMaxScroll = (function (_ref) {\n  var scrollHeight = _ref.scrollHeight,\n      scrollWidth = _ref.scrollWidth,\n      height = _ref.height,\n      width = _ref.width;\n  var maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  var adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n});\n\nvar getDocumentElement = (function () {\n  var doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant(false) : void 0;\n  return doc;\n});\n\nvar getMaxWindowScroll = (function () {\n  var doc = getDocumentElement();\n  var maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n});\n\nvar getViewport = (function () {\n  var scroll = getWindowScroll();\n  var maxScroll = getMaxWindowScroll();\n  var top = scroll.y;\n  var left = scroll.x;\n  var doc = getDocumentElement();\n  var width = doc.clientWidth;\n  var height = doc.clientHeight;\n  var right = left + width;\n  var bottom = top + height;\n  var frame = getRect({\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  });\n  var viewport = {\n    frame: frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n});\n\nvar getInitialPublish = (function (_ref) {\n  var critical = _ref.critical,\n      scrollOptions = _ref.scrollOptions,\n      registry = _ref.registry;\n  start();\n  var viewport = getViewport();\n  var windowScroll = viewport.scroll.current;\n  var home = critical.droppable;\n  var droppables = registry.droppable.getAllByType(home.type).map(function (entry) {\n    return entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions);\n  });\n  var draggables = registry.draggable.getAllByType(critical.draggable.type).map(function (entry) {\n    return entry.getDimension(windowScroll);\n  });\n  var dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  var result = {\n    dimensions: dimensions,\n    critical: critical,\n    viewport: viewport\n  };\n  return result;\n});\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n\n  var home = registry.droppable.getById(entry.descriptor.droppableId);\n\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      You are attempting to add or remove a Draggable [id: \" + entry.descriptor.id + \"]\\n      while a drag is occurring. This is only supported for virtual lists.\\n\\n      See https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/patterns/virtual-lists.md\\n    \") : void 0;\n    return false;\n  }\n\n  return true;\n}\n\nvar createDimensionMarshal = (function (registry, callbacks) {\n  var collection = null;\n  var publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry: registry\n  });\n\n  var updateDroppableIsEnabled = function updateDroppableIsEnabled(id, isEnabled) {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update is enabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n\n    if (!collection) {\n      return;\n    }\n\n    callbacks.updateDroppableIsEnabled({\n      id: id,\n      isEnabled: isEnabled\n    });\n  };\n\n  var updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(id, isCombineEnabled) {\n    if (!collection) {\n      return;\n    }\n\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update isCombineEnabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id: id,\n      isCombineEnabled: isCombineEnabled\n    });\n  };\n\n  var updateDroppableScroll = function updateDroppableScroll(id, newScroll) {\n    if (!collection) {\n      return;\n    }\n\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update the scroll on Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableScroll({\n      id: id,\n      newScroll: newScroll\n    });\n  };\n\n  var scrollDroppable = function scrollDroppable(id, change) {\n    if (!collection) {\n      return;\n    }\n\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n\n  var stopPublishing = function stopPublishing() {\n    if (!collection) {\n      return;\n    }\n\n    publisher.stop();\n    var home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(function (entry) {\n      return entry.callbacks.dragStopped();\n    });\n    collection.unsubscribe();\n    collection = null;\n  };\n\n  var subscriber = function subscriber(event) {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant(false) : void 0;\n    var dragging = collection.critical.draggable;\n\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n\n  var startPublishing = function startPublishing(request) {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant(false) : void 0;\n    var entry = registry.draggable.getById(request.draggableId);\n    var home = registry.droppable.getById(entry.descriptor.droppableId);\n    var critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    var unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical: critical,\n      unsubscribe: unsubscribe\n    };\n    return getInitialPublish({\n      critical: critical,\n      registry: registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n\n  var marshal = {\n    updateDroppableIsEnabled: updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n    scrollDroppable: scrollDroppable,\n    updateDroppableScroll: updateDroppableScroll,\n    startPublishing: startPublishing,\n    stopPublishing: stopPublishing\n  };\n  return marshal;\n});\n\nvar canStartDrag = (function (state, id) {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n\n  return state.completed.result.reason === 'DROP';\n});\n\nvar scrollWindow = (function (change) {\n  window.scrollBy(change.x, change.y);\n});\n\nvar getScrollableDroppables = memoizeOne(function (droppables) {\n  return toDroppableList(droppables).filter(function (droppable) {\n    if (!droppable.isEnabled) {\n      return false;\n    }\n\n    if (!droppable.frame) {\n      return false;\n    }\n\n    return true;\n  });\n});\n\nvar getScrollableDroppableOver = function getScrollableDroppableOver(target, droppables) {\n  var maybe = find(getScrollableDroppables(droppables), function (droppable) {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant(false) : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  });\n  return maybe;\n};\n\nvar getBestScrollableDroppable = (function (_ref) {\n  var center = _ref.center,\n      destination = _ref.destination,\n      droppables = _ref.droppables;\n\n  if (destination) {\n    var _dimension = droppables[destination];\n\n    if (!_dimension.frame) {\n      return null;\n    }\n\n    return _dimension;\n  }\n\n  var dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n});\n\nvar config = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: function ease(percentage) {\n    return Math.pow(percentage, 2);\n  },\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  }\n};\n\nvar getDistanceThresholds = (function (container, axis) {\n  var startScrollingFrom = container[axis.size] * config.startFromPercentage;\n  var maxScrollValueAt = container[axis.size] * config.maxScrollAtPercentage;\n  var thresholds = {\n    startScrollingFrom: startScrollingFrom,\n    maxScrollValueAt: maxScrollValueAt\n  };\n  return thresholds;\n});\n\nvar getPercentage = (function (_ref) {\n  var startOfRange = _ref.startOfRange,\n      endOfRange = _ref.endOfRange,\n      current = _ref.current;\n  var range = endOfRange - startOfRange;\n\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Detected distance range of 0 in the fluid auto scroller\\n      This is unexpected and would cause a divide by 0 issue.\\n      Not allowing an auto scroll\\n    \") : void 0;\n    return 0;\n  }\n\n  var currentInRange = current - startOfRange;\n  var percentage = currentInRange / range;\n  return percentage;\n});\n\nvar minScroll = 1;\n\nvar getValueFromDistance = (function (distanceToEdge, thresholds) {\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return config.maxPixelScroll;\n  }\n\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n\n  var percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  var percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  var scroll = config.maxPixelScroll * config.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n});\n\nvar accelerateAt = config.durationDampening.accelerateAt;\nvar stopAt = config.durationDampening.stopDampeningAt;\nvar dampenValueByTime = (function (proposedScroll, dragStartTime) {\n  var startOfRange = dragStartTime;\n  var endOfRange = stopAt;\n  var now = Date.now();\n  var runTime = now - startOfRange;\n\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n\n  var betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange: endOfRange,\n    current: runTime\n  });\n  var scroll = proposedScroll * config.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n});\n\nvar getValue = (function (_ref) {\n  var distanceToEdge = _ref.distanceToEdge,\n      thresholds = _ref.thresholds,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getValueFromDistance(distanceToEdge, thresholds);\n\n  if (scroll === 0) {\n    return 0;\n  }\n\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n\n  return Math.max(dampenValueByTime(scroll, dragStartTime), minScroll);\n});\n\nvar getScrollOnAxis = (function (_ref) {\n  var container = _ref.container,\n      distanceToEdges = _ref.distanceToEdges,\n      dragStartTime = _ref.dragStartTime,\n      axis = _ref.axis,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var thresholds = getDistanceThresholds(container, axis);\n  var isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds: thresholds,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  }\n\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds: thresholds,\n    dragStartTime: dragStartTime,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n});\n\nvar adjustForSizeLimits = (function (_ref) {\n  var container = _ref.container,\n      subject = _ref.subject,\n      proposedScroll = _ref.proposedScroll;\n  var isTooBigVertically = subject.height > container.height;\n  var isTooBigHorizontally = subject.width > container.width;\n\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n});\n\nvar clean$2 = apply(function (value) {\n  return value === 0 ? 0 : value;\n});\nvar getScroll = (function (_ref) {\n  var dragStartTime = _ref.dragStartTime,\n      container = _ref.container,\n      subject = _ref.subject,\n      center = _ref.center,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  var y = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var x = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var required = clean$2({\n    x: x,\n    y: y\n  });\n\n  if (isEqual(required, origin)) {\n    return null;\n  }\n\n  var limited = adjustForSizeLimits({\n    container: container,\n    subject: subject,\n    proposedScroll: required\n  });\n\n  if (!limited) {\n    return null;\n  }\n\n  return isEqual(limited, origin) ? null : limited;\n});\n\nvar smallestSigned = apply(function (value) {\n  if (value === 0) {\n    return 0;\n  }\n\n  return value > 0 ? 1 : -1;\n});\nvar getOverlap = function () {\n  var getRemainder = function getRemainder(target, max) {\n    if (target < 0) {\n      return target;\n    }\n\n    if (target > max) {\n      return target - max;\n    }\n\n    return 0;\n  };\n\n  return function (_ref) {\n    var current = _ref.current,\n        max = _ref.max,\n        change = _ref.change;\n    var targetScroll = add(current, change);\n    var overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n\n    if (isEqual(overlap, origin)) {\n      return null;\n    }\n\n    return overlap;\n  };\n}();\nvar canPartiallyScroll = function canPartiallyScroll(_ref2) {\n  var rawMax = _ref2.max,\n      current = _ref2.current,\n      change = _ref2.change;\n  var max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  var smallestChange = smallestSigned(change);\n  var overlap = getOverlap({\n    max: max,\n    current: current,\n    change: smallestChange\n  });\n\n  if (!overlap) {\n    return true;\n  }\n\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n\n  return false;\n};\nvar canScrollWindow = function canScrollWindow(viewport, change) {\n  return canPartiallyScroll({\n    current: viewport.scroll.current,\n    max: viewport.scroll.max,\n    change: change\n  });\n};\nvar getWindowOverlap = function getWindowOverlap(viewport, change) {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n\n  var max = viewport.scroll.max;\n  var current = viewport.scroll.current;\n  return getOverlap({\n    current: current,\n    max: max,\n    change: change\n  });\n};\nvar canScrollDroppable = function canScrollDroppable(droppable, change) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return false;\n  }\n\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\nvar getDroppableOverlap = function getDroppableOverlap(droppable, change) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return null;\n  }\n\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\n\nvar getWindowScrollChange = (function (_ref) {\n  var viewport = _ref.viewport,\n      subject = _ref.subject,\n      center = _ref.center,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: viewport.frame,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n});\n\nvar getDroppableScrollChange = (function (_ref) {\n  var droppable = _ref.droppable,\n      subject = _ref.subject,\n      center = _ref.center,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return null;\n  }\n\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: frame.pageMarginBox,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n});\n\nvar scroll$1 = (function (_ref) {\n  var state = _ref.state,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening,\n      scrollWindow = _ref.scrollWindow,\n      scrollDroppable = _ref.scrollDroppable;\n  var center = state.current.page.borderBoxCenter;\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var subject = draggable.page.marginBox;\n\n  if (state.isWindowScrollAllowed) {\n    var viewport = state.viewport;\n\n    var _change = getWindowScrollChange({\n      dragStartTime: dragStartTime,\n      viewport: viewport,\n      subject: subject,\n      center: center,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n\n    if (_change) {\n      scrollWindow(_change);\n      return;\n    }\n  }\n\n  var droppable = getBestScrollableDroppable({\n    center: center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n\n  if (!droppable) {\n    return;\n  }\n\n  var change = getDroppableScrollChange({\n    dragStartTime: dragStartTime,\n    droppable: droppable,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n});\n\nvar createFluidScroller = (function (_ref) {\n  var scrollWindow = _ref.scrollWindow,\n      scrollDroppable = _ref.scrollDroppable;\n  var scheduleWindowScroll = rafSchd(scrollWindow);\n  var scheduleDroppableScroll = rafSchd(scrollDroppable);\n  var dragging = null;\n\n  var tryScroll = function tryScroll(state) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant(false) : void 0;\n    var _dragging = dragging,\n        shouldUseTimeDampening = _dragging.shouldUseTimeDampening,\n        dragStartTime = _dragging.dragStartTime;\n    scroll$1({\n      state: state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  };\n\n  var start$1 = function start$1(state) {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant(false) : void 0;\n    var dragStartTime = Date.now();\n    var wasScrollNeeded = false;\n\n    var fakeScrollCallback = function fakeScrollCallback() {\n      wasScrollNeeded = true;\n    };\n\n    scroll$1({\n      state: state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback\n    });\n    dragging = {\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n\n  var stop = function stop() {\n    if (!dragging) {\n      return;\n    }\n\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n\n  return {\n    start: start$1,\n    stop: stop,\n    scroll: tryScroll\n  };\n});\n\nvar createJumpScroller = (function (_ref) {\n  var move = _ref.move,\n      scrollDroppable = _ref.scrollDroppable,\n      scrollWindow = _ref.scrollWindow;\n\n  var moveByOffset = function moveByOffset(state, offset) {\n    var client = add(state.current.client.selection, offset);\n    move({\n      client: client\n    });\n  };\n\n  var scrollDroppableAsMuchAsItCan = function scrollDroppableAsMuchAsItCan(droppable, change) {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n\n    var overlap = getDroppableOverlap(droppable, change);\n\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n\n    var whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    var remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n\n  var scrollWindowAsMuchAsItCan = function scrollWindowAsMuchAsItCan(isWindowScrollAllowed, viewport, change) {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n\n    var overlap = getWindowOverlap(viewport, change);\n\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n\n    var whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    var remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n\n  var jumpScroller = function jumpScroller(state) {\n    var request = state.scrollJumpRequest;\n\n    if (!request) {\n      return;\n    }\n\n    var destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant(false) : void 0;\n    var droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n\n    if (!droppableRemainder) {\n      return;\n    }\n\n    var viewport = state.viewport;\n    var windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n\n    if (!windowRemainder) {\n      return;\n    }\n\n    moveByOffset(state, windowRemainder);\n  };\n\n  return jumpScroller;\n});\n\nvar createAutoScroller = (function (_ref) {\n  var scrollDroppable = _ref.scrollDroppable,\n      scrollWindow = _ref.scrollWindow,\n      move = _ref.move;\n  var fluidScroller = createFluidScroller({\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n  var jumpScroll = createJumpScroller({\n    move: move,\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n\n  var scroll = function scroll(state) {\n    if (state.phase !== 'DRAGGING') {\n      return;\n    }\n\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n\n    jumpScroll(state);\n  };\n\n  var scroller = {\n    scroll: scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n});\n\nvar prefix$1 = 'data-rbd';\nvar dragHandle = function () {\n  var base = prefix$1 + \"-drag-handle\";\n  return {\n    base: base,\n    draggableId: base + \"-draggable-id\",\n    contextId: base + \"-context-id\"\n  };\n}();\nvar draggable = function () {\n  var base = prefix$1 + \"-draggable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar droppable = function () {\n  var base = prefix$1 + \"-droppable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar scrollContainer = {\n  contextId: prefix$1 + \"-scroll-container-context-id\"\n};\n\nvar makeGetSelector = function makeGetSelector(context) {\n  return function (attribute) {\n    return \"[\" + attribute + \"=\\\"\" + context + \"\\\"]\";\n  };\n};\n\nvar getStyles = function getStyles(rules, property) {\n  return rules.map(function (rule) {\n    var value = rule.styles[property];\n\n    if (!value) {\n      return '';\n    }\n\n    return rule.selector + \" { \" + value + \" }\";\n  }).join(' ');\n};\n\nvar noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = (function (contextId) {\n  var getSelector = makeGetSelector(contextId);\n\n  var dragHandle$1 = function () {\n    var grabCursor = \"\\n      cursor: -webkit-grab;\\n      cursor: grab;\\n    \";\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: \"\\n          -webkit-touch-callout: none;\\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\\n          touch-action: manipulation;\\n        \",\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  }();\n\n  var draggable$1 = function () {\n    var transition = \"\\n      transition: \" + transitions.outOfTheWay + \";\\n    \";\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  }();\n\n  var droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: \"overflow-anchor: none;\"\n    }\n  };\n  var body = {\n    selector: 'body',\n    styles: {\n      dragging: \"\\n        cursor: grabbing;\\n        cursor: -webkit-grabbing;\\n        user-select: none;\\n        -webkit-user-select: none;\\n        -moz-user-select: none;\\n        -ms-user-select: none;\\n        overflow-anchor: none;\\n      \"\n    }\n  };\n  var rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n});\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nvar getHead = function getHead() {\n  var head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant(false) : void 0;\n  return head;\n};\n\nvar createStyleEl = function createStyleEl(nonce) {\n  var el = document.createElement('style');\n\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n\n  el.type = 'text/css';\n  return el;\n};\n\nfunction useStyleMarshal(contextId, nonce) {\n  var styles = useMemo(function () {\n    return getStyles$1(contextId);\n  }, [contextId]);\n  var alwaysRef = useRef(null);\n  var dynamicRef = useRef(null);\n  var setDynamicStyle = useCallback(memoizeOne(function (proposed) {\n    var el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }), []);\n  var setAlwaysStyle = useCallback(function (proposed) {\n    var el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant(false) : void 0;\n    var always = createStyleEl(nonce);\n    var dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(prefix$1 + \"-always\", contextId);\n    dynamic.setAttribute(prefix$1 + \"-dynamic\", contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return function () {\n      var remove = function remove(ref) {\n        var current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant(false) : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  var dragging = useCallback(function () {\n    return setDynamicStyle(styles.dragging);\n  }, [setDynamicStyle, styles.dragging]);\n  var dropping = useCallback(function (reason) {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  var resting = useCallback(function () {\n    if (!dynamicRef.current) {\n      return;\n    }\n\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  var marshal = useMemo(function () {\n    return {\n      dragging: dragging,\n      dropping: dropping,\n      resting: resting\n    };\n  }, [dragging, dropping, resting]);\n  return marshal;\n}\n\nvar getWindowFromEl = (function (el) {\n  return el && el.ownerDocument ? el.ownerDocument.defaultView : window;\n});\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  var selector = \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find any drag handles in the context \\\"\" + contextId + \"\\\"\") : void 0;\n    return null;\n  }\n\n  var handle = find(possible, function (el) {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find drag handle with id \\\"\" + draggableId + \"\\\" as no handle with a matching id was found\") : void 0;\n    return null;\n  }\n\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  var entriesRef = useRef({});\n  var recordRef = useRef(null);\n  var restoreFocusFrameRef = useRef(null);\n  var isMountedRef = useRef(false);\n  var register = useCallback(function register(id, focus) {\n    var entry = {\n      id: id,\n      focus: focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      var entries = entriesRef.current;\n      var current = entries[id];\n\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  var tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    var handle = findDragHandle(contextId, tryGiveFocusTo);\n\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  var tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  var tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n\n    if (!isMountedRef.current) {\n      return;\n    }\n\n    restoreFocusFrameRef.current = requestAnimationFrame(function () {\n      restoreFocusFrameRef.current = null;\n      var record = recordRef.current;\n\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  var tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    var focused = document.activeElement;\n\n    if (!focused) {\n      return;\n    }\n\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      var frameId = restoreFocusFrameRef.current;\n\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  var marshal = useMemo(function () {\n    return {\n      register: register,\n      tryRecordFocus: tryRecordFocus,\n      tryRestoreFocusRecorded: tryRestoreFocusRecorded,\n      tryShiftRecord: tryShiftRecord\n    };\n  }, [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  var entries = {\n    draggables: {},\n    droppables: {}\n  };\n  var subscribers = [];\n\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      var index = subscribers.indexOf(cb);\n\n      if (index === -1) {\n        return;\n      }\n\n      subscribers.splice(index, 1);\n    };\n  }\n\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(function (cb) {\n        return cb(event);\n      });\n    }\n  }\n\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n\n  function getDraggableById(id) {\n    var entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find draggable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n\n  var draggableAPI = {\n    register: function register(entry) {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: function update(entry, last) {\n      var current = entries.draggables[last.descriptor.id];\n\n      if (!current) {\n        return;\n      }\n\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var draggableId = entry.descriptor.id;\n      var current = findDraggableById(draggableId);\n\n      if (!current) {\n        return;\n      }\n\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n\n      delete entries.draggables[draggableId];\n      notify({\n        type: 'REMOVAL',\n        value: entry\n      });\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: function exists(id) {\n      return Boolean(findDraggableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.draggables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n\n  function getDroppableById(id) {\n    var entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find droppable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n\n  var droppableAPI = {\n    register: function register(entry) {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var current = findDroppableById(entry.descriptor.id);\n\n      if (!current) {\n        return;\n      }\n\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: function exists(id) {\n      return Boolean(findDroppableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.droppables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe: subscribe,\n    clean: clean\n  };\n}\n\nfunction useRegistry() {\n  var registry = useMemo(createRegistry, []);\n  useEffect(function () {\n    return function unmount() {\n      requestAnimationFrame(registry.clean);\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = (function () {\n  var body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant(false) : void 0;\n  return body;\n});\n\nvar visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\n\nvar getId = function getId(contextId) {\n  return \"rbd-announcement-\" + contextId;\n};\nfunction useAnnouncer(contextId) {\n  var id = useMemo(function () {\n    return getId(contextId);\n  }, [contextId]);\n  var ref = useRef(null);\n  useEffect(function setup() {\n    var el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n\n    _extends(el.style, visuallyHidden);\n\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        var body = getBodyElement();\n\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  var announce = useCallback(function (message) {\n    var el = ref.current;\n\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      A screen reader message was trying to be announced but it was unable to do so.\\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\\n      Consider calling provided.announce() before the unmount so that the instruction will\\n      not be lost for users relying on a screen reader.\\n\\n      Message not passed to screen reader:\\n\\n      \\\"\" + message + \"\\\"\\n    \") : void 0;\n  }, []);\n  return announce;\n}\n\nvar count = 0;\nvar defaults = {\n  separator: '::'\n};\nfunction reset() {\n  count = 0;\n}\nfunction useUniqueId(prefix, options) {\n  if (options === void 0) {\n    options = defaults;\n  }\n\n  return useMemo(function () {\n    return \"\" + prefix + options.separator + count++;\n  }, [options.separator, prefix]);\n}\n\nfunction getElementId(_ref) {\n  var contextId = _ref.contextId,\n      uniqueId = _ref.uniqueId;\n  return \"rbd-hidden-text-\" + contextId + \"-\" + uniqueId;\n}\nfunction useHiddenTextElement(_ref2) {\n  var contextId = _ref2.contextId,\n      text = _ref2.text;\n  var uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  var id = useMemo(function () {\n    return getElementId({\n      contextId: contextId,\n      uniqueId: uniqueId\n    });\n  }, [uniqueId, contextId]);\n  useEffect(function mount() {\n    var el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      var body = getBodyElement();\n\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^16.8.5 || ^17.0.0 || ^18.0.0\",\n\t\"react-dom\": \"^16.8.5 || ^17.0.0 || ^18.0.0\"\n};\n\nvar semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\n\nvar getVersion = function getVersion(value) {\n  var result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unable to parse React version \" + value) : invariant(false) : void 0;\n  var major = Number(result[1]);\n  var minor = Number(result[2]);\n  var patch = Number(result[3]);\n  return {\n    major: major,\n    minor: minor,\n    patch: patch,\n    raw: value\n  };\n};\n\nvar isSatisfied = function isSatisfied(expected, actual) {\n  if (actual.major > expected.major) {\n    return true;\n  }\n\n  if (actual.major < expected.major) {\n    return false;\n  }\n\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n\n  return actual.patch >= expected.patch;\n};\n\nvar checkReactVersion = (function (peerDepValue, actualValue) {\n  var peerDep = getVersion(peerDepValue);\n  var actual = getVersion(actualValue);\n\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    React version: [\" + actual.raw + \"]\\n    does not satisfy expected peer dependency version: [\" + peerDep.raw + \"]\\n\\n    This can result in run time bugs, and even fatal crashes\\n  \") : void 0;\n});\n\nvar suffix = \"\\n  We expect a html5 doctype: <!doctype html>\\n  This is to ensure consistent browser layout and measurement\\n\\n  More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/doctype.md\\n\";\nvar checkDoctype = (function (doc) {\n  var doctype = doc.doctype;\n\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      No <!doctype html> found.\\n\\n      \" + suffix + \"\\n    \") : void 0;\n    return;\n  }\n\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> found: (\" + doctype.name + \")\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> publicId found: (\" + doctype.publicId + \")\\n      A html5 doctype does not have a publicId\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n});\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(function () {\n    useEffect(function () {\n      try {\n        fn();\n      } catch (e) {\n        error(\"\\n          A setup problem was encountered.\\n\\n          > \" + e.message + \"\\n        \");\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(function () {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  var ref = useRef(current);\n  useEffect(function () {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  var lock = null;\n\n  function isClaimed() {\n    return Boolean(lock);\n  }\n\n  function isActive(value) {\n    return value === lock;\n  }\n\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant(false) : void 0;\n    var newLock = {\n      abandon: abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant(false) : void 0;\n    lock = null;\n  }\n\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n\n  return {\n    isClaimed: isClaimed,\n    isActive: isActive,\n    claim: claim,\n    release: release,\n    tryAbandon: tryAbandon\n  };\n}\n\nvar tab = 9;\nvar enter = 13;\nvar escape = 27;\nvar space = 32;\nvar pageUp = 33;\nvar pageDown = 34;\nvar end = 35;\nvar home = 36;\nvar arrowLeft = 37;\nvar arrowUp = 38;\nvar arrowRight = 39;\nvar arrowDown = 40;\n\nvar _preventedKeys;\nvar preventedKeys = (_preventedKeys = {}, _preventedKeys[enter] = true, _preventedKeys[tab] = true, _preventedKeys);\nvar preventStandardKeyEvents = (function (event) {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n});\n\nvar supportedEventName = function () {\n  var base = 'visibilitychange';\n\n  if (typeof document === 'undefined') {\n    return base;\n  }\n\n  var candidates = [base, \"ms\" + base, \"webkit\" + base, \"moz\" + base, \"o\" + base];\n  var supported = find(candidates, function (eventName) {\n    return \"on\" + eventName in document;\n  });\n  return supported || base;\n}();\n\nvar primaryButton = 0;\nvar sloppyClickThreshold = 5;\n\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\n\nvar idle$1 = {\n  type: 'IDLE'\n};\n\nfunction getCaptureBindings(_ref) {\n  var cancel = _ref.cancel,\n      completed = _ref.completed,\n      getPhase = _ref.getPhase,\n      setPhase = _ref.setPhase;\n  return [{\n    eventName: 'mousemove',\n    fn: function fn(event) {\n      var button = event.button,\n          clientX = event.clientX,\n          clientY = event.clientY;\n\n      if (button !== primaryButton) {\n        return;\n      }\n\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      var phase = getPhase();\n\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant(false) : void 0;\n      var pending = phase.point;\n\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n\n      event.preventDefault();\n      var actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions: actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: function fn(event) {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn() {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant(false) : void 0;\n\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useMouseSensor(api) {\n  var phaseRef = useRef(idle$1);\n  var unbindEventsRef = useRef(noop);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'mousedown',\n      fn: function onMouseDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        if (event.button !== primaryButton) {\n          return;\n        }\n\n        if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!actions) {\n          return;\n        }\n\n        event.preventDefault();\n        var point = {\n          x: event.clientX,\n          y: event.clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var preventForcePressBinding = useMemo(function () {\n    return {\n      eventName: 'webkitmouseforcewillbegin',\n      fn: function fn(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        var id = api.findClosestDraggableId(event);\n\n        if (!id) {\n          return;\n        }\n\n        var options = api.findOptionsForDraggable(id);\n\n        if (!options) {\n          return;\n        }\n\n        if (options.shouldRespectForcePress) {\n          return;\n        }\n\n        if (!api.canGetLock(id)) {\n          return;\n        }\n\n        event.preventDefault();\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n\n    if (current.type === 'IDLE') {\n      return;\n    }\n\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var bindings = getCaptureBindings({\n      cancel: cancel,\n      completed: stop,\n      getPhase: function getPhase() {\n        return phaseRef.current;\n      },\n      setPhase: function setPhase(phase) {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point: point,\n      actions: actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nvar _scrollJumpKeys;\n\nfunction noop$1() {}\n\nvar scrollJumpKeys = (_scrollJumpKeys = {}, _scrollJumpKeys[pageDown] = true, _scrollJumpKeys[pageUp] = true, _scrollJumpKeys[home] = true, _scrollJumpKeys[end] = true, _scrollJumpKeys);\n\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n\n  function drop() {\n    stop();\n    actions.drop();\n  }\n\n  return [{\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useKeyboardSensor(api) {\n  var unbindEventsRef = useRef(noop$1);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'keydown',\n      fn: function onKeyDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        if (event.keyCode !== space) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var preDrag = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!preDrag) {\n          return;\n        }\n\n        event.preventDefault();\n        var isCapturing = true;\n        var actions = preDrag.snapLift();\n        unbindEventsRef.current();\n\n        function stop() {\n          !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant(false) : void 0;\n          isCapturing = false;\n          unbindEventsRef.current();\n          listenForCapture();\n        }\n\n        unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n          capture: true,\n          passive: false\n        });\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function tryStartCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nvar idle$2 = {\n  type: 'IDLE'\n};\nvar timeForLongPress = 120;\nvar forcePressThreshold = 0.15;\n\nfunction getWindowBindings(_ref) {\n  var cancel = _ref.cancel,\n      getPhase = _ref.getPhase;\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: function fn(event) {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction getHandleBindings(_ref2) {\n  var cancel = _ref2.cancel,\n      completed = _ref2.completed,\n      getPhase = _ref2.getPhase;\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      phase.hasMoved = true;\n      var _event$touches$ = event.touches[0],\n          clientX = _event$touches$.clientX,\n          clientY = _event$touches$.clientY;\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n      var touch = event.touches[0];\n\n      if (!touch) {\n        return;\n      }\n\n      var isForcePress = touch.force >= forcePressThreshold;\n\n      if (!isForcePress) {\n        return;\n      }\n\n      var shouldRespect = phase.actions.shouldRespectForcePress();\n\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n\n        return;\n      }\n\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useTouchSensor(api) {\n  var phaseRef = useRef(idle$2);\n  var unbindEventsRef = useRef(noop);\n  var getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  var setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'touchstart',\n      fn: function onTouchStart(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!actions) {\n          return;\n        }\n\n        var touch = event.touches[0];\n        var clientX = touch.clientX,\n            clientY = touch.clientY;\n        var point = {\n          x: clientX,\n          y: clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n\n    if (current.type === 'IDLE') {\n      return;\n    }\n\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n\n    setPhase(idle$2);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var args = {\n      cancel: cancel,\n      completed: stop,\n      getPhase: getPhase\n    };\n    var unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    var unbindWindow = bindEvents(window, getWindowBindings(args), options);\n\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  var startDragging = useCallback(function startDragging() {\n    var phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot start dragging from phase \" + phase.type) : invariant(false) : void 0;\n    var actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions: actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    var longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point: point,\n      actions: actions,\n      longPressTimerId: longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      var phase = getPhase();\n\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle$2);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    var unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: function fn() {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(function () {\n    var previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(function () {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nvar interactiveTagNames = {\n  input: true,\n  button: true,\n  textarea: true,\n  select: true,\n  option: true,\n  optgroup: true,\n  video: true,\n  audio: true\n};\n\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n\n  var hasAnInteractiveTag = Boolean(interactiveTagNames[current.tagName.toLowerCase()]);\n\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n\n  var attribute = current.getAttribute('contenteditable');\n\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n\n  if (current === parent) {\n    return false;\n  }\n\n  return isAnInteractiveElement(parent, current.parentElement);\n}\n\nfunction isEventInInteractiveElement(draggable, event) {\n  var target = event.target;\n\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = (function (el) {\n  return getRect(el.getBoundingClientRect()).center;\n});\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nvar supportedMatchesName = function () {\n  var base = 'matches';\n\n  if (typeof document === 'undefined') {\n    return base;\n  }\n\n  var candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  var value = find(candidates, function (name) {\n    return name in Element.prototype;\n  });\n  return value || base;\n}();\n\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n\n  return closestPonyfill(el.parentElement, selector);\n}\n\nfunction closest$1(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n}\n\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  var target = event.target;\n\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n\n  var selector = getSelector(contextId);\n  var handle = closest$1(target, selector);\n\n  if (!handle) {\n    return null;\n  }\n\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n\n  return handle;\n}\n\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  var handle = findClosestDragHandleFromEvent(contextId, event);\n\n  if (!handle) {\n    return null;\n  }\n\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  var selector = \"[\" + draggable.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n  var draggable$1 = find(possible, function (el) {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n\n  if (!draggable$1) {\n    return null;\n  }\n\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\n\nfunction _isActive(_ref) {\n  var expected = _ref.expected,\n      phase = _ref.phase,\n      isLockActive = _ref.isLockActive,\n      shouldWarn = _ref.shouldWarn;\n\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The sensor no longer has an action lock.\\n\\n        Tips:\\n\\n        - Throw away your action handlers when forceStop() is called\\n        - Check actions.isActive() if you really need to\\n      \") : void 0;\n    }\n\n    return false;\n  }\n\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The actions you used belong to an outdated phase\\n\\n        Current phase: \" + expected + \"\\n        You called an action from outdated phase: \" + phase + \"\\n\\n        Tips:\\n\\n        - Do not use preDragActions actions after calling preDragActions.lift()\\n      \") : void 0;\n    }\n\n    return false;\n  }\n\n  return true;\n}\n\nfunction canStart(_ref2) {\n  var lockAPI = _ref2.lockAPI,\n      store = _ref2.store,\n      registry = _ref2.registry,\n      draggableId = _ref2.draggableId;\n\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n\n  var entry = registry.draggable.findById(draggableId);\n\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable with id: \" + draggableId) : void 0;\n    return false;\n  }\n\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction tryStart(_ref3) {\n  var lockAPI = _ref3.lockAPI,\n      contextId = _ref3.contextId,\n      store = _ref3.store,\n      registry = _ref3.registry,\n      draggableId = _ref3.draggableId,\n      forceSensorStop = _ref3.forceSensorStop,\n      sourceEvent = _ref3.sourceEvent;\n  var shouldStart = canStart({\n    lockAPI: lockAPI,\n    store: store,\n    registry: registry,\n    draggableId: draggableId\n  });\n\n  if (!shouldStart) {\n    return null;\n  }\n\n  var entry = registry.draggable.getById(draggableId);\n  var el = findDraggable(contextId, entry.descriptor.id);\n\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable element with id: \" + draggableId) : void 0;\n    return null;\n  }\n\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n\n  var lock = lockAPI.claim(forceSensorStop || noop);\n  var phase = 'PRE_DRAG';\n\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n\n  function tryDispatch(expected, getAction) {\n    if (_isActive({\n      expected: expected,\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n\n  var tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n\n  function lift$1(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      !(phase === 'PRE_DRAG') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot lift in phase \" + phase) : invariant(false) : void 0;\n    }\n\n    store.dispatch(lift(args.liftActionArgs));\n    phase = 'DRAGGING';\n\n    function finish(reason, options) {\n      if (options === void 0) {\n        options = {\n          shouldBlockNextClick: false\n        };\n      }\n\n      args.cleanup();\n\n      if (options.shouldBlockNextClick) {\n        var unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n\n      completed();\n      store.dispatch(drop({\n        reason: reason\n      }));\n    }\n\n    return _extends({\n      isActive: function isActive() {\n        return _isActive({\n          expected: 'DRAGGING',\n          phase: phase,\n          isLockActive: isLockActive,\n          shouldWarn: false\n        });\n      },\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: function drop(options) {\n        return finish('DROP', options);\n      },\n      cancel: function cancel(options) {\n        return finish('CANCEL', options);\n      }\n    }, args.actions);\n  }\n\n  function fluidLift(clientSelection) {\n    var move$1 = rafSchd(function (client) {\n      tryDispatchWhenDragging(function () {\n        return move({\n          client: client\n        });\n      });\n    });\n    var api = lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: function cleanup() {\n        return move$1.cancel();\n      },\n      actions: {\n        move: move$1\n      }\n    });\n    return _extends({}, api, {\n      move: move$1\n    });\n  }\n\n  function snapLift() {\n    var actions = {\n      moveUp: function moveUp$1() {\n        return tryDispatchWhenDragging(moveUp);\n      },\n      moveRight: function moveRight$1() {\n        return tryDispatchWhenDragging(moveRight);\n      },\n      moveDown: function moveDown$1() {\n        return tryDispatchWhenDragging(moveDown);\n      },\n      moveLeft: function moveLeft$1() {\n        return tryDispatchWhenDragging(moveLeft);\n      }\n    };\n    return lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop,\n      actions: actions\n    });\n  }\n\n  function abortPreDrag() {\n    var shouldRelease = _isActive({\n      expected: 'PRE_DRAG',\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    });\n\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n\n  var preDrag = {\n    isActive: function isActive() {\n      return _isActive({\n        expected: 'PRE_DRAG',\n        phase: phase,\n        isLockActive: isLockActive,\n        shouldWarn: false\n      });\n    },\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift: fluidLift,\n    snapLift: snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\n\nvar defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal(_ref4) {\n  var contextId = _ref4.contextId,\n      store = _ref4.store,\n      registry = _ref4.registry,\n      customSensors = _ref4.customSensors,\n      enableDefaultSensors = _ref4.enableDefaultSensors;\n  var useSensors = [].concat(enableDefaultSensors ? defaultSensors : [], customSensors || []);\n  var lockAPI = useState(function () {\n    return create();\n  })[0];\n  var tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (previous.isDragging && !current.isDragging) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    var previous = store.getState();\n    var unsubscribe = store.subscribe(function () {\n      var current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(function () {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  var canGetLock = useCallback(function (draggableId) {\n    return canStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      store: store,\n      draggableId: draggableId\n    });\n  }, [lockAPI, registry, store]);\n  var tryGetLock = useCallback(function (draggableId, forceStop, options) {\n    return tryStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      contextId: contextId,\n      store: store,\n      draggableId: draggableId,\n      forceSensorStop: forceStop,\n      sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n    });\n  }, [contextId, lockAPI, registry, store]);\n  var findClosestDraggableId = useCallback(function (event) {\n    return tryGetClosestDraggableIdFromEvent(contextId, event);\n  }, [contextId]);\n  var findOptionsForDraggable = useCallback(function (id) {\n    var entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  var tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n\n    lockAPI.tryAbandon();\n\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  var isLockClaimed = useCallback(lockAPI.isClaimed, [lockAPI]);\n  var api = useMemo(function () {\n    return {\n      canGetLock: canGetLock,\n      tryGetLock: tryGetLock,\n      findClosestDraggableId: findClosestDraggableId,\n      findOptionsForDraggable: findOptionsForDraggable,\n      tryReleaseLock: tryReleaseLock,\n      isLockClaimed: isLockClaimed\n    };\n  }, [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n\n  for (var i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nvar createResponders = function createResponders(props) {\n  return {\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragEnd: props.onDragEnd,\n    onDragUpdate: props.onDragUpdate\n  };\n};\n\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant(false) : void 0;\n  return lazyRef.current;\n}\n\nfunction App(props) {\n  var contextId = props.contextId,\n      setCallbacks = props.setCallbacks,\n      sensors = props.sensors,\n      nonce = props.nonce,\n      dragHandleUsageInstructions = props.dragHandleUsageInstructions;\n  var lazyStoreRef = useRef(null);\n  useStartupValidation();\n  var lastPropsRef = usePrevious(props);\n  var getResponders = useCallback(function () {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  var announce = useAnnouncer(contextId);\n  var dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId: contextId,\n    text: dragHandleUsageInstructions\n  });\n  var styleMarshal = useStyleMarshal(contextId, nonce);\n  var lazyDispatch = useCallback(function (action) {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  var marshalCallbacks = useMemo(function () {\n    return bindActionCreators({\n      publishWhileDragging: publishWhileDragging,\n      updateDroppableScroll: updateDroppableScroll,\n      updateDroppableIsEnabled: updateDroppableIsEnabled,\n      updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n      collectionStarting: collectionStarting\n    }, lazyDispatch);\n  }, [lazyDispatch]);\n  var registry = useRegistry();\n  var dimensionMarshal = useMemo(function () {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  var autoScroller = useMemo(function () {\n    return createAutoScroller(_extends({\n      scrollWindow: scrollWindow,\n      scrollDroppable: dimensionMarshal.scrollDroppable\n    }, bindActionCreators({\n      move: move\n    }, lazyDispatch)));\n  }, [dimensionMarshal.scrollDroppable, lazyDispatch]);\n  var focusMarshal = useFocusMarshal(contextId);\n  var store = useMemo(function () {\n    return createStore({\n      announce: announce,\n      autoScroller: autoScroller,\n      dimensionMarshal: dimensionMarshal,\n      focusMarshal: focusMarshal,\n      getResponders: getResponders,\n      styleMarshal: styleMarshal\n    });\n  }, [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n\n  lazyStoreRef.current = store;\n  var tryResetStore = useCallback(function () {\n    var current = getStore(lazyStoreRef);\n    var state = current.getState();\n\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  var isDragging = useCallback(function () {\n    var state = getStore(lazyStoreRef).getState();\n    return state.isDragging || state.phase === 'DROP_ANIMATING';\n  }, []);\n  var appCallbacks = useMemo(function () {\n    return {\n      isDragging: isDragging,\n      tryAbort: tryResetStore\n    };\n  }, [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  var getCanLift = useCallback(function (id) {\n    return canStartDrag(getStore(lazyStoreRef).getState(), id);\n  }, []);\n  var getIsMovementAllowed = useCallback(function () {\n    return isMovementAllowed(getStore(lazyStoreRef).getState());\n  }, []);\n  var appContext = useMemo(function () {\n    return {\n      marshal: dimensionMarshal,\n      focus: focusMarshal,\n      contextId: contextId,\n      canLift: getCanLift,\n      isMovementAllowed: getIsMovementAllowed,\n      dragHandleUsageInstructionsId: dragHandleUsageInstructionsId,\n      registry: registry\n    };\n  }, [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId: contextId,\n    store: store,\n    registry: registry,\n    customSensors: sensors,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(function () {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nvar count$1 = 0;\nfunction reset$1() {\n  count$1 = 0;\n}\nfunction useInstanceCount() {\n  return useMemo(function () {\n    return \"\" + count$1++;\n  }, []);\n}\n\nfunction resetServerContext() {\n  reset$1();\n  reset();\n}\nfunction DragDropContext(props) {\n  var contextId = useInstanceCount();\n  var dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, function (setCallbacks) {\n    return React.createElement(App, {\n      nonce: props.nonce,\n      contextId: contextId,\n      setCallbacks: setCallbacks,\n      dragHandleUsageInstructions: dragHandleUsageInstructions,\n      enableDefaultSensors: props.enableDefaultSensors,\n      sensors: props.sensors,\n      onBeforeCapture: props.onBeforeCapture,\n      onBeforeDragStart: props.onBeforeDragStart,\n      onDragStart: props.onDragStart,\n      onDragUpdate: props.onDragUpdate,\n      onDragEnd: props.onDragEnd\n    }, props.children);\n  });\n}\n\nvar isEqual$1 = function isEqual(base) {\n  return function (value) {\n    return base === value;\n  };\n};\n\nvar isScroll = isEqual$1('scroll');\nvar isAuto = isEqual$1('auto');\nvar isVisible$1 = isEqual$1('visible');\n\nvar isEither = function isEither(overflow, fn) {\n  return fn(overflow.overflowX) || fn(overflow.overflowY);\n};\n\nvar isBoth = function isBoth(overflow, fn) {\n  return fn(overflow.overflowX) && fn(overflow.overflowY);\n};\n\nvar isElementScrollable = function isElementScrollable(el) {\n  var style = window.getComputedStyle(el);\n  var overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\n\nvar isBodyScrollable = function isBodyScrollable() {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n\n  var body = getBodyElement();\n  var html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n\n  var htmlStyle = window.getComputedStyle(html);\n  var htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n\n  if (isBoth(htmlOverflow, isVisible$1)) {\n    return false;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    We have detected that your <body> element might be a scroll container.\\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\\n\\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\\n    we will be treating the <body> as *not* a scroll container\\n\\n    More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/how-we-detect-scroll-containers.md\\n  \") : void 0;\n  return false;\n};\n\nvar getClosestScrollable = function getClosestScrollable(el) {\n  if (el == null) {\n    return null;\n  }\n\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n\n  if (el === document.documentElement) {\n    return null;\n  }\n\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n\n  return el;\n};\n\nvar checkForNestedScrollContainers = (function (scrollable) {\n  if (!scrollable) {\n    return;\n  }\n\n  var anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n\n  if (!anotherScrollParent) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Droppable: unsupported nested scroll container detected.\\n    A Droppable can only have one scroll parent (which can be itself)\\n    Nested scroll containers are currently not supported.\\n\\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\\n  \") : void 0;\n});\n\nvar getScroll$1 = (function (el) {\n  return {\n    x: el.scrollLeft,\n    y: el.scrollTop\n  };\n});\n\nvar getIsFixed = function getIsFixed(el) {\n  if (!el) {\n    return false;\n  }\n\n  var style = window.getComputedStyle(el);\n\n  if (style.position === 'fixed') {\n    return true;\n  }\n\n  return getIsFixed(el.parentElement);\n};\n\nvar getEnv = (function (start) {\n  var closestScrollable = getClosestScrollable(start);\n  var isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable: closestScrollable,\n    isFixedOnPage: isFixedOnPage\n  };\n});\n\nvar getDroppableDimension = (function (_ref) {\n  var descriptor = _ref.descriptor,\n      isEnabled = _ref.isEnabled,\n      isCombineEnabled = _ref.isCombineEnabled,\n      isFixedOnPage = _ref.isFixedOnPage,\n      direction = _ref.direction,\n      client = _ref.client,\n      page = _ref.page,\n      closest = _ref.closest;\n\n  var frame = function () {\n    if (!closest) {\n      return null;\n    }\n\n    var scrollSize = closest.scrollSize,\n        frameClient = closest.client;\n    var maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient: frameClient,\n      scrollSize: scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  }();\n\n  var axis = direction === 'vertical' ? vertical : horizontal;\n  var subject = getSubject({\n    page: page,\n    withPlaceholder: null,\n    axis: axis,\n    frame: frame\n  });\n  var dimension = {\n    descriptor: descriptor,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: isFixedOnPage,\n    axis: axis,\n    isEnabled: isEnabled,\n    client: client,\n    page: page,\n    frame: frame,\n    subject: subject\n  };\n  return dimension;\n});\n\nvar getClient = function getClient(targetRef, closestScrollable) {\n  var base = getBox(targetRef);\n\n  if (!closestScrollable) {\n    return base;\n  }\n\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n\n  var top = base.paddingBox.top - closestScrollable.scrollTop;\n  var left = base.paddingBox.left - closestScrollable.scrollLeft;\n  var bottom = top + closestScrollable.scrollHeight;\n  var right = left + closestScrollable.scrollWidth;\n  var paddingBox = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left\n  };\n  var borderBox = expand(paddingBox, base.border);\n  var client = createBox({\n    borderBox: borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\n\nvar getDimension = (function (_ref) {\n  var ref = _ref.ref,\n      descriptor = _ref.descriptor,\n      env = _ref.env,\n      windowScroll = _ref.windowScroll,\n      direction = _ref.direction,\n      isDropDisabled = _ref.isDropDisabled,\n      isCombineEnabled = _ref.isCombineEnabled,\n      shouldClipSubject = _ref.shouldClipSubject;\n  var closestScrollable = env.closestScrollable;\n  var client = getClient(ref, closestScrollable);\n  var page = withScroll(client, windowScroll);\n\n  var closest = function () {\n    if (!closestScrollable) {\n      return null;\n    }\n\n    var frameClient = getBox(closestScrollable);\n    var scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll$1(closestScrollable),\n      scrollSize: scrollSize,\n      shouldClipSubject: shouldClipSubject\n    };\n  }();\n\n  var dimension = getDroppableDimension({\n    descriptor: descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction: direction,\n    client: client,\n    page: page,\n    closest: closest\n  });\n  return dimension;\n});\n\nvar immediate = {\n  passive: false\n};\nvar delayed = {\n  passive: true\n};\nvar getListenerOptions = (function (options) {\n  return options.shouldPublishImmediately ? immediate : delayed;\n});\n\nfunction useRequiredContext(Context) {\n  var result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant(false) : void 0;\n  return result;\n}\n\nvar getClosestScrollableFromDrag = function getClosestScrollableFromDrag(dragging) {\n  return dragging && dragging.env.closestScrollable || null;\n};\n\nfunction useDroppablePublisher(args) {\n  var whileDraggingRef = useRef(null);\n  var appContext = useRequiredContext(AppContext);\n  var uniqueId = useUniqueId('droppable');\n  var registry = appContext.registry,\n      marshal = appContext.marshal;\n  var previousRef = usePrevious(args);\n  var descriptor = useMemo(function () {\n    return {\n      id: args.droppableId,\n      type: args.type,\n      mode: args.mode\n    };\n  }, [args.droppableId, args.mode, args.type]);\n  var publishedDescriptorRef = useRef(descriptor);\n  var memoizedUpdateScroll = useMemo(function () {\n    return memoizeOne(function (x, y) {\n      !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant(false) : void 0;\n      var scroll = {\n        x: x,\n        y: y\n      };\n      marshal.updateDroppableScroll(descriptor.id, scroll);\n    });\n  }, [descriptor.id, marshal]);\n  var getClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n\n    return getScroll$1(dragging.env.closestScrollable);\n  }, []);\n  var updateScroll = useCallback(function () {\n    var scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  var scheduleScrollUpdate = useMemo(function () {\n    return rafSchd(updateScroll);\n  }, [updateScroll]);\n  var onClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant(false) : void 0;\n    var options = dragging.scrollOptions;\n\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  var getDimensionAndWatchScroll = useCallback(function (windowScroll, options) {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant(false) : void 0;\n    var previous = previousRef.current;\n    var ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant(false) : void 0;\n    var env = getEnv(ref);\n    var dragging = {\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    var dimension = getDimension({\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      windowScroll: windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    var scrollable = env.closestScrollable;\n\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  var getScrollWhileDragging = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant(false) : void 0;\n    return getScroll$1(closest);\n  }, []);\n  var dragStopped = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n\n    if (!closest) {\n      return;\n    }\n\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  var scroll = useCallback(function (change) {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant(false) : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  var callbacks = useMemo(function () {\n    return {\n      getDimensionAndWatchScroll: getDimensionAndWatchScroll,\n      getScrollWhileDragging: getScrollWhileDragging,\n      dragStopped: dragStopped,\n      scroll: scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      callbacks: callbacks\n    };\n  }, [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(function () {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return function () {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop$2() {}\n\nvar empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\n\nvar getSize = function getSize(_ref) {\n  var isAnimatingOpenOnMount = _ref.isAnimatingOpenOnMount,\n      placeholder = _ref.placeholder,\n      animate = _ref.animate;\n\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n\n  if (animate === 'close') {\n    return empty;\n  }\n\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\n\nvar getStyle = function getStyle(_ref2) {\n  var isAnimatingOpenOnMount = _ref2.isAnimatingOpenOnMount,\n      placeholder = _ref2.placeholder,\n      animate = _ref2.animate;\n  var size = getSize({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    placeholder: placeholder,\n    animate: animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\n\nfunction Placeholder(props) {\n  var animateOpenTimerRef = useRef(null);\n  var tryClearAnimateOpenTimer = useCallback(function () {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  var animate = props.animate,\n      onTransitionEnd = props.onTransitionEnd,\n      onClose = props.onClose,\n      contextId = props.contextId;\n\n  var _useState = useState(props.animate === 'open'),\n      isAnimatingOpenOnMount = _useState[0],\n      setIsAnimatingOpenOnMount = _useState[1];\n\n  useEffect(function () {\n    if (!isAnimatingOpenOnMount) {\n      return noop$2;\n    }\n\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop$2;\n    }\n\n    if (animateOpenTimerRef.current) {\n      return noop$2;\n    }\n\n    animateOpenTimerRef.current = setTimeout(function () {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  var onSizeChangeEnd = useCallback(function (event) {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n\n    onTransitionEnd();\n\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  var style = getStyle({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style: style,\n    'data-rbd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n}\n\nvar Placeholder$1 = React.memo(Placeholder);\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"\\n    provided.innerRef has not been provided with a HTMLElement.\\n\\n    You can find a guide on using the innerRef callback functions at:\\n    https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/using-inner-ref.md\\n  \") : invariant(false) : void 0;\n}\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\n\nfunction runChecks(args, checks) {\n  checks.forEach(function (check) {\n    return check(args);\n  });\n}\n\nvar shared = [function required(_ref) {\n  var props = _ref.props;\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant(false) : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"A Droppable requires a [string] droppableId. Provided: [\" + typeof props.droppableId + \"]\") : invariant(false) : void 0;\n}, function _boolean(_ref2) {\n  var props = _ref2.props;\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant(false) : void 0;\n}, function ref(_ref3) {\n  var getDroppableRef = _ref3.getDroppableRef;\n  checkIsValidInnerRef(getDroppableRef());\n}];\nvar standard = [function placeholder(_ref4) {\n  var props = _ref4.props,\n      getPlaceholderRef = _ref4.getPlaceholderRef;\n\n  if (!props.placeholder) {\n    return;\n  }\n\n  var ref = getPlaceholderRef();\n\n  if (ref) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Droppable setup issue [droppableId: \\\"\" + props.droppableId + \"\\\"]:\\n      DroppableProvided > placeholder could not be found.\\n\\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\\n      More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/api/droppable.md\\n    \") : void 0;\n}];\nvar virtual = [function hasClone(_ref5) {\n  var props = _ref5.props;\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant(false) : void 0;\n}, function hasNoPlaceholder(_ref6) {\n  var getPlaceholderRef = _ref6.getPlaceholderRef;\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant(false) : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(function () {\n    runChecks(args, shared);\n\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nvar AnimateInOut = function (_React$PureComponent) {\n  _inheritsLoose(AnimateInOut, _React$PureComponent);\n\n  function AnimateInOut() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$PureComponent.call.apply(_React$PureComponent, [this].concat(args)) || this;\n    _this.state = {\n      isVisible: Boolean(_this.props.on),\n      data: _this.props.on,\n      animate: _this.props.shouldAnimate && _this.props.on ? 'open' : 'none'\n    };\n\n    _this.onClose = function () {\n      if (_this.state.animate !== 'close') {\n        return;\n      }\n\n      _this.setState({\n        isVisible: false\n      });\n    };\n\n    return _this;\n  }\n\n  AnimateInOut.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  };\n\n  var _proto = AnimateInOut.prototype;\n\n  _proto.render = function render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n\n    var provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  };\n\n  return AnimateInOut;\n}(React.PureComponent);\n\nvar zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\n\nvar getDraggingTransition = function getDraggingTransition(shouldAnimateDragMovement, dropping) {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n\n  return transitions.fluid;\n};\n\nvar getDraggingOpacity = function getDraggingOpacity(isCombining, isDropAnimating) {\n  if (!isCombining) {\n    return null;\n  }\n\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\n\nvar getShouldDraggingAnimate = function getShouldDraggingAnimate(dragging) {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n\n  return dragging.mode === 'SNAP';\n};\n\nfunction getDraggingStyle(dragging) {\n  var dimension = dragging.dimension;\n  var box = dimension.client;\n  var offset = dragging.offset,\n      combineWith = dragging.combineWith,\n      dropping = dragging.dropping;\n  var isCombining = Boolean(combineWith);\n  var shouldAnimate = getShouldDraggingAnimate(dragging);\n  var isDropAnimating = Boolean(dropping);\n  var transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  var style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform: transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\n\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? null : 'none'\n  };\n}\n\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll) {\n  if (windowScroll === void 0) {\n    windowScroll = origin;\n  }\n\n  var computedStyles = window.getComputedStyle(el);\n  var borderBox = el.getBoundingClientRect();\n  var client = calculateBox(borderBox, computedStyles);\n  var page = withScroll(client, windowScroll);\n  var placeholder = {\n    client: client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  var displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  var dimension = {\n    descriptor: descriptor,\n    placeholder: placeholder,\n    displaceBy: displaceBy,\n    client: client,\n    page: page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  var uniqueId = useUniqueId('draggable');\n  var descriptor = args.descriptor,\n      registry = args.registry,\n      getDraggableRef = args.getDraggableRef,\n      canDragInteractiveElements = args.canDragInteractiveElements,\n      shouldRespectForcePress = args.shouldRespectForcePress,\n      isEnabled = args.isEnabled;\n  var options = useMemo(function () {\n    return {\n      canDragInteractiveElements: canDragInteractiveElements,\n      shouldRespectForcePress: shouldRespectForcePress,\n      isEnabled: isEnabled\n    };\n  }, [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  var getDimension = useCallback(function (windowScroll) {\n    var el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant(false) : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      options: options,\n      getDimension: getDimension\n    };\n  }, [descriptor, getDimension, options, uniqueId]);\n  var publishedRef = useRef(entry);\n  var isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(function () {\n    registry.draggable.register(publishedRef.current);\n    return function () {\n      return registry.draggable.unregister(publishedRef.current);\n    };\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(function () {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n\n    var last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(function () {\n    function prefix(id) {\n      return \"Draggable[id: \" + id + \"]: \";\n    }\n\n    var id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Draggable requires a [string] draggableId.\\n      Provided: [type: \" + typeof id + \"] (value: \" + id + \")\") : invariant(false) : void 0;\n    !isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" requires an integer index prop\") : invariant(false) : void 0;\n\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n\n    checkIsValidInnerRef(getRef());\n\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" Unable to find drag handle\") : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(function () {\n    var initialRef = useRef(isClone);\n    useDevSetupWarning(function () {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\n\nfunction Draggable(props) {\n  var ref = useRef(null);\n  var setRef = useCallback(function (el) {\n    ref.current = el;\n  }, []);\n  var getRef = useCallback(function () {\n    return ref.current;\n  }, []);\n\n  var _useRequiredContext = useRequiredContext(AppContext),\n      contextId = _useRequiredContext.contextId,\n      dragHandleUsageInstructionsId = _useRequiredContext.dragHandleUsageInstructionsId,\n      registry = _useRequiredContext.registry;\n\n  var _useRequiredContext2 = useRequiredContext(DroppableContext),\n      type = _useRequiredContext2.type,\n      droppableId = _useRequiredContext2.droppableId;\n\n  var descriptor = useMemo(function () {\n    return {\n      id: props.draggableId,\n      index: props.index,\n      type: type,\n      droppableId: droppableId\n    };\n  }, [props.draggableId, props.index, type, droppableId]);\n  var children = props.children,\n      draggableId = props.draggableId,\n      isEnabled = props.isEnabled,\n      shouldRespectForcePress = props.shouldRespectForcePress,\n      canDragInteractiveElements = props.canDragInteractiveElements,\n      isClone = props.isClone,\n      mapped = props.mapped,\n      dropAnimationFinishedAction = props.dropAnimationFinished;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n\n  if (!isClone) {\n    var forPublisher = useMemo(function () {\n      return {\n        descriptor: descriptor,\n        registry: registry,\n        getDraggableRef: getRef,\n        canDragInteractiveElements: canDragInteractiveElements,\n        shouldRespectForcePress: shouldRespectForcePress,\n        isEnabled: isEnabled\n      };\n    }, [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n\n  var dragHandleProps = useMemo(function () {\n    return isEnabled ? {\n      tabIndex: 0,\n      role: 'button',\n      'aria-describedby': dragHandleUsageInstructionsId,\n      'data-rbd-drag-handle-draggable-id': draggableId,\n      'data-rbd-drag-handle-context-id': contextId,\n      draggable: false,\n      onDragStart: preventHtml5Dnd\n    } : null;\n  }, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  var onMoveEnd = useCallback(function (event) {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n\n    if (!mapped.dropping) {\n      return;\n    }\n\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n\n    dropAnimationFinishedAction();\n  }, [dropAnimationFinishedAction, mapped]);\n  var provided = useMemo(function () {\n    var style = getStyle$1(mapped);\n    var onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : null;\n    var result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rbd-draggable-context-id': contextId,\n        'data-rbd-draggable-id': draggableId,\n        style: style,\n        onTransitionEnd: onTransitionEnd\n      },\n      dragHandleProps: dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  var rubric = useMemo(function () {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  }, [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return children(provided, mapped.snapshot, rubric);\n}\n\nvar isStrictEqual = (function (a, b) {\n  return a === b;\n});\n\nvar whatIsDraggedOverFromResult = (function (result) {\n  var combine = result.combine,\n      destination = result.destination;\n\n  if (destination) {\n    return destination.droppableId;\n  }\n\n  if (combine) {\n    return combine.droppableId;\n  }\n\n  return null;\n});\n\nvar getCombineWithFromResult = function getCombineWithFromResult(result) {\n  return result.combine ? result.combine.draggableId : null;\n};\n\nvar getCombineWithFromImpact = function getCombineWithFromImpact(impact) {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\n\nfunction getDraggableSelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(function (mode, isClone, draggingOver, combineWith, dropping) {\n    return {\n      isDragging: true,\n      isClone: isClone,\n      isDropAnimating: Boolean(dropping),\n      dropAnimation: dropping,\n      mode: mode,\n      draggingOver: draggingOver,\n      combineWith: combineWith,\n      combineTargetFor: null\n    };\n  });\n  var getMemoizedProps = memoizeOne(function (offset, mode, dimension, isClone, draggingOver, combineWith, forceShouldAnimate) {\n    return {\n      mapped: {\n        type: 'DRAGGING',\n        dropping: null,\n        draggingOver: draggingOver,\n        combineWith: combineWith,\n        mode: mode,\n        offset: offset,\n        dimension: dimension,\n        forceShouldAnimate: forceShouldAnimate,\n        snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n      }\n    };\n  });\n\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n\n      var offset = state.current.client.offset;\n      var dimension = state.dimensions.draggables[ownProps.draggableId];\n      var draggingOver = whatIsDraggedOver(state.impact);\n      var combineWith = getCombineWithFromImpact(state.impact);\n      var forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n\n      var isClone = ownProps.isClone;\n      var _dimension = state.dimensions.draggables[ownProps.draggableId];\n      var result = completed.result;\n      var mode = result.mode;\n\n      var _draggingOver = whatIsDraggedOverFromResult(result);\n\n      var _combineWith = getCombineWithFromResult(result);\n\n      var duration = state.dropDuration;\n      var dropping = {\n        duration: duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: _combineWith ? combine.opacity.drop : null,\n        scale: _combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension: _dimension,\n          dropping: dropping,\n          draggingOver: _draggingOver,\n          combineWith: _combineWith,\n          mode: mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, _draggingOver, _combineWith, dropping)\n        }\n      };\n    }\n\n    return null;\n  };\n\n  return selector;\n}\n\nfunction getSecondarySnapshot(combineTargetFor) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor: combineTargetFor,\n    combineWith: null\n  };\n}\n\nvar atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\n\nfunction getSecondarySelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  var getMemoizedProps = memoizeOne(function (offset, combineTargetFor, shouldAnimateDisplacement) {\n    if (combineTargetFor === void 0) {\n      combineTargetFor = null;\n    }\n\n    return {\n      mapped: {\n        type: 'SECONDARY',\n        offset: offset,\n        combineTargetFor: combineTargetFor,\n        shouldAnimateDisplacement: shouldAnimateDisplacement,\n        snapshot: getMemoizedSnapshot(combineTargetFor)\n      }\n    };\n  });\n\n  var getFallback = function getFallback(combineTargetFor) {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n\n  var getProps = function getProps(ownId, draggingId, impact, afterCritical) {\n    var visualDisplacement = impact.displaced.visible[ownId];\n    var isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    var combine = tryGetCombine(impact);\n    var combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n\n      var change = negate(afterCritical.displacedBy.point);\n\n      var _offset = memoizedOffset(change.x, change.y);\n\n      return getMemoizedProps(_offset, combineTargetFor, true);\n    }\n\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n\n    var displaceBy = impact.displacedBy.point;\n    var offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n\n    return null;\n  };\n\n  return selector;\n}\n\nvar makeMapStateToProps = function makeMapStateToProps() {\n  var draggingSelector = getDraggableSelector();\n  var secondarySelector = getSecondarySelector();\n\n  var selector = function selector(state, ownProps) {\n    return draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  };\n\n  return selector;\n};\nvar mapDispatchToProps = {\n  dropAnimationFinished: dropAnimationFinished\n};\nvar ConnectedDraggable = connect(makeMapStateToProps, mapDispatchToProps, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\n\nfunction PrivateDraggable(props) {\n  var droppableContext = useRequiredContext(DroppableContext);\n  var isUsingCloneFor = droppableContext.isUsingCloneFor;\n\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  var isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  var canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  var shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nfunction Droppable(props) {\n  var appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant(false) : void 0;\n  var contextId = appContext.contextId,\n      isMovementAllowed = appContext.isMovementAllowed;\n  var droppableRef = useRef(null);\n  var placeholderRef = useRef(null);\n  var children = props.children,\n      droppableId = props.droppableId,\n      type = props.type,\n      mode = props.mode,\n      direction = props.direction,\n      ignoreContainerClipping = props.ignoreContainerClipping,\n      isDropDisabled = props.isDropDisabled,\n      isCombineEnabled = props.isCombineEnabled,\n      snapshot = props.snapshot,\n      useClone = props.useClone,\n      updateViewportMaxScroll = props.updateViewportMaxScroll,\n      getContainerForClone = props.getContainerForClone;\n  var getDroppableRef = useCallback(function () {\n    return droppableRef.current;\n  }, []);\n  var setDroppableRef = useCallback(function (value) {\n    droppableRef.current = value;\n  }, []);\n  var getPlaceholderRef = useCallback(function () {\n    return placeholderRef.current;\n  }, []);\n  var setPlaceholderRef = useCallback(function (value) {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props: props,\n    getDroppableRef: getDroppableRef,\n    getPlaceholderRef: getPlaceholderRef\n  });\n  var onPlaceholderTransitionEnd = useCallback(function () {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId: droppableId,\n    type: type,\n    mode: mode,\n    direction: direction,\n    isDropDisabled: isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    ignoreContainerClipping: ignoreContainerClipping,\n    getDroppableRef: getDroppableRef\n  });\n  var placeholder = React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, function (_ref) {\n    var onClose = _ref.onClose,\n        data = _ref.data,\n        animate = _ref.animate;\n    return React.createElement(Placeholder$1, {\n      placeholder: data,\n      onClose: onClose,\n      innerRef: setPlaceholderRef,\n      animate: animate,\n      contextId: contextId,\n      onTransitionEnd: onPlaceholderTransitionEnd\n    });\n  });\n  var provided = useMemo(function () {\n    return {\n      innerRef: setDroppableRef,\n      placeholder: placeholder,\n      droppableProps: {\n        'data-rbd-droppable-id': droppableId,\n        'data-rbd-droppable-context-id': contextId\n      }\n    };\n  }, [contextId, droppableId, placeholder, setDroppableRef]);\n  var isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  var droppableContext = useMemo(function () {\n    return {\n      droppableId: droppableId,\n      type: type,\n      isUsingCloneFor: isUsingCloneFor\n    };\n  }, [droppableId, isUsingCloneFor, type]);\n\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n\n    var dragging = useClone.dragging,\n        render = useClone.render;\n    var node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, function (draggableProvided, draggableSnapshot) {\n      return render(draggableProvided, draggableSnapshot, dragging);\n    });\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n}\n\nvar isMatchingType = function isMatchingType(type, critical) {\n  return type === critical.droppable.type;\n};\n\nvar getDraggable = function getDraggable(critical, dimensions) {\n  return dimensions.draggables[critical.draggable.id];\n};\n\nvar makeMapStateToProps$1 = function makeMapStateToProps() {\n  var idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n\n  var idleWithoutAnimation = _extends({}, idleWithAnimation, {\n    shouldAnimatePlaceholder: false\n  });\n\n  var getDraggableRubric = memoizeOne(function (descriptor) {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  });\n  var getMapProps = memoizeOne(function (id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) {\n    var draggableId = dragging.descriptor.id;\n    var isHome = dragging.descriptor.droppableId === id;\n\n    if (isHome) {\n      var useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      var _snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot: _snapshot,\n        useClone: useClone\n      };\n    }\n\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n\n    var snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot: snapshot,\n      useClone: null\n    };\n  });\n\n  var selector = function selector(state, ownProps) {\n    var id = ownProps.droppableId;\n    var type = ownProps.type;\n    var isEnabled = !ownProps.isDropDisabled;\n    var renderClone = ownProps.renderClone;\n\n    if (state.isDragging) {\n      var critical = state.critical;\n\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var dragging = getDraggable(critical, state.dimensions);\n      var isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var _dragging = getDraggable(completed.critical, state.dimensions);\n\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, _dragging, renderClone);\n    }\n\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      var _completed = state.completed;\n\n      if (!isMatchingType(type, _completed.critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var wasOver = whatIsDraggedOver(_completed.impact) === id;\n      var wasCombining = Boolean(_completed.impact.at && _completed.impact.at.type === 'COMBINE');\n      var isHome = _completed.critical.droppable.id === id;\n\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n\n      if (isHome) {\n        return idleWithAnimation;\n      }\n\n      return idleWithoutAnimation;\n    }\n\n    return idleWithoutAnimation;\n  };\n\n  return selector;\n};\nvar mapDispatchToProps$1 = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant(false) : void 0;\n  return document.body;\n}\n\nvar defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nvar ConnectedDroppable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\nConnectedDroppable.defaultProps = defaultProps;\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable as Droppable, resetServerContext, useKeyboardSensor, useMouseSensor, useTouchSensor };\n", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "import React, { useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider(_ref) {\n  var store = _ref.store,\n      context = _ref.context,\n      children = _ref.children;\n  var contextValue = useMemo(function () {\n    var subscription = createSubscription(store);\n    return {\n      store: store,\n      subscription: subscription\n    };\n  }, [store]);\n  var previousState = useMemo(function () {\n    return store.getState();\n  }, [store]);\n  useIsomorphicLayoutEffect(function () {\n    var subscription = contextValue.subscription;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return function () {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = null;\n    };\n  }, [contextValue, previousState]);\n  var Context = context || ReactReduxContext;\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nif (process.env.NODE_ENV !== 'production') {\n  Provider.propTypes = {\n    store: PropTypes.shape({\n      subscribe: PropTypes.func.isRequired,\n      dispatch: PropTypes.func.isRequired,\n      getState: PropTypes.func.isRequired\n    }),\n    context: PropTypes.object,\n    children: PropTypes.any\n  };\n}\n\nexport default Provider;", "import React from 'react';\nexport var ReactReduxContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\n\nexport default ReactReduxContext;", "// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\n\nvar batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport var setBatch = function setBatch(newBatch) {\n  return batch = newBatch;\n}; // Supply a getter just to skip dealing with ESM bindings\n\nexport var getBatch = function getBatch() {\n  return batch;\n};", "import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  var batch = getBatch();\n  var first = null;\n  var last = null;\n  return {\n    clear: function clear() {\n      first = null;\n      last = null;\n    },\n    notify: function notify() {\n      batch(function () {\n        var listener = first;\n\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get: function get() {\n      var listeners = [];\n      var listener = first;\n\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n\n      return listeners;\n    },\n    subscribe: function subscribe(callback) {\n      var isSubscribed = true;\n      var listener = last = {\n        callback: callback,\n        next: null,\n        prev: last\n      };\n\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\n\nvar nullListeners = {\n  notify: function notify() {},\n  get: function get() {\n    return [];\n  }\n};\nexport function createSubscription(store, parentSub) {\n  var unsubscribe;\n  var listeners = nullListeners;\n\n  function addNestedSub(listener) {\n    trySubscribe();\n    return listeners.subscribe(listener);\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n\n  function isSubscribed() {\n    return Boolean(unsubscribe);\n  }\n\n  function trySubscribe() {\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n\n  function tryUnsubscribe() {\n    if (unsubscribe) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n\n  var subscription = {\n    addNestedSub: addNestedSub,\n    notifyNestedSubs: notifyNestedSubs,\n    handleChangeWrapper: handleChangeWrapper,\n    isSubscribed: isSubscribed,\n    trySubscribe: trySubscribe,\n    tryUnsubscribe: tryUnsubscribe,\n    getListeners: function getListeners() {\n      return listeners;\n    }\n  };\n  return subscription;\n}", "import { useEffect, useLayoutEffect } from 'react'; // <PERSON><PERSON> currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"getDisplayName\", \"methodName\", \"renderCountProp\", \"shouldHandleStateChanges\", \"storeKey\", \"withRef\", \"forwardRef\", \"context\"],\n    _excluded2 = [\"reactReduxForwardedRef\"];\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef, useReducer } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from './Context'; // Define some constant arrays just to avoid re-creating these\n\nvar EMPTY_ARRAY = [];\nvar NO_SUBSCRIPTION_ARRAY = [null, null];\n\nvar stringifyComponent = function stringifyComponent(Comp) {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\n\nfunction storeStateUpdatesReducer(state, action) {\n  var updateCount = state[1];\n  return [action.payload, updateCount + 1];\n}\n\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(function () {\n    return effectFunc.apply(void 0, effectArgs);\n  }, dependencies);\n}\n\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  lastChildProps.current = actualChildProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n}\n\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return; // Capture values for checking if and when this component unmounts\n\n  var didUnsubscribe = false;\n  var lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  var checkForUpdates = function checkForUpdates() {\n    if (didUnsubscribe) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    }\n\n    var latestStoreState = store.getState();\n    var newChildProps, error;\n\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // If the child props _did_ change (or we caught an error), this wrapper component needs to re-render\n\n      forceComponentUpdateDispatch({\n        type: 'STORE_UPDATED',\n        payload: {\n          error: error\n        }\n      });\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n\n  var unsubscribeWrapper = function unsubscribeWrapper() {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n\n  return unsubscribeWrapper;\n}\n\nvar initStateUpdates = function initStateUpdates() {\n  return [null, 0];\n};\n\nexport default function connectAdvanced(\n/*\r\n  selectorFactory is a func that is responsible for returning the selector function used to\r\n  compute new props from state, props, and dispatch. For example:\r\n      export default connectAdvanced((dispatch, options) => (state, props) => ({\r\n      thing: state.things[props.thingId],\r\n      saveThing: fields => dispatch(actionCreators.saveThing(props.thingId, fields)),\r\n    }))(YourComponent)\r\n    Access to dispatch is provided to the factory so selectorFactories can bind actionCreators\r\n  outside of their selector as an optimization. Options passed to connectAdvanced are passed to\r\n  the selectorFactory, along with displayName and WrappedComponent, as the second argument.\r\n    Note that selectorFactory is responsible for all caching/memoization of inbound and outbound\r\n  props. Do not use connectAdvanced directly without memoizing results between calls to your\r\n  selector, otherwise the Connect component will re-render on every state or props change.\r\n*/\nselectorFactory, // options object:\n_ref) {\n  if (_ref === void 0) {\n    _ref = {};\n  }\n\n  var _ref2 = _ref,\n      _ref2$getDisplayName = _ref2.getDisplayName,\n      getDisplayName = _ref2$getDisplayName === void 0 ? function (name) {\n    return \"ConnectAdvanced(\" + name + \")\";\n  } : _ref2$getDisplayName,\n      _ref2$methodName = _ref2.methodName,\n      methodName = _ref2$methodName === void 0 ? 'connectAdvanced' : _ref2$methodName,\n      _ref2$renderCountProp = _ref2.renderCountProp,\n      renderCountProp = _ref2$renderCountProp === void 0 ? undefined : _ref2$renderCountProp,\n      _ref2$shouldHandleSta = _ref2.shouldHandleStateChanges,\n      shouldHandleStateChanges = _ref2$shouldHandleSta === void 0 ? true : _ref2$shouldHandleSta,\n      _ref2$storeKey = _ref2.storeKey,\n      storeKey = _ref2$storeKey === void 0 ? 'store' : _ref2$storeKey,\n      _ref2$withRef = _ref2.withRef,\n      withRef = _ref2$withRef === void 0 ? false : _ref2$withRef,\n      _ref2$forwardRef = _ref2.forwardRef,\n      forwardRef = _ref2$forwardRef === void 0 ? false : _ref2$forwardRef,\n      _ref2$context = _ref2.context,\n      context = _ref2$context === void 0 ? ReactReduxContext : _ref2$context,\n      connectOptions = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (renderCountProp !== undefined) {\n      throw new Error(\"renderCountProp is removed. render counting is built into the latest React Dev Tools profiling extension\");\n    }\n\n    if (withRef) {\n      throw new Error('withRef is removed. To access the wrapped instance, use a ref on the connected component');\n    }\n\n    var customStoreWarningMessage = 'To use a custom Redux store for specific components, create a custom React context with ' + \"React.createContext(), and pass the context object to React Redux's Provider and specific components\" + ' like: <Provider context={MyContext}><ConnectedComponent context={MyContext} /></Provider>. ' + 'You may also pass a {context : MyContext} option to connect';\n\n    if (storeKey !== 'store') {\n      throw new Error('storeKey has been removed and does not do anything. ' + customStoreWarningMessage);\n    }\n  }\n\n  var Context = context;\n  return function wrapWithConnect(WrappedComponent) {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(\"You must pass a component to the function returned by \" + (methodName + \". Instead received \" + stringifyComponent(WrappedComponent)));\n    }\n\n    var wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    var displayName = getDisplayName(wrappedComponentName);\n\n    var selectorFactoryOptions = _extends({}, connectOptions, {\n      getDisplayName: getDisplayName,\n      methodName: methodName,\n      renderCountProp: renderCountProp,\n      shouldHandleStateChanges: shouldHandleStateChanges,\n      storeKey: storeKey,\n      displayName: displayName,\n      wrappedComponentName: wrappedComponentName,\n      WrappedComponent: WrappedComponent\n    });\n\n    var pure = connectOptions.pure;\n\n    function createChildSelector(store) {\n      return selectorFactory(store.dispatch, selectorFactoryOptions);\n    } // If we aren't running in \"pure\" mode, we don't want to memoize values.\n    // To avoid conditionally calling hooks, we fall back to a tiny wrapper\n    // that just executes the given callback immediately.\n\n\n    var usePureOnlyMemo = pure ? useMemo : function (callback) {\n      return callback();\n    };\n\n    function ConnectFunction(props) {\n      var _useMemo = useMemo(function () {\n        // Distinguish between actual \"data\" props that were passed to the wrapper component,\n        // and values needed to control behavior (forwarded refs, alternate context instances).\n        // To maintain the wrapperProps object reference, memoize this destructuring.\n        var reactReduxForwardedRef = props.reactReduxForwardedRef,\n            wrapperProps = _objectWithoutPropertiesLoose(props, _excluded2);\n\n        return [props.context, reactReduxForwardedRef, wrapperProps];\n      }, [props]),\n          propsContext = _useMemo[0],\n          reactReduxForwardedRef = _useMemo[1],\n          wrapperProps = _useMemo[2];\n\n      var ContextToUse = useMemo(function () {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer && isContextConsumer( /*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      var contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      var didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      var didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(\"Could not find \\\"store\\\" in the context of \" + (\"\\\"\" + displayName + \"\\\". Either wrap the root component in a <Provider>, \") + \"or pass a custom React context provider to <Provider> and the corresponding \" + (\"React context consumer to \" + displayName + \" in connect options.\"));\n      } // Based on the previous check, one of these must be true\n\n\n      var store = didStoreComeFromProps ? props.store : contextValue.store;\n      var childPropsSelector = useMemo(function () {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return createChildSelector(store);\n      }, [store]);\n\n      var _useMemo2 = useMemo(function () {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n        // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n        var subscription = createSubscription(store, didStoreComeFromProps ? null : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n        var notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n        return [subscription, notifyNestedSubs];\n      }, [store, didStoreComeFromProps, contextValue]),\n          subscription = _useMemo2[0],\n          notifyNestedSubs = _useMemo2[1]; // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n\n      var overriddenContextValue = useMemo(function () {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n\n        return _extends({}, contextValue, {\n          subscription: subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // We need to force this wrapper component to re-render whenever a Redux store update\n      // causes a change to the calculated child component props (or we caught an error in mapState)\n\n      var _useReducer = useReducer(storeStateUpdatesReducer, EMPTY_ARRAY, initStateUpdates),\n          _useReducer$ = _useReducer[0],\n          previousStateUpdateResult = _useReducer$[0],\n          forceComponentUpdateDispatch = _useReducer[1]; // Propagate any mapState/mapDispatch errors upwards\n\n\n      if (previousStateUpdateResult && previousStateUpdateResult.error) {\n        throw previousStateUpdateResult.error;\n      } // Set up refs to coordinate values between the subscription effect and the render logic\n\n\n      var lastChildProps = useRef();\n      var lastWrapperProps = useRef(wrapperProps);\n      var childPropsFromStoreUpdate = useRef();\n      var renderIsScheduled = useRef(false);\n      var actualChildProps = usePureOnlyMemo(function () {\n        // Tricky logic here:\n        // - This render may have been triggered by a Redux store update that produced new child props\n        // - However, we may have gotten new wrapper props after that\n        // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n        // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n        // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n        if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n          return childPropsFromStoreUpdate.current;\n        } // TODO We're reading the store directly in render() here. Bad idea?\n        // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n        // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n        // to determine what the child props should be.\n\n\n        return childPropsSelector(store.getState(), wrapperProps);\n      }, [store, previousStateUpdateResult, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs]); // Our re-subscribe logic only runs when the store/subscription setup changes\n\n      useIsomorphicLayoutEffectWithArgs(subscribeUpdates, [shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch], [store, subscription, childPropsSelector]); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      var renderedWrappedComponent = useMemo(function () {\n        return /*#__PURE__*/React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n          ref: reactReduxForwardedRef\n        }));\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      var renderedChild = useMemo(function () {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    } // If we're in \"pure\" mode, ensure our wrapper component only re-renders when incoming props have changed.\n\n\n    var Connect = pure ? React.memo(ConnectFunction) : ConnectFunction;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n\n    if (forwardRef) {\n      var forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n\n    return hoistStatics(Connect, WrappedComponent);\n  };\n}", "function is(x, y) {\n  if (x === y) {\n    return x !== 0 || y !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nexport default function shallowEqual(objA, objB) {\n  if (is(objA, objB)) return true;\n\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}", "export default function bindActionCreators(actionCreators, dispatch) {\n  var boundActionCreators = {};\n\n  var _loop = function _loop(key) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = function () {\n        return dispatch(actionCreator.apply(void 0, arguments));\n      };\n    }\n  };\n\n  for (var key in actionCreators) {\n    _loop(key);\n  }\n\n  return boundActionCreators;\n}", "/**\r\n * @param {any} obj The object to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n */\nexport default function isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = Object.getPrototypeOf(obj);\n  if (proto === null) return true;\n  var baseProto = proto;\n\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto);\n  }\n\n  return proto === baseProto;\n}", "/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\nexport default function warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n    /* eslint-disable no-empty */\n  } catch (e) {}\n  /* eslint-enable no-empty */\n\n}", "import isPlainObject from './isPlainObject';\nimport warning from './warning';\nexport default function verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(methodName + \"() in \" + displayName + \" must return a plain object. Instead received \" + value + \".\");\n  }\n}", "import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant(getConstant) {\n  return function initConstantSelector(dispatch, options) {\n    var constant = getConstant(dispatch, options);\n\n    function constantSelector() {\n      return constant;\n    }\n\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps !== null && mapToProps.dependsOnOwnProps !== undefined ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, _ref) {\n    var displayName = _ref.displayName;\n\n    var proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n\n    proxy.dependsOnOwnProps = true;\n\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      var props = proxy(stateOrDispatch, ownProps);\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n\n    return proxy;\n  };\n}", "import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapDispatchToPropsIsFunction(mapDispatchToProps) {\n  return typeof mapDispatchToProps === 'function' ? wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : undefined;\n}\nexport function whenMapDispatchToPropsIsMissing(mapDispatchToProps) {\n  return !mapDispatchToProps ? wrapMapToPropsConstant(function (dispatch) {\n    return {\n      dispatch: dispatch\n    };\n  }) : undefined;\n}\nexport function whenMapDispatchToPropsIsObject(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(function (dispatch) {\n    return bindActionCreators(mapDispatchToProps, dispatch);\n  }) : undefined;\n}\nexport default [whenMapDispatchToPropsIsFunction, whenMapDispatchToPropsIsMissing, whenMapDispatchToPropsIsObject];", "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapStateToPropsIsFunction(mapStateToProps) {\n  return typeof mapStateToProps === 'function' ? wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : undefined;\n}\nexport function whenMapStateToPropsIsMissing(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(function () {\n    return {};\n  }) : undefined;\n}\nexport default [whenMapStateToPropsIsFunction, whenMapStateToPropsIsMissing];", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, _ref) {\n    var displayName = _ref.displayName,\n        pure = _ref.pure,\n        areMergedPropsEqual = _ref.areMergedPropsEqual;\n    var hasRunOnce = false;\n    var mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      var nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n\n      if (hasRunOnce) {\n        if (!pure || !areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n\n      return mergedProps;\n    };\n  };\n}\nexport function whenMergePropsIsFunction(mergeProps) {\n  return typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : undefined;\n}\nexport function whenMergePropsIsOmitted(mergeProps) {\n  return !mergeProps ? function () {\n    return defaultMergeProps;\n  } : undefined;\n}\nexport default [whenMergePropsIsFunction, whenMergePropsIsOmitted];", "import warning from '../utils/warning';\n\nfunction verify(selector, methodName, displayName) {\n  if (!selector) {\n    throw new Error(\"Unexpected value for \" + methodName + \" in \" + displayName + \".\");\n  } else if (methodName === 'mapStateToProps' || methodName === 'mapDispatchToProps') {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(\"The selector for \" + methodName + \" of \" + displayName + \" did not specify a value for dependsOnOwnProps.\");\n    }\n  }\n}\n\nexport default function verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, displayName) {\n  verify(mapStateToProps, 'mapStateToProps', displayName);\n  verify(mapDispatchToProps, 'mapDispatchToProps', displayName);\n  verify(mergeProps, 'mergeProps', displayName);\n}", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function impureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch) {\n  return function impureFinalPropsSelector(state, ownProps) {\n    return mergeProps(mapStateToProps(state, ownProps), mapDispatchToProps(dispatch, ownProps), ownProps);\n  };\n}\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, _ref) {\n  var areStatesEqual = _ref.areStatesEqual,\n      areOwnPropsEqual = _ref.areOwnPropsEqual,\n      areStatePropsEqual = _ref.areStatePropsEqual;\n  var hasRunAtLeastOnce = false;\n  var state;\n  var ownProps;\n  var stateProps;\n  var dispatchProps;\n  var mergedProps;\n\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewState() {\n    var nextStateProps = mapStateToProps(state, ownProps);\n    var statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    var propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    var stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n} // TODO: Add more comments\n// If pure is true, the selector returned by selectorFactory will memoize its results,\n// allowing connectAdvanced's shouldComponentUpdate to return false if final\n// props have not changed. If false, the selector will always return a new\n// object and shouldComponentUpdate will always return true.\n\nexport default function finalPropsSelectorFactory(dispatch, _ref2) {\n  var initMapStateToProps = _ref2.initMapStateToProps,\n      initMapDispatchToProps = _ref2.initMapDispatchToProps,\n      initMergeProps = _ref2.initMergeProps,\n      options = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  var mapStateToProps = initMapStateToProps(dispatch, options);\n  var mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  var mergeProps = initMergeProps(dispatch, options);\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, options.displayName);\n  }\n\n  var selectorFactory = options.pure ? pureFinalPropsSelectorFactory : impureFinalPropsSelectorFactory;\n  return selectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"pure\", \"areStatesEqual\", \"areOwnPropsEqual\", \"areStatePropsEqual\", \"areMergedPropsEqual\"];\nimport connectAdvanced from '../components/connectAdvanced';\nimport shallowEqual from '../utils/shallowEqual';\nimport defaultMapDispatchToPropsFactories from './mapDispatchToProps';\nimport defaultMapStateToPropsFactories from './mapStateToProps';\nimport defaultMergePropsFactories from './mergeProps';\nimport defaultSelectorFactory from './selectorFactory';\n/*\r\n  connect is a facade over connectAdvanced. It turns its args into a compatible\r\n  selectorFactory, which has the signature:\r\n\r\n    (dispatch, options) => (nextState, nextOwnProps) => nextFinalProps\r\n  \r\n  connect passes its args to connectAdvanced as options, which will in turn pass them to\r\n  selectorFactory each time a Connect component instance is instantiated or hot reloaded.\r\n\r\n  selectorFactory returns a final props selector from its mapStateToProps,\r\n  mapStateToPropsFactories, mapDispatchToProps, mapDispatchToPropsFactories, mergeProps,\r\n  mergePropsFactories, and pure args.\r\n\r\n  The resulting final props selector is called by the Connect component instance whenever\r\n  it receives new props or store state.\r\n */\n\nfunction match(arg, factories, name) {\n  for (var i = factories.length - 1; i >= 0; i--) {\n    var result = factories[i](arg);\n    if (result) return result;\n  }\n\n  return function (dispatch, options) {\n    throw new Error(\"Invalid value of type \" + typeof arg + \" for \" + name + \" argument when connecting component \" + options.wrappedComponentName + \".\");\n  };\n}\n\nfunction strictEqual(a, b) {\n  return a === b;\n} // createConnect with default args builds the 'official' connect behavior. Calling it with\n// different options opens up some testing and extensibility scenarios\n\n\nexport function createConnect(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      _ref$connectHOC = _ref.connectHOC,\n      connectHOC = _ref$connectHOC === void 0 ? connectAdvanced : _ref$connectHOC,\n      _ref$mapStateToPropsF = _ref.mapStateToPropsFactories,\n      mapStateToPropsFactories = _ref$mapStateToPropsF === void 0 ? defaultMapStateToPropsFactories : _ref$mapStateToPropsF,\n      _ref$mapDispatchToPro = _ref.mapDispatchToPropsFactories,\n      mapDispatchToPropsFactories = _ref$mapDispatchToPro === void 0 ? defaultMapDispatchToPropsFactories : _ref$mapDispatchToPro,\n      _ref$mergePropsFactor = _ref.mergePropsFactories,\n      mergePropsFactories = _ref$mergePropsFactor === void 0 ? defaultMergePropsFactories : _ref$mergePropsFactor,\n      _ref$selectorFactory = _ref.selectorFactory,\n      selectorFactory = _ref$selectorFactory === void 0 ? defaultSelectorFactory : _ref$selectorFactory;\n\n  return function connect(mapStateToProps, mapDispatchToProps, mergeProps, _ref2) {\n    if (_ref2 === void 0) {\n      _ref2 = {};\n    }\n\n    var _ref3 = _ref2,\n        _ref3$pure = _ref3.pure,\n        pure = _ref3$pure === void 0 ? true : _ref3$pure,\n        _ref3$areStatesEqual = _ref3.areStatesEqual,\n        areStatesEqual = _ref3$areStatesEqual === void 0 ? strictEqual : _ref3$areStatesEqual,\n        _ref3$areOwnPropsEqua = _ref3.areOwnPropsEqual,\n        areOwnPropsEqual = _ref3$areOwnPropsEqua === void 0 ? shallowEqual : _ref3$areOwnPropsEqua,\n        _ref3$areStatePropsEq = _ref3.areStatePropsEqual,\n        areStatePropsEqual = _ref3$areStatePropsEq === void 0 ? shallowEqual : _ref3$areStatePropsEq,\n        _ref3$areMergedPropsE = _ref3.areMergedPropsEqual,\n        areMergedPropsEqual = _ref3$areMergedPropsE === void 0 ? shallowEqual : _ref3$areMergedPropsE,\n        extraOptions = _objectWithoutPropertiesLoose(_ref3, _excluded);\n\n    var initMapStateToProps = match(mapStateToProps, mapStateToPropsFactories, 'mapStateToProps');\n    var initMapDispatchToProps = match(mapDispatchToProps, mapDispatchToPropsFactories, 'mapDispatchToProps');\n    var initMergeProps = match(mergeProps, mergePropsFactories, 'mergeProps');\n    return connectHOC(selectorFactory, _extends({\n      // used in error messages\n      methodName: 'connect',\n      // used to compute Connect's displayName from the wrapped component's displayName.\n      getDisplayName: function getDisplayName(name) {\n        return \"Connect(\" + name + \")\";\n      },\n      // if mapStateToProps is falsy, the Connect component doesn't subscribe to store state changes\n      shouldHandleStateChanges: Boolean(mapStateToProps),\n      // passed through to selectorFactory\n      initMapStateToProps: initMapStateToProps,\n      initMapDispatchToProps: initMapDispatchToProps,\n      initMergeProps: initMergeProps,\n      pure: pure,\n      areStatesEqual: areStatesEqual,\n      areOwnPropsEqual: areOwnPropsEqual,\n      areStatePropsEqual: areStatePropsEqual,\n      areMergedPropsEqual: areMergedPropsEqual\n    }, extraOptions));\n  };\n}\nexport default /*#__PURE__*/createConnect();", "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useStore() {\n    var _useReduxContext = useReduxContext(),\n        store = _useReduxContext.store;\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport var useStore = /*#__PURE__*/createStoreHook();", "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport function useReduxContext() {\n  var contextValue = useContext(ReactReduxContext);\n\n  if (process.env.NODE_ENV !== 'production' && !contextValue) {\n    throw new Error('could not find react-redux context value; please ensure the component is wrapped in a <Provider>');\n  }\n\n  return contextValue;\n}", "import { ReactReduxContext } from '../components/Context';\nimport { useStore as useDefaultStore, createStoreHook } from './useStore';\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\n\nexport function createDispatchHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useStore = context === ReactReduxContext ? useDefaultStore : createStoreHook(context);\n  return function useDispatch() {\n    var store = useStore();\n    return store.dispatch;\n  };\n}\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\n\nexport var useDispatch = /*#__PURE__*/createDispatchHook();", "import { useReducer, useRef, useMemo, useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from '../components/Context';\n\nvar refEquality = function refEquality(a, b) {\n  return a === b;\n};\n\nfunction useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub) {\n  var _useReducer = useReducer(function (s) {\n    return s + 1;\n  }, 0),\n      forceRender = _useReducer[1];\n\n  var subscription = useMemo(function () {\n    return createSubscription(store, contextSub);\n  }, [store, contextSub]);\n  var latestSubscriptionCallbackError = useRef();\n  var latestSelector = useRef();\n  var latestStoreState = useRef();\n  var latestSelectedState = useRef();\n  var storeState = store.getState();\n  var selectedState;\n\n  try {\n    if (selector !== latestSelector.current || storeState !== latestStoreState.current || latestSubscriptionCallbackError.current) {\n      var newSelectedState = selector(storeState); // ensure latest selected state is reused so that a custom equality function can result in identical references\n\n      if (latestSelectedState.current === undefined || !equalityFn(newSelectedState, latestSelectedState.current)) {\n        selectedState = newSelectedState;\n      } else {\n        selectedState = latestSelectedState.current;\n      }\n    } else {\n      selectedState = latestSelectedState.current;\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current) {\n      err.message += \"\\nThe error may be correlated with this previous error:\\n\" + latestSubscriptionCallbackError.current.stack + \"\\n\\n\";\n    }\n\n    throw err;\n  }\n\n  useIsomorphicLayoutEffect(function () {\n    latestSelector.current = selector;\n    latestStoreState.current = storeState;\n    latestSelectedState.current = selectedState;\n    latestSubscriptionCallbackError.current = undefined;\n  });\n  useIsomorphicLayoutEffect(function () {\n    function checkForUpdates() {\n      try {\n        var newStoreState = store.getState(); // Avoid calling selector multiple times if the store's state has not changed\n\n        if (newStoreState === latestStoreState.current) {\n          return;\n        }\n\n        var _newSelectedState = latestSelector.current(newStoreState);\n\n        if (equalityFn(_newSelectedState, latestSelectedState.current)) {\n          return;\n        }\n\n        latestSelectedState.current = _newSelectedState;\n        latestStoreState.current = newStoreState;\n      } catch (err) {\n        // we ignore all errors here, since when the component\n        // is re-rendered, the selectors are called again, and\n        // will throw again, if neither props nor store state\n        // changed\n        latestSubscriptionCallbackError.current = err;\n      }\n\n      forceRender();\n    }\n\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    return function () {\n      return subscription.tryUnsubscribe();\n    };\n  }, [store, subscription]);\n  return selectedState;\n}\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\n\nexport function createSelectorHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useSelector(selector, equalityFn) {\n    if (equalityFn === void 0) {\n      equalityFn = refEquality;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(\"You must pass a selector to useSelector\");\n      }\n\n      if (typeof selector !== 'function') {\n        throw new Error(\"You must pass a function as a selector to useSelector\");\n      }\n\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\"You must pass a function as an equality function to useSelector\");\n      }\n    }\n\n    var _useReduxContext = useReduxContext(),\n        store = _useReduxContext.store,\n        contextSub = _useReduxContext.subscription;\n\n    var selectedState = useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport var useSelector = /*#__PURE__*/createSelectorHook();", "/* eslint-disable import/no-unresolved */\nexport { unstable_batchedUpdates } from 'react-dom';", "export * from './exports';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch'; // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };", "import { useState, useRef, useEffect } from 'react';\n\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n\n  for (var i = 0; i < newInputs.length; i++) {\n    if (newInputs[i] !== lastInputs[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction useMemoOne(getResult, inputs) {\n  var initial = useState(function () {\n    return {\n      inputs: inputs,\n      result: getResult()\n    };\n  })[0];\n  var isFirstRun = useRef(true);\n  var committed = useRef(initial);\n  var useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  var cache = useCache ? committed.current : {\n    inputs: inputs,\n    result: getResult()\n  };\n  useEffect(function () {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallbackOne(callback, inputs) {\n  return useMemoOne(function () {\n    return callback;\n  }, inputs);\n}\nvar useMemo = useMemoOne;\nvar useCallback = useCallbackOne;\n\nexport { useCallback, useCallbackOne, useMemo, useMemoOne };\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "import invariant from 'tiny-invariant';\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };\n", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAOA,YAAI,qBAAqB;AACzB,YAAI,oBAAoB;AACxB,YAAI,sBAAsB;AAC1B,YAAI,yBAAyB;AAC7B,YAAI,sBAAsB;AAC1B,YAAI,sBAAsB;AAC1B,YAAI,qBAAqB;AACzB,YAAI,yBAAyB;AAC7B,YAAI,sBAAsB;AAC1B,YAAI,2BAA2B;AAC/B,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,mBAAmB;AACvB,YAAI,0BAA0B;AAC9B,YAAI,yBAAyB;AAC7B,YAAI,mBAAmB;AACvB,YAAI,uBAAuB;AAC3B,YAAI,gCAAgC;AACpC,YAAI,uBAAuB;AAC3B,YAAI,2BAA2B;AAE/B,YAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,cAAI,YAAY,OAAO;AACvB,+BAAqB,UAAU,eAAe;AAC9C,8BAAoB,UAAU,cAAc;AAC5C,gCAAsB,UAAU,gBAAgB;AAChD,mCAAyB,UAAU,mBAAmB;AACtD,gCAAsB,UAAU,gBAAgB;AAChD,gCAAsB,UAAU,gBAAgB;AAChD,+BAAqB,UAAU,eAAe;AAC9C,mCAAyB,UAAU,mBAAmB;AACtD,gCAAsB,UAAU,gBAAgB;AAChD,qCAA2B,UAAU,qBAAqB;AAC1D,4BAAkB,UAAU,YAAY;AACxC,4BAAkB,UAAU,YAAY;AACxC,6BAAmB,UAAU,aAAa;AAC1C,oCAA0B,UAAU,oBAAoB;AACxD,mCAAyB,UAAU,mBAAmB;AACtD,6BAAmB,UAAU,aAAa;AAC1C,iCAAuB,UAAU,iBAAiB;AAClD,0CAAgC,UAAU,wBAAwB;AAClE,iCAAuB,UAAU,iBAAiB;AAClD,qCAA2B,UAAU,qBAAqB;AAAA,QAC5D;AAIA,YAAI,iBAAiB;AAErB,iBAASA,oBAAmB,MAAM;AAChC,cAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,mBAAO;AAAA,UACT;AAGA,cAAI,SAAS,uBAAuB,SAAS,uBAAuB,SAAS,iCAAiC,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,SAAS,4BAA4B,gBAAiB;AAC1Q,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,gBAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,oBAAoB,KAAK,CAAC,MAAM,yBAAyB;AAChU,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAIC,WAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAC1C,YAAI,2CAA2C;AAE/C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,wFAA6F;AAAA,YAC/G;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,iBAAiB,QAAQ;AAChC;AACE,gBAAI,CAAC,0CAA0C;AAC7C,yDAA2C;AAE3C,sBAAQ,MAAM,EAAE,6FAAkG;AAAA,YACpH;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAASC,mBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAASC,WAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAUF;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoBC;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAYC;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqBH;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACjOA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA,IAAAI,gBAAgF;;;ACAhF,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUC,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,eAAe,GAAG,GAAG;AAC5B,IAAE,YAAY,OAAO,OAAO,EAAE,SAAS,GAAG,EAAE,UAAU,cAAc,GAAG,gBAAe,GAAG,CAAC;AAC5F;;;ACHA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK;AAAG,SAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACRA,IAAAE,gBAA+B;AAC/B,wBAAsB;;;ACDtB,mBAAkB;AACX,IAAI,oBAAiC,aAAAC,QAAM,cAAc,IAAI;AAEpE,IAAI,MAAuC;AACzC,oBAAkB,cAAc;AAClC;;;ACJA,SAAS,iBAAiB,UAAU;AAClC,WAAS;AACX;AAEA,IAAI,QAAQ;AAEL,IAAI,WAAW,SAASC,UAAS,UAAU;AAChD,SAAO,QAAQ;AACjB;AAEO,IAAI,WAAW,SAASC,YAAW;AACxC,SAAO;AACT;;;ACTA,SAAS,2BAA2B;AAClC,MAAIC,SAAQ,SAAS;AACrB,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,SAAO;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,cAAQ;AACR,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,SAASC,UAAS;AACxB,MAAAD,OAAM,WAAY;AAChB,YAAI,WAAW;AAEf,eAAO,UAAU;AACf,mBAAS,SAAS;AAClB,qBAAW,SAAS;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,KAAK,SAASE,OAAM;AAClB,UAAI,YAAY,CAAC;AACjB,UAAI,WAAW;AAEf,aAAO,UAAU;AACf,kBAAU,KAAK,QAAQ;AACvB,mBAAW,SAAS;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,UAAU,UAAU;AACtC,UAAI,eAAe;AACnB,UAAI,WAAW,OAAO;AAAA,QACpB;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,KAAK,OAAO;AAAA,MACvB,OAAO;AACL,gBAAQ;AAAA,MACV;AAEA,aAAO,SAAS,cAAc;AAC5B,YAAI,CAAC,gBAAgB,UAAU;AAAM;AACrC,uBAAe;AAEf,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;AAAA,QAChC,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAEA,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;AAAA,QAChC,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,SAAS,SAAS;AAAA,EAAC;AAAA,EAC3B,KAAK,SAAS,MAAM;AAClB,WAAO,CAAC;AAAA,EACV;AACF;AACO,SAAS,mBAAmB,OAAO,WAAW;AACnD,MAAI;AACJ,MAAI,YAAY;AAEhB,WAAS,aAAa,UAAU;AAC9B,iBAAa;AACb,WAAO,UAAU,UAAU,QAAQ;AAAA,EACrC;AAEA,WAAS,mBAAmB;AAC1B,cAAU,OAAO;AAAA,EACnB;AAEA,WAAS,sBAAsB;AAC7B,QAAI,aAAa,eAAe;AAC9B,mBAAa,cAAc;AAAA,IAC7B;AAAA,EACF;AAEA,WAAS,eAAe;AACtB,WAAO,QAAQ,WAAW;AAAA,EAC5B;AAEA,WAAS,eAAe;AACtB,QAAI,CAAC,aAAa;AAChB,oBAAc,YAAY,UAAU,aAAa,mBAAmB,IAAI,MAAM,UAAU,mBAAmB;AAC3G,kBAAY,yBAAyB;AAAA,IACvC;AAAA,EACF;AAEA,WAAS,iBAAiB;AACxB,QAAI,aAAa;AACf,kBAAY;AACZ,oBAAc;AACd,gBAAU,MAAM;AAChB,kBAAY;AAAA,IACd;AAAA,EACF;AAEA,MAAI,eAAe;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,SAAS,eAAe;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AC7HA,IAAAC,gBAA2C;AASpC,IAAI,4BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB,cAAc,gCAAkB;;;AJH3L,SAAS,SAAS,MAAM;AACtB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,mBAAe,uBAAQ,WAAY;AACrC,QAAI,eAAe,mBAAmB,KAAK;AAC3C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,MAAI,oBAAgB,uBAAQ,WAAY;AACtC,WAAO,MAAM,SAAS;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,4BAA0B,WAAY;AACpC,QAAI,eAAe,aAAa;AAChC,iBAAa,gBAAgB,aAAa;AAC1C,iBAAa,aAAa;AAE1B,QAAI,kBAAkB,MAAM,SAAS,GAAG;AACtC,mBAAa,iBAAiB;AAAA,IAChC;AAEA,WAAO,WAAY;AACjB,mBAAa,eAAe;AAC5B,mBAAa,gBAAgB;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,cAAc,aAAa,CAAC;AAChC,MAAI,UAAU,WAAW;AACzB,SAAoB,cAAAC,QAAM,cAAc,QAAQ,UAAU;AAAA,IACxD,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AAEA,IAAI,MAAuC;AACzC,WAAS,YAAY;AAAA,IACnB,OAAO,kBAAAC,QAAU,MAAM;AAAA,MACrB,WAAW,kBAAAA,QAAU,KAAK;AAAA,MAC1B,UAAU,kBAAAA,QAAU,KAAK;AAAA,MACzB,UAAU,kBAAAA,QAAU,KAAK;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,kBAAAA,QAAU;AAAA,IACnB,UAAU,kBAAAA,QAAU;AAAA,EACtB;AACF;AAEA,IAAO,mBAAQ;;;AKpDf,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ;AAAG,WAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AAAG,QAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,UAAI,OAAO,EAAE,QAAQ,CAAC;AAAG;AACzB,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACZ;AACA,SAAO;AACT;;;ACJA,qCAAyB;AACzB,IAAAC,gBAA+D;AAC/D,sBAAsD;AAJtD,IAAI,YAAY,CAAC,kBAAkB,cAAc,mBAAmB,4BAA4B,YAAY,WAAW,cAAc,SAAS;AAA9I,IACI,aAAa,CAAC,wBAAwB;AAQ1C,IAAI,cAAc,CAAC;AACnB,IAAI,wBAAwB,CAAC,MAAM,IAAI;AAEvC,IAAI,qBAAqB,SAASC,oBAAmB,MAAM;AACzD,MAAI;AACF,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B,SAAS,KAAK;AACZ,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;AAEA,SAAS,yBAAyB,OAAO,QAAQ;AAC/C,MAAI,cAAc,MAAM,CAAC;AACzB,SAAO,CAAC,OAAO,SAAS,cAAc,CAAC;AACzC;AAEA,SAAS,kCAAkC,YAAY,YAAY,cAAc;AAC/E,4BAA0B,WAAY;AACpC,WAAO,WAAW,MAAM,QAAQ,UAAU;AAAA,EAC5C,GAAG,YAAY;AACjB;AAEA,SAAS,oBAAoB,kBAAkB,gBAAgB,mBAAmB,cAAc,kBAAkB,2BAA2B,kBAAkB;AAE7J,mBAAiB,UAAU;AAC3B,iBAAe,UAAU;AACzB,oBAAkB,UAAU;AAE5B,MAAI,0BAA0B,SAAS;AACrC,8BAA0B,UAAU;AACpC,qBAAiB;AAAA,EACnB;AACF;AAEA,SAAS,iBAAiB,0BAA0B,OAAO,cAAc,oBAAoB,kBAAkB,gBAAgB,mBAAmB,2BAA2B,kBAAkB,8BAA8B;AAE3N,MAAI,CAAC;AAA0B;AAE/B,MAAI,iBAAiB;AACrB,MAAI,kBAAkB;AAEtB,MAAI,kBAAkB,SAASC,mBAAkB;AAC/C,QAAI,gBAAgB;AAGlB;AAAA,IACF;AAEA,QAAI,mBAAmB,MAAM,SAAS;AACtC,QAAI,eAAeC;AAEnB,QAAI;AAGF,sBAAgB,mBAAmB,kBAAkB,iBAAiB,OAAO;AAAA,IAC/E,SAAS,GAAG;AACV,MAAAA,SAAQ;AACR,wBAAkB;AAAA,IACpB;AAEA,QAAI,CAACA,QAAO;AACV,wBAAkB;AAAA,IACpB;AAGA,QAAI,kBAAkB,eAAe,SAAS;AAC5C,UAAI,CAAC,kBAAkB,SAAS;AAC9B,yBAAiB;AAAA,MACnB;AAAA,IACF,OAAO;AAKL,qBAAe,UAAU;AACzB,gCAA0B,UAAU;AACpC,wBAAkB,UAAU;AAE5B,mCAA6B;AAAA,QAC3B,MAAM;AAAA,QACN,SAAS;AAAA,UACP,OAAOA;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAGA,eAAa,gBAAgB;AAC7B,eAAa,aAAa;AAG1B,kBAAgB;AAEhB,MAAI,qBAAqB,SAASC,sBAAqB;AACrD,qBAAiB;AACjB,iBAAa,eAAe;AAC5B,iBAAa,gBAAgB;AAE7B,QAAI,iBAAiB;AAMnB,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB,SAASC,oBAAmB;AACjD,SAAO,CAAC,MAAM,CAAC;AACjB;AAEe,SAAR,gBAeP,iBACA,MAAM;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,QAAQ,MACR,uBAAuB,MAAM,gBAC7B,iBAAiB,yBAAyB,SAAS,SAAU,MAAM;AACrE,WAAO,qBAAqB,OAAO;AAAA,EACrC,IAAI,sBACA,mBAAmB,MAAM,YACzB,aAAa,qBAAqB,SAAS,oBAAoB,kBAC/D,wBAAwB,MAAM,iBAC9B,kBAAkB,0BAA0B,SAAS,SAAY,uBACjE,wBAAwB,MAAM,0BAC9B,2BAA2B,0BAA0B,SAAS,OAAO,uBACrE,iBAAiB,MAAM,UACvB,WAAW,mBAAmB,SAAS,UAAU,gBACjD,gBAAgB,MAAM,SACtB,UAAU,kBAAkB,SAAS,QAAQ,eAC7C,mBAAmB,MAAM,YACzB,aAAa,qBAAqB,SAAS,QAAQ,kBACnD,gBAAgB,MAAM,SACtB,UAAU,kBAAkB,SAAS,oBAAoB,eACzD,iBAAiB,8BAA8B,OAAO,SAAS;AAEnE,MAAI,MAAuC;AACzC,QAAI,oBAAoB,QAAW;AACjC,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAEA,QAAI,SAAS;AACX,YAAM,IAAI,MAAM,0FAA0F;AAAA,IAC5G;AAEA,QAAI,4BAA4B;AAEhC,QAAI,aAAa,SAAS;AACxB,YAAM,IAAI,MAAM,yDAAyD,yBAAyB;AAAA,IACpG;AAAA,EACF;AAEA,MAAI,UAAU;AACd,SAAO,SAAS,gBAAgB,kBAAkB;AAChD,QAA6C,KAAC,oCAAmB,gBAAgB,GAAG;AAClF,YAAM,IAAI,MAAM,4DAA4D,aAAa,wBAAwB,mBAAmB,gBAAgB,EAAE;AAAA,IACxJ;AAEA,QAAI,uBAAuB,iBAAiB,eAAe,iBAAiB,QAAQ;AACpF,QAAI,cAAc,eAAe,oBAAoB;AAErD,QAAI,yBAAyB,SAAS,CAAC,GAAG,gBAAgB;AAAA,MACxD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,OAAO,eAAe;AAE1B,aAAS,oBAAoB,OAAO;AAClC,aAAO,gBAAgB,MAAM,UAAU,sBAAsB;AAAA,IAC/D;AAKA,QAAI,kBAAkB,OAAO,wBAAU,SAAU,UAAU;AACzD,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,gBAAgB,OAAO;AAC9B,UAAI,eAAW,uBAAQ,WAAY;AAIjC,YAAIC,0BAAyB,MAAM,wBAC/BC,gBAAe,8BAA8B,OAAO,UAAU;AAElE,eAAO,CAAC,MAAM,SAASD,yBAAwBC,aAAY;AAAA,MAC7D,GAAG,CAAC,KAAK,CAAC,GACN,eAAe,SAAS,CAAC,GACzB,yBAAyB,SAAS,CAAC,GACnC,eAAe,SAAS,CAAC;AAE7B,UAAI,mBAAe,uBAAQ,WAAY;AAGrC,eAAO,gBAAgB,aAAa,gBAAY,mCAAgC,cAAAC,QAAM,cAAc,aAAa,UAAU,IAAI,CAAC,IAAI,eAAe;AAAA,MACrJ,GAAG,CAAC,cAAc,OAAO,CAAC;AAE1B,UAAI,mBAAe,0BAAW,YAAY;AAI1C,UAAI,wBAAwB,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,MAAM,QAAQ,KAAK,QAAQ,MAAM,MAAM,QAAQ;AACjH,UAAI,0BAA0B,QAAQ,YAAY,KAAK,QAAQ,aAAa,KAAK;AAEjF,UAA6C,CAAC,yBAAyB,CAAC,yBAAyB;AAC/F,cAAM,IAAI,MAAM,+CAAiD,MAAO,cAAc,yDAA0D,kFAAkF,+BAA+B,cAAc,uBAAuB;AAAA,MACxS;AAGA,UAAI,QAAQ,wBAAwB,MAAM,QAAQ,aAAa;AAC/D,UAAI,yBAAqB,uBAAQ,WAAY;AAG3C,eAAO,oBAAoB,KAAK;AAAA,MAClC,GAAG,CAAC,KAAK,CAAC;AAEV,UAAI,gBAAY,uBAAQ,WAAY;AAClC,YAAI,CAAC;AAA0B,iBAAO;AAKtC,YAAIC,gBAAe,mBAAmB,OAAO,wBAAwB,OAAO,aAAa,YAAY;AASrG,YAAIC,oBAAmBD,cAAa,iBAAiB,KAAKA,aAAY;AACtE,eAAO,CAACA,eAAcC,iBAAgB;AAAA,MACxC,GAAG,CAAC,OAAO,uBAAuB,YAAY,CAAC,GAC3C,eAAe,UAAU,CAAC,GAC1B,mBAAmB,UAAU,CAAC;AAIlC,UAAI,6BAAyB,uBAAQ,WAAY;AAC/C,YAAI,uBAAuB;AAIzB,iBAAO;AAAA,QACT;AAIA,eAAO,SAAS,CAAC,GAAG,cAAc;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,GAAG,CAAC,uBAAuB,cAAc,YAAY,CAAC;AAGtD,UAAI,kBAAc,0BAAW,0BAA0B,aAAa,gBAAgB,GAChF,eAAe,YAAY,CAAC,GAC5B,4BAA4B,aAAa,CAAC,GAC1C,+BAA+B,YAAY,CAAC;AAGhD,UAAI,6BAA6B,0BAA0B,OAAO;AAChE,cAAM,0BAA0B;AAAA,MAClC;AAGA,UAAI,qBAAiB,sBAAO;AAC5B,UAAI,uBAAmB,sBAAO,YAAY;AAC1C,UAAI,gCAA4B,sBAAO;AACvC,UAAI,wBAAoB,sBAAO,KAAK;AACpC,UAAI,mBAAmB,gBAAgB,WAAY;AAOjD,YAAI,0BAA0B,WAAW,iBAAiB,iBAAiB,SAAS;AAClF,iBAAO,0BAA0B;AAAA,QACnC;AAMA,eAAO,mBAAmB,MAAM,SAAS,GAAG,YAAY;AAAA,MAC1D,GAAG,CAAC,OAAO,2BAA2B,YAAY,CAAC;AAInD,wCAAkC,qBAAqB,CAAC,kBAAkB,gBAAgB,mBAAmB,cAAc,kBAAkB,2BAA2B,gBAAgB,CAAC;AAEzL,wCAAkC,kBAAkB,CAAC,0BAA0B,OAAO,cAAc,oBAAoB,kBAAkB,gBAAgB,mBAAmB,2BAA2B,kBAAkB,4BAA4B,GAAG,CAAC,OAAO,cAAc,kBAAkB,CAAC;AAGlS,UAAI,+BAA2B,uBAAQ,WAAY;AACjD,eAAoB,cAAAF,QAAM,cAAc,kBAAkB,SAAS,CAAC,GAAG,kBAAkB;AAAA,UACvF,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ,GAAG,CAAC,wBAAwB,kBAAkB,gBAAgB,CAAC;AAG/D,UAAI,oBAAgB,uBAAQ,WAAY;AACtC,YAAI,0BAA0B;AAI5B,iBAAoB,cAAAA,QAAM,cAAc,aAAa,UAAU;AAAA,YAC7D,OAAO;AAAA,UACT,GAAG,wBAAwB;AAAA,QAC7B;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,cAAc,0BAA0B,sBAAsB,CAAC;AACnE,aAAO;AAAA,IACT;AAGA,QAAI,UAAU,OAAO,cAAAA,QAAM,KAAK,eAAe,IAAI;AACnD,YAAQ,mBAAmB;AAC3B,YAAQ,cAAc,gBAAgB,cAAc;AAEpD,QAAI,YAAY;AACd,UAAI,YAAY,cAAAA,QAAM,WAAW,SAAS,kBAAkB,OAAOG,MAAK;AACtE,eAAoB,cAAAH,QAAM,cAAc,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA,UACnE,wBAAwBG;AAAA,QAC1B,CAAC,CAAC;AAAA,MACJ,CAAC;AACD,gBAAU,cAAc;AACxB,gBAAU,mBAAmB;AAC7B,iBAAO,+BAAAC,SAAa,WAAW,gBAAgB;AAAA,IACjD;AAEA,eAAO,+BAAAA,SAAa,SAAS,gBAAgB;AAAA,EAC/C;AACF;;;ACxXA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EAC7C,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;AAAA,EAC1B;AACF;AAEe,SAAR,aAA8B,MAAM,MAAM;AAC/C,MAAI,GAAG,MAAM,IAAI;AAAG,WAAO;AAE3B,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,MAAM,WAAW,MAAM;AAAQ,WAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;AAChG,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AC1Be,SAARC,oBAAoC,gBAAgB,UAAU;AACnE,MAAI,sBAAsB,CAAC;AAE3B,MAAI,QAAQ,SAASC,OAAMC,MAAK;AAC9B,QAAI,gBAAgB,eAAeA,IAAG;AAEtC,QAAI,OAAO,kBAAkB,YAAY;AACvC,0BAAoBA,IAAG,IAAI,WAAY;AACrC,eAAO,SAAS,cAAc,MAAM,QAAQ,SAAS,CAAC;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAEA,WAAS,OAAO,gBAAgB;AAC9B,UAAM,GAAG;AAAA,EACX;AAEA,SAAO;AACT;;;ACde,SAAR,cAA+B,KAAK;AACzC,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAM,WAAO;AACpD,MAAI,QAAQ,OAAO,eAAe,GAAG;AACrC,MAAI,UAAU;AAAM,WAAO;AAC3B,MAAI,YAAY;AAEhB,SAAO,OAAO,eAAe,SAAS,MAAM,MAAM;AAChD,gBAAY,OAAO,eAAe,SAAS;AAAA,EAC7C;AAEA,SAAO,UAAU;AACnB;;;ACTe,SAAR,QAAyB,SAAS;AAEvC,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,UAAU,YAAY;AACzE,YAAQ,MAAM,OAAO;AAAA,EACvB;AAIA,MAAI;AAIF,UAAM,IAAI,MAAM,OAAO;AAAA,EAEzB,SAAS,GAAG;AAAA,EAAC;AAGf;;;ACrBe,SAAR,kBAAmC,OAAO,aAAa,YAAY;AACxE,MAAI,CAAC,cAAc,KAAK,GAAG;AACzB,YAAQ,aAAa,WAAW,cAAc,mDAAmD,QAAQ,GAAG;AAAA,EAC9G;AACF;;;ACLO,SAAS,uBAAuB,aAAa;AAClD,SAAO,SAAS,qBAAqB,UAAU,SAAS;AACtD,QAAI,WAAW,YAAY,UAAU,OAAO;AAE5C,aAAS,mBAAmB;AAC1B,aAAO;AAAA,IACT;AAEA,qBAAiB,oBAAoB;AACrC,WAAO;AAAA,EACT;AACF;AAQO,SAAS,qBAAqB,YAAY;AAC/C,SAAO,WAAW,sBAAsB,QAAQ,WAAW,sBAAsB,SAAY,QAAQ,WAAW,iBAAiB,IAAI,WAAW,WAAW;AAC7J;AAaO,SAAS,mBAAmB,YAAY,YAAY;AACzD,SAAO,SAAS,kBAAkB,UAAU,MAAM;AAChD,QAAI,cAAc,KAAK;AAEvB,QAAI,QAAQ,SAAS,gBAAgB,iBAAiB,UAAU;AAC9D,aAAO,MAAM,oBAAoB,MAAM,WAAW,iBAAiB,QAAQ,IAAI,MAAM,WAAW,eAAe;AAAA,IACjH;AAGA,UAAM,oBAAoB;AAE1B,UAAM,aAAa,SAAS,uBAAuB,iBAAiB,UAAU;AAC5E,YAAM,aAAa;AACnB,YAAM,oBAAoB,qBAAqB,UAAU;AACzD,UAAI,QAAQ,MAAM,iBAAiB,QAAQ;AAE3C,UAAI,OAAO,UAAU,YAAY;AAC/B,cAAM,aAAa;AACnB,cAAM,oBAAoB,qBAAqB,KAAK;AACpD,gBAAQ,MAAM,iBAAiB,QAAQ;AAAA,MACzC;AAEA,UAAI;AAAuC,0BAAkB,OAAO,aAAa,UAAU;AAC3F,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;;;AC7DO,SAAS,iCAAiCC,qBAAoB;AACnE,SAAO,OAAOA,wBAAuB,aAAa,mBAAmBA,qBAAoB,oBAAoB,IAAI;AACnH;AACO,SAAS,gCAAgCA,qBAAoB;AAClE,SAAO,CAACA,sBAAqB,uBAAuB,SAAU,UAAU;AACtE,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF,CAAC,IAAI;AACP;AACO,SAAS,+BAA+BA,qBAAoB;AACjE,SAAOA,uBAAsB,OAAOA,wBAAuB,WAAW,uBAAuB,SAAU,UAAU;AAC/G,WAAOC,oBAAmBD,qBAAoB,QAAQ;AAAA,EACxD,CAAC,IAAI;AACP;AACA,IAAO,6BAAQ,CAAC,kCAAkC,iCAAiC,8BAA8B;;;AChB1G,SAAS,8BAA8B,iBAAiB;AAC7D,SAAO,OAAO,oBAAoB,aAAa,mBAAmB,iBAAiB,iBAAiB,IAAI;AAC1G;AACO,SAAS,6BAA6B,iBAAiB;AAC5D,SAAO,CAAC,kBAAkB,uBAAuB,WAAY;AAC3D,WAAO,CAAC;AAAA,EACV,CAAC,IAAI;AACP;AACA,IAAO,0BAAQ,CAAC,+BAA+B,4BAA4B;;;ACPpE,SAAS,kBAAkB,YAAY,eAAe,UAAU;AACrE,SAAO,SAAS,CAAC,GAAG,UAAU,YAAY,aAAa;AACzD;AACO,SAAS,mBAAmB,YAAY;AAC7C,SAAO,SAAS,oBAAoB,UAAU,MAAM;AAClD,QAAI,cAAc,KAAK,aACnB,OAAO,KAAK,MACZ,sBAAsB,KAAK;AAC/B,QAAI,aAAa;AACjB,QAAI;AACJ,WAAO,SAAS,gBAAgB,YAAY,eAAe,UAAU;AACnE,UAAI,kBAAkB,WAAW,YAAY,eAAe,QAAQ;AAEpE,UAAI,YAAY;AACd,YAAI,CAAC,QAAQ,CAAC,oBAAoB,iBAAiB,WAAW;AAAG,wBAAc;AAAA,MACjF,OAAO;AACL,qBAAa;AACb,sBAAc;AACd,YAAI;AAAuC,4BAAkB,aAAa,aAAa,YAAY;AAAA,MACrG;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACO,SAAS,yBAAyB,YAAY;AACnD,SAAO,OAAO,eAAe,aAAa,mBAAmB,UAAU,IAAI;AAC7E;AACO,SAAS,wBAAwB,YAAY;AAClD,SAAO,CAAC,aAAa,WAAY;AAC/B,WAAO;AAAA,EACT,IAAI;AACN;AACA,IAAO,qBAAQ,CAAC,0BAA0B,uBAAuB;;;ACjCjE,SAAS,OAAO,UAAU,YAAY,aAAa;AACjD,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,0BAA0B,aAAa,SAAS,cAAc,GAAG;AAAA,EACnF,WAAW,eAAe,qBAAqB,eAAe,sBAAsB;AAClF,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,mBAAmB,GAAG;AACxE,cAAQ,sBAAsB,aAAa,SAAS,cAAc,iDAAiD;AAAA,IACrH;AAAA,EACF;AACF;AAEe,SAAR,mBAAoC,iBAAiBE,qBAAoB,YAAY,aAAa;AACvG,SAAO,iBAAiB,mBAAmB,WAAW;AACtD,SAAOA,qBAAoB,sBAAsB,WAAW;AAC5D,SAAO,YAAY,cAAc,WAAW;AAC9C;;;ACfA,IAAIC,aAAY,CAAC,uBAAuB,0BAA0B,gBAAgB;AAE3E,SAAS,gCAAgC,iBAAiBC,qBAAoB,YAAY,UAAU;AACzG,SAAO,SAAS,yBAAyB,OAAO,UAAU;AACxD,WAAO,WAAW,gBAAgB,OAAO,QAAQ,GAAGA,oBAAmB,UAAU,QAAQ,GAAG,QAAQ;AAAA,EACtG;AACF;AACO,SAAS,8BAA8B,iBAAiBA,qBAAoB,YAAY,UAAU,MAAM;AAC7G,MAAI,iBAAiB,KAAK,gBACtB,mBAAmB,KAAK,kBACxB,qBAAqB,KAAK;AAC9B,MAAI,oBAAoB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,gBAAgB,YAAY,eAAe;AAClD,YAAQ;AACR,eAAW;AACX,iBAAa,gBAAgB,OAAO,QAAQ;AAC5C,oBAAgBA,oBAAmB,UAAU,QAAQ;AACrD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,wBAAoB;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,4BAA4B;AACnC,iBAAa,gBAAgB,OAAO,QAAQ;AAC5C,QAAIA,oBAAmB;AAAmB,sBAAgBA,oBAAmB,UAAU,QAAQ;AAC/F,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB;AACxB,QAAI,gBAAgB;AAAmB,mBAAa,gBAAgB,OAAO,QAAQ;AACnF,QAAIA,oBAAmB;AAAmB,sBAAgBA,oBAAmB,UAAU,QAAQ;AAC/F,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB;AACxB,QAAI,iBAAiB,gBAAgB,OAAO,QAAQ;AACpD,QAAI,oBAAoB,CAAC,mBAAmB,gBAAgB,UAAU;AACtE,iBAAa;AACb,QAAI;AAAmB,oBAAc,WAAW,YAAY,eAAe,QAAQ;AACnF,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,WAAW,cAAc;AACtD,QAAI,eAAe,CAAC,iBAAiB,cAAc,QAAQ;AAC3D,QAAI,eAAe,CAAC,eAAe,WAAW,OAAO,cAAc,QAAQ;AAC3E,YAAQ;AACR,eAAW;AACX,QAAI,gBAAgB;AAAc,aAAO,0BAA0B;AACnE,QAAI;AAAc,aAAO,eAAe;AACxC,QAAI;AAAc,aAAO,eAAe;AACxC,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,uBAAuB,WAAW,cAAc;AAC9D,WAAO,oBAAoB,sBAAsB,WAAW,YAAY,IAAI,gBAAgB,WAAW,YAAY;AAAA,EACrH;AACF;AAMe,SAAR,0BAA2C,UAAU,OAAO;AACjE,MAAI,sBAAsB,MAAM,qBAC5B,yBAAyB,MAAM,wBAC/B,iBAAiB,MAAM,gBACvB,UAAU,8BAA8B,OAAOD,UAAS;AAE5D,MAAI,kBAAkB,oBAAoB,UAAU,OAAO;AAC3D,MAAIC,sBAAqB,uBAAuB,UAAU,OAAO;AACjE,MAAI,aAAa,eAAe,UAAU,OAAO;AAEjD,MAAI,MAAuC;AACzC,uBAAmB,iBAAiBA,qBAAoB,YAAY,QAAQ,WAAW;AAAA,EACzF;AAEA,MAAI,kBAAkB,QAAQ,OAAO,gCAAgC;AACrE,SAAO,gBAAgB,iBAAiBA,qBAAoB,YAAY,UAAU,OAAO;AAC3F;;;ACrFA,IAAIC,aAAY,CAAC,QAAQ,kBAAkB,oBAAoB,sBAAsB,qBAAqB;AAwB1G,SAAS,MAAM,KAAK,WAAW,MAAM;AACnC,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,QAAI,SAAS,UAAU,CAAC,EAAE,GAAG;AAC7B,QAAI;AAAQ,aAAO;AAAA,EACrB;AAEA,SAAO,SAAU,UAAU,SAAS;AAClC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,UAAU,OAAO,yCAAyC,QAAQ,uBAAuB,GAAG;AAAA,EACtJ;AACF;AAEA,SAAS,YAAY,GAAG,GAAG;AACzB,SAAO,MAAM;AACf;AAIO,SAAS,cAAc,OAAO;AACnC,MAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OAC/B,kBAAkB,KAAK,YACvB,aAAa,oBAAoB,SAAS,kBAAkB,iBAC5D,wBAAwB,KAAK,0BAC7B,2BAA2B,0BAA0B,SAAS,0BAAkC,uBAChG,wBAAwB,KAAK,6BAC7B,8BAA8B,0BAA0B,SAAS,6BAAqC,uBACtG,wBAAwB,KAAK,qBAC7B,sBAAsB,0BAA0B,SAAS,qBAA6B,uBACtF,uBAAuB,KAAK,iBAC5B,kBAAkB,yBAAyB,SAAS,4BAAyB;AAEjF,SAAO,SAAS,QAAQ,iBAAiBC,qBAAoB,YAAY,OAAO;AAC9E,QAAI,UAAU,QAAQ;AACpB,cAAQ,CAAC;AAAA,IACX;AAEA,QAAI,QAAQ,OACR,aAAa,MAAM,MACnB,OAAO,eAAe,SAAS,OAAO,YACtC,uBAAuB,MAAM,gBAC7B,iBAAiB,yBAAyB,SAAS,cAAc,sBACjE,wBAAwB,MAAM,kBAC9B,mBAAmB,0BAA0B,SAAS,eAAe,uBACrE,wBAAwB,MAAM,oBAC9B,qBAAqB,0BAA0B,SAAS,eAAe,uBACvE,wBAAwB,MAAM,qBAC9B,sBAAsB,0BAA0B,SAAS,eAAe,uBACxE,eAAe,8BAA8B,OAAOD,UAAS;AAEjE,QAAI,sBAAsB,MAAM,iBAAiB,0BAA0B,iBAAiB;AAC5F,QAAI,yBAAyB,MAAMC,qBAAoB,6BAA6B,oBAAoB;AACxG,QAAI,iBAAiB,MAAM,YAAY,qBAAqB,YAAY;AACxE,WAAO,WAAW,iBAAiB,SAAS;AAAA;AAAA,MAE1C,YAAY;AAAA;AAAA,MAEZ,gBAAgB,SAAS,eAAe,MAAM;AAC5C,eAAO,aAAa,OAAO;AAAA,MAC7B;AAAA;AAAA,MAEA,0BAA0B,QAAQ,eAAe;AAAA;AAAA,MAEjD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,YAAY,CAAC;AAAA,EAClB;AACF;AACA,IAAO,kBAAqB,cAAc;;;AClG1C,IAAAC,gBAA2B;;;ACA3B,IAAAC,gBAA2B;AAmBpB,SAAS,kBAAkB;AAChC,MAAI,mBAAe,0BAAW,iBAAiB;AAE/C,MAA6C,CAAC,cAAc;AAC1D,UAAM,IAAI,MAAM,kGAAkG;AAAA,EACpH;AAEA,SAAO;AACT;;;ADjBO,SAAS,gBAAgB,SAAS;AACvC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAIC,mBAAkB,YAAY,oBAAoB,kBAAyB,WAAY;AACzF,eAAO,0BAAW,OAAO;AAAA,EAC3B;AACA,SAAO,SAASC,YAAW;AACzB,QAAI,mBAAmBD,iBAAgB,GACnC,QAAQ,iBAAiB;AAE7B,WAAO;AAAA,EACT;AACF;AAiBO,IAAI,WAAwB,gBAAgB;;;AEhC5C,SAAS,mBAAmB,SAAS;AAC1C,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAIE,YAAW,YAAY,oBAAoB,WAAkB,gBAAgB,OAAO;AACxF,SAAO,SAASC,eAAc;AAC5B,QAAI,QAAQD,UAAS;AACrB,WAAO,MAAM;AAAA,EACf;AACF;AAuBO,IAAI,cAA2B,mBAAmB;;;AC1CzD,IAAAE,gBAAuE;AAMvE,IAAI,cAAc,SAASC,aAAY,GAAG,GAAG;AAC3C,SAAO,MAAM;AACf;AAEA,SAAS,oCAAoC,UAAU,YAAY,OAAO,YAAY;AACpF,MAAI,kBAAc,0BAAW,SAAU,GAAG;AACxC,WAAO,IAAI;AAAA,EACb,GAAG,CAAC,GACA,cAAc,YAAY,CAAC;AAE/B,MAAI,mBAAe,uBAAQ,WAAY;AACrC,WAAO,mBAAmB,OAAO,UAAU;AAAA,EAC7C,GAAG,CAAC,OAAO,UAAU,CAAC;AACtB,MAAI,sCAAkC,sBAAO;AAC7C,MAAI,qBAAiB,sBAAO;AAC5B,MAAI,uBAAmB,sBAAO;AAC9B,MAAI,0BAAsB,sBAAO;AACjC,MAAI,aAAa,MAAM,SAAS;AAChC,MAAI;AAEJ,MAAI;AACF,QAAI,aAAa,eAAe,WAAW,eAAe,iBAAiB,WAAW,gCAAgC,SAAS;AAC7H,UAAI,mBAAmB,SAAS,UAAU;AAE1C,UAAI,oBAAoB,YAAY,UAAa,CAAC,WAAW,kBAAkB,oBAAoB,OAAO,GAAG;AAC3G,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,oBAAoB;AAAA,MACtC;AAAA,IACF,OAAO;AACL,sBAAgB,oBAAoB;AAAA,IACtC;AAAA,EACF,SAAS,KAAK;AACZ,QAAI,gCAAgC,SAAS;AAC3C,UAAI,WAAW,8DAA8D,gCAAgC,QAAQ,QAAQ;AAAA,IAC/H;AAEA,UAAM;AAAA,EACR;AAEA,4BAA0B,WAAY;AACpC,mBAAe,UAAU;AACzB,qBAAiB,UAAU;AAC3B,wBAAoB,UAAU;AAC9B,oCAAgC,UAAU;AAAA,EAC5C,CAAC;AACD,4BAA0B,WAAY;AACpC,aAAS,kBAAkB;AACzB,UAAI;AACF,YAAI,gBAAgB,MAAM,SAAS;AAEnC,YAAI,kBAAkB,iBAAiB,SAAS;AAC9C;AAAA,QACF;AAEA,YAAI,oBAAoB,eAAe,QAAQ,aAAa;AAE5D,YAAI,WAAW,mBAAmB,oBAAoB,OAAO,GAAG;AAC9D;AAAA,QACF;AAEA,4BAAoB,UAAU;AAC9B,yBAAiB,UAAU;AAAA,MAC7B,SAAS,KAAK;AAKZ,wCAAgC,UAAU;AAAA,MAC5C;AAEA,kBAAY;AAAA,IACd;AAEA,iBAAa,gBAAgB;AAC7B,iBAAa,aAAa;AAC1B,oBAAgB;AAChB,WAAO,WAAY;AACjB,aAAO,aAAa,eAAe;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,OAAO,YAAY,CAAC;AACxB,SAAO;AACT;AASO,SAAS,mBAAmB,SAAS;AAC1C,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAIC,mBAAkB,YAAY,oBAAoB,kBAAyB,WAAY;AACzF,eAAO,0BAAW,OAAO;AAAA,EAC3B;AACA,SAAO,SAASC,aAAY,UAAU,YAAY;AAChD,QAAI,eAAe,QAAQ;AACzB,mBAAa;AAAA,IACf;AAEA,QAAI,MAAuC;AACzC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAEA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,UAAI,OAAO,eAAe,YAAY;AACpC,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAAA,IACF;AAEA,QAAI,mBAAmBD,iBAAgB,GACnC,QAAQ,iBAAiB,OACzB,aAAa,iBAAiB;AAElC,QAAI,gBAAgB,oCAAoC,UAAU,YAAY,OAAO,UAAU;AAC/F,qCAAc,aAAa;AAC3B,WAAO;AAAA,EACT;AACF;AAyBO,IAAI,cAA2B,mBAAmB;;;AC5JzD,uBAAwC;;;ACIxC,SAAS,wCAAK;;;ACLd,IAAAE,gBAA4C;AAE5C,SAAS,eAAe,WAAW,YAAY;AAC7C,MAAI,UAAU,WAAW,WAAW,QAAQ;AAC1C,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,UAAU,CAAC,MAAM,WAAW,CAAC,GAAG;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,WAAW,QAAQ;AACrC,MAAI,cAAU,wBAAS,WAAY;AACjC,WAAO;AAAA,MACL;AAAA,MACA,QAAQ,UAAU;AAAA,IACpB;AAAA,EACF,CAAC,EAAE,CAAC;AACJ,MAAI,iBAAa,sBAAO,IAAI;AAC5B,MAAI,gBAAY,sBAAO,OAAO;AAC9B,MAAI,WAAW,WAAW,WAAW,QAAQ,UAAU,UAAU,QAAQ,UAAU,eAAe,QAAQ,UAAU,QAAQ,MAAM,CAAC;AACnI,MAAI,QAAQ,WAAW,UAAU,UAAU;AAAA,IACzC;AAAA,IACA,QAAQ,UAAU;AAAA,EACpB;AACA,+BAAU,WAAY;AACpB,eAAW,UAAU;AACrB,cAAU,UAAU;AAAA,EACtB,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,MAAM;AACf;AACA,SAAS,eAAe,UAAU,QAAQ;AACxC,SAAO,WAAW,WAAY;AAC5B,WAAO;AAAA,EACT,GAAG,MAAM;AACX;AACA,IAAIC,WAAU;AACd,IAAI,cAAc;;;AC1ClB,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,SAAS,UAAU,WAAW,SAAS;AACnC,MAAI,WAAW;AACX;AAAA,EACJ;AACA,MAAI,cAAc;AACd,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B;AACA,MAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,MAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,QAAM,IAAI,MAAM,KAAK;AACzB;;;ACVA,IAAI,UAAU,SAASC,SAAQ,MAAM;AACnC,MAAI,MAAM,KAAK,KACX,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,OAAO,KAAK;AAChB,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,SAAS;AACtB,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,IACH,QAAQ;AAAA,MACN,IAAI,QAAQ,QAAQ;AAAA,MACpB,IAAI,SAAS,OAAO;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,SAAS,SAASC,QAAO,QAAQ,UAAU;AAC7C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,SAAS;AAAA,IAC3B,MAAM,OAAO,OAAO,SAAS;AAAA,IAC7B,QAAQ,OAAO,SAAS,SAAS;AAAA,IACjC,OAAO,OAAO,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,IAAI,SAAS,SAASC,QAAO,QAAQ,UAAU;AAC7C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,SAAS;AAAA,IAC3B,MAAM,OAAO,OAAO,SAAS;AAAA,IAC7B,QAAQ,OAAO,SAAS,SAAS;AAAA,IACjC,OAAO,OAAO,QAAQ,SAAS;AAAA,EACjC;AACF;AAEA,IAAI,QAAQ,SAASC,OAAM,QAAQ,SAAS;AAC1C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,QAAQ;AAAA,IAC1B,MAAM,OAAO,OAAO,QAAQ;AAAA,IAC5B,QAAQ,OAAO,SAAS,QAAQ;AAAA,IAChC,OAAO,OAAO,QAAQ,QAAQ;AAAA,EAChC;AACF;AAEA,IAAI,YAAY;AAAA,EACd,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AACA,IAAI,YAAY,SAASC,WAAU,OAAO;AACxC,MAAI,YAAY,MAAM,WAClB,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,YAAY,cAC/C,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,YAAY,cAC/C,gBAAgB,MAAM,SACtB,UAAU,kBAAkB,SAAS,YAAY;AACrD,MAAI,YAAY,QAAQ,OAAO,WAAW,MAAM,CAAC;AACjD,MAAI,aAAa,QAAQ,OAAO,WAAW,MAAM,CAAC;AAClD,MAAI,aAAa,QAAQ,OAAO,YAAY,OAAO,CAAC;AACpD,SAAO;AAAA,IACL;AAAA,IACA,WAAW,QAAQ,SAAS;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,QAAQ,SAASC,OAAM,KAAK;AAC9B,MAAI,QAAQ,IAAI,MAAM,GAAG,EAAE;AAC3B,MAAIC,UAAS,IAAI,MAAM,EAAE;AAEzB,MAAIA,YAAW,MAAM;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,OAAO,KAAK;AACzB,GAAC,CAAC,MAAM,MAAM,IAAI,OAAwC,UAAU,OAAO,iCAAiC,MAAM,uBAAuB,QAAQ,GAAG,IAAI,UAAU,KAAK,IAAI;AAC3K,SAAO;AACT;AAEA,IAAI,kBAAkB,SAASC,mBAAkB;AAC/C,SAAO;AAAA,IACL,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF;AAEA,IAAI,SAAS,SAASC,QAAO,UAAU,QAAQ;AAC7C,MAAI,YAAY,SAAS,WACrB,SAAS,SAAS,QAClB,SAAS,SAAS,QAClB,UAAU,SAAS;AACvB,MAAI,UAAU,MAAM,WAAW,MAAM;AACrC,SAAO,UAAU;AAAA,IACf,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,aAAa,SAASC,YAAW,UAAUC,SAAQ;AACrD,MAAIA,YAAW,QAAQ;AACrB,IAAAA,UAAS,gBAAgB;AAAA,EAC3B;AAEA,SAAO,OAAO,UAAUA,OAAM;AAChC;AACA,IAAI,eAAe,SAASC,cAAa,WAAW,QAAQ;AAC1D,MAAI,SAAS;AAAA,IACX,KAAK,MAAM,OAAO,SAAS;AAAA,IAC3B,OAAO,MAAM,OAAO,WAAW;AAAA,IAC/B,QAAQ,MAAM,OAAO,YAAY;AAAA,IACjC,MAAM,MAAM,OAAO,UAAU;AAAA,EAC/B;AACA,MAAI,UAAU;AAAA,IACZ,KAAK,MAAM,OAAO,UAAU;AAAA,IAC5B,OAAO,MAAM,OAAO,YAAY;AAAA,IAChC,QAAQ,MAAM,OAAO,aAAa;AAAA,IAClC,MAAM,MAAM,OAAO,WAAW;AAAA,EAChC;AACA,MAAI,SAAS;AAAA,IACX,KAAK,MAAM,OAAO,cAAc;AAAA,IAChC,OAAO,MAAM,OAAO,gBAAgB;AAAA,IACpC,QAAQ,MAAM,OAAO,iBAAiB;AAAA,IACtC,MAAM,MAAM,OAAO,eAAe;AAAA,EACpC;AACA,SAAO,UAAU;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,SAAS,SAASC,QAAO,IAAI;AAC/B,MAAI,YAAY,GAAG,sBAAsB;AACzC,MAAI,SAAS,OAAO,iBAAiB,EAAE;AACvC,SAAO,aAAa,WAAW,MAAM;AACvC;;;ACrJA,IAAI,YAAY,OAAO,SACnB,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACJ,SAAS,QAAQ,OAAO,QAAQ;AAC5B,MAAI,UAAU,QAAQ;AAClB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,KAAK,UAAU,MAAM,GAAG;AACvC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAASC,gBAAe,WAAW,YAAY;AAC3C,MAAI,UAAU,WAAW,WAAW,QAAQ;AACxC,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,CAAC,QAAQ,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG;AACvC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,UAAUC,UAAS;AACnC,MAAIA,aAAY,QAAQ;AAAE,IAAAA,WAAUD;AAAA,EAAgB;AACpD,MAAI;AACJ,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,aAAa;AACjB,WAAS,WAAW;AAChB,QAAI,UAAU,CAAC;AACf,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAQ,EAAE,IAAI,UAAU,EAAE;AAAA,IAC9B;AACA,QAAI,cAAc,aAAa,QAAQC,SAAQ,SAAS,QAAQ,GAAG;AAC/D,aAAO;AAAA,IACX;AACA,iBAAa,SAAS,MAAM,MAAM,OAAO;AACzC,iBAAa;AACb,eAAW;AACX,eAAW;AACX,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAO,0BAAQ;;;AChDf,IAAI,UAAU,SAASC,SAAQ,IAAI;AACjC,MAAI,WAAW,CAAC;AAChB,MAAI,UAAU;AAEd,MAAI,YAAY,SAASC,aAAY;AACnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,eAAW;AAEX,QAAI,SAAS;AACX;AAAA,IACF;AAEA,cAAU,sBAAsB,WAAY;AAC1C,gBAAU;AACV,SAAG,MAAM,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACH;AAEA,YAAU,SAAS,WAAY;AAC7B,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA,yBAAqB,OAAO;AAC5B,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAEA,IAAO,uBAAQ;;;AjCxBf,IAAAC,oBAAqB;AAErB,IAAIC,gBAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAE1B,IAAI,QAAQ,SAASC,OAAM,OAAO;AAChC,SAAO,MAAM,QAAQ,eAAe,GAAG,EAAE,QAAQ,qBAAqB,EAAE,EAAE,KAAK;AACjF;AAEA,IAAI,gBAAgB,SAASC,eAAc,SAAS;AAClD,SAAO,MAAM,sCAAsC,MAAM,OAAO,IAAI,4FAA2G;AACjL;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,SAAS;AAC9D,SAAO,CAAC,cAAc,OAAO,GAAG,wDAAwD,oBAAoB,iBAAiB;AAC/H;AACA,IAAI,iBAAiB;AACrB,SAAS,IAAI,MAAM,SAAS;AAC1B,MAAI;AAEJ,MAAIH,eAAc;AAChB;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc,GAAG;AAC3D;AAAA,EACF;AAEA,GAAC,WAAW,SAAS,IAAI,EAAE,MAAM,UAAU,oBAAoB,OAAO,CAAC;AACzE;AACA,IAAII,WAAU,IAAI,KAAK,MAAM,MAAM;AACnC,IAAI,QAAQ,IAAI,KAAK,MAAM,OAAO;AAElC,SAAS,OAAO;AAAC;AAEjB,SAAS,WAAWC,SAAQ,aAAa;AACvC,SAAO,SAAS,CAAC,GAAGA,SAAQ,CAAC,GAAG,WAAW;AAC7C;AAEA,SAAS,WAAW,IAAI,UAAU,eAAe;AAC/C,MAAI,aAAa,SAAS,IAAI,SAAU,SAAS;AAC/C,QAAI,UAAU,WAAW,eAAe,QAAQ,OAAO;AACvD,OAAG,iBAAiB,QAAQ,WAAW,QAAQ,IAAI,OAAO;AAC1D,WAAO,SAAS,SAAS;AACvB,SAAG,oBAAoB,QAAQ,WAAW,QAAQ,IAAI,OAAO;AAAA,IAC/D;AAAA,EACF,CAAC;AACD,SAAO,SAAS,YAAY;AAC1B,eAAW,QAAQ,SAAU,QAAQ;AACnC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEA,IAAI,iBAAiB;AACrB,IAAIC,UAAS;AACb,SAAS,aAAa,SAAS;AAC7B,OAAK,UAAU;AACjB;AAEA,aAAa,UAAU,WAAW,SAAS,WAAW;AACpD,SAAO,KAAK;AACd;AAEA,SAASC,WAAU,WAAW,SAAS;AACrC,MAAI,WAAW;AACb;AAAA,EACF;AAEA,MAAI,gBAAgB;AAClB,UAAM,IAAI,aAAaD,OAAM;AAAA,EAC/B,OAAO;AACL,UAAM,IAAI,aAAaA,UAAS,QAAQ,WAAW,GAAG;AAAA,EACxD;AACF;AAEA,IAAI,gBAAgB,SAAU,kBAAkB;AAC9C,iBAAeE,gBAAe,gBAAgB;AAE9C,WAASA,iBAAgB;AACvB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,UAAM,YAAY;AAClB,UAAM,SAAS;AAEf,UAAM,gBAAgB,SAAU,OAAO;AACrC,UAAI,YAAY,MAAM,aAAa;AAEnC,UAAI,UAAU,WAAW,GAAG;AAC1B,kBAAU,SAAS;AACnB,eAAwCJ,SAAQ,mJAAmJ,IAAI;AAAA,MACzM;AAEA,UAAI,MAAM,MAAM;AAEhB,UAAI,eAAe,cAAc;AAC/B,cAAM,eAAe;AAErB,YAAI,MAAuC;AACzC,gBAAM,IAAI,OAAO;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,UAAM,eAAe,WAAY;AAC/B,UAAI,CAAC,MAAM,WAAW;AACpB,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AAEA,aAAO,MAAM;AAAA,IACf;AAEA,UAAM,eAAe,SAAU,WAAW;AACxC,YAAM,YAAY;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASI,eAAc;AAE3B,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,SAAS,WAAW,QAAQ,CAAC;AAAA,MAChC,WAAW;AAAA,MACX,IAAI,KAAK;AAAA,IACX,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO,oBAAoB,SAAS,kBAAkB,KAAK;AACzD,QAAI,eAAe,cAAc;AAC/B,UAAI,MAAuC;AACzC,cAAM,IAAI,OAAO;AAAA,MACnB;AAEA,WAAK,SAAS,CAAC,CAAC;AAChB;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,OAAO;AAAA,EACd;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAO,KAAK,MAAM,SAAS,KAAK,YAAY;AAAA,EAC9C;AAEA,SAAOA;AACT,EAAE,cAAAC,QAAM,SAAS;AAEjB,IAAI,8BAA8B;AAElC,IAAI,WAAW,SAASC,UAAS,OAAO;AACtC,SAAO,QAAQ;AACjB;AAEA,IAAI,cAAc,SAASC,aAAYC,QAAO;AAC5C,SAAO,6CAA6C,SAASA,OAAM,OAAO,KAAK,IAAI;AACrF;AAEA,IAAI,eAAe,SAASC,cAAa,QAAQ,aAAa;AAC5D,MAAI,eAAe,OAAO,gBAAgB,YAAY;AACtD,MAAI,gBAAgB,SAAS,OAAO,KAAK;AACzC,MAAI,cAAc,SAAS,YAAY,KAAK;AAE5C,MAAI,cAAc;AAChB,WAAO,mDAAmD,gBAAgB,yBAAyB,cAAc;AAAA,EACnH;AAEA,SAAO,iDAAiD,gBAAgB,mBAAmB,OAAO,cAAc,mBAAmB,YAAY,cAAc,uBAAuB,cAAc;AACpM;AAEA,IAAI,cAAc,SAASC,aAAY,IAAI,QAAQC,UAAS;AAC1D,MAAI,aAAa,OAAO,gBAAgBA,SAAQ;AAEhD,MAAI,YAAY;AACd,WAAO,sBAAsB,KAAK,oCAAoCA,SAAQ;AAAA,EAChF;AAEA,SAAO,sBAAsB,KAAK,qBAAqB,OAAO,cAAc,oCAAoCA,SAAQ,cAAc,qBAAqBA,SAAQ,cAAc;AACnL;AAEA,IAAI,eAAe,SAASC,cAAaC,SAAQ;AAC/C,MAAI,WAAWA,QAAO;AAEtB,MAAI,UAAU;AACZ,WAAO,aAAaA,QAAO,QAAQ,QAAQ;AAAA,EAC7C;AAEA,MAAIF,WAAUE,QAAO;AAErB,MAAIF,UAAS;AACX,WAAO,YAAYE,QAAO,aAAaA,QAAO,QAAQF,QAAO;AAAA,EAC/D;AAEA,SAAO;AACT;AAEA,IAAI,kBAAkB,SAASG,iBAAgB,QAAQ;AACrD,SAAO,8DAA8D,SAAS,OAAO,KAAK,IAAI;AAChG;AAEA,IAAI,YAAY,SAASC,WAAU,QAAQ;AACzC,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO,wCAAwC,gBAAgB,OAAO,MAAM,IAAI;AAAA,EAClF;AAEA,MAAI,WAAW,OAAO;AACtB,MAAIJ,WAAU,OAAO;AAErB,MAAI,UAAU;AACZ,WAAO,+CAA+C,aAAa,OAAO,QAAQ,QAAQ,IAAI;AAAA,EAChG;AAEA,MAAIA,UAAS;AACX,WAAO,+CAA+C,YAAY,OAAO,aAAa,OAAO,QAAQA,QAAO,IAAI;AAAA,EAClH;AAEA,SAAO,sEAAsE,gBAAgB,OAAO,MAAM,IAAI;AAChH;AAEA,IAAI,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,SAAS;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAI,MAAM,SAASK,KAAI,QAAQ,QAAQ;AACrC,SAAO;AAAA,IACL,GAAG,OAAO,IAAI,OAAO;AAAA,IACrB,GAAG,OAAO,IAAI,OAAO;AAAA,EACvB;AACF;AACA,IAAI,WAAW,SAASC,UAAS,QAAQ,QAAQ;AAC/C,SAAO;AAAA,IACL,GAAG,OAAO,IAAI,OAAO;AAAA,IACrB,GAAG,OAAO,IAAI,OAAO;AAAA,EACvB;AACF;AACA,IAAIC,WAAU,SAASA,SAAQ,QAAQ,QAAQ;AAC7C,SAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO;AACtD;AACA,IAAI,SAAS,SAASC,QAAO,OAAO;AAClC,SAAO;AAAA,IACL,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI;AAAA,IAC9B,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI;AAAA,EAChC;AACF;AACA,IAAI,QAAQ,SAASC,OAAM,MAAM,OAAO,YAAY;AAClD,MAAI;AAEJ,MAAI,eAAe,QAAQ;AACzB,iBAAa;AAAA,EACf;AAEA,SAAO,OAAO,CAAC,GAAG,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,MAAM,MAAM,GAAG,IAAI,YAAY;AACrF;AACA,IAAI,WAAW,SAASC,UAAS,QAAQ,QAAQ;AAC/C,SAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,CAAC;AACtF;AACA,IAAI,UAAU,SAASC,SAAQ,QAAQ,QAAQ;AAC7C,SAAO,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,SAAU,OAAO;AACtD,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B,CAAC,CAAC;AACJ;AACA,IAAI,QAAQ,SAASC,OAAM,IAAI;AAC7B,SAAO,SAAU,OAAO;AACtB,WAAO;AAAA,MACL,GAAG,GAAG,MAAM,CAAC;AAAA,MACb,GAAG,GAAG,MAAM,CAAC;AAAA,IACf;AAAA,EACF;AACF;AAEA,IAAI,cAAe,SAAU,OAAO,SAAS;AAC3C,MAAI,SAAS,QAAQ;AAAA,IACnB,KAAK,KAAK,IAAI,QAAQ,KAAK,MAAM,GAAG;AAAA,IACpC,OAAO,KAAK,IAAI,QAAQ,OAAO,MAAM,KAAK;AAAA,IAC1C,QAAQ,KAAK,IAAI,QAAQ,QAAQ,MAAM,MAAM;AAAA,IAC7C,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,IAAI;AAAA,EACzC,CAAC;AAED,MAAI,OAAO,SAAS,KAAK,OAAO,UAAU,GAAG;AAC3C,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB,SAASC,kBAAiB,SAAS,OAAO;AAC/D,SAAO;AAAA,IACL,KAAK,QAAQ,MAAM,MAAM;AAAA,IACzB,MAAM,QAAQ,OAAO,MAAM;AAAA,IAC3B,QAAQ,QAAQ,SAAS,MAAM;AAAA,IAC/B,OAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/B;AACF;AACA,IAAI,aAAa,SAASC,YAAW,SAAS;AAC5C,SAAO,CAAC;AAAA,IACN,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,EACb,GAAG;AAAA,IACD,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,EACb,GAAG;AAAA,IACD,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,EACb,GAAG;AAAA,IACD,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,EACb,CAAC;AACH;AACA,IAAIC,aAAY;AAAA,EACd,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAEA,IAAI,SAAS,SAASC,QAAO,QAAQ,OAAO;AAC1C,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,SAAO,iBAAiB,QAAQ,MAAM,OAAO,KAAK,YAAY;AAChE;AAEA,IAAI,WAAW,SAASC,UAAS,QAAQ,MAAM,iBAAiB;AAC9D,MAAI,mBAAmB,gBAAgB,aAAa;AAClD,QAAI;AAEJ,WAAO,SAAS,CAAC,GAAG,SAAS,YAAY,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,OAAO,KAAK,GAAG,IAAI,gBAAgB,YAAY,KAAK,IAAI,GAAG,UAAU;AAAA,EAC1I;AAEA,SAAO;AACT;AAEA,IAAI,OAAO,SAASC,MAAK,QAAQ,OAAO;AACtC,MAAI,SAAS,MAAM,mBAAmB;AACpC,WAAO,YAAY,MAAM,eAAe,MAAM;AAAA,EAChD;AAEA,SAAO,QAAQ,MAAM;AACvB;AAEA,IAAI,aAAc,SAAU,MAAM;AAChC,MAAI,OAAO,KAAK,MACZ,kBAAkB,KAAK,iBACvB,OAAO,KAAK,MACZ,QAAQ,KAAK;AACjB,MAAI,WAAW,OAAO,KAAK,WAAW,KAAK;AAC3C,MAAI,YAAY,SAAS,UAAU,MAAM,eAAe;AACxD,MAAI,UAAU,KAAK,WAAW,KAAK;AACnC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV;AACF;AAEA,IAAI,kBAAmB,SAAUC,YAAW,WAAW;AACrD,GAACA,WAAU,QAAQ,OAAwC3B,WAAU,KAAK,IAAIA,WAAU,KAAK,IAAI;AACjG,MAAI,aAAa2B,WAAU;AAC3B,MAAI,aAAa,SAAS,WAAW,WAAW,OAAO,OAAO;AAC9D,MAAI,qBAAqB,OAAO,UAAU;AAE1C,MAAI,QAAQ,SAAS,CAAC,GAAG,YAAY;AAAA,IACnC,QAAQ;AAAA,MACN,SAAS,WAAW,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,KAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACF,CAAC;AAED,MAAI,UAAU,WAAW;AAAA,IACvB,MAAMA,WAAU,QAAQ;AAAA,IACxB,iBAAiBA,WAAU,QAAQ;AAAA,IACnC,MAAMA,WAAU;AAAA,IAChB;AAAA,EACF,CAAC;AAED,MAAI,SAAS,SAAS,CAAC,GAAGA,YAAW;AAAA,IACnC;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,OAAO,WAAW;AACpB,WAAO,OAAO,UAAU,KAAK;AAAA,EAC/B;AAEA,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AAC/E;AACA,SAAS,OAAO,KAAK;AACnB,MAAI,OAAO,QAAQ;AACjB,WAAO,OAAO,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO,OAAO,KAAK,GAAG,EAAE,IAAI,SAAU,KAAK;AACzC,WAAO,IAAI,GAAG;AAAA,EAChB,CAAC;AACH;AACA,SAAS,UAAU,MAAM,WAAW;AAClC,MAAI,KAAK,WAAW;AAClB,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAEA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,UAAU,KAAK,CAAC,CAAC,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AACA,SAAS,KAAK,MAAM,WAAW;AAC7B,MAAI,KAAK,MAAM;AACb,WAAO,KAAK,KAAK,SAAS;AAAA,EAC5B;AAEA,MAAI,QAAQ,UAAU,MAAM,SAAS;AAErC,MAAI,UAAU,IAAI;AAChB,WAAO,KAAK,KAAK;AAAA,EACnB;AAEA,SAAO;AACT;AACA,SAAS,QAAQ,MAAM;AACrB,SAAO,MAAM,UAAU,MAAM,KAAK,IAAI;AACxC;AAEA,IAAI,iBAAiB,wBAAW,SAAU,YAAY;AACpD,SAAO,WAAW,OAAO,SAAU,UAAU,SAAS;AACpD,aAAS,QAAQ,WAAW,EAAE,IAAI;AAClC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP,CAAC;AACD,IAAI,iBAAiB,wBAAW,SAAU,YAAY;AACpD,SAAO,WAAW,OAAO,SAAU,UAAU,SAAS;AACpD,aAAS,QAAQ,WAAW,EAAE,IAAI;AAClC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP,CAAC;AACD,IAAI,kBAAkB,wBAAW,SAAU,YAAY;AACrD,SAAO,OAAO,UAAU;AAC1B,CAAC;AACD,IAAI,kBAAkB,wBAAW,SAAU,YAAY;AACrD,SAAO,OAAO,UAAU;AAC1B,CAAC;AAED,IAAI,+BAA+B,wBAAW,SAAU,aAAa,YAAY;AAC/E,MAAI,SAAS,gBAAgB,UAAU,EAAE,OAAO,SAAUC,YAAW;AACnE,WAAO,gBAAgBA,WAAU,WAAW;AAAA,EAC9C,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AACtB,WAAO,EAAE,WAAW,QAAQ,EAAE,WAAW;AAAA,EAC3C,CAAC;AACD,SAAO;AACT,CAAC;AAED,SAAS,kBAAkB,QAAQ;AACjC,MAAI,OAAO,MAAM,OAAO,GAAG,SAAS,WAAW;AAC7C,WAAO,OAAO,GAAG;AAAA,EACnB;AAEA,SAAO;AACT;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,OAAO,MAAM,OAAO,GAAG,SAAS,WAAW;AAC7C,WAAO,OAAO,GAAG;AAAA,EACnB;AAEA,SAAO;AACT;AAEA,IAAI,0BAA0B,wBAAW,SAAU,QAAQ,MAAM;AAC/D,SAAO,KAAK,OAAO,SAAU,MAAM;AACjC,WAAO,KAAK,WAAW,OAAO,OAAO,WAAW;AAAA,EAClD,CAAC;AACH,CAAC;AAED,IAAI,oBAAqB,SAAU,MAAM;AACvC,MAAI,kBAAkB,KAAK,iBACvBA,aAAY,KAAK,WACjB,cAAc,KAAK,aACnB,oBAAoB,KAAK,mBACzB,iBAAiB,KAAK;AAE1B,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,kBAAkB,cAAc;AAE/C,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,QAAQ;AACzB,QAAI,KAAK;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,QACP,aAAa;AAAA,QACb,aAAa,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AACA,WAAO,SAAS,CAAC,GAAG,gBAAgB;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,MAAM,eAAe,UAAU;AACnC,MAAI,YAAY,IAAI,SAAS,IAAI,CAAC,IAAI;AAEtC,MAAI,iBAAiB;AACnB,WAAO,YAAY,UAAU,SAAS,IAAI;AAAA,EAC5C;AAEA,MAAI,mBAAmB,wBAAwBA,YAAW,iBAAiB;AAE3E,MAAI,CAAC,WAAW;AACd,QAAI,CAAC,iBAAiB,QAAQ;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,iBAAiB,iBAAiB,SAAS,CAAC;AACvD,WAAO,UAAU,KAAK,WAAW,EAAE;AAAA,EACrC;AAEA,MAAI,iBAAiB,UAAU,kBAAkB,SAAU,GAAG;AAC5D,WAAO,EAAE,WAAW,OAAO;AAAA,EAC7B,CAAC;AACD,IAAE,mBAAmB,MAAM,OAAwC5B,WAAU,OAAO,sCAAsC,IAAIA,WAAU,KAAK,IAAI;AACjJ,MAAI,gBAAgB,iBAAiB;AAErC,MAAI,gBAAgB,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,iBAAiB,aAAa;AAC3C,SAAO,UAAU,OAAO,WAAW,EAAE;AACvC;AAEA,IAAI,WAAY,SAAU4B,YAAW,aAAa;AAChD,SAAOA,WAAU,WAAW,gBAAgB,YAAY,WAAW;AACrE;AAEA,IAAI,gBAAgB;AAAA,EAClB,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,WAAW,CAAC;AAAA,EACZ,SAAS,CAAC;AAAA,EACV,KAAK,CAAC;AACR;AACA,IAAI,WAAW;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,IAAI;AACN;AAEA,IAAI,WAAY,SAAU,YAAY,YAAY;AAChD,SAAO,SAAU,OAAO;AACtB,WAAO,cAAc,SAAS,SAAS;AAAA,EACzC;AACF;AAEA,IAAI,iCAAkC,SAAU,OAAO;AACrD,MAAI,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACvD,MAAI,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AACzD,SAAO,SAAU,SAAS;AACxB,QAAI,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAE3J,QAAI,aAAa;AACf,aAAO;AAAA,IACT;AAEA,QAAI,+BAA+B,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;AACnG,QAAI,iCAAiC,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AACzG,QAAI,uBAAuB,gCAAgC;AAE3D,QAAI,sBAAsB;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB,QAAQ,MAAM,MAAM,OAAO,QAAQ,SAAS,MAAM;AAC3E,QAAI,uBAAuB,QAAQ,OAAO,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AAC9E,QAAI,0BAA0B,sBAAsB;AAEpD,QAAI,yBAAyB;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,0BAA0B,sBAAsB,kCAAkC,wBAAwB;AAC9G,WAAO;AAAA,EACT;AACF;AAEA,IAAI,+BAAgC,SAAU,OAAO;AACnD,MAAI,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACvD,MAAI,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AACzD,SAAO,SAAU,SAAS;AACxB,QAAI,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAC3J,WAAO;AAAA,EACT;AACF;AAEA,IAAI,WAAW;AAAA,EACb,WAAW;AAAA,EACX,MAAM;AAAA,EACN,eAAe;AAAA,EACf,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AACjB;AACA,IAAI,aAAa;AAAA,EACf,WAAW;AAAA,EACX,MAAM;AAAA,EACN,eAAe;AAAA,EACf,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AACjB;AAEA,IAAI,qCAAsC,SAAU,MAAM;AACxD,SAAO,SAAU,OAAO;AACtB,QAAI,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACvD,QAAI,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AACzD,WAAO,SAAU,SAAS;AACxB,UAAI,SAAS,UAAU;AACrB,eAAO,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;AAAA,MACzE;AAEA,aAAO,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAAA,IAC7E;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,SAASC,uBAAsB,QAAQ,aAAa;AAC9E,MAAI,eAAe,YAAY,QAAQ,YAAY,MAAM,OAAO,KAAK,eAAe;AACpF,SAAO,iBAAiB,QAAQ,YAAY;AAC9C;AAEA,IAAI,uBAAuB,SAASC,sBAAqB,QAAQ,aAAa,yBAAyB;AACrG,MAAI,CAAC,YAAY,QAAQ,QAAQ;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO,wBAAwB,YAAY,QAAQ,MAAM,EAAE,MAAM;AACnE;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,QAAQ,UAAU,yBAAyB;AAChG,SAAO,wBAAwB,QAAQ,EAAE,MAAM;AACjD;AAEA,IAAI,YAAY,SAASC,WAAU,MAAM;AACvC,MAAI,gBAAgB,KAAK,QACrB,cAAc,KAAK,aACnB,WAAW,KAAK,UAChBC,6BAA4B,KAAK,2BACjC,0BAA0B,KAAK;AACnC,MAAI,kBAAkBA,6BAA4B,sBAAsB,eAAe,WAAW,IAAI;AACtG,SAAO,qBAAqB,iBAAiB,aAAa,uBAAuB,KAAK,oBAAoB,iBAAiB,UAAU,uBAAuB;AAC9J;AAEA,IAAI,qBAAqB,SAASC,oBAAmB,MAAM;AACzD,SAAO,UAAU,SAAS,CAAC,GAAG,MAAM;AAAA,IAClC,yBAAyB;AAAA,EAC3B,CAAC,CAAC;AACJ;AACA,IAAI,mBAAmB,SAASC,kBAAiB,MAAM;AACrD,SAAO,UAAU,SAAS,CAAC,GAAG,MAAM;AAAA,IAClC,yBAAyB;AAAA,EAC3B,CAAC,CAAC;AACJ;AACA,IAAI,yBAAyB,SAASC,wBAAuB,MAAM;AACjE,SAAO,UAAU,SAAS,CAAC,GAAG,MAAM;AAAA,IAClC,yBAAyB,mCAAmC,KAAK,YAAY,IAAI;AAAA,EACnF,CAAC,CAAC;AACJ;AAEA,IAAI,mBAAmB,SAASC,kBAAiB,IAAI,MAAM,oBAAoB;AAC7E,MAAI,OAAO,uBAAuB,WAAW;AAC3C,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AAEnB,MAAI,UAAU,EAAE,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,QAAQ,EAAE;AACzB,SAAO,WAAW,SAAS,gBAAgB;AAC7C;AAEA,SAAS,UAAUT,YAAW,aAAa;AACzC,MAAI,YAAYA,WAAU,KAAK;AAC/B,MAAI,WAAW;AAAA,IACb,KAAK,YAAY,MAAM;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM,YAAY,MAAM;AAAA,EAC1B;AACA,SAAO,QAAQ,OAAO,WAAW,QAAQ,CAAC;AAC5C;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,gBAAgB,KAAK,eACrB,cAAc,KAAK,aACnB,cAAc,KAAK,aACnB,WAAW,KAAK,UAChB,qBAAqB,KAAK,oBAC1B,OAAO,KAAK;AAChB,SAAO,cAAc,OAAO,SAASU,SAAQ,QAAQV,YAAW;AAC9D,QAAI,SAAS,UAAUA,YAAW,WAAW;AAC7C,QAAI,KAAKA,WAAU,WAAW;AAC9B,WAAO,IAAI,KAAK,EAAE;AAClB,QAAII,aAAY,mBAAmB;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA,2BAA2B;AAAA,IAC7B,CAAC;AAED,QAAI,CAACA,YAAW;AACd,aAAO,UAAUJ,WAAU,WAAW,EAAE,IAAI;AAC5C,aAAO;AAAA,IACT;AAEA,QAAI,gBAAgB,iBAAiB,IAAI,MAAM,kBAAkB;AACjE,QAAI,eAAe;AAAA,MACjB,aAAa;AAAA,MACb;AAAA,IACF;AACA,WAAO,QAAQ,EAAE,IAAI;AACrB,WAAO;AAAA,EACT,GAAG;AAAA,IACD,KAAK,CAAC;AAAA,IACN,SAAS,CAAC;AAAA,IACV,WAAW,CAAC;AAAA,EACd,CAAC;AACH;AAEA,SAAS,mBAAmB,YAAY,SAAS;AAC/C,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,kBAAkB,WAAW,WAAW,SAAS,CAAC,EAAE,WAAW;AACnE,SAAO,QAAQ,aAAa,kBAAkB,kBAAkB;AAClE;AAEA,SAAS,QAAQ,MAAM;AACrB,MAAI,oBAAoB,KAAK,mBACzB,aAAa,KAAK,YAClB,cAAc,KAAK,aACnB,cAAc,KAAK;AACvB,MAAI,WAAW,mBAAmB,mBAAmB;AAAA,IACnD;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa;AAAA,QACX,aAAa,YAAY,WAAW;AAAA,QACpC,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,uBAAuB,OAAO;AACrC,MAAIA,aAAY,MAAM,WAClB,oBAAoB,MAAM,mBAC1B,cAAc,MAAM,aACpB,WAAW,MAAM,UACjB,cAAc,MAAM,aACpB,OAAO,MAAM,MACb,QAAQ,MAAM,OACd,qBAAqB,MAAM;AAC/B,MAAI,aAAa,SAASA,YAAW,WAAW;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIW,SAAQ,KAAK,mBAAmB,SAAU,MAAM;AAClD,WAAO,KAAK,WAAW,UAAU;AAAA,EACnC,CAAC;AAED,MAAI,CAACA,QAAO;AACV,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,kBAAkB,wBAAwBX,YAAW,iBAAiB;AAC1E,MAAI,YAAY,kBAAkB,QAAQW,MAAK;AAC/C,MAAI,WAAW,gBAAgB,MAAM,SAAS;AAC9C,MAAI,YAAY,sBAAsB;AAAA,IACpC,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,SAAS;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa;AAAA,QACX,aAAa,YAAY,WAAW;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,aAAa,eAAe;AACzD,SAAO,QAAQ,cAAc,SAAS,WAAW,CAAC;AACpD;AAEA,IAAI,cAAe,SAAU,MAAM;AACjC,MAAI,kBAAkB,KAAK,iBACvB,cAAc,KAAK,aACnB,aAAa,KAAK,YAClB/B,WAAU,KAAK,SACf,gBAAgB,KAAK;AAEzB,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,YAAYA,SAAQ;AACxB,MAAI,cAAc,WAAW,SAAS;AACtC,MAAI,mBAAmB,YAAY,WAAW;AAC9C,MAAI,mCAAmC,sBAAsB,WAAW,aAAa;AAErF,MAAI,kCAAkC;AACpC,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,mBAAmB;AAAA,EAC5B;AAEA,MAAI,iBAAiB;AACnB,WAAO,mBAAmB;AAAA,EAC5B;AAEA,SAAO;AACT;AAEA,IAAI,cAAe,SAAU,MAAM;AACjC,MAAI,kBAAkB,KAAK,iBACvB,eAAe,KAAK,cACpB,oBAAoB,KAAK,mBACzB,WAAW,KAAK;AAEpB,MAAI,CAAC,kBAAkB,QAAQ;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,SAAS;AAC5B,MAAI,gBAAgB,kBAAkB,eAAe,IAAI,eAAe;AACxE,MAAI,aAAa,kBAAkB,CAAC,EAAE,WAAW;AACjD,MAAI,YAAY,kBAAkB,kBAAkB,SAAS,CAAC,EAAE,WAAW;AAC3E,MAAI,aAAa,eAAe,YAAY,YAAY;AAExD,MAAI,gBAAgB,YAAY;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,YAAY;AAC9B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAI,kBAAkB,KAAK,iBACvB,eAAe,KAAK,cACpBoB,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,cAAc,KAAK,aACnB,oBAAoB,KAAK,mBACzB,iBAAiB,KAAK,gBACtB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AACzB,MAAI,QAAQ,eAAe;AAC3B,GAAC,QAAQ,OAAwC5B,WAAU,OAAO,2DAA2D,IAAIA,WAAU,KAAK,IAAI;AAEpJ,MAAI,MAAM,SAAS,WAAW;AAC5B,QAAI,YAAY,YAAY;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,UAAU,MAAM;AAAA,MAChB;AAAA,IACF,CAAC;AAED,QAAI,aAAa,MAAM;AACrB,aAAO;AAAA,IACT;AAEA,WAAO,uBAAuB;AAAA,MAC5B,WAAW4B;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA,MACrB,aAAa,eAAe;AAAA,MAC5B,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,MAAI,WAAW,YAAY;AAAA,IACzB;AAAA,IACA;AAAA,IACA,WAAW,eAAe;AAAA,IAC1B;AAAA,IACA,SAAS,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AAED,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuB;AAAA,IAC5B,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,eAAe;AAAA,IACrB,aAAa,eAAe;AAAA,IAC5B,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,8BAA+B,SAAU,MAAM;AACjD,MAAI,YAAY,KAAK,WACjB,gBAAgB,KAAK,eACrB,cAAc,KAAK,aACnB,cAAc,KAAK;AACvB,MAAI,cAAc,QAAQ,UAAU,QAAQ,WAAW,KAAK,UAAU,UAAU,WAAW,CAAC;AAE5F,MAAI,sBAAsB,aAAa,aAAa,GAAG;AACrD,WAAO,cAAc,SAAS,OAAO,YAAY,KAAK;AAAA,EACxD;AAEA,SAAO,cAAc,YAAY,QAAQ;AAC3C;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,gBAAgB,KAAK,eACrB,SAAS,KAAK,QACd,aAAa,KAAK;AACtB,MAAIpB,WAAU,cAAc,MAAM;AAClC,GAACA,WAAU,OAAwCR,WAAU,KAAK,IAAIA,WAAU,KAAK,IAAI;AACzF,MAAI,cAAcQ,SAAQ;AAC1B,MAAI,SAAS,WAAW,WAAW,EAAE,KAAK,UAAU;AACpD,MAAI,aAAa,4BAA4B;AAAA,IAC3C,WAAW,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA,aAAa,OAAO;AAAA,EACtB,CAAC;AACD,SAAO,IAAI,QAAQ,UAAU;AAC/B;AAEA,IAAI,qCAAqC,SAASgC,oCAAmC,MAAM,KAAK;AAC9F,SAAO,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI;AAC7D;AAEA,IAAI,mCAAmC,SAASC,kCAAiC,MAAM,KAAK;AAC1F,SAAO,IAAI,OAAO,KAAK,GAAG,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI;AAC3D;AAEA,IAAI,8BAA8B,SAASC,6BAA4B,MAAM,QAAQ,UAAU;AAC7F,SAAO,OAAO,KAAK,cAAc,IAAI,SAAS,OAAO,KAAK,cAAc,IAAI,SAAS,UAAU,KAAK,aAAa,IAAI;AACvH;AAEA,IAAI,UAAU,SAASC,SAAQ,MAAM;AACnC,MAAI,OAAO,KAAK,MACZ,iBAAiB,KAAK,gBACtB,WAAW,KAAK;AACpB,SAAO,MAAM,KAAK,MAAM,eAAe,UAAU,KAAK,GAAG,IAAI,mCAAmC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,eAAe,WAAW,QAAQ,CAAC;AACxL;AACA,IAAI,WAAW,SAASC,UAAS,OAAO;AACtC,MAAI,OAAO,MAAM,MACb,iBAAiB,MAAM,gBACvB,WAAW,MAAM;AACrB,SAAO,MAAM,KAAK,MAAM,eAAe,UAAU,KAAK,KAAK,IAAI,iCAAiC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,eAAe,WAAW,QAAQ,CAAC;AACxL;AACA,IAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,MAAI,OAAO,MAAM,MACb,WAAW,MAAM,UACjB,WAAW,MAAM;AACrB,SAAO,MAAM,KAAK,MAAM,SAAS,WAAW,KAAK,KAAK,IAAI,mCAAmC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,SAAS,YAAY,QAAQ,CAAC;AAChL;AAEA,IAAI,iBAAkB,SAAU,MAAM;AACpC,MAAI,SAAS,KAAK,QACdjB,aAAY,KAAK,WACjB,aAAa,KAAK,YAClBD,aAAY,KAAK,WACjB,gBAAgB,KAAK;AACzB,MAAI,oBAAoB,6BAA6BA,WAAU,WAAW,IAAI,UAAU;AACxF,MAAI,gBAAgBC,WAAU;AAC9B,MAAI,OAAOD,WAAU;AAErB,MAAI,CAAC,kBAAkB,QAAQ;AAC7B,WAAO,YAAY;AAAA,MACjB;AAAA,MACA,UAAUA,WAAU;AAAA,MACpB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,MAAI,YAAY,OAAO,WACnB,cAAc,OAAO;AACzB,MAAI,eAAe,UAAU,IAAI,CAAC;AAElC,MAAI,cAAc;AAChB,QAAIR,WAAU,WAAW,YAAY;AAErC,QAAI,sBAAsB,cAAc,aAAa,GAAG;AACtD,aAAO,SAAS;AAAA,QACd;AAAA,QACA,gBAAgBA,SAAQ;AAAA,QACxB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,QAAI,mBAAmB,OAAOA,SAAQ,MAAM,YAAY,KAAK;AAC7D,WAAO,SAAS;AAAA,MACd;AAAA,MACA,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,kBAAkB,kBAAkB,SAAS,CAAC;AAEzD,MAAI,KAAK,WAAW,OAAOS,WAAU,WAAW,IAAI;AAClD,WAAO,cAAc,UAAU;AAAA,EACjC;AAEA,MAAI,sBAAsB,KAAK,WAAW,IAAI,aAAa,GAAG;AAC5D,QAAI,OAAO,OAAO,KAAK,MAAM,OAAO,cAAc,YAAY,KAAK,CAAC;AACpE,WAAO,QAAQ;AAAA,MACb;AAAA,MACA,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,gBAAgB,KAAK;AAAA,IACrB,UAAU;AAAA,EACZ,CAAC;AACH;AAEA,IAAI,4BAA6B,SAAUD,YAAW,OAAO;AAC3D,MAAI,QAAQA,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY;AAClD;AAEA,IAAI,wCAAwC,SAASmB,uCAAsC,MAAM;AAC/F,MAAI,SAAS,KAAK,QACdlB,aAAY,KAAK,WACjBD,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,gBAAgB,KAAK;AACzB,MAAI,WAAWC,WAAU,KAAK,UAAU;AACxC,MAAI,KAAK,OAAO;AAEhB,MAAI,CAACD,YAAW;AACd,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,SAAS,WAAW;AACzB,WAAO,eAAe;AAAA,MACpB;AAAA,MACA,WAAWC;AAAA,MACX;AAAA,MACA,WAAWD;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,mCAAoC,SAAU,MAAM;AACtD,MAAI,sBAAsB,sCAAsC,IAAI;AACpE,MAAIA,aAAY,KAAK;AACrB,MAAI,mBAAmBA,aAAY,0BAA0BA,YAAW,mBAAmB,IAAI;AAC/F,SAAO;AACT;AAEA,IAAI,iBAAkB,SAAU,UAAU,WAAW;AACnD,MAAI,OAAO,SAAS,WAAW,SAAS,OAAO,OAAO;AACtD,MAAI,eAAe,OAAO,IAAI;AAC9B,MAAI,QAAQ,QAAQ;AAAA,IAClB,KAAK,UAAU;AAAA,IACf,QAAQ,UAAU,IAAI,SAAS,MAAM;AAAA,IACrC,MAAM,UAAU;AAAA,IAChB,OAAO,UAAU,IAAI,SAAS,MAAM;AAAA,EACtC,CAAC;AACD,MAAI,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,SAAS,OAAO;AAAA,MACzB,KAAK,SAAS,OAAO;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,YAAY;AACtC,SAAO,IAAI,IAAI,SAAU,IAAI;AAC3B,WAAO,WAAW,EAAE;AAAA,EACtB,CAAC;AACH;AAEA,SAAS,cAAc,IAAI,QAAQ;AACjC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,eAAe,OAAO,CAAC,EAAE,QAAQ,EAAE;AAEvC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,wBAAyB,SAAU,MAAM;AAC3C,MAAI,SAAS,KAAK,QACd,WAAW,KAAK,UAChB,cAAc,KAAK,aACnB,aAAa,KAAK,YAClB,kBAAkB,KAAK;AAC3B,MAAI,mBAAmB,eAAe,UAAU,IAAI,SAAS,OAAO,SAAS,eAAe,CAAC;AAC7F,MAAI,oBAAoB,YAAY,QAAQ,gBAAgB,aAAa,IAAI,YAAY,MAAM,OAAO,SAAS,eAAe,CAAC,IAAI;AACnI,MAAI,OAAO,OAAO;AAClB,MAAI,qBAAqB,sBAAsB;AAAA,IAC7C,eAAe,cAAc,KAAK,KAAK,UAAU;AAAA,IACjD;AAAA,IACA,aAAa,OAAO;AAAA,IACpB,UAAU,iBAAiB;AAAA,IAC3B;AAAA,IACA,oBAAoB;AAAA,EACtB,CAAC;AACD,MAAIoB,uBAAsB,sBAAsB;AAAA,IAC9C,eAAe,cAAc,KAAK,KAAK,UAAU;AAAA,IACjD,aAAa;AAAA,IACb,aAAa,OAAO;AAAA,IACpB,UAAU,SAAS;AAAA,IACnB;AAAA,IACA,oBAAoB;AAAA,EACtB,CAAC;AACD,MAAI,YAAY,CAAC;AACjB,MAAI,UAAU,CAAC;AACf,MAAI,SAAS,CAAC,MAAM,oBAAoBA,oBAAmB;AAC3D,OAAK,IAAI,QAAQ,SAAU,IAAI;AAC7B,QAAI,eAAe,cAAc,IAAI,MAAM;AAE3C,QAAI,cAAc;AAChB,cAAQ,EAAE,IAAI;AACd;AAAA,IACF;AAEA,cAAU,EAAE,IAAI;AAAA,EAClB,CAAC;AAED,MAAI,YAAY,SAAS,CAAC,GAAG,QAAQ;AAAA,IACnC,WAAW;AAAA,MACT,KAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAI,2BAA4B,SAAU,UAAU,OAAO;AACzD,SAAO,IAAI,SAAS,OAAO,KAAK,cAAc,KAAK;AACrD;AAEA,IAAI,mCAAoC,SAAU,MAAM;AACtD,MAAI,sBAAsB,KAAK,qBAC3BnB,aAAY,KAAK,WACjB,WAAW,KAAK;AACpB,MAAI,0BAA0B,yBAAyB,UAAU,mBAAmB;AACpF,MAAIoB,UAAS,SAAS,yBAAyBpB,WAAU,KAAK,UAAU,MAAM;AAC9E,SAAO,IAAIA,WAAU,OAAO,UAAU,QAAQoB,OAAM;AACtD;AAEA,IAAI,gCAAiC,SAAU,MAAM;AACnD,MAAIpB,aAAY,KAAK,WACjB,cAAc,KAAK,aACnB,yBAAyB,KAAK,wBAC9B,WAAW,KAAK,UAChBK,6BAA4B,KAAK,2BACjC,sBAAsB,KAAK,gBAC3B,iBAAiB,wBAAwB,SAAS,QAAQ;AAC9D,MAAI,eAAe,SAAS,wBAAwBL,WAAU,KAAK,UAAU,MAAM;AACnF,MAAI,UAAU,iBAAiBA,WAAU,KAAK,WAAW,YAAY;AACrE,MAAI,OAAO;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA,2BAA2BK;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,uBAAuB,IAAI,IAAI,iBAAiB,IAAI;AAC9E;AAEA,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAI,kBAAkB,KAAK,iBACvBL,aAAY,KAAK,WACjB,cAAc,KAAK,aACnB,aAAa,KAAK,YAClB,iBAAiB,KAAK,gBACtB,WAAW,KAAK,UAChB,8BAA8B,KAAK,6BACnC,0BAA0B,KAAK,yBAC/B,gBAAgB,KAAK;AAEzB,MAAI,CAAC,YAAY,WAAW;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC1F,MAAI,eAAe,SAASA,YAAW,WAAW;AAClD,MAAI,SAAS,kBAAkB;AAAA,IAC7B;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,KAAK,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,sBAAsB,iCAAiC;AAAA,IACzD;AAAA,IACA,WAAWA;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,yBAAyB,8BAA8B;AAAA,IACzD,WAAWA;AAAA,IACX;AAAA,IACA,wBAAwB;AAAA,IACxB,UAAU,SAAS;AAAA,IACnB,2BAA2B;AAAA,IAC3B,gBAAgB;AAAA,EAClB,CAAC;AAED,MAAI,wBAAwB;AAC1B,QAAI,kBAAkB,iCAAiC;AAAA,MACrD;AAAA,MACA,WAAWA;AAAA,MACX;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,IACrB;AAAA,EACF;AAEA,MAAIV,YAAW,SAAS,qBAAqB,2BAA2B;AACxE,MAAI,WAAW,sBAAsB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiBA;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,mBAAmBA;AAAA,EACrB;AACF;AAEA,IAAI,iBAAiB,SAAS+B,gBAAetB,YAAW;AACtD,MAAI,OAAOA,WAAU,QAAQ;AAC7B,GAAC,OAAO,OAAwC3B,WAAU,OAAO,wCAAwC,IAAIA,WAAU,KAAK,IAAI;AAChI,SAAO;AACT;AAEA,IAAI,4BAA6B,SAAU,MAAM;AAC/C,MAAI,kBAAkB,KAAK,iBACvB,sBAAsB,KAAK,qBAC3B,SAAS,KAAK,QACd,aAAa,KAAK,YAClB,WAAW,KAAK;AACpB,MAAI,SAAS,OAAO,QAAQ;AAE5B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,OAAO;AAClB,MAAI,yBAAyB,SAAS,OAAO,KAAK,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC;AAC1E,MAAI,aAAa,gBAAgB,UAAU,EAAE,OAAO,SAAU2B,YAAW;AACvE,WAAOA,eAAc;AAAA,EACvB,CAAC,EAAE,OAAO,SAAUA,YAAW;AAC7B,WAAOA,WAAU;AAAA,EACnB,CAAC,EAAE,OAAO,SAAUA,YAAW;AAC7B,WAAO,QAAQA,WAAU,QAAQ,MAAM;AAAA,EACzC,CAAC,EAAE,OAAO,SAAUA,YAAW;AAC7B,WAAO,+BAA+B,SAAS,KAAK,EAAE,eAAeA,UAAS,CAAC;AAAA,EACjF,CAAC,EAAE,OAAO,SAAUA,YAAW;AAC7B,QAAI,iBAAiB,eAAeA,UAAS;AAE7C,QAAI,iBAAiB;AACnB,aAAO,OAAO,KAAK,YAAY,IAAI,eAAe,KAAK,YAAY;AAAA,IACrE;AAEA,WAAO,eAAe,KAAK,cAAc,IAAI,OAAO,KAAK,cAAc;AAAA,EACzE,CAAC,EAAE,OAAO,SAAUA,YAAW;AAC7B,QAAI,iBAAiB,eAAeA,UAAS;AAC7C,QAAI,8BAA8B,SAAS,eAAe,KAAK,KAAK,GAAG,eAAe,KAAK,GAAG,CAAC;AAC/F,WAAO,uBAAuB,eAAe,KAAK,KAAK,CAAC,KAAK,uBAAuB,eAAe,KAAK,GAAG,CAAC,KAAK,4BAA4B,OAAO,KAAK,KAAK,CAAC,KAAK,4BAA4B,OAAO,KAAK,GAAG,CAAC;AAAA,EAClN,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AACtB,QAAI,QAAQ,eAAe,CAAC,EAAE,KAAK,cAAc;AACjD,QAAI,SAAS,eAAe,CAAC,EAAE,KAAK,cAAc;AAElD,QAAI,iBAAiB;AACnB,aAAO,QAAQ;AAAA,IACjB;AAEA,WAAO,SAAS;AAAA,EAClB,CAAC,EAAE,OAAO,SAAUA,YAAW,OAAO,OAAO;AAC3C,WAAO,eAAeA,UAAS,EAAE,KAAK,cAAc,MAAM,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,cAAc;AAAA,EACxG,CAAC;AAED,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,WAAW,CAAC;AAAA,EACrB;AAEA,MAAI,WAAW,WAAW,OAAO,SAAUA,YAAW;AACpD,QAAI,oBAAoB,SAAS,eAAeA,UAAS,EAAE,KAAK,KAAK,GAAG,eAAeA,UAAS,EAAE,KAAK,GAAG,CAAC;AAC3G,WAAO,kBAAkB,oBAAoB,KAAK,IAAI,CAAC;AAAA,EACzD,CAAC;AAED,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO,SAAS,CAAC;AAAA,EACnB;AAEA,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO,SAAS,KAAK,SAAU,GAAG,GAAG;AACnC,aAAO,eAAe,CAAC,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC,EAAE,KAAK,KAAK;AAAA,IACrE,CAAC,EAAE,CAAC;AAAA,EACN;AAEA,SAAO,WAAW,KAAK,SAAU,GAAG,GAAG;AACrC,QAAI,QAAQ,QAAQ,qBAAqB,WAAW,eAAe,CAAC,CAAC,CAAC;AACtE,QAAI,SAAS,QAAQ,qBAAqB,WAAW,eAAe,CAAC,CAAC,CAAC;AAEvE,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ;AAAA,IACjB;AAEA,WAAO,eAAe,CAAC,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC,EAAE,KAAK,KAAK;AAAA,EACrE,CAAC,EAAE,CAAC;AACN;AAEA,IAAI,gCAAgC,SAASuB,+BAA8BtB,YAAW,eAAe;AACnG,MAAI,WAAWA,WAAU,KAAK,UAAU;AACxC,SAAO,sBAAsBA,WAAU,WAAW,IAAI,aAAa,IAAI,SAAS,UAAU,cAAc,YAAY,KAAK,IAAI;AAC/H;AACA,IAAI,0BAA0B,SAASuB,yBAAwBvB,YAAW,eAAe;AACvF,MAAI,WAAWA,WAAU,KAAK;AAC9B,SAAO,sBAAsBA,WAAU,WAAW,IAAI,aAAa,IAAI,iBAAiB,UAAU,OAAO,cAAc,YAAY,KAAK,CAAC,IAAI;AAC/I;AAEA,IAAI,sBAAuB,SAAU,MAAM;AACzC,MAAI,sBAAsB,KAAK,qBAC3B,WAAW,KAAK,UAChB,cAAc,KAAK,aACnB,oBAAoB,KAAK,mBACzB,gBAAgB,KAAK;AACzB,MAAI,SAAS,kBAAkB,OAAO,SAAUA,YAAW;AACzD,WAAO,iBAAiB;AAAA,MACtB,QAAQ,wBAAwBA,YAAW,aAAa;AAAA,MACxD;AAAA,MACA,UAAU,SAAS;AAAA,MACnB,2BAA2B;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AACtB,QAAI,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG,aAAa,CAAC,CAAC;AACvI,QAAI,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG,aAAa,CAAC,CAAC;AAEvI,QAAI,cAAc,aAAa;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,aAAa;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,EAAE,WAAW,QAAQ,EAAE,WAAW;AAAA,EAC3C,CAAC;AACD,SAAO,OAAO,CAAC,KAAK;AACtB;AAEA,IAAI,iBAAiB,wBAAW,SAASwB,gBAAe,MAAM,YAAY;AACxE,MAAI,eAAe,WAAW,KAAK,IAAI;AACvC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO,MAAM,KAAK,MAAM,YAAY;AAAA,EACtC;AACF,CAAC;AAED,IAAI,kCAAkC,SAASC,iCAAgC1B,YAAW,iBAAiB,YAAY;AACrH,MAAI,OAAOA,WAAU;AAErB,MAAIA,WAAU,WAAW,SAAS,WAAW;AAC3C,WAAO,MAAM,KAAK,MAAM,gBAAgB,KAAK,IAAI,CAAC;AAAA,EACpD;AAEA,MAAI,iBAAiBA,WAAU,QAAQ,KAAK,WAAW,KAAK,IAAI;AAChE,MAAI,kBAAkB,6BAA6BA,WAAU,WAAW,IAAI,UAAU;AACtF,MAAI,YAAY,gBAAgB,OAAO,SAAU,KAAK,WAAW;AAC/D,WAAO,MAAM,UAAU,OAAO,UAAU,KAAK,IAAI;AAAA,EACnD,GAAG,CAAC;AACJ,MAAI,gBAAgB,YAAY,gBAAgB,KAAK,IAAI;AACzD,MAAI,gBAAgB,gBAAgB;AAEpC,MAAI,iBAAiB,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,KAAK,MAAM,aAAa;AACvC;AAEA,IAAI,gBAAgB,SAAS2B,eAAc,OAAO,KAAK;AACrD,SAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACzB,QAAQ,SAAS,CAAC,GAAG,MAAM,QAAQ;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAI,iBAAiB,SAASC,gBAAe5B,YAAWC,YAAW,YAAY;AAC7E,MAAI,QAAQD,WAAU;AACtB,GAAC,CAAC,SAASC,YAAWD,UAAS,IAAI,OAAwC3B,WAAU,OAAO,+CAA+C,IAAIA,WAAU,KAAK,IAAI;AAClK,GAAC,CAAC2B,WAAU,QAAQ,kBAAkB,OAAwC3B,WAAU,OAAO,kEAAkE,IAAIA,WAAU,KAAK,IAAI;AACxL,MAAI,kBAAkB,eAAe2B,WAAU,MAAMC,WAAU,UAAU,EAAE;AAC3E,MAAI,iBAAiB,gCAAgCD,YAAW,iBAAiB,UAAU;AAC3F,MAAI,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,IACb,mBAAmBA,WAAU,QAAQA,WAAU,MAAM,OAAO,MAAM;AAAA,EACpE;AAEA,MAAI,CAAC,OAAO;AACV,QAAI,WAAW,WAAW;AAAA,MACxB,MAAMA,WAAU,QAAQ;AAAA,MACxB,iBAAiB;AAAA,MACjB,MAAMA,WAAU;AAAA,MAChB,OAAOA,WAAU;AAAA,IACnB,CAAC;AAED,WAAO,SAAS,CAAC,GAAGA,YAAW;AAAA,MAC7B,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,YAAY,iBAAiB,IAAI,MAAM,OAAO,KAAK,cAAc,IAAI,MAAM,OAAO;AACtF,MAAI,WAAW,cAAc,OAAO,SAAS;AAC7C,MAAI,UAAU,WAAW;AAAA,IACvB,MAAMA,WAAU,QAAQ;AAAA,IACxB,iBAAiB;AAAA,IACjB,MAAMA,WAAU;AAAA,IAChB,OAAO;AAAA,EACT,CAAC;AACD,SAAO,SAAS,CAAC,GAAGA,YAAW;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AACA,IAAI,oBAAoB,SAAS6B,mBAAkB7B,YAAW;AAC5D,MAAI,QAAQA,WAAU,QAAQ;AAC9B,GAAC,QAAQ,OAAwC3B,WAAU,OAAO,4DAA4D,IAAIA,WAAU,KAAK,IAAI;AACrJ,MAAI,QAAQ2B,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,QAAI,YAAY,WAAW;AAAA,MACzB,MAAMA,WAAU,QAAQ;AAAA,MACxB,MAAMA,WAAU;AAAA,MAChB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB,CAAC;AAED,WAAO,SAAS,CAAC,GAAGA,YAAW;AAAA,MAC7B,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,eAAe,MAAM;AACzB,GAAC,eAAe,OAAwC3B,WAAU,OAAO,sFAAsF,IAAIA,WAAU,KAAK,IAAI;AACtL,MAAI,WAAW,cAAc,OAAO,YAAY;AAChD,MAAI,UAAU,WAAW;AAAA,IACvB,MAAM2B,WAAU,QAAQ;AAAA,IACxB,MAAMA,WAAU;AAAA,IAChB,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB,CAAC;AACD,SAAO,SAAS,CAAC,GAAGA,YAAW;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,qBAAsB,SAAU,MAAM;AACxC,MAAI,8BAA8B,KAAK,6BACnC,iBAAiB,KAAK,gBACtB,oBAAoB,KAAK,mBACzBC,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,cAAc,KAAK,aACnB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AAEzB,MAAI,CAAC,gBAAgB;AACnB,QAAI,kBAAkB,QAAQ;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AAAA,MACb,WAAW;AAAA,MACX,aAAa;AAAA,MACb,IAAI;AAAA,QACF,MAAM;AAAA,QACN,aAAa;AAAA,UACX,aAAa,YAAY,WAAW;AAAA,UACpC,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,8BAA8B,iCAAiC;AAAA,MACjE,QAAQ;AAAA,MACR,WAAWA;AAAA,MACX,WAAW;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,kBAAkB,SAASA,YAAW,WAAW,IAAI,cAAc,eAAe,aAAaA,YAAW,UAAU;AACxH,QAAI,yBAAyB,8BAA8B;AAAA,MACzD,WAAWA;AAAA,MACX,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,UAAU,SAAS;AAAA,MACnB,2BAA2B;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AACD,WAAO,yBAAyB,WAAW;AAAA,EAC7C;AAEA,MAAI,sBAAsB,QAAQ,4BAA4B,YAAY,KAAK,IAAI,KAAK,eAAe,KAAK,UAAU,OAAO,YAAY,KAAK,IAAI,CAAC;AAEnJ,MAAI,gBAAgB,WAAY;AAC9B,QAAI,aAAa,eAAe,WAAW;AAE3C,QAAI,eAAe,WAAW,OAAOA,WAAU,WAAW,IAAI;AAC5D,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB;AACvB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa;AAAA,EACtB,EAAE;AAEF,MAAI,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACvE,SAAO,uBAAuB;AAAA,IAC5B,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,kBAAkB,KAAK,iBACvB,8BAA8B,KAAK,6BACnCA,aAAY,KAAK,WACjB,SAAS,KAAK,QACd,aAAa,KAAK,YAClB,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AACzB,MAAI,cAAc,0BAA0B;AAAA,IAC1C;AAAA,IACA,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC1F,MAAI,iBAAiB,oBAAoB;AAAA,IACvC,qBAAqB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,SAAS,mBAAmB;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,sBAAsB,iCAAiC;AAAA,IACzD;AAAA,IACA,WAAWA;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB,iCAAiC;AAAA,IACrD;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,EACrB;AACF;AAEA,IAAI,oBAAqB,SAAU,QAAQ;AACzC,MAAI,KAAK,OAAO;AAEhB,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,SAAS,WAAW;AACzB,WAAO,GAAG,YAAY;AAAA,EACxB;AAEA,SAAO,GAAG,QAAQ;AACpB;AAEA,IAAI,mBAAmB,SAAS6B,kBAAiB,QAAQ,YAAY;AACnE,MAAI,KAAK,kBAAkB,MAAM;AACjC,SAAO,KAAK,WAAW,EAAE,IAAI;AAC/B;AAEA,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,iBAAiB,iBAAiB,MAAM,QAAQ,MAAM,WAAW,UAAU;AAC/E,MAAI,4BAA4B,QAAQ,cAAc;AACtD,MAAIC,QAAO,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAClE,MAAI,SAAS,kBAAkBA;AAC/B,MAAI,YAAY,OAAO,KAAK;AAC5B,MAAI,qBAAqB,cAAc,eAAe,SAAS,aAAa,SAAS,gBAAgB,cAAc,iBAAiB,SAAS,eAAe,SAAS;AAErK,MAAI,sBAAsB,CAAC,2BAA2B;AACpD,WAAO;AAAA,EACT;AAEA,MAAI,kBAAkB,SAAS,eAAe,SAAS;AACvD,MAAI9B,aAAY,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACvE,MAAI,8BAA8B,MAAM,QAAQ,KAAK;AACrD,MAAI,oBAAoB,MAAM,YAC1B,aAAa,kBAAkB,YAC/B,aAAa,kBAAkB;AACnC,SAAO,qBAAqB,gBAAgB;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,WAAWA;AAAA,IACX,aAAa;AAAA,IACb;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,yBAAyB,MAAM,QAAQ,OAAO;AAAA,IAC9C,gBAAgB,MAAM;AAAA,IACtB,eAAe,MAAM;AAAA,EACvB,CAAC,IAAI,cAAc;AAAA,IACjB;AAAA,IACA;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,MAAM,UAAU,cAAc,MAAM,UAAU;AACvD;AAEA,SAAS,kBAAkB,OAAO;AAChC,MAAI,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACvD,MAAI,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AACzD,SAAO,SAAS,IAAI,OAAO;AACzB,WAAO,iBAAiB,MAAM,CAAC,KAAK,mBAAmB,MAAM,CAAC;AAAA,EAChE;AACF;AAEA,SAAS,cAAc,OAAO,QAAQ;AACpC,SAAO,MAAM,OAAO,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,MAAM,MAAM,OAAO,UAAU,MAAM,SAAS,OAAO;AACtH;AAEA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,gBAAgB,KAAK,eACrBA,aAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,cAAcA,WAAU,KAAK,UAAU;AAC3C,MAAI,SAAS,WAAW,IAAI,SAAU,WAAW;AAC/C,QAAI,OAAO,UAAU;AACrB,QAAI,SAAS,MAAM,UAAU,KAAK,MAAM,cAAc,OAAO,KAAK,IAAI,GAAG,UAAU,KAAK,UAAU,OAAO,KAAK,aAAa,CAAC;AAC5H,WAAO;AAAA,MACL,IAAI,UAAU,WAAW;AAAA,MACzB,UAAU,SAAS,aAAa,MAAM;AAAA,IACxC;AAAA,EACF,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AACtB,WAAO,EAAE,WAAW,EAAE;AAAA,EACxB,CAAC;AACD,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,KAAK;AACpC;AAEA,SAAS,mBAAmB,OAAO;AACjC,MAAI,gBAAgB,MAAM,eACtBA,aAAY,MAAM,WAClB,aAAa,MAAM;AACvB,MAAI,aAAa,gBAAgB,UAAU,EAAE,OAAO,SAAU,MAAM;AAClE,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,KAAK,QAAQ;AAE1B,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,cAAc,eAAe,MAAM,GAAG;AACzC,aAAO;AAAA,IACT;AAEA,QAAI,kBAAkB,MAAM,EAAE,cAAc,MAAM,GAAG;AACnD,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK;AAChB,QAAI,cAAc,OAAO,OAAO,KAAK,aAAa;AAClD,QAAI,iBAAiB,cAAc,KAAK,cAAc;AACtD,QAAI,eAAe,cAAc,KAAK,YAAY;AAClD,QAAI,cAAc,SAAS,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,YAAY,CAAC;AACjF,QAAI,mBAAmB,YAAY,cAAc;AACjD,QAAI,iBAAiB,YAAY,YAAY;AAE7C,QAAI,CAAC,oBAAoB,CAAC,gBAAgB;AACxC,aAAO;AAAA,IACT;AAEA,QAAI,kBAAkB;AACpB,aAAO,iBAAiB;AAAA,IAC1B;AAEA,WAAO,eAAe;AAAA,EACxB,CAAC;AAED,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,WAAW,CAAC,EAAE,WAAW;AAAA,EAClC;AAEA,SAAO,gBAAgB;AAAA,IACrB;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,IAAI,uBAAuB,SAAS+B,sBAAqB,MAAM,OAAO;AACpE,SAAO,QAAQ,iBAAiB,MAAM,KAAK,CAAC;AAC9C;AAEA,IAAI,sBAAuB,SAAUhC,YAAW,MAAM;AACpD,MAAI,QAAQA,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,SAAO,qBAAqB,MAAM,MAAM,OAAO,KAAK,KAAK;AAC3D;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,YAAY,KAAK,WACjB,KAAK,KAAK;AACd,SAAO,QAAQ,UAAU,QAAQ,EAAE,KAAK,UAAU,UAAU,EAAE,CAAC;AACjE;AAEA,SAAS,QAAQ,MAAM;AACrB,MAAIC,aAAY,KAAK,WACjBT,WAAU,KAAK,SACf,aAAa,KAAK;AAEtB,MAAI,CAACA,UAAS;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,YAAY;AACf,WAAOA,SAAQ,WAAW;AAAA,EAC5B;AAEA,MAAIA,SAAQ,WAAW,QAAQS,WAAU,WAAW,OAAO;AACzD,WAAOT,SAAQ,WAAW,QAAQ;AAAA,EACpC;AAEA,SAAOA,SAAQ,WAAW;AAC5B;AAEA,IAAI,mBAAoB,SAAU,OAAO;AACvC,MAAI,aAAa,MAAM,kCACnBS,aAAY,MAAM,WAClB,cAAc,MAAM,aACpB,oBAAoB,MAAM,mBAC1B,OAAO,MAAM,MACb,WAAW,MAAM,UACjB,gBAAgB,MAAM;AAC1B,MAAI,OAAO,YAAY;AACvB,MAAI,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACvE,MAAI,eAAe,YAAY;AAC/B,MAAI,cAAc,WAAW,KAAK,KAAK;AACvC,MAAI,YAAY,WAAW,KAAK,GAAG;AACnC,MAAI,kBAAkB,wBAAwBA,YAAW,iBAAiB;AAC1E,MAAIT,WAAU,KAAK,iBAAiB,SAAU,OAAO;AACnD,QAAI,KAAK,MAAM,WAAW;AAC1B,QAAI,cAAc,MAAM,KAAK,UAAU,OAAO,KAAK,IAAI;AACvD,QAAI,0BAA0B,sBAAsB,IAAI,aAAa;AACrE,QAAI,cAAc,eAAe;AAAA,MAC/B,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAED,QAAI,yBAAyB;AAC3B,UAAI,aAAa;AACf,eAAO,aAAa;AAAA,MACtB;AAEA,aAAO,cAAc,cAAc;AAAA,IACrC;AAEA,QAAI,aAAa;AACf,aAAO,aAAa,cAAc;AAAA,IACpC;AAEA,WAAO,cAAc;AAAA,EACvB,CAAC;AACD,MAAI,WAAW,QAAQ;AAAA,IACrB,WAAWS;AAAA,IACX,SAAST;AAAA,IACT,YAAY,SAASS,YAAW,WAAW;AAAA,EAC7C,CAAC;AACD,SAAO,uBAAuB;AAAA,IAC5B,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,0BAA0B;AAC9B,IAAI,mBAAoB,SAAU,MAAM;AACtC,MAAIA,aAAY,KAAK,WACjB,aAAa,KAAK,kCAClB,iBAAiB,KAAK,gBACtB,cAAc,KAAK,aACnB,oBAAoB,KAAK,mBACzB,gBAAgB,KAAK;AAEzB,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY;AACvB,MAAI,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACvE,MAAI,eAAe,YAAY;AAC/B,MAAI,cAAc,WAAW,KAAK,KAAK;AACvC,MAAI,YAAY,WAAW,KAAK,GAAG;AACnC,MAAI,kBAAkB,wBAAwBA,YAAW,iBAAiB;AAC1E,MAAI,cAAc,KAAK,iBAAiB,SAAU,OAAO;AACvD,QAAI,KAAK,MAAM,WAAW;AAC1B,QAAI,YAAY,MAAM,KAAK;AAC3B,QAAI,YAAY,UAAU,KAAK,IAAI;AACnC,QAAI,YAAY,YAAY;AAC5B,QAAI,0BAA0B,sBAAsB,IAAI,aAAa;AACrE,QAAI,cAAc,eAAe;AAAA,MAC/B,WAAW,eAAe;AAAA,MAC1B;AAAA,IACF,CAAC;AAED,QAAI,yBAAyB;AAC3B,UAAI,aAAa;AACf,eAAO,YAAY,UAAU,KAAK,KAAK,IAAI,aAAa,YAAY,UAAU,KAAK,GAAG,IAAI;AAAA,MAC5F;AAEA,aAAO,cAAc,UAAU,KAAK,KAAK,IAAI,eAAe,aAAa,cAAc,UAAU,KAAK,GAAG,IAAI,eAAe;AAAA,IAC9H;AAEA,QAAI,aAAa;AACf,aAAO,YAAY,UAAU,KAAK,KAAK,IAAI,eAAe,aAAa,YAAY,UAAU,KAAK,GAAG,IAAI,eAAe;AAAA,IAC1H;AAEA,WAAO,cAAc,UAAU,KAAK,KAAK,IAAI,aAAa,cAAc,UAAU,KAAK,GAAG,IAAI;AAAA,EAChG,CAAC;AAED,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AAAA,IACX;AAAA,IACA,WAAW,eAAe;AAAA,IAC1B,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,QACP,aAAa,YAAY,WAAW;AAAA,QACpC,aAAa,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,aAAa,KAAK,YAClBA,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,aAAa,KAAK,YAClB,iBAAiB,KAAK,gBACtB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AACzB,MAAI,gBAAgB,qBAAqBA,WAAU,KAAK,WAAW,UAAU;AAC7E,MAAI,gBAAgB,mBAAmB;AAAA,IACrC;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,EACF,CAAC;AAED,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,WAAW,aAAa;AAC1C,MAAI,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC1F,MAAI,mCAAmC,oBAAoB,aAAa,aAAa;AACrF,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,KAAK,iBAAiB;AAAA,IACrB;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA,MAAM,eAAe;AAAA,IACrB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,oBAAqB,SAAU,YAAY,SAAS;AACtD,MAAI;AAEJ,SAAO,SAAS,CAAC,GAAG,aAAa,YAAY,CAAC,GAAG,UAAU,QAAQ,WAAW,EAAE,IAAI,SAAS,UAAU;AACzG;AAEA,IAAI,yBAAyB,SAASgC,wBAAuB,MAAM;AACjE,MAAI,iBAAiB,KAAK,gBACtB,SAAS,KAAK,QACd,aAAa,KAAK;AACtB,MAAI,OAAO,kBAAkB,cAAc;AAC3C,MAAI,MAAM,kBAAkB,MAAM;AAElC,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,gBAAgB,WAAW,IAAI;AAEnC,MAAI,CAAC,cAAc,QAAQ,iBAAiB;AAC1C,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,kBAAkB,aAAa;AAC7C,SAAO,kBAAkB,YAAY,OAAO;AAC9C;AAEA,IAAI,wBAAyB,SAAU,OAAO;AAC5C,MAAIhC,aAAY,MAAM,WAClB,aAAa,MAAM,YACnB,aAAa,MAAM,YACnB,iBAAiB,MAAM,gBACvB,SAAS,MAAM;AACnB,MAAI,UAAU,uBAAuB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,SAAS,kBAAkB,MAAM;AAErC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAID,aAAY,WAAW,MAAM;AAEjC,MAAI,SAASC,YAAWD,UAAS,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,MAAIA,WAAU,QAAQ,iBAAiB;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,eAAeA,YAAWC,YAAW,UAAU;AAC7D,SAAO,kBAAkB,SAAS,OAAO;AAC3C;AAEA,IAAI,SAAU,SAAU,MAAM;AAC5B,MAAI,QAAQ,KAAK,OACb,wBAAwB,KAAK,iBAC7B,mBAAmB,KAAK,YACxB,iBAAiB,KAAK,UACtB,eAAe,KAAK,QACpB,oBAAoB,KAAK;AAC7B,MAAI,WAAW,kBAAkB,MAAM;AACvC,MAAI,aAAa,oBAAoB,MAAM;AAC3C,MAAI,kBAAkB,yBAAyB,MAAM,QAAQ,OAAO;AACpE,MAAIoB,UAAS,SAAS,iBAAiB,MAAM,QAAQ,OAAO,SAAS;AACrE,MAAI,SAAS;AAAA,IACX,QAAQA;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB,IAAI,MAAM,QAAQ,OAAO,iBAAiBA,OAAM;AAAA,EACnE;AACA,MAAI,OAAO;AAAA,IACT,WAAW,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,IACxD,iBAAiB,IAAI,OAAO,iBAAiB,SAAS,OAAO,OAAO;AAAA,IACpE,QAAQ,IAAI,OAAO,QAAQ,SAAS,OAAO,KAAK,KAAK;AAAA,EACvD;AACA,MAAI,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM,UAAU,cAAc;AAChC,WAAO,SAAS;AAAA,MACd,OAAO;AAAA,IACT,GAAG,OAAO;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIpB,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACjE,MAAI,YAAY,gBAAgB,cAAc;AAAA,IAC5C,YAAY,KAAK;AAAA,IACjB,WAAWA;AAAA,IACX,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB,gBAAgB,MAAM;AAAA,IACtB;AAAA,IACA,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,MAAI,0BAA0B,sBAAsB;AAAA,IAClD,WAAWA;AAAA,IACX,QAAQ;AAAA,IACR,gBAAgB,MAAM;AAAA,IACtB,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,EACzB,CAAC;AAED,MAAI,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA,IAC/B;AAAA,IACA,YAAY;AAAA,MACV,YAAY,WAAW;AAAA,MACvB,YAAY;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,mBAAmB,qBAAqB;AAAA,IACxC,oBAAoB,oBAAoB,QAAQ;AAAA,EAClD,CAAC;AAED,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,YAAY;AACxC,SAAO,IAAI,IAAI,SAAU,IAAI;AAC3B,WAAO,WAAW,EAAE;AAAA,EACtB,CAAC;AACH;AAEA,IAAI,YAAa,SAAU,MAAM;AAC/B,MAAI,SAAS,KAAK,QACd,WAAW,KAAK,UAChB,aAAa,KAAK,YAClB,cAAc,KAAK,aACnB,qBAAqB,KAAK;AAC9B,MAAI,OAAO,OAAO;AAClB,MAAI,gBAAgB,gBAAgB,KAAK,KAAK,UAAU;AACxD,MAAI,YAAY,sBAAsB;AAAA,IACpC;AAAA,IACA;AAAA,IACA,aAAa,OAAO;AAAA,IACpB,UAAU,SAAS;AAAA,IACnB;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,SAAS,CAAC,GAAG,QAAQ;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AAEA,IAAI,2BAA4B,SAAU,MAAM;AAC9C,MAAI,SAAS,KAAK,QACdA,aAAY,KAAK,WACjBD,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AACzB,MAAI,sBAAsB,iCAAiC;AAAA,IACzD;AAAA,IACA,WAAWC;AAAA,IACX;AAAA,IACA,WAAWD;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO,iCAAiC;AAAA,IACtC;AAAA,IACA,WAAWC;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,IAAI,cAAe,SAAU,MAAM;AACjC,MAAI,QAAQ,KAAK,OACb,mBAAmB,KAAK,YACxB,iBAAiB,KAAK;AAC1B,IAAE,MAAM,iBAAiB,UAAU,OAAwC5B,WAAU,KAAK,IAAIA,WAAU,KAAK,IAAI;AACjH,MAAI,uBAAuB,MAAM;AACjC,MAAI,WAAW,kBAAkB,MAAM;AACvC,MAAI,aAAa,oBAAoB,MAAM;AAC3C,MAAI,aAAa,WAAW,YACxB,aAAa,WAAW;AAC5B,MAAI4B,aAAY,WAAW,MAAM,SAAS,UAAU,EAAE;AACtD,MAAI,SAAS,kBAAkB,oBAAoB;AACnD,GAAC,SAAS,OAAwC5B,WAAU,OAAO,kDAAkD,IAAIA,WAAU,KAAK,IAAI;AAC5I,MAAI,cAAc,WAAW,MAAM;AACnC,MAAI,SAAS,UAAU;AAAA,IACrB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB,yBAAyB;AAAA,IAC7C;AAAA,IACA,WAAW4B;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,kBAAmB,SAAU,YAAY;AAC3C,SAAO;AAAA,IACL,OAAO,WAAW;AAAA,IAClB,aAAa,WAAW;AAAA,EAC1B;AACF;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAIA,aAAY,KAAK,WACjB8B,QAAO,KAAK,MACZ,aAAa,KAAK,YAClB,WAAW,KAAK;AACpB,MAAI,cAAc,eAAeA,MAAK,MAAM9B,WAAU,UAAU;AAChE,MAAI,aAAa,6BAA6B8B,MAAK,WAAW,IAAI,UAAU;AAC5E,MAAI,WAAW,WAAW,QAAQ9B,UAAS;AAC3C,IAAE,aAAa,MAAM,OAAwC5B,WAAU,OAAO,2CAA2C,IAAIA,WAAU,KAAK,IAAI;AAChJ,MAAI,gBAAgB,WAAW,MAAM,WAAW,CAAC;AACjD,MAAI,WAAW,cAAc,OAAO,SAAU,UAAU,MAAM;AAC5D,aAAS,KAAK,WAAW,EAAE,IAAI;AAC/B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB;AAAA,IAClB,eAAe0D,MAAK,WAAW,SAAS;AAAA,IACxC;AAAA,IACA;AAAA,EACF;AACA,MAAI,YAAY,sBAAsB;AAAA,IACpC;AAAA,IACA,aAAaA;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,UAAU,SAAS;AAAA,IACnB,oBAAoB;AAAA,EACtB,CAAC;AACD,MAAI,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa,gBAAgB9B,WAAU,UAAU;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,oBAAqB,SAAU,YAAY,SAAS;AACtD,SAAO;AAAA,IACL,YAAY,WAAW;AAAA,IACvB,YAAY,kBAAkB,WAAW,YAAY,OAAO;AAAA,EAC9D;AACF;AAEA,IAAI,QAAQ,SAASvB,OAAM,KAAK;AAC9B,MAAI,MAAuC;AACzC;AACE;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,SAAS,SAASwD,QAAO,KAAK;AAChC,MAAI,MAAuC;AACzC;AACE;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAIjC,aAAY,KAAK,WACjB,WAAW,KAAK,QAChB,sBAAsB,KAAK;AAC/B,MAAI,SAAS,OAAOA,WAAU,QAAQ,QAAQ;AAC9C,MAAI,OAAO,WAAW,QAAQ,mBAAmB;AAEjD,MAAI,QAAQ,SAAS,CAAC,GAAGA,YAAW;AAAA,IAClC,aAAa,SAAS,CAAC,GAAGA,WAAU,aAAa;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAI,WAAY,SAAUD,YAAW;AACnC,MAAI,QAAQA,WAAU;AACtB,GAAC,QAAQ,OAAwC3B,WAAU,OAAO,oCAAoC,IAAIA,WAAU,KAAK,IAAI;AAC7H,SAAO;AACT;AAEA,IAAI,kCAAmC,SAAU,MAAM;AACrD,MAAI,YAAY,KAAK,WACjB,oBAAoB,KAAK,mBACzB,WAAW,KAAK;AACpB,MAAI,qBAAqB,SAAS,OAAO,KAAK;AAC9C,SAAO,UAAU,IAAI,SAAU4B,YAAW;AACxC,QAAI,cAAcA,WAAU,WAAW;AACvC,QAAI,WAAW,kBAAkB,WAAW;AAC5C,QAAI,QAAQ,SAAS,QAAQ;AAC7B,QAAI,wBAAwB,MAAM,OAAO,KAAK;AAC9C,QAAI,cAAc,IAAI,oBAAoB,qBAAqB;AAC/D,QAAI,QAAQ,gBAAgB;AAAA,MAC1B,WAAWA;AAAA,MACX,QAAQ;AAAA,MACR,qBAAqB,SAAS,OAAO;AAAA,IACvC,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,gCAAiC,SAAU,MAAM;AACnD,MAAI,QAAQ,KAAK,OACb,YAAY,KAAK;AACrB,QAAM;AACN,MAAI,mBAAmB,UAAU,SAAS,IAAI,SAAUlB,SAAQ;AAC9D,QAAI,WAAW,MAAM,WAAW,WAAWA,QAAO,WAAW;AAC7D,QAAI,WAAW,gBAAgB,UAAUA,QAAO,MAAM;AACtD,WAAO;AAAA,EACT,CAAC;AAED,MAAI,aAAa,SAAS,CAAC,GAAG,MAAM,WAAW,YAAY,CAAC,GAAG,eAAe,gBAAgB,CAAC;AAE/F,MAAI,mBAAmB,eAAe,gCAAgC;AAAA,IACpE,WAAW,UAAU;AAAA,IACrB,mBAAmB;AAAA,IACnB,UAAU,MAAM;AAAA,EAClB,CAAC,CAAC;AAEF,MAAI,aAAa,SAAS,CAAC,GAAG,MAAM,WAAW,YAAY,CAAC,GAAG,gBAAgB;AAE/E,YAAU,SAAS,QAAQ,SAAU,IAAI;AACvC,WAAO,WAAW,EAAE;AAAA,EACtB,CAAC;AACD,MAAI,aAAa;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACA,MAAI,YAAY,kBAAkB,MAAM,MAAM;AAC9C,MAAI,UAAU,YAAY,WAAW,WAAW,SAAS,IAAI;AAC7D,MAAIkB,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACjE,MAAI8B,QAAO,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAE5D,MAAI,iBAAiB,cAAc;AAAA,IACjC,WAAW9B;AAAA,IACX,MAAM8B;AAAA,IACN;AAAA,IACA,UAAU,MAAM;AAAA,EAClB,CAAC,GACG,eAAe,eAAe,QAC9B,gBAAgB,eAAe;AAEnC,MAAI,iBAAiB,WAAW,QAAQ,mBAAmB,MAAM,SAAS;AAC1E,MAAI,SAAS,cAAc;AAAA,IACzB,YAAY,MAAM,QAAQ,KAAK;AAAA,IAC/B,WAAW,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAAA,IAC5D,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB;AAAA,IACA,UAAU,MAAM;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AAEP,MAAI,gBAAgB,SAAS;AAAA,IAC3B,OAAO;AAAA,EACT,GAAG,OAAO;AAAA,IACR,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB,CAAC;AAED,MAAI,MAAM,UAAU,cAAc;AAChC,WAAO;AAAA,EACT;AAEA,MAAII,eAAc,SAAS;AAAA,IACzB,OAAO;AAAA,EACT,GAAG,eAAe;AAAA,IAChB,OAAO;AAAA,IACP,QAAQ,MAAM;AAAA,IACd,WAAW;AAAA,EACb,CAAC;AAED,SAAOA;AACT;AAEA,IAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,SAAO,MAAM,iBAAiB;AAChC;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,OAAO,SAAS,mBAAmB;AACxF,MAAI,aAAa,kBAAkB,MAAM,YAAY,OAAO;AAE5D,MAAI,CAAC,WAAW,KAAK,KAAK,mBAAmB;AAC3C,WAAO,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,wBAAwB,OAAO;AACtC,MAAI,MAAM,cAAc,MAAM,iBAAiB,QAAQ;AACrD,WAAO,SAAS;AAAA,MACd,OAAO;AAAA,IACT,GAAG,OAAO;AAAA,MACR,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,IAAI,OAAO;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AACf;AACA,IAAI,UAAW,SAAU,OAAO,QAAQ;AACtC,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AAEA,MAAI,OAAO,SAAS,SAAS;AAC3B,WAAO,SAAS,CAAC,GAAG,MAAM;AAAA,MACxB,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,mBAAmB;AACrC,MAAE,MAAM,UAAU,UAAU,OAAwChE,WAAU,OAAO,8CAA8C,IAAIA,WAAU,KAAK,IAAI;AAC1J,QAAI,kBAAkB,OAAO,SACzB,WAAW,gBAAgB,UAC3B,kBAAkB,gBAAgB,iBAClC,WAAW,gBAAgB,UAC3B,aAAa,gBAAgB,YAC7B,eAAe,gBAAgB;AACnC,QAAI4B,aAAY,WAAW,WAAW,SAAS,UAAU,EAAE;AAC3D,QAAI8B,QAAO,WAAW,WAAW,SAAS,UAAU,EAAE;AACtD,QAAI,SAAS;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB9B,WAAU,OAAO,UAAU;AAAA,MAC5C,QAAQ;AAAA,IACV;AACA,QAAI,UAAU;AAAA,MACZ;AAAA,MACA,MAAM;AAAA,QACJ,WAAW,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,QACxD,iBAAiB,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,QAC9D,QAAQ,IAAI,OAAO,WAAW,SAAS,OAAO,KAAK,KAAK;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,wBAAwB,gBAAgB,WAAW,UAAU,EAAE,MAAM,SAAU,MAAM;AACvF,aAAO,CAAC,KAAK;AAAA,IACf,CAAC;AAED,QAAI,iBAAiB,cAAc;AAAA,MACjC,WAAWA;AAAA,MACX,MAAM8B;AAAA,MACN,YAAY,WAAW;AAAA,MACvB;AAAA,IACF,CAAC,GACG,SAAS,eAAe,QACxB,gBAAgB,eAAe;AAEnC,QAAI,SAAS;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,uBAAuB;AACzC,QAAI,MAAM,UAAU,gBAAgB,MAAM,UAAU,gBAAgB;AAClE,aAAO;AAAA,IACT;AAEA,MAAE,MAAM,UAAU,cAAc,OAAwC1D,WAAU,OAAO,wCAAwC,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AAEnK,QAAI,UAAU,SAAS;AAAA,MACrB,OAAO;AAAA,IACT,GAAG,OAAO;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,0BAA0B;AAC5C,MAAE,MAAM,UAAU,gBAAgB,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,gBAAgB,OAAO,OAAO,wBAAwB,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AACrN,WAAO,8BAA8B;AAAA,MACnC;AAAA,MACA,WAAW,OAAO;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,QAAQ;AAC1B,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AAEA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,OAAO,OAAO,6BAA6B,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AAClK,QAAI,mBAAmB,OAAO,QAAQ;AAEtC,QAAIe,SAAQ,kBAAkB,MAAM,QAAQ,OAAO,SAAS,GAAG;AAC7D,aAAO;AAAA,IACT;AAEA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA,iBAAiB;AAAA,MACjB,QAAQ,WAAW,KAAK,IAAI,MAAM,SAAS;AAAA,IAC7C,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,2BAA2B;AAC7C,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO,wBAAwB,KAAK;AAAA,IACtC;AAEA,QAAI,MAAM,UAAU,cAAc;AAChC,aAAO,wBAAwB,KAAK;AAAA,IACtC;AAEA,KAAC,kBAAkB,KAAK,IAAI,OAAwCf,WAAU,OAAO,OAAO,OAAO,6BAA6B,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AAClK,QAAI,mBAAmB,OAAO,SAC1B,KAAK,iBAAiB,IACtB,YAAY,iBAAiB;AACjC,QAAI,SAAS,MAAM,WAAW,WAAW,EAAE;AAE3C,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,gBAAgB,QAAQ,SAAS;AAChD,WAAO,oBAAoB,OAAO,UAAU,KAAK;AAAA,EACnD;AAEA,MAAI,OAAO,SAAS,+BAA+B;AACjD,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AAEA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,gDAAgD,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AACvK,QAAI,mBAAmB,OAAO,SAC1B,MAAM,iBAAiB,IACvB,YAAY,iBAAiB;AACjC,QAAI,UAAU,MAAM,WAAW,WAAW,GAAG;AAC7C,KAAC,UAAU,OAAwCA,WAAU,OAAO,+BAA+B,MAAM,+BAA+B,IAAIA,WAAU,KAAK,IAAI;AAC/J,MAAE,QAAQ,cAAc,aAAa,OAAwCA,WAAU,OAAO,0CAA0C,OAAO,SAAS,IAAI,+BAA+B,OAAO,QAAQ,SAAS,CAAC,IAAIA,WAAU,KAAK,IAAI;AAE3O,QAAI,UAAU,SAAS,CAAC,GAAG,SAAS;AAAA,MAClC;AAAA,IACF,CAAC;AAED,WAAO,oBAAoB,OAAO,SAAS,IAAI;AAAA,EACjD;AAEA,MAAI,OAAO,SAAS,uCAAuC;AACzD,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AAEA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,gDAAgD,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AACvK,QAAI,mBAAmB,OAAO,SAC1B,OAAO,iBAAiB,IACxB,mBAAmB,iBAAiB;AACxC,QAAI,WAAW,MAAM,WAAW,WAAW,IAAI;AAC/C,KAAC,WAAW,OAAwCA,WAAU,OAAO,+BAA+B,OAAO,wCAAwC,IAAIA,WAAU,KAAK,IAAI;AAC1K,MAAE,SAAS,qBAAqB,oBAAoB,OAAwCA,WAAU,OAAO,iDAAiD,OAAO,gBAAgB,IAAI,+BAA+B,OAAO,SAAS,gBAAgB,CAAC,IAAIA,WAAU,KAAK,IAAI;AAEhR,QAAI,WAAW,SAAS,CAAC,GAAG,UAAU;AAAA,MACpC;AAAA,IACF,CAAC;AAED,WAAO,oBAAoB,OAAO,UAAU,IAAI;AAAA,EAClD;AAEA,MAAI,OAAO,SAAS,yBAAyB;AAC3C,QAAI,MAAM,UAAU,kBAAkB,MAAM,UAAU,kBAAkB;AACtE,aAAO;AAAA,IACT;AAEA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,oCAAoC,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AAC3J,KAAC,MAAM,wBAAwB,OAAwCA,WAAU,OAAO,6DAA6D,IAAIA,WAAU,KAAK,IAAI;AAC5K,QAAI,aAAa,OAAO,QAAQ;AAEhC,QAAIe,SAAQ,MAAM,SAAS,OAAO,SAAS,UAAU,GAAG;AACtD,aAAO,wBAAwB,KAAK;AAAA,IACtC;AAEA,QAAI,YAAY,eAAe,MAAM,UAAU,UAAU;AAEzD,QAAI,WAAW,KAAK,GAAG;AACrB,aAAO,YAAY;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,8BAA8B;AAChD,QAAI,CAAC,kBAAkB,KAAK,GAAG;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,OAAO,QAAQ;AAE/B,QAAIA,SAAQ,WAAW,MAAM,SAAS,OAAO,GAAG,GAAG;AACjD,aAAO;AAAA,IACT;AAEA,QAAIuC,iBAAgB,SAAS,CAAC,GAAG,MAAM,UAAU;AAAA,MAC/C,QAAQ,SAAS,CAAC,GAAG,MAAM,SAAS,QAAQ;AAAA,QAC1C,KAAK;AAAA,MACP,CAAC;AAAA,IACH,CAAC;AAED,WAAO,SAAS;AAAA,MACd,OAAO;AAAA,IACT,GAAG,OAAO;AAAA,MACR,UAAUA;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,aAAa,OAAO,SAAS,eAAe,OAAO,SAAS,eAAe,OAAO,SAAS,cAAc;AAC3H,QAAI,MAAM,UAAU,gBAAgB,MAAM,UAAU,gBAAgB;AAClE,aAAO;AAAA,IACT;AAEA,MAAE,MAAM,UAAU,cAAc,OAAwCtD,WAAU,OAAO,OAAO,OAAO,uCAAuC,IAAIA,WAAU,KAAK,IAAI;AAErK,QAAI,WAAW,gBAAgB;AAAA,MAC7B;AAAA,MACA,MAAM,OAAO;AAAA,IACf,CAAC;AAED,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA,QAAQ,SAAS;AAAA,MACjB,iBAAiB,SAAS;AAAA,MAC1B,mBAAmB,SAAS;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,gBAAgB;AAClC,QAAI,SAAS,OAAO,QAAQ;AAC5B,MAAE,MAAM,UAAU,gBAAgB,OAAwCA,WAAU,OAAO,qEAAqE,IAAIA,WAAU,KAAK,IAAI;AAEvL,QAAI,WAAW,SAAS;AAAA,MACtB,OAAO;AAAA,IACT,GAAG,OAAO;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,gBAAgB;AAClC,QAAI,mBAAmB,OAAO,SAC1B,YAAY,iBAAiB,WAC7B,eAAe,iBAAiB,cAChC,sBAAsB,iBAAiB;AAC3C,MAAE,MAAM,UAAU,cAAc,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,oCAAoC,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AACjM,QAAI,WAAW;AAAA,MACb,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,iBAAiB;AACnC,QAAI,aAAa,OAAO,QAAQ;AAChC,WAAO;AAAA,MACL,OAAO;AAAA,MACP,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,uBAAuB,SAASiE,sBAAqB,MAAM;AAC7D,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,OAAO,SAASC,MAAK,MAAM;AAC7B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,iBAAiB,SAASC,gBAAe,MAAM;AACjD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,uBAAuB,SAASC,sBAAqB,MAAM;AAC7D,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,qBAAqB,SAASC,sBAAqB;AACrD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,wBAAwB,SAASC,uBAAsB,MAAM;AAC/D,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,2BAA2B,SAASC,0BAAyB,MAAM;AACrE,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,kCAAkC,SAASC,iCAAgC,MAAM;AACnF,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,OAAO,SAASC,MAAK,MAAM;AAC7B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,qBAAqB,SAASC,oBAAmB,MAAM;AACzD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,0BAA0B,SAASC,yBAAwB,MAAM;AACnE,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,SAAS,SAASC,UAAS;AAC7B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,WAAW,SAASC,YAAW;AACjC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,YAAY,SAASC,aAAY;AACnC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,WAAW,SAASC,YAAW;AACjC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,QAAQ,SAASC,SAAQ;AAC3B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,cAAc,SAASC,aAAY,MAAM;AAC3C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,eAAe,SAASC,cAAa,MAAM;AAC7C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,OAAO,SAASC,MAAK,MAAM;AAC7B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,cAAc,SAASrB,aAAY,MAAM;AAC3C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AACA,IAAI,wBAAwB,SAASsB,yBAAwB;AAC3D,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AAEA,SAAS,aAAa,mBAAmB;AACvC,MAAI,kBAAkB,UAAU,GAAG;AACjC;AAAA,EACF;AAEA,MAAI,UAAU,kBAAkB,IAAI,SAAU,GAAG;AAC/C,WAAO,EAAE,WAAW;AAAA,EACtB,CAAC;AACD,MAAI,SAAS,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,UAAU,QAAQ,CAAC;AACvB,QAAI,WAAW,QAAQ,IAAI,CAAC;AAE5B,QAAI,YAAY,WAAW,GAAG;AAC5B,aAAO,OAAO,IAAI;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,CAAC,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC/B;AAAA,EACF;AAEA,MAAI,YAAY,QAAQ,IAAI,SAAU,OAAO;AAC3C,QAAI,WAAW,QAAQ,OAAO,KAAK,CAAC;AACpC,WAAO,WAAW,QAAkB,QAAQ,MAAM,KAAK;AAAA,EACzD,CAAC,EAAE,KAAK,IAAI;AACZ,SAAwCvF,SAAQ,0GAA0G,YAAY,MAAM,IAAI;AAClL;AAEA,SAAS,mBAAmB,UAAU,YAAY;AAChD,MAAI,MAAuC;AACzC,QAAI,oBAAoB,6BAA6B,SAAS,UAAU,IAAI,WAAW,UAAU;AACjG,iBAAa,iBAAiB;AAAA,EAChC;AACF;AAEA,IAAI,SAAU,SAAU,SAAS;AAC/B,SAAO,SAAU,MAAM;AACrB,QAAI,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,OAAO,SAAS,QAAQ;AAC1B,eAAK,MAAM;AACX;AAAA,QACF;AAEA,YAAI,kBAAkB,OAAO,SACzB,KAAK,gBAAgB,IACrB,kBAAkB,gBAAgB,iBAClC,eAAe,gBAAgB;AACnC,YAAI,UAAU,SAAS;AAEvB,YAAI,QAAQ,UAAU,kBAAkB;AACtC,mBAAS,aAAa;AAAA,YACpB,WAAW,QAAQ;AAAA,UACrB,CAAC,CAAC;AAAA,QACJ;AAEA,UAAE,SAAS,EAAE,UAAU,UAAU,OAAwCG,WAAU,OAAO,kCAAkC,IAAIA,WAAU,KAAK,IAAI;AACnJ,iBAAS,MAAM,CAAC;AAChB,iBAAS,qBAAqB;AAAA,UAC5B,aAAa;AAAA,UACb;AAAA,QACF,CAAC,CAAC;AACF,YAAI,gBAAgB;AAAA,UAClB,0BAA0B,iBAAiB;AAAA,QAC7C;AACA,YAAI,UAAU;AAAA,UACZ,aAAa;AAAA,UACb;AAAA,QACF;AAEA,YAAI,wBAAwB,QAAQ,gBAAgB,OAAO,GACvD,WAAW,sBAAsB,UACjC,aAAa,sBAAsB,YACnC,WAAW,sBAAsB;AAErC,2BAAmB,UAAU,UAAU;AACvC,iBAAS,eAAe;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,QAAS,SAAU,SAAS;AAC9B,SAAO,WAAY;AACjB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,OAAO,SAAS,mBAAmB;AACrC,kBAAQ,SAAS;AAAA,QACnB;AAEA,YAAI,OAAO,SAAS,gBAAgB;AAClC,kBAAQ,SAAS,OAAO,QAAQ,UAAU,OAAO,MAAM;AAAA,QACzD;AAEA,YAAI,OAAO,SAAS,WAAW,OAAO,SAAS,iBAAiB;AAC9D,kBAAQ,QAAQ;AAAA,QAClB;AAEA,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,UAAU;AAAA,EACZ,SAAS;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AACF;AACA,IAAI,UAAU;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf;AACA,IAAI,oBAAoB,QAAQ,cAAc,OAAO,OAAO;AAC5D,IAAI,cAAc;AAAA,EAChB,OAAO,aAAa;AAAA,EACpB,MAAM,eAAe,oBAAoB,eAAe;AAAA,EACxD,MAAM,SAASmF,MAAK,UAAU;AAC5B,QAAI,SAAS,WAAW,OAAO,OAAO;AACtC,WAAO,eAAe,SAAS,eAAe;AAAA,EAChD;AAAA,EACA,aAAa,eAAe;AAAA,EAC5B,aAAa,YAAY,oBAAoB,aAAa,oBAAoB,cAAc;AAC9F;AAEA,IAAI,SAAS,SAASE,QAAOrC,SAAQ;AACnC,SAAOjC,SAAQiC,SAAQ,MAAM,IAAI,OAAO,eAAeA,QAAO,IAAI,SAASA,QAAO,IAAI;AACxF;AAEA,IAAI,aAAa;AAAA,EACf;AAAA,EACA,MAAM,SAASmC,MAAKnC,SAAQ,aAAa;AACvC,QAAI,YAAY,OAAOA,OAAM;AAE7B,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,YAAY,QAAQ,MAAM,OAAO;AAAA,EACtD;AACF;AAEA,IAAI,cAAc,QAAQ;AAA1B,IACI,cAAc,QAAQ;AAC1B,IAAI,gBAAgB,cAAc;AAClC,IAAI,wBAAwB;AAC5B,IAAI,qBAAqB;AACzB,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAI,UAAU,KAAK,SACf,cAAc,KAAK,aACnB,SAAS,KAAK;AAClB,MAAI,aAAa,SAAS,SAAS,WAAW;AAE9C,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,uBAAuB;AACvC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,aAAa;AAC9B,MAAI,WAAW,cAAc,gBAAgB;AAC7C,MAAI,eAAe,WAAW,WAAW,WAAW,qBAAqB;AACzE,SAAO,OAAO,aAAa,QAAQ,CAAC,CAAC;AACvC;AAEA,IAAI,yBAA0B,SAAU,MAAM;AAC5C,MAAI,SAAS,KAAK,QACdpB,aAAY,KAAK,WACjB,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,gBAAgB,KAAK;AACzB,MAAI,aAAa,WAAW,YACxB,aAAa,WAAW;AAC5B,MAAI,cAAc,kBAAkB,MAAM;AAC1C,MAAI,cAAc,cAAc,WAAW,WAAW,IAAI;AAC1D,MAAI8B,QAAO,WAAW9B,WAAU,WAAW,WAAW;AACtD,MAAI,kBAAkB,yBAAyB;AAAA,IAC7C;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA,WAAW,eAAe8B;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,MAAIV,UAAS,SAAS,iBAAiBpB,WAAU,OAAO,UAAU,MAAM;AACxE,SAAOoB;AACT;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,aAAa,KAAK,YAClB,SAAS,KAAK,QACd,aAAa,KAAK,YAClBU,QAAO,KAAK,MACZ,WAAW,KAAK,UAChB,eAAe,KAAK;AAExB,MAAI,CAAC,WAAW,MAAM,WAAW,QAAQ;AACvC,QAAI,uBAAuB,UAAU;AAAA,MACnC;AAAA,MACA,QAAQ;AAAA,MACR,aAAaA;AAAA,MACb;AAAA,MACA,oBAAoB;AAAA,IACtB,CAAC;AACD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,wBAAwB;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,WAAW,GAAG,SAAS,WAAW;AACpC,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,wBAAwB;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,kBAAkB,SAAS,CAAC,GAAG,YAAY;AAAA,IAC7C,WAAW;AAAA,EACb,CAAC;AAED,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,wBAAwB;AAAA,EAC1B;AACF;AAEA,IAAI,SAAU,SAAU,MAAM;AAC5B,MAAI,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,SAAO,SAAU,MAAM;AACrB,WAAO,SAAU,QAAQ;AACvB,UAAI,OAAO,SAAS,QAAQ;AAC1B,aAAK,MAAM;AACX;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS;AACrB,UAAI,SAAS,OAAO,QAAQ;AAE5B,UAAI,MAAM,UAAU,cAAc;AAChC,iBAAS,YAAY;AAAA,UACnB;AAAA,QACF,CAAC,CAAC;AACF;AAAA,MACF;AAEA,UAAI,MAAM,UAAU,QAAQ;AAC1B;AAAA,MACF;AAEA,UAAI,mBAAmB,MAAM,UAAU,kBAAkB,MAAM;AAC/D,OAAC,CAAC,mBAAmB,OAAwC1D,WAAU,OAAO,6DAA6D,IAAIA,WAAU,KAAK,IAAI;AAClK,QAAE,MAAM,UAAU,cAAc,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,2BAA2B,MAAM,KAAK,IAAIA,WAAU,KAAK,IAAI;AACxL,UAAI,WAAW,MAAM;AACrB,UAAI,aAAa,MAAM;AACvB,UAAI4B,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAEjE,UAAI,iBAAiB,cAAc;AAAA,QACjC;AAAA,QACA,YAAY,MAAM;AAAA,QAClB,eAAe,MAAM;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,MAAM,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAAA,QAC7D,UAAU,MAAM;AAAA,QAChB,YAAY,MAAM,WAAW;AAAA,MAC/B,CAAC,GACG,SAAS,eAAe,QACxB,yBAAyB,eAAe;AAE5C,UAAI,cAAc,yBAAyB,kBAAkB,MAAM,IAAI;AACvE,UAAIpB,WAAU,yBAAyB,cAAc,MAAM,IAAI;AAC/D,UAAI,SAAS;AAAA,QACX,OAAO,SAAS,UAAU;AAAA,QAC1B,aAAa,SAAS,UAAU;AAAA,MAClC;AACA,UAAI,SAAS;AAAA,QACX,aAAaoB,WAAU,WAAW;AAAA,QAClC,MAAMA,WAAU,WAAW;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,MAAM,MAAM;AAAA,QACZ;AAAA,QACA,SAASpB;AAAA,MACX;AACA,UAAI,sBAAsB,uBAAuB;AAAA,QAC/C;AAAA,QACA,WAAWoB;AAAA,QACX;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,eAAe,MAAM;AAAA,MACvB,CAAC;AACD,UAAI,YAAY;AAAA,QACd,UAAU,MAAM;AAAA,QAChB,eAAe,MAAM;AAAA,QACrB;AAAA,QACA;AAAA,MACF;AACA,UAAI,sBAAsB,CAACb,SAAQ,MAAM,QAAQ,OAAO,QAAQ,mBAAmB,KAAK,QAAQ,OAAO,OAAO;AAE9G,UAAI,CAAC,qBAAqB;AACxB,iBAAS,aAAa;AAAA,UACpB;AAAA,QACF,CAAC,CAAC;AACF;AAAA,MACF;AAEA,UAAI,eAAe,gBAAgB;AAAA,QACjC,SAAS,MAAM,QAAQ,OAAO;AAAA,QAC9B,aAAa;AAAA,QACb;AAAA,MACF,CAAC;AACD,UAAI,OAAO;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,eAAS,YAAY,IAAI,CAAC;AAAA,IAC5B;AAAA,EACF;AACF;AAEA,IAAIuE,mBAAmB,WAAY;AACjC,SAAO;AAAA,IACL,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF;AAEA,SAAS,uBAAuB5E,SAAQ;AACtC,SAAO;AAAA,IACL,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,MAAM,WAAW,UAAU,MAAM,WAAW,OAAO,UAAU;AAC/D;AAAA,MACF;AAEA,MAAAA,QAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,iBAAiB,KAAK;AAE1B,WAAS,eAAe;AACtB,mBAAe4E,iBAAgB,CAAC;AAAA,EAClC;AAEA,MAAI,YAAY,qBAAQ,YAAY;AACpC,MAAI,UAAU,uBAAuB,SAAS;AAC9C,MAAI,SAAS;AAEb,WAAS,WAAW;AAClB,WAAO,WAAW;AAAA,EACpB;AAEA,WAASjF,SAAQ;AACf,KAAC,CAAC,SAAS,IAAI,OAAwCL,WAAU,OAAO,kDAAkD,IAAIA,WAAU,KAAK,IAAI;AACjJ,aAAS,WAAW,QAAQ,CAAC,OAAO,CAAC;AAAA,EACvC;AAEA,WAAS,OAAO;AACd,KAAC,SAAS,IAAI,OAAwCA,WAAU,OAAO,6CAA6C,IAAIA,WAAU,KAAK,IAAI;AAC3I,cAAU,OAAO;AACjB,WAAO;AACP,aAAS;AAAA,EACX;AAEA,SAAO;AAAA,IACL,OAAOK;AAAA,IACP;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,YAAY,SAASkF,WAAU,QAAQ;AACzC,SAAO,OAAO,SAAS,mBAAmB,OAAO,SAAS,kBAAkB,OAAO,SAAS;AAC9F;AAEA,IAAI,iBAAkB,SAAU,OAAO;AACrC,MAAI,WAAW,kBAAkB;AAAA,IAC/B,gBAAgB,SAAS,eAAe,WAAW;AACjD,YAAM,SAAS,mBAAmB;AAAA,QAChC;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,CAAC;AACD,SAAO,SAAU,MAAM;AACrB,WAAO,SAAU,QAAQ;AACvB,UAAI,CAAC,SAAS,SAAS,KAAK,OAAO,SAAS,mBAAmB;AAC7D,iBAAS,MAAM;AAAA,MACjB;AAEA,UAAI,SAAS,SAAS,KAAK,UAAU,MAAM,GAAG;AAC5C,iBAAS,KAAK;AAAA,MAChB;AAEA,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACF;AAEA,IAAI,sBAAuB,SAAU,UAAU;AAC7C,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,YAAY,WAAW,WAAY;AACrC,gBAAY;AAAA,EACd,CAAC;AAED,MAAI,SAAS,SAASC,QAAO,SAAS;AACpC,QAAI,WAAW;AACb,aAAwC3F,SAAQ,6DAA6D,IAAI;AACjH;AAAA,IACF;AAEA,QAAI,WAAW;AACb,aAAwCA,SAAQ,qHAAqH,IAAI;AACzK;AAAA,IACF;AAEA,gBAAY;AACZ,aAAS,OAAO;AAChB,iBAAa,SAAS;AAAA,EACxB;AAEA,SAAO,YAAY,WAAY;AAC7B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,kBAAmB,WAAY;AACjC,MAAI,UAAU,CAAC;AAEf,MAAI4F,WAAU,SAASA,SAAQ,SAAS;AACtC,QAAI,QAAQ,UAAU,SAAS,SAAU,MAAM;AAC7C,aAAO,KAAK,YAAY;AAAA,IAC1B,CAAC;AACD,MAAE,UAAU,MAAM,OAAwCzF,WAAU,OAAO,sBAAsB,IAAIA,WAAU,KAAK,IAAI;AAExH,QAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC,GACzC,QAAQ,gBAAgB,CAAC;AAE7B,UAAM,SAAS;AAAA,EACjB;AAEA,MAAIa,OAAM,SAASA,KAAI,IAAI;AACzB,QAAI,UAAU,WAAW,WAAY;AACnC,aAAO4E,SAAQ,OAAO;AAAA,IACxB,CAAC;AACD,QAAI,QAAQ;AAAA,MACV;AAAA,MACA,UAAU;AAAA,IACZ;AACA,YAAQ,KAAK,KAAK;AAAA,EACpB;AAEA,MAAIT,SAAQ,SAASA,SAAQ;AAC3B,QAAI,CAAC,QAAQ,QAAQ;AACnB;AAAA,IACF;AAEA,QAAI,UAAU,CAAC,EAAE,OAAO,OAAO;AAC/B,YAAQ,SAAS;AACjB,YAAQ,QAAQ,SAAU,OAAO;AAC/B,mBAAa,MAAM,OAAO;AAC1B,YAAM,SAAS;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL,KAAKnE;AAAA,IACL,OAAOmE;AAAA,EACT;AACF;AAEA,IAAI,oBAAoB,SAASU,mBAAkB,OAAO,QAAQ;AAChE,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,gBAAgB,OAAO,eAAe,MAAM,UAAU,OAAO;AAC5E;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,QAAQ;AAC1D,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,gBAAgB,OAAO,eAAe,MAAM,gBAAgB,OAAO;AAClF;AACA,IAAI,kBAAkB,SAASC,iBAAgB,OAAO,QAAQ;AAC5D,MAAI,UAAU,QAAQ;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,MAAM,UAAU,gBAAgB,OAAO,UAAU,eAAe,MAAM,UAAU,SAAS,OAAO,UAAU,QAAQ,MAAM,UAAU,UAAU,OAAO,UAAU;AAClO,MAAI,mBAAmB,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,MAAM,UAAU,SAAS,OAAO,UAAU;AAC/G,SAAO,oBAAoB;AAC7B;AAEA,IAAI,cAAc,SAASC,aAAY,KAAK,IAAI;AAC9C,QAAM;AACN,KAAG;AACH,SAAO;AACT;AAEA,IAAI,eAAe,SAASC,cAAa,UAAU,MAAM;AACvD,SAAO;AAAA,IACL,aAAa,SAAS,UAAU;AAAA,IAChC,MAAM,SAAS,UAAU;AAAA,IACzB,QAAQ;AAAA,MACN,aAAa,SAAS,UAAU;AAAA,MAChC,OAAO,SAAS,UAAU;AAAA,IAC5B;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,UAAU,SAASL,SAAQ,WAAW,MAAM,UAAU,mBAAmB;AAC3E,MAAI,CAAC,WAAW;AACd,aAAS,kBAAkB,IAAI,CAAC;AAChC;AAAA,EACF;AAEA,MAAI,aAAa,oBAAoB,QAAQ;AAC7C,MAAI,WAAW;AAAA,IACb,UAAU;AAAA,EACZ;AACA,YAAU,MAAM,QAAQ;AAExB,MAAI,CAAC,WAAW,UAAU,GAAG;AAC3B,aAAS,kBAAkB,IAAI,CAAC;AAAA,EAClC;AACF;AAEA,IAAI,eAAgB,SAAU,eAAe,UAAU;AACrD,MAAI,eAAe,gBAAgB;AACnC,MAAI,WAAW;AAEf,MAAI,gBAAgB,SAASM,eAAc,aAAa,MAAM;AAC5D,KAAC,CAAC,WAAW,OAAwC/F,WAAU,OAAO,wEAAwE,IAAIA,WAAU,KAAK,IAAI;AACrK,gBAAY,mBAAmB,WAAY;AACzC,UAAI,KAAK,cAAc,EAAE;AAEzB,UAAI,IAAI;AACN,YAAI,SAAS;AAAA,UACX;AAAA,UACA;AAAA,QACF;AACA,WAAG,MAAM;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,cAAc,SAASgG,aAAY,UAAU,MAAM;AACrD,KAAC,CAAC,WAAW,OAAwChG,WAAU,OAAO,0EAA0E,IAAIA,WAAU,KAAK,IAAI;AACvK,gBAAY,qBAAqB,WAAY;AAC3C,UAAI,KAAK,cAAc,EAAE;AAEzB,UAAI,IAAI;AACN,WAAG,aAAa,UAAU,IAAI,CAAC;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIK,SAAQ,SAASA,OAAM,UAAU,MAAM;AACzC,KAAC,CAAC,WAAW,OAAwCL,WAAU,OAAO,0EAA0E,IAAIA,WAAU,KAAK,IAAI;AACvK,QAAI,OAAO,aAAa,UAAU,IAAI;AACtC,eAAW;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,cAAc,KAAK;AAAA,MACnB,aAAa;AAAA,IACf;AACA,iBAAa,IAAI,WAAY;AAC3B,kBAAY,eAAe,WAAY;AACrC,eAAO,QAAQ,cAAc,EAAE,aAAa,MAAM,UAAU,OAAO,WAAW;AAAA,MAChF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,MAAIU,UAAS,SAASA,QAAO,UAAU,QAAQ;AAC7C,QAAI,WAAW,kBAAkB,MAAM;AACvC,QAAIF,WAAU,cAAc,MAAM;AAClC,KAAC,WAAW,OAAwCR,WAAU,OAAO,6DAA6D,IAAIA,WAAU,KAAK,IAAI;AACzJ,QAAI,qBAAqB,CAAC,gBAAgB,UAAU,SAAS,YAAY;AAEzE,QAAI,oBAAoB;AACtB,eAAS,eAAe;AAAA,IAC1B;AAEA,QAAI,qBAAqB,CAAC,kBAAkB,SAAS,cAAc,QAAQ;AAE3E,QAAI,oBAAoB;AACtB,eAAS,eAAe;AAAA,IAC1B;AAEA,QAAI,qBAAqB,CAAC,eAAe,SAAS,aAAaQ,QAAO;AAEtE,QAAI,oBAAoB;AACtB,eAAS,cAAcA;AAAA,IACzB;AAEA,QAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,oBAAoB;AACrE;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,CAAC,GAAG,aAAa,UAAU,SAAS,IAAI,GAAG;AAAA,MAC7D,SAASA;AAAA,MACT,aAAa;AAAA,IACf,CAAC;AAED,iBAAa,IAAI,WAAY;AAC3B,kBAAY,gBAAgB,WAAY;AACtC,eAAO,QAAQ,cAAc,EAAE,cAAc,MAAM,UAAU,OAAO,YAAY;AAAA,MAClF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,MAAIwE,SAAQ,SAASA,SAAQ;AAC3B,KAAC,WAAW,OAAwChF,WAAU,OAAO,0CAA0C,IAAIA,WAAU,KAAK,IAAI;AACtI,iBAAa,MAAM;AAAA,EACrB;AAEA,MAAImF,QAAO,SAASA,MAAK,QAAQ;AAC/B,KAAC,WAAW,OAAwCnF,WAAU,OAAO,6DAA6D,IAAIA,WAAU,KAAK,IAAI;AACzJ,eAAW;AACX,gBAAY,aAAa,WAAY;AACnC,aAAO,QAAQ,cAAc,EAAE,WAAW,QAAQ,UAAU,OAAO,SAAS;AAAA,IAC9E,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ,SAASiG,SAAQ;AAC3B,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AAEA,QAAI,SAAS,SAAS,CAAC,GAAG,aAAa,SAAS,cAAc,SAAS,IAAI,GAAG;AAAA,MAC5E,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV,CAAC;AAED,IAAAd,MAAK,MAAM;AAAA,EACb;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO9E;AAAA,IACP,QAAQK;AAAA,IACR,OAAOsE;AAAA,IACP,MAAMG;AAAA,IACN;AAAA,EACF;AACF;AAEA,IAAI,aAAc,SAAU,eAAe,UAAU;AACnD,MAAI,YAAY,aAAa,eAAe,QAAQ;AACpD,SAAO,SAAU,OAAO;AACtB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,OAAO,SAAS,0BAA0B;AAC5C,oBAAU,cAAc,OAAO,QAAQ,aAAa,OAAO,QAAQ,YAAY;AAC/E;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,mBAAmB;AACrC,cAAI,WAAW,OAAO,QAAQ;AAC9B,oBAAU,YAAY,UAAU,OAAO,QAAQ,YAAY;AAC3D,eAAK,MAAM;AACX,oBAAU,MAAM,UAAU,OAAO,QAAQ,YAAY;AACrD;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,iBAAiB;AACnC,cAAI,SAAS,OAAO,QAAQ,UAAU;AACtC,oBAAU,MAAM;AAChB,eAAK,MAAM;AACX,oBAAU,KAAK,MAAM;AACrB;AAAA,QACF;AAEA,aAAK,MAAM;AAEX,YAAI,OAAO,SAAS,SAAS;AAC3B,oBAAU,MAAM;AAChB;AAAA,QACF;AAEA,YAAI,QAAQ,MAAM,SAAS;AAE3B,YAAI,MAAM,UAAU,YAAY;AAC9B,oBAAU,OAAO,MAAM,UAAU,MAAM,MAAM;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,sBAAuB,SAAU,OAAO;AAC1C,SAAO,SAAU,MAAM;AACrB,WAAO,SAAU,QAAQ;AACvB,UAAI,OAAO,SAAS,2BAA2B;AAC7C,aAAK,MAAM;AACX;AAAA,MACF;AAEA,UAAI,QAAQ,MAAM,SAAS;AAC3B,QAAE,MAAM,UAAU,oBAAoB,OAAwCnF,WAAU,OAAO,0DAA0D,IAAIA,WAAU,KAAK,IAAI;AAChL,YAAM,SAAS,aAAa;AAAA,QAC1B,WAAW,MAAM;AAAA,MACnB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF;AAEA,IAAI,6BAA8B,SAAU,OAAO;AACjD,MAAI,SAAS;AACb,MAAI,UAAU;AAEd,WAAS,QAAQ;AACf,QAAI,SAAS;AACX,2BAAqB,OAAO;AAC5B,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AACV,aAAO;AACP,eAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO,SAAU,MAAM;AACrB,WAAO,SAAU,QAAQ;AACvB,UAAI,OAAO,SAAS,WAAW,OAAO,SAAS,mBAAmB,OAAO,SAAS,2BAA2B;AAC3G,cAAM;AAAA,MACR;AAEA,WAAK,MAAM;AAEX,UAAI,OAAO,SAAS,gBAAgB;AAClC;AAAA,MACF;AAEA,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,UACP,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,QACR;AAAA,QACA,IAAI,SAAS,qBAAqB;AAChC,cAAI,QAAQ,MAAM,SAAS;AAE3B,cAAI,MAAM,UAAU,kBAAkB;AACpC,kBAAM,SAAS,sBAAsB,CAAC;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AACA,gBAAU,sBAAsB,WAAY;AAC1C,kBAAU;AACV,iBAAS,WAAW,QAAQ,CAAC,OAAO,CAAC;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAI,0BAA2B,SAAU,SAAS;AAChD,SAAO,WAAY;AACjB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,OAAO,SAAS,mBAAmB,OAAO,SAAS,WAAW,OAAO,SAAS,gBAAgB;AAChG,kBAAQ,eAAe;AAAA,QACzB;AAEA,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,QAAS,SAAU,SAAS;AAC9B,MAAI,aAAa;AACjB,SAAO,WAAY;AACjB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,OAAO,SAAS,mBAAmB;AACrC,uBAAa;AACb,kBAAQ,eAAe,OAAO,QAAQ,SAAS,UAAU,EAAE;AAC3D,eAAK,MAAM;AACX,kBAAQ,wBAAwB;AAChC;AAAA,QACF;AAEA,aAAK,MAAM;AAEX,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,SAAS;AAC3B,uBAAa;AACb,kBAAQ,wBAAwB;AAChC;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,iBAAiB;AACnC,uBAAa;AACb,cAAI,SAAS,OAAO,QAAQ,UAAU;AAEtC,cAAI,OAAO,SAAS;AAClB,oBAAQ,eAAe,OAAO,aAAa,OAAO,QAAQ,WAAW;AAAA,UACvE;AAEA,kBAAQ,wBAAwB;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa,SAASkG,YAAW,QAAQ;AAC3C,SAAO,OAAO,SAAS,mBAAmB,OAAO,SAAS,kBAAkB,OAAO,SAAS;AAC9F;AAEA,IAAI,aAAc,SAAU,cAAc;AACxC,SAAO,SAAU,OAAO;AACtB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AACvB,YAAI,WAAW,MAAM,GAAG;AACtB,uBAAa,KAAK;AAClB,eAAK,MAAM;AACX;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,mBAAmB;AACrC,eAAK,MAAM;AACX,cAAI,QAAQ,MAAM,SAAS;AAC3B,YAAE,MAAM,UAAU,cAAc,OAAwClG,WAAU,OAAO,qDAAqD,IAAIA,WAAU,KAAK,IAAI;AACrK,uBAAa,MAAM,KAAK;AACxB;AAAA,QACF;AAEA,aAAK,MAAM;AACX,qBAAa,OAAO,MAAM,SAAS,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,cAAe,SAAU,OAAO;AAClC,SAAO,SAAU,MAAM;AACrB,WAAO,SAAU,QAAQ;AACvB,WAAK,MAAM;AAEX,UAAI,OAAO,SAAS,0BAA0B;AAC5C;AAAA,MACF;AAEA,UAAI,kBAAkB,MAAM,SAAS;AAErC,UAAI,gBAAgB,UAAU,gBAAgB;AAC5C;AAAA,MACF;AAEA,UAAI,gBAAgB,WAAW;AAC7B;AAAA,MACF;AAEA,YAAM,SAAS,KAAK;AAAA,QAClB,QAAQ,gBAAgB;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF;AAEA,IAAI,mBAA4D,OAAO,WAAW,eAAe,OAAO,uCAAuC,OAAO,qCAAqC;AAAA,EACzL,MAAM;AACR,CAAC,IAAI;AACL,IAAImG,eAAe,SAAU,MAAM;AACjC,MAAI,mBAAmB,KAAK,kBACxB,eAAe,KAAK,cACpB,eAAe,KAAK,cACpB,gBAAgB,KAAK,eACrB,WAAW,KAAK,UAChB,eAAe,KAAK;AACxB,SAAO,YAAc,SAAS,iBAAiB,gBAAgB,MAAM,YAAY,GAAG,wBAAwB,gBAAgB,GAAG,OAAO,gBAAgB,GAAG,QAAQ,qBAAqB,4BAA4B,aAAa,WAAW,YAAY,GAAG,gBAAgB,MAAM,YAAY,GAAG,WAAW,eAAe,QAAQ,CAAC,CAAC,CAAC;AACrU;AAEA,IAAI,UAAU,SAASzG,SAAQ;AAC7B,SAAO;AAAA,IACL,WAAW,CAAC;AAAA,IACZ,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,EACb;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,WAAW,KAAK,UAChB,YAAY,KAAK;AACrB,MAAI,UAAU,QAAQ;AACtB,MAAI,UAAU;AAEd,MAAI,UAAU,SAAS0G,WAAU;AAC/B,QAAI,SAAS;AACX;AAAA,IACF;AAEA,cAAU,mBAAmB;AAC7B,cAAU,sBAAsB,WAAY;AAC1C,gBAAU;AACV,YAAM;AACN,UAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,WAAW,SAAS;AACxB,UAAI,QAAQ,OAAO,KAAK,SAAS,EAAE,IAAI,SAAU,IAAI;AACnD,eAAO,SAAS,UAAU,QAAQ,EAAE,EAAE,aAAa,MAAM;AAAA,MAC3D,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AACtB,eAAO,EAAE,WAAW,QAAQ,EAAE,WAAW;AAAA,MAC3C,CAAC;AACD,UAAI,UAAU,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAU,IAAI;AACpD,YAAI,QAAQ,SAAS,UAAU,QAAQ,EAAE;AACzC,YAAI5E,UAAS,MAAM,UAAU,uBAAuB;AACpD,eAAO;AAAA,UACL,aAAa;AAAA,UACb,QAAQA;AAAA,QACV;AAAA,MACF,CAAC;AACD,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,UAAU,OAAO,KAAK,QAAQ;AAAA,QAC9B,UAAU;AAAA,MACZ;AACA,gBAAU,QAAQ;AAClB,aAAO;AACP,gBAAU,QAAQ,MAAM;AAAA,IAC1B,CAAC;AAAA,EACH;AAEA,MAAIX,OAAM,SAASA,KAAI,OAAO;AAC5B,QAAI,KAAK,MAAM,WAAW;AAC1B,YAAQ,UAAU,EAAE,IAAI;AACxB,YAAQ,SAAS,MAAM,WAAW,WAAW,IAAI;AAEjD,QAAI,QAAQ,SAAS,EAAE,GAAG;AACxB,aAAO,QAAQ,SAAS,EAAE;AAAA,IAC5B;AAEA,YAAQ;AAAA,EACV;AAEA,MAAI,SAAS,SAASwF,QAAO,OAAO;AAClC,QAAI,aAAa,MAAM;AACvB,YAAQ,SAAS,WAAW,EAAE,IAAI;AAClC,YAAQ,SAAS,WAAW,WAAW,IAAI;AAE3C,QAAI,QAAQ,UAAU,WAAW,EAAE,GAAG;AACpC,aAAO,QAAQ,UAAU,WAAW,EAAE;AAAA,IACxC;AAEA,YAAQ;AAAA,EACV;AAEA,MAAI,OAAO,SAASC,QAAO;AACzB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA,yBAAqB,OAAO;AAC5B,cAAU;AACV,cAAU,QAAQ;AAAA,EACpB;AAEA,SAAO;AAAA,IACL,KAAKzF;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,eAAgB,SAAU,MAAM;AAClC,MAAI,eAAe,KAAK,cACpB,cAAc,KAAK,aACnB,SAAS,KAAK,QACd,QAAQ,KAAK;AACjB,MAAI,YAAY,SAAS;AAAA,IACvB,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,MAAI,oBAAoB;AAAA,IACtB,GAAG,KAAK,IAAI,GAAG,UAAU,CAAC;AAAA,IAC1B,GAAG,KAAK,IAAI,GAAG,UAAU,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,IAAI,qBAAsB,WAAY;AACpC,MAAI,MAAM,SAAS;AACnB,GAAC,MAAM,OAAwCb,WAAU,OAAO,sCAAsC,IAAIA,WAAU,KAAK,IAAI;AAC7H,SAAO;AACT;AAEA,IAAI,qBAAsB,WAAY;AACpC,MAAI,MAAM,mBAAmB;AAC7B,MAAI,YAAY,aAAa;AAAA,IAC3B,cAAc,IAAI;AAAA,IAClB,aAAa,IAAI;AAAA,IACjB,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,EACd,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAe,WAAY;AAC7B,MAAIwB,UAAS8D,iBAAgB;AAC7B,MAAI,YAAY,mBAAmB;AACnC,MAAI,MAAM9D,QAAO;AACjB,MAAI,OAAOA,QAAO;AAClB,MAAI,MAAM,mBAAmB;AAC7B,MAAI,QAAQ,IAAI;AAChB,MAAI,SAAS,IAAI;AACjB,MAAI,QAAQ,OAAO;AACnB,MAAI,SAAS,MAAM;AACnB,MAAI,QAAQ,QAAQ;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,SAASA;AAAA,MACT,SAASA;AAAA,MACT,KAAK;AAAA,MACL,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,oBAAqB,SAAU,MAAM;AACvC,MAAI,WAAW,KAAK,UAChB,gBAAgB,KAAK,eACrB,WAAW,KAAK;AACpB,QAAM;AACN,MAAI,WAAW,YAAY;AAC3B,MAAI,eAAe,SAAS,OAAO;AACnC,MAAIkC,QAAO,SAAS;AACpB,MAAI,aAAa,SAAS,UAAU,aAAaA,MAAK,IAAI,EAAE,IAAI,SAAU,OAAO;AAC/E,WAAO,MAAM,UAAU,2BAA2B,cAAc,aAAa;AAAA,EAC/E,CAAC;AACD,MAAI,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU,IAAI,EAAE,IAAI,SAAU,OAAO;AAC7F,WAAO,MAAM,aAAa,YAAY;AAAA,EACxC,CAAC;AACD,MAAI,aAAa;AAAA,IACf,YAAY,eAAe,UAAU;AAAA,IACrC,YAAY,eAAe,UAAU;AAAA,EACvC;AACA,SAAO;AACP,MAAI,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,UAAU,UAAU,OAAO;AACtD,MAAI,MAAM,WAAW,OAAO,SAAS,IAAI;AACvC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,WAAW,SAAS,SAAS,MAAM;AAC3C,WAAO;AAAA,EACT;AAEA,MAAIA,QAAO,SAAS,UAAU,QAAQ,MAAM,WAAW,WAAW;AAElE,MAAIA,MAAK,WAAW,SAAS,WAAW;AACtC,WAAwC7D,SAAQ,kEAAkE,MAAM,WAAW,KAAK,8LAA8L,IAAI;AAC1U,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,yBAA0B,SAAU,UAAU,WAAW;AAC3D,MAAI,aAAa;AACjB,MAAI,YAAY,gBAAgB;AAAA,IAC9B,WAAW;AAAA,MACT,SAAS,UAAU;AAAA,MACnB,oBAAoB,UAAU;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI0E,4BAA2B,SAASA,0BAAyB,IAAI,WAAW;AAC9E,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwCvE,WAAU,OAAO,gDAAgD,KAAK,0BAA0B,IAAIA,WAAU,KAAK,IAAI;AAEhM,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,cAAU,yBAAyB;AAAA,MACjC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIwE,mCAAkC,SAASA,iCAAgC,IAAI,kBAAkB;AACnG,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwCxE,WAAU,OAAO,sDAAsD,KAAK,0BAA0B,IAAIA,WAAU,KAAK,IAAI;AACtM,cAAU,gCAAgC;AAAA,MACxC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIsE,yBAAwB,SAASA,uBAAsB,IAAI,WAAW;AACxE,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwCtE,WAAU,OAAO,2CAA2C,KAAK,0BAA0B,IAAIA,WAAU,KAAK,IAAI;AAC3L,cAAU,sBAAsB;AAAA,MAC9B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIuG,mBAAkB,SAASA,iBAAgB,IAAI,QAAQ;AACzD,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,EAAE,EAAE,UAAU,OAAO,MAAM;AAAA,EACxD;AAEA,MAAI,iBAAiB,SAASC,kBAAiB;AAC7C,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,cAAU,KAAK;AACf,QAAI9C,QAAO,WAAW,SAAS;AAC/B,aAAS,UAAU,aAAaA,MAAK,IAAI,EAAE,QAAQ,SAAU,OAAO;AAClE,aAAO,MAAM,UAAU,YAAY;AAAA,IACrC,CAAC;AACD,eAAW,YAAY;AACvB,iBAAa;AAAA,EACf;AAEA,MAAI,aAAa,SAAS+C,YAAW,OAAO;AAC1C,KAAC,aAAa,OAAwCzG,WAAU,OAAO,0DAA0D,IAAIA,WAAU,KAAK,IAAI;AACxJ,QAAI,WAAW,WAAW,SAAS;AAEnC,QAAI,MAAM,SAAS,YAAY;AAC7B,UAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,kBAAU,IAAI,MAAM,KAAK;AAAA,MAC3B;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,WAAW;AAC5B,UAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,kBAAU,OAAO,MAAM,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAEA,MAAI,kBAAkB,SAAS0G,iBAAgB,SAAS;AACtD,KAAC,CAAC,aAAa,OAAwC1G,WAAU,OAAO,6EAA6E,IAAIA,WAAU,KAAK,IAAI;AAC5K,QAAI,QAAQ,SAAS,UAAU,QAAQ,QAAQ,WAAW;AAC1D,QAAI0D,QAAO,SAAS,UAAU,QAAQ,MAAM,WAAW,WAAW;AAClE,QAAI,WAAW;AAAA,MACb,WAAW,MAAM;AAAA,MACjB,WAAWA,MAAK;AAAA,IAClB;AACA,QAAI,cAAc,SAAS,UAAU,UAAU;AAC/C,iBAAa;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,WAAO,kBAAkB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,eAAe,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AAEA,MAAI,UAAU;AAAA,IACZ,0BAA0Ba;AAAA,IAC1B,iCAAiCC;AAAA,IACjC,iBAAiB+B;AAAA,IACjB,uBAAuBjC;AAAA,IACvB;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,eAAgB,SAAU,OAAO,IAAI;AACvC,MAAI,MAAM,UAAU,QAAQ;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU,kBAAkB;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU,OAAO,gBAAgB,IAAI;AAC7C,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,UAAU,OAAO,WAAW;AAC3C;AAEA,IAAI,eAAgB,SAAU,QAAQ;AACpC,SAAO,SAAS,OAAO,GAAG,OAAO,CAAC;AACpC;AAEA,IAAI,0BAA0B,wBAAW,SAAU,YAAY;AAC7D,SAAO,gBAAgB,UAAU,EAAE,OAAO,SAAU3C,YAAW;AAC7D,QAAI,CAACA,WAAU,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,CAACA,WAAU,OAAO;AACpB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,CAAC;AACH,CAAC;AAED,IAAI,6BAA6B,SAASgF,4BAA2B,QAAQ,YAAY;AACvF,MAAI,QAAQ,KAAK,wBAAwB,UAAU,GAAG,SAAUhF,YAAW;AACzE,KAACA,WAAU,QAAQ,OAAwC3B,WAAU,OAAO,gBAAgB,IAAIA,WAAU,KAAK,IAAI;AACnH,WAAO,kBAAkB2B,WAAU,MAAM,aAAa,EAAE,MAAM;AAAA,EAChE,CAAC;AACD,SAAO;AACT;AAEA,IAAI,6BAA8B,SAAU,MAAM;AAChD,MAAI,SAAS,KAAK,QACd,cAAc,KAAK,aACnB,aAAa,KAAK;AAEtB,MAAI,aAAa;AACf,QAAI,aAAa,WAAW,WAAW;AAEvC,QAAI,CAAC,WAAW,OAAO;AACrB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,2BAA2B,QAAQ,UAAU;AAC7D,SAAO;AACT;AAEA,IAAI,SAAS;AAAA,EACX,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,MAAM,SAAS,KAAK,YAAY;AAC9B,WAAO,KAAK,IAAI,YAAY,CAAC;AAAA,EAC/B;AAAA,EACA,mBAAmB;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc;AAAA,EAChB;AACF;AAEA,IAAI,wBAAyB,SAAU,WAAW,MAAM;AACtD,MAAI,qBAAqB,UAAU,KAAK,IAAI,IAAI,OAAO;AACvD,MAAI,mBAAmB,UAAU,KAAK,IAAI,IAAI,OAAO;AACrD,MAAI,aAAa;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,eAAe,KAAK,cACpB,aAAa,KAAK,YAClB,UAAU,KAAK;AACnB,MAAI,QAAQ,aAAa;AAEzB,MAAI,UAAU,GAAG;AACf,WAAwC9B,SAAQ,yKAAyK,IAAI;AAC7N,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,UAAU;AAC/B,MAAI,aAAa,iBAAiB;AAClC,SAAO;AACT;AAEA,IAAI,YAAY;AAEhB,IAAI,uBAAwB,SAAU,gBAAgB,YAAY;AAChE,MAAI,iBAAiB,WAAW,oBAAoB;AAClD,WAAO;AAAA,EACT;AAEA,MAAI,kBAAkB,WAAW,kBAAkB;AACjD,WAAO,OAAO;AAAA,EAChB;AAEA,MAAI,mBAAmB,WAAW,oBAAoB;AACpD,WAAO;AAAA,EACT;AAEA,MAAI,iCAAiC,cAAc;AAAA,IACjD,cAAc,WAAW;AAAA,IACzB,YAAY,WAAW;AAAA,IACvB,SAAS;AAAA,EACX,CAAC;AACD,MAAI,mCAAmC,IAAI;AAC3C,MAAI2B,UAAS,OAAO,iBAAiB,OAAO,KAAK,gCAAgC;AACjF,SAAO,KAAK,KAAKA,OAAM;AACzB;AAEA,IAAI,eAAe,OAAO,kBAAkB;AAC5C,IAAI,SAAS,OAAO,kBAAkB;AACtC,IAAI,oBAAqB,SAAU,gBAAgB,eAAe;AAChE,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI,MAAM,KAAK,IAAI;AACnB,MAAI,UAAU,MAAM;AAEpB,MAAI,WAAW,QAAQ;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,cAAc;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,yCAAyC,cAAc;AAAA,IACzD,cAAc;AAAA,IACd;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACD,MAAIA,UAAS,iBAAiB,OAAO,KAAK,sCAAsC;AAChF,SAAO,KAAK,KAAKA,OAAM;AACzB;AAEA,IAAI,WAAY,SAAU,MAAM;AAC9B,MAAI,iBAAiB,KAAK,gBACtB,aAAa,KAAK,YAClB,gBAAgB,KAAK,eACrB,yBAAyB,KAAK;AAClC,MAAIA,UAAS,qBAAqB,gBAAgB,UAAU;AAE5D,MAAIA,YAAW,GAAG;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,wBAAwB;AAC3B,WAAOA;AAAA,EACT;AAEA,SAAO,KAAK,IAAI,kBAAkBA,SAAQ,aAAa,GAAG,SAAS;AACrE;AAEA,IAAI,kBAAmB,SAAU,MAAM;AACrC,MAAI,YAAY,KAAK,WACjB,kBAAkB,KAAK,iBACvB,gBAAgB,KAAK,eACrB,OAAO,KAAK,MACZ,yBAAyB,KAAK;AAClC,MAAI,aAAa,sBAAsB,WAAW,IAAI;AACtD,MAAI,gBAAgB,gBAAgB,KAAK,GAAG,IAAI,gBAAgB,KAAK,KAAK;AAE1E,MAAI,eAAe;AACjB,WAAO,SAAS;AAAA,MACd,gBAAgB,gBAAgB,KAAK,GAAG;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,KAAK,SAAS;AAAA,IACnB,gBAAgB,gBAAgB,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,sBAAuB,SAAU,MAAM;AACzC,MAAI,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,iBAAiB,KAAK;AAC1B,MAAI,qBAAqB,QAAQ,SAAS,UAAU;AACpD,MAAI,uBAAuB,QAAQ,QAAQ,UAAU;AAErD,MAAI,CAAC,wBAAwB,CAAC,oBAAoB;AAChD,WAAO;AAAA,EACT;AAEA,MAAI,wBAAwB,oBAAoB;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,GAAG,uBAAuB,IAAI,eAAe;AAAA,IAC7C,GAAG,qBAAqB,IAAI,eAAe;AAAA,EAC7C;AACF;AAEA,IAAI,UAAU,MAAM,SAAU,OAAO;AACnC,SAAO,UAAU,IAAI,IAAI;AAC3B,CAAC;AACD,IAAI,YAAa,SAAU,MAAM;AAC/B,MAAI,gBAAgB,KAAK,eACrB,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,yBAAyB,KAAK;AAClC,MAAI,kBAAkB;AAAA,IACpB,KAAK,OAAO,IAAI,UAAU;AAAA,IAC1B,OAAO,UAAU,QAAQ,OAAO;AAAA,IAChC,QAAQ,UAAU,SAAS,OAAO;AAAA,IAClC,MAAM,OAAO,IAAI,UAAU;AAAA,EAC7B;AACA,MAAI,IAAI,gBAAgB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACD,MAAI,IAAI,gBAAgB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACD,MAAIoF,YAAW,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI7F,SAAQ6F,WAAU,MAAM,GAAG;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,oBAAoB;AAAA,IAChC;AAAA,IACA;AAAA,IACA,gBAAgBA;AAAA,EAClB,CAAC;AAED,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO7F,SAAQ,SAAS,MAAM,IAAI,OAAO;AAC3C;AAEA,IAAI,iBAAiB,MAAM,SAAU,OAAO;AAC1C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,IAAI,IAAI;AACzB,CAAC;AACD,IAAI,aAAa,2BAAY;AAC3B,MAAI,eAAe,SAAS8F,cAAa,QAAQ,KAAK;AACpD,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,SAAS;AAAA,IAClB;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAU,MAAM;AACrB,QAAI,UAAU,KAAK,SACf,MAAM,KAAK,KACX,SAAS,KAAK;AAClB,QAAI,eAAe,IAAI,SAAS,MAAM;AACtC,QAAI,UAAU;AAAA,MACZ,GAAG,aAAa,aAAa,GAAG,IAAI,CAAC;AAAA,MACrC,GAAG,aAAa,aAAa,GAAG,IAAI,CAAC;AAAA,IACvC;AAEA,QAAI9F,SAAQ,SAAS,MAAM,GAAG;AAC5B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF,EAAE;AACF,IAAI,qBAAqB,SAAS+F,oBAAmB,OAAO;AAC1D,MAAI,SAAS,MAAM,KACf,UAAU,MAAM,SAChB,SAAS,MAAM;AACnB,MAAI,MAAM;AAAA,IACR,GAAG,KAAK,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,IAC/B,GAAG,KAAK,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,EACjC;AACA,MAAI,iBAAiB,eAAe,MAAM;AAC1C,MAAI,UAAU,WAAW;AAAA,IACvB;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AAED,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC7C,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC7C,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AACA,IAAI,kBAAkB,SAASC,iBAAgB,UAAU,QAAQ;AAC/D,SAAO,mBAAmB;AAAA,IACxB,SAAS,SAAS,OAAO;AAAA,IACzB,KAAK,SAAS,OAAO;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AACA,IAAI,mBAAmB,SAASC,kBAAiB,UAAU,QAAQ;AACjE,MAAI,CAAC,gBAAgB,UAAU,MAAM,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,SAAS,OAAO;AAC1B,MAAI,UAAU,SAAS,OAAO;AAC9B,SAAO,WAAW;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,qBAAqB,SAASC,oBAAmBtF,YAAW,QAAQ;AACtE,MAAI,QAAQA,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,SAAO,mBAAmB;AAAA,IACxB,SAAS,MAAM,OAAO;AAAA,IACtB,KAAK,MAAM,OAAO;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,IAAI,sBAAsB,SAASuF,qBAAoBvF,YAAW,QAAQ;AACxE,MAAI,QAAQA,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,mBAAmBA,YAAW,MAAM,GAAG;AAC1C,WAAO;AAAA,EACT;AAEA,SAAO,WAAW;AAAA,IAChB,SAAS,MAAM,OAAO;AAAA,IACtB,KAAK,MAAM,OAAO;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,wBAAyB,SAAU,MAAM;AAC3C,MAAI,WAAW,KAAK,UAChB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,gBAAgB,KAAK,eACrB,yBAAyB,KAAK;AAClC,MAAIH,UAAS,UAAU;AAAA,IACrB;AAAA,IACA,WAAW,SAAS;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAOA,WAAU,gBAAgB,UAAUA,OAAM,IAAIA,UAAS;AAChE;AAEA,IAAI,2BAA4B,SAAU,MAAM;AAC9C,MAAIG,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,gBAAgB,KAAK,eACrB,yBAAyB,KAAK;AAClC,MAAI,QAAQA,WAAU;AAEtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AAEA,MAAIH,UAAS,UAAU;AAAA,IACrB;AAAA,IACA,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAOA,WAAU,mBAAmBG,YAAWH,OAAM,IAAIA,UAAS;AACpE;AAEA,IAAI,WAAY,SAAU,MAAM;AAC9B,MAAI,QAAQ,KAAK,OACb,gBAAgB,KAAK,eACrB,yBAAyB,KAAK,wBAC9B2F,gBAAe,KAAK,cACpBZ,mBAAkB,KAAK;AAC3B,MAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,MAAI3E,aAAY,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACvE,MAAI,UAAUA,WAAU,KAAK;AAE7B,MAAI,MAAM,uBAAuB;AAC/B,QAAI,WAAW,MAAM;AAErB,QAAI,UAAU,sBAAsB;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,SAAS;AACX,MAAAuF,cAAa,OAAO;AACpB;AAAA,IACF;AAAA,EACF;AAEA,MAAIxF,aAAY,2BAA2B;AAAA,IACzC;AAAA,IACA,aAAa,kBAAkB,MAAM,MAAM;AAAA,IAC3C,YAAY,MAAM,WAAW;AAAA,EAC/B,CAAC;AAED,MAAI,CAACA,YAAW;AACd;AAAA,EACF;AAEA,MAAI,SAAS,yBAAyB;AAAA,IACpC;AAAA,IACA,WAAWA;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,QAAQ;AACV,IAAA4E,iBAAgB5E,WAAU,WAAW,IAAI,MAAM;AAAA,EACjD;AACF;AAEA,IAAI,sBAAuB,SAAU,MAAM;AACzC,MAAIwF,gBAAe,KAAK,cACpBZ,mBAAkB,KAAK;AAC3B,MAAI,uBAAuB,qBAAQY,aAAY;AAC/C,MAAI,0BAA0B,qBAAQZ,gBAAe;AACrD,MAAI,WAAW;AAEf,MAAI,YAAY,SAASa,WAAU,OAAO;AACxC,KAAC,WAAW,OAAwCpH,WAAU,OAAO,qCAAqC,IAAIA,WAAU,KAAK,IAAI;AACjI,QAAI,YAAY,UACZ,yBAAyB,UAAU,wBACnC,gBAAgB,UAAU;AAC9B,aAAS;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,UAAU,SAASqH,SAAQ,OAAO;AACpC,UAAM;AACN,KAAC,CAAC,WAAW,OAAwCrH,WAAU,OAAO,kDAAkD,IAAIA,WAAU,KAAK,IAAI;AAC/I,QAAI,gBAAgB,KAAK,IAAI;AAC7B,QAAI,kBAAkB;AAEtB,QAAI,qBAAqB,SAASsH,sBAAqB;AACrD,wBAAkB;AAAA,IACpB;AAEA,aAAS;AAAA,MACP;AAAA,MACA,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,iBAAiB;AAAA,IACnB,CAAC;AACD,eAAW;AAAA,MACT;AAAA,MACA,wBAAwB;AAAA,IAC1B;AACA,WAAO;AAEP,QAAI,iBAAiB;AACnB,gBAAU,KAAK;AAAA,IACjB;AAAA,EACF;AAEA,MAAI,OAAO,SAAShB,QAAO;AACzB,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AAEA,yBAAqB,OAAO;AAC5B,4BAAwB,OAAO;AAC/B,eAAW;AAAA,EACb;AAEA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA,QAAQ;AAAA,EACV;AACF;AAEA,IAAI,qBAAsB,SAAU,MAAM;AACxC,MAAI7B,QAAO,KAAK,MACZ8B,mBAAkB,KAAK,iBACvBY,gBAAe,KAAK;AAExB,MAAI,eAAe,SAASI,cAAa,OAAOvE,SAAQ;AACtD,QAAI,SAAS,IAAI,MAAM,QAAQ,OAAO,WAAWA,OAAM;AACvD,IAAAyB,MAAK;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,+BAA+B,SAAS+C,8BAA6B7F,YAAW,QAAQ;AAC1F,QAAI,CAAC,mBAAmBA,YAAW,MAAM,GAAG;AAC1C,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,oBAAoBA,YAAW,MAAM;AAEnD,QAAI,CAAC,SAAS;AACZ,MAAA4E,iBAAgB5E,WAAU,WAAW,IAAI,MAAM;AAC/C,aAAO;AAAA,IACT;AAEA,QAAI,4BAA4B,SAAS,QAAQ,OAAO;AACxD,IAAA4E,iBAAgB5E,WAAU,WAAW,IAAI,yBAAyB;AAClE,QAAI,YAAY,SAAS,QAAQ,yBAAyB;AAC1D,WAAO;AAAA,EACT;AAEA,MAAI,4BAA4B,SAAS8F,2BAA0B,uBAAuB,UAAU,QAAQ;AAC1G,QAAI,CAAC,uBAAuB;AAC1B,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,gBAAgB,UAAU,MAAM,GAAG;AACtC,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,iBAAiB,UAAU,MAAM;AAE/C,QAAI,CAAC,SAAS;AACZ,MAAAN,cAAa,MAAM;AACnB,aAAO;AAAA,IACT;AAEA,QAAI,yBAAyB,SAAS,QAAQ,OAAO;AACrD,IAAAA,cAAa,sBAAsB;AACnC,QAAI,YAAY,SAAS,QAAQ,sBAAsB;AACvD,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,SAASO,cAAa,OAAO;AAC9C,QAAI,UAAU,MAAM;AAEpB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA,QAAI,cAAc,kBAAkB,MAAM,MAAM;AAChD,KAAC,cAAc,OAAwC1H,WAAU,OAAO,2DAA2D,IAAIA,WAAU,KAAK,IAAI;AAC1J,QAAI,qBAAqB,6BAA6B,MAAM,WAAW,WAAW,WAAW,GAAG,OAAO;AAEvG,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AAEA,QAAI,WAAW,MAAM;AACrB,QAAI,kBAAkB,0BAA0B,MAAM,uBAAuB,UAAU,kBAAkB;AAEzG,QAAI,CAAC,iBAAiB;AACpB;AAAA,IACF;AAEA,iBAAa,OAAO,eAAe;AAAA,EACrC;AAEA,SAAO;AACT;AAEA,IAAI,qBAAsB,SAAU,MAAM;AACxC,MAAIuG,mBAAkB,KAAK,iBACvBY,gBAAe,KAAK,cACpB1C,QAAO,KAAK;AAChB,MAAI,gBAAgB,oBAAoB;AAAA,IACtC,cAAc0C;AAAA,IACd,iBAAiBZ;AAAA,EACnB,CAAC;AACD,MAAI,aAAa,mBAAmB;AAAA,IAClC,MAAM9B;AAAA,IACN,cAAc0C;AAAA,IACd,iBAAiBZ;AAAA,EACnB,CAAC;AAED,MAAI/E,UAAS,SAASA,QAAO,OAAO;AAClC,QAAI,MAAM,UAAU,YAAY;AAC9B;AAAA,IACF;AAEA,QAAI,MAAM,iBAAiB,SAAS;AAClC,oBAAc,OAAO,KAAK;AAC1B;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,mBAAmB;AAC5B;AAAA,IACF;AAEA,eAAW,KAAK;AAAA,EAClB;AAEA,MAAI,WAAW;AAAA,IACb,QAAQA;AAAA,IACR,OAAO,cAAc;AAAA,IACrB,MAAM,cAAc;AAAA,EACtB;AACA,SAAO;AACT;AAEA,IAAI,WAAW;AACf,IAAI,aAAa,WAAY;AAC3B,MAAI,OAAO,WAAW;AACtB,SAAO;AAAA,IACL;AAAA,IACA,aAAa,OAAO;AAAA,IACpB,WAAW,OAAO;AAAA,EACpB;AACF,EAAE;AACF,IAAI,YAAY,WAAY;AAC1B,MAAI,OAAO,WAAW;AACtB,SAAO;AAAA,IACL;AAAA,IACA,WAAW,OAAO;AAAA,IAClB,IAAI,OAAO;AAAA,EACb;AACF,EAAE;AACF,IAAI,YAAY,WAAY;AAC1B,MAAI,OAAO,WAAW;AACtB,SAAO;AAAA,IACL;AAAA,IACA,WAAW,OAAO;AAAA,IAClB,IAAI,OAAO;AAAA,EACb;AACF,EAAE;AACF,IAAI,kBAAkB;AAAA,EACpB,WAAW,WAAW;AACxB;AAEA,IAAI,kBAAkB,SAASmG,iBAAgB,SAAS;AACtD,SAAO,SAAU,WAAW;AAC1B,WAAO,MAAM,YAAY,OAAQ,UAAU;AAAA,EAC7C;AACF;AAEA,IAAI,YAAY,SAASC,WAAU,OAAO,UAAU;AAClD,SAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,QAAI,QAAQ,KAAK,OAAO,QAAQ;AAEhC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,WAAW,QAAQ,QAAQ;AAAA,EACzC,CAAC,EAAE,KAAK,GAAG;AACb;AAEA,IAAI,kBAAkB;AACtB,IAAI,cAAe,SAAU,WAAW;AACtC,MAAIC,eAAc,gBAAgB,SAAS;AAE3C,MAAI,eAAe,WAAY;AAC7B,QAAI,aAAa;AACjB,WAAO;AAAA,MACL,UAAUA,aAAY,WAAW,SAAS;AAAA,MAC1C,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,EAAE;AAEF,MAAI,cAAc,WAAY;AAC5B,QAAI,aAAa,yBAAyB,YAAY,cAAc;AACpE,WAAO;AAAA,MACL,UAAUA,aAAY,UAAU,SAAS;AAAA,MACzC,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE;AAEF,MAAI,cAAc;AAAA,IAChB,UAAUA,aAAY,UAAU,SAAS;AAAA,IACzC,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,OAAO;AAAA,IACT,UAAU;AAAA,IACV,QAAQ;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,CAAC,aAAa,cAAc,aAAa,IAAI;AACzD,SAAO;AAAA,IACL,QAAQ,UAAU,OAAO,QAAQ;AAAA,IACjC,SAAS,UAAU,OAAO,SAAS;AAAA,IACnC,UAAU,UAAU,OAAO,UAAU;AAAA,IACrC,eAAe,UAAU,OAAO,eAAe;AAAA,IAC/C,YAAY,UAAU,OAAO,YAAY;AAAA,EAC3C;AACF;AAEA,IAAIC,6BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB,cAAc,gCAAkB;AAEpL,IAAI,UAAU,SAASC,WAAU;AAC/B,MAAI,OAAO,SAAS,cAAc,MAAM;AACxC,GAAC,OAAO,OAAwC/H,WAAU,OAAO,2CAA2C,IAAIA,WAAU,KAAK,IAAI;AACnI,SAAO;AACT;AAEA,IAAI,gBAAgB,SAASgI,eAAc,OAAO;AAChD,MAAI,KAAK,SAAS,cAAc,OAAO;AAEvC,MAAI,OAAO;AACT,OAAG,aAAa,SAAS,KAAK;AAAA,EAChC;AAEA,KAAG,OAAO;AACV,SAAO;AACT;AAEA,SAAS,gBAAgB,WAAW,OAAO;AACzC,MAAI,SAASC,SAAQ,WAAY;AAC/B,WAAO,YAAY,SAAS;AAAA,EAC9B,GAAG,CAAC,SAAS,CAAC;AACd,MAAI,gBAAY,sBAAO,IAAI;AAC3B,MAAI,iBAAa,sBAAO,IAAI;AAC5B,MAAI,kBAAkB,YAAY,wBAAW,SAAU,UAAU;AAC/D,QAAI,KAAK,WAAW;AACpB,KAAC,KAAK,OAAwCjI,WAAU,OAAO,mDAAmD,IAAIA,WAAU,KAAK,IAAI;AACzI,OAAG,cAAc;AAAA,EACnB,CAAC,GAAG,CAAC,CAAC;AACN,MAAI,iBAAiB,YAAY,SAAU,UAAU;AACnD,QAAI,KAAK,UAAU;AACnB,KAAC,KAAK,OAAwCA,WAAU,OAAO,mDAAmD,IAAIA,WAAU,KAAK,IAAI;AACzI,OAAG,cAAc;AAAA,EACnB,GAAG,CAAC,CAAC;AACL,EAAA8H,2BAA0B,WAAY;AACpC,MAAE,CAAC,UAAU,WAAW,CAAC,WAAW,WAAW,OAAwC9H,WAAU,OAAO,gCAAgC,IAAIA,WAAU,KAAK,IAAI;AAC/J,QAAI,SAAS,cAAc,KAAK;AAChC,QAAI,UAAU,cAAc,KAAK;AACjC,cAAU,UAAU;AACpB,eAAW,UAAU;AACrB,WAAO,aAAa,WAAW,WAAW,SAAS;AACnD,YAAQ,aAAa,WAAW,YAAY,SAAS;AACrD,YAAQ,EAAE,YAAY,MAAM;AAC5B,YAAQ,EAAE,YAAY,OAAO;AAC7B,mBAAe,OAAO,MAAM;AAC5B,oBAAgB,OAAO,OAAO;AAC9B,WAAO,WAAY;AACjB,UAAI,SAAS,SAASqG,QAAO6B,MAAK;AAChC,YAAI,UAAUA,KAAI;AAClB,SAAC,UAAU,OAAwClI,WAAU,OAAO,qCAAqC,IAAIA,WAAU,KAAK,IAAI;AAChI,gBAAQ,EAAE,YAAY,OAAO;AAC7B,QAAAkI,KAAI,UAAU;AAAA,MAChB;AAEA,aAAO,SAAS;AAChB,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,OAAO,gBAAgB,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,CAAC;AACrF,MAAI,WAAW,YAAY,WAAY;AACrC,WAAO,gBAAgB,OAAO,QAAQ;AAAA,EACxC,GAAG,CAAC,iBAAiB,OAAO,QAAQ,CAAC;AACrC,MAAI,WAAW,YAAY,SAAU,QAAQ;AAC3C,QAAI,WAAW,QAAQ;AACrB,sBAAgB,OAAO,aAAa;AACpC;AAAA,IACF;AAEA,oBAAgB,OAAO,UAAU;AAAA,EACnC,GAAG,CAAC,iBAAiB,OAAO,eAAe,OAAO,UAAU,CAAC;AAC7D,MAAI,UAAU,YAAY,WAAY;AACpC,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AAEA,oBAAgB,OAAO,OAAO;AAAA,EAChC,GAAG,CAAC,iBAAiB,OAAO,OAAO,CAAC;AACpC,MAAI,UAAUD,SAAQ,WAAY;AAChC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,OAAO,CAAC;AAChC,SAAO;AACT;AAEA,IAAI,kBAAmB,SAAU,IAAI;AACnC,SAAO,MAAM,GAAG,gBAAgB,GAAG,cAAc,cAAc;AACjE;AAEA,SAAS,cAAc,IAAI;AACzB,SAAO,cAAc,gBAAgB,EAAE,EAAE;AAC3C;AAEA,SAAS,eAAe,WAAW,aAAa;AAC9C,MAAI,WAAW,MAAM,WAAW,YAAY,OAAQ,YAAY;AAChE,MAAI,WAAW,QAAQ,SAAS,iBAAiB,QAAQ,CAAC;AAE1D,MAAI,CAAC,SAAS,QAAQ;AACpB,WAAwCpI,SAAQ,qDAAsD,YAAY,GAAI,IAAI;AAC1H,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,KAAK,UAAU,SAAU,IAAI;AACxC,WAAO,GAAG,aAAa,WAAW,WAAW,MAAM;AAAA,EACrD,CAAC;AAED,MAAI,CAAC,QAAQ;AACX,WAAwCA,SAAQ,yCAA0C,cAAc,6CAA8C,IAAI;AAC1J,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAwCA,SAAQ,uCAAuC,IAAI;AAC3F,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,WAAW;AAClC,MAAI,iBAAa,sBAAO,CAAC,CAAC;AAC1B,MAAI,gBAAY,sBAAO,IAAI;AAC3B,MAAI,2BAAuB,sBAAO,IAAI;AACtC,MAAI,mBAAe,sBAAO,KAAK;AAC/B,MAAI,WAAW,YAAY,SAASsI,UAAS,IAAIC,QAAO;AACtD,QAAI,QAAQ;AAAA,MACV;AAAA,MACA,OAAOA;AAAA,IACT;AACA,eAAW,QAAQ,EAAE,IAAI;AACzB,WAAO,SAAS,aAAa;AAC3B,UAAI,UAAU,WAAW;AACzB,UAAI,UAAU,QAAQ,EAAE;AAExB,UAAI,YAAY,OAAO;AACrB,eAAO,QAAQ,EAAE;AAAA,MACnB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,eAAe,YAAY,SAASC,cAAa,gBAAgB;AACnE,QAAI,SAAS,eAAe,WAAW,cAAc;AAErD,QAAI,UAAU,WAAW,SAAS,eAAe;AAC/C,aAAO,MAAM;AAAA,IACf;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,MAAI,iBAAiB,YAAY,SAASC,gBAAe,UAAU,YAAY;AAC7E,QAAI,UAAU,YAAY,UAAU;AAClC,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,0BAA0B,YAAY,SAASC,2BAA0B;AAC3E,QAAI,qBAAqB,SAAS;AAChC;AAAA,IACF;AAEA,QAAI,CAAC,aAAa,SAAS;AACzB;AAAA,IACF;AAEA,yBAAqB,UAAU,sBAAsB,WAAY;AAC/D,2BAAqB,UAAU;AAC/B,UAAI,SAAS,UAAU;AAEvB,UAAI,QAAQ;AACV,qBAAa,MAAM;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,YAAY,CAAC;AACjB,MAAI,iBAAiB,YAAY,SAASC,gBAAe,IAAI;AAC3D,cAAU,UAAU;AACpB,QAAI,UAAU,SAAS;AAEvB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA,QAAI,QAAQ,aAAa,WAAW,WAAW,MAAM,IAAI;AACvD;AAAA,IACF;AAEA,cAAU,UAAU;AAAA,EACtB,GAAG,CAAC,CAAC;AACL,EAAAV,2BAA0B,WAAY;AACpC,iBAAa,UAAU;AACvB,WAAO,SAAS,sBAAsB;AACpC,mBAAa,UAAU;AACvB,UAAI,UAAU,qBAAqB;AAEnC,UAAI,SAAS;AACX,6BAAqB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,UAAUG,SAAQ,WAAY;AAChC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,gBAAgB,yBAAyB,cAAc,CAAC;AACtE,SAAO;AACT;AAEA,SAAS,iBAAiB;AACxB,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,EACf;AACA,MAAI,cAAc,CAAC;AAEnB,WAAS,UAAU,IAAI;AACrB,gBAAY,KAAK,EAAE;AACnB,WAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ,YAAY,QAAQ,EAAE;AAElC,UAAI,UAAU,IAAI;AAChB;AAAA,MACF;AAEA,kBAAY,OAAO,OAAO,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,WAASQ,QAAO,OAAO;AACrB,QAAI,YAAY,QAAQ;AACtB,kBAAY,QAAQ,SAAU,IAAI;AAChC,eAAO,GAAG,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,QAAQ,WAAW,EAAE,KAAK;AAAA,EACnC;AAEA,WAAS,iBAAiB,IAAI;AAC5B,QAAI,QAAQ,kBAAkB,EAAE;AAChC,KAAC,QAAQ,OAAwCzI,WAAU,OAAO,0CAA0C,KAAK,GAAG,IAAIA,WAAU,KAAK,IAAI;AAC3I,WAAO;AAAA,EACT;AAEA,MAAI,eAAe;AAAA,IACjB,UAAU,SAAS,SAAS,OAAO;AACjC,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAC1C,MAAAyI,QAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,QAAQ,SAAS/H,QAAO,OAAO,MAAM;AACnC,UAAI,UAAU,QAAQ,WAAW,KAAK,WAAW,EAAE;AAEnD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,UAAI,QAAQ,aAAa,MAAM,UAAU;AACvC;AAAA,MACF;AAEA,aAAO,QAAQ,WAAW,KAAK,WAAW,EAAE;AAC5C,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAAA,IAC5C;AAAA,IACA,YAAY,SAAS,WAAW,OAAO;AACrC,UAAI,cAAc,MAAM,WAAW;AACnC,UAAI,UAAU,kBAAkB,WAAW;AAE3C,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,UAAI,MAAM,aAAa,QAAQ,UAAU;AACvC;AAAA,MACF;AAEA,aAAO,QAAQ,WAAW,WAAW;AACrC,MAAA+H,QAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,SAAS,OAAO,IAAI;AAC1B,aAAO,QAAQ,kBAAkB,EAAE,CAAC;AAAA,IACtC;AAAA,IACA,cAAc,SAAS,aAAa,MAAM;AACxC,aAAO,OAAO,QAAQ,UAAU,EAAE,OAAO,SAAU,OAAO;AACxD,eAAO,MAAM,WAAW,SAAS;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,QAAQ,WAAW,EAAE,KAAK;AAAA,EACnC;AAEA,WAAS,iBAAiB,IAAI;AAC5B,QAAI,QAAQ,kBAAkB,EAAE;AAChC,KAAC,QAAQ,OAAwCzI,WAAU,OAAO,0CAA0C,KAAK,GAAG,IAAIA,WAAU,KAAK,IAAI;AAC3I,WAAO;AAAA,EACT;AAEA,MAAI,eAAe;AAAA,IACjB,UAAU,SAAS,SAAS,OAAO;AACjC,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAAA,IAC5C;AAAA,IACA,YAAY,SAAS,WAAW,OAAO;AACrC,UAAI,UAAU,kBAAkB,MAAM,WAAW,EAAE;AAEnD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,UAAI,MAAM,aAAa,QAAQ,UAAU;AACvC;AAAA,MACF;AAEA,aAAO,QAAQ,WAAW,MAAM,WAAW,EAAE;AAAA,IAC/C;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,SAAS,OAAO,IAAI;AAC1B,aAAO,QAAQ,kBAAkB,EAAE,CAAC;AAAA,IACtC;AAAA,IACA,cAAc,SAAS,aAAa,MAAM;AACxC,aAAO,OAAO,QAAQ,UAAU,EAAE,OAAO,SAAU,OAAO;AACxD,eAAO,MAAM,WAAW,SAAS;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAASN,SAAQ;AACf,YAAQ,aAAa,CAAC;AACtB,YAAQ,aAAa,CAAC;AACtB,gBAAY,SAAS;AAAA,EACvB;AAEA,SAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA,OAAOA;AAAA,EACT;AACF;AAEA,SAAS,cAAc;AACrB,MAAI,WAAWuI,SAAQ,gBAAgB,CAAC,CAAC;AACzC,+BAAU,WAAY;AACpB,WAAO,SAAS,UAAU;AACxB,4BAAsB,SAAS,KAAK;AAAA,IACtC;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO;AACT;AAEA,IAAI,eAAe,cAAA/H,QAAM,cAAc,IAAI;AAE3C,IAAI,iBAAkB,WAAY;AAChC,MAAI,OAAO,SAAS;AACpB,GAAC,OAAO,OAAwCF,WAAU,OAAO,2BAA2B,IAAIA,WAAU,KAAK,IAAI;AACnH,SAAO;AACT;AAEA,IAAI,iBAAiB;AAAA,EACnB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AACf;AAEA,IAAI,QAAQ,SAAS0I,OAAM,WAAW;AACpC,SAAO,sBAAsB;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,MAAI,KAAKT,SAAQ,WAAY;AAC3B,WAAO,MAAM,SAAS;AAAA,EACxB,GAAG,CAAC,SAAS,CAAC;AACd,MAAIC,WAAM,sBAAO,IAAI;AACrB,+BAAU,SAAS,QAAQ;AACzB,QAAI,KAAK,SAAS,cAAc,KAAK;AACrC,IAAAA,KAAI,UAAU;AACd,OAAG,KAAK;AACR,OAAG,aAAa,aAAa,WAAW;AACxC,OAAG,aAAa,eAAe,MAAM;AAErC,aAAS,GAAG,OAAO,cAAc;AAEjC,mBAAe,EAAE,YAAY,EAAE;AAC/B,WAAO,SAAS,UAAU;AACxB,iBAAW,SAAS,SAAS;AAC3B,YAAI,OAAO,eAAe;AAE1B,YAAI,KAAK,SAAS,EAAE,GAAG;AACrB,eAAK,YAAY,EAAE;AAAA,QACrB;AAEA,YAAI,OAAOA,KAAI,SAAS;AACtB,UAAAA,KAAI,UAAU;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,EAAE,CAAC;AACP,MAAI,WAAW,YAAY,SAAU,SAAS;AAC5C,QAAI,KAAKA,KAAI;AAEb,QAAI,IAAI;AACN,SAAG,cAAc;AACjB;AAAA,IACF;AAEA,WAAwCrI,SAAQ,0XAA2X,UAAU,SAAU,IAAI;AAAA,EACrc,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAEA,IAAI,QAAQ;AACZ,IAAI,WAAW;AAAA,EACb,WAAW;AACb;AACA,SAAS,QAAQ;AACf,UAAQ;AACV;AACA,SAAS,YAAYE,SAAQ,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,SAAOkI,SAAQ,WAAY;AACzB,WAAO,KAAKlI,UAAS,QAAQ,YAAY;AAAA,EAC3C,GAAG,CAAC,QAAQ,WAAWA,OAAM,CAAC;AAChC;AAEA,SAAS,aAAa,MAAM;AAC1B,MAAI,YAAY,KAAK,WACjB,WAAW,KAAK;AACpB,SAAO,qBAAqB,YAAY,MAAM;AAChD;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,YAAY,MAAM,WAClB,OAAO,MAAM;AACjB,MAAI,WAAW,YAAY,eAAe;AAAA,IACxC,WAAW;AAAA,EACb,CAAC;AACD,MAAI,KAAKkI,SAAQ,WAAY;AAC3B,WAAO,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,SAAS,CAAC;AACxB,+BAAU,SAAS,QAAQ;AACzB,QAAI,KAAK,SAAS,cAAc,KAAK;AACrC,OAAG,KAAK;AACR,OAAG,cAAc;AACjB,OAAG,MAAM,UAAU;AACnB,mBAAe,EAAE,YAAY,EAAE;AAC/B,WAAO,SAAS,UAAU;AACxB,UAAI,OAAO,eAAe;AAE1B,UAAI,KAAK,SAAS,EAAE,GAAG;AACrB,aAAK,YAAY,EAAE;AAAA,MACrB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,IAAI,CAAC;AACb,SAAO;AACT;AAEA,IAAI,aAAa,cAAA/H,QAAM,cAAc,IAAI;AAEzC,IAAI,mBAAmB;AAAA,EACtB,OAAO;AAAA,EACP,aAAa;AACd;AAEA,IAAI,SAAS;AAEb,IAAI,aAAa,SAASyI,YAAW,OAAO;AAC1C,MAAI,SAAS,OAAO,KAAK,KAAK;AAC9B,IAAE,UAAU,QAAQ,OAAwC3I,WAAU,OAAO,mCAAmC,KAAK,IAAIA,WAAU,KAAK,IAAI;AAC5I,MAAI,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC5B,MAAI,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC5B,MAAIiB,SAAQ,OAAO,OAAO,CAAC,CAAC;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAOA;AAAA,IACP,KAAK;AAAA,EACP;AACF;AAEA,IAAI,cAAc,SAAS2H,aAAY,UAAU,QAAQ;AACvD,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,SAAS,SAAS;AAClC;AAEA,IAAI,oBAAqB,SAAU,cAAc,aAAa;AAC5D,MAAI,UAAU,WAAW,YAAY;AACrC,MAAI,SAAS,WAAW,WAAW;AAEnC,MAAI,YAAY,SAAS,MAAM,GAAG;AAChC;AAAA,EACF;AAEA,SAAwC/I,SAAQ,2BAA2B,OAAO,MAAM,gEAAgE,QAAQ,MAAM,uEAAuE,IAAI;AACnP;AAEA,IAAI,SAAS;AACb,IAAI,eAAgB,SAAU,KAAK;AACjC,MAAI,UAAU,IAAI;AAElB,MAAI,CAAC,SAAS;AACZ,WAAwCA,SAAQ,gDAAgD,SAAS,QAAQ,IAAI;AACrH;AAAA,EACF;AAEA,MAAI,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACzC,WAAwCA,SAAQ,2CAA2C,QAAQ,OAAO,gBAAgB,SAAS,QAAQ,IAAI;AAAA,EACjJ;AAEA,MAAI,QAAQ,aAAa,IAAI;AAC3B,WAAwCA,SAAQ,oDAAoD,QAAQ,WAAW,gEAAgE,SAAS,QAAQ,IAAI;AAAA,EAC9M;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,MAAuC;AACzC,YAAQ;AAAA,EACV;AACF;AAEA,SAAS,mBAAmB,IAAI,QAAQ;AACtC,SAAO,WAAY;AACjB,iCAAU,WAAY;AACpB,UAAI;AACF,WAAG;AAAA,MACL,SAAS,GAAG;AACV,cAAM,iEAAiE,EAAE,UAAU,YAAY;AAAA,MACjG;AAAA,IACF,GAAG,MAAM;AAAA,EACX,CAAC;AACH;AAEA,SAAS,uBAAuB;AAC9B,qBAAmB,WAAY;AAC7B,sBAAkB,iBAAiB,OAAO,cAAAK,QAAM,OAAO;AACvD,iBAAa,QAAQ;AAAA,EACvB,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,YAAY,SAAS;AAC5B,MAAIgI,WAAM,sBAAO,OAAO;AACxB,+BAAU,WAAY;AACpB,IAAAA,KAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAOA;AACT;AAEA,SAAS,SAAS;AAChB,MAAI,OAAO;AAEX,WAAS,YAAY;AACnB,WAAO,QAAQ,IAAI;AAAA,EACrB;AAEA,WAAS,SAAS,OAAO;AACvB,WAAO,UAAU;AAAA,EACnB;AAEA,WAAS,MAAM,SAAS;AACtB,KAAC,CAAC,OAAO,OAAwClI,WAAU,OAAO,4CAA4C,IAAIA,WAAU,KAAK,IAAI;AACrI,QAAI,UAAU;AAAA,MACZ;AAAA,IACF;AACA,WAAO;AACP,WAAO;AAAA,EACT;AAEA,WAAS,UAAU;AACjB,KAAC,OAAO,OAAwCA,WAAU,OAAO,2CAA2C,IAAIA,WAAU,KAAK,IAAI;AACnI,WAAO;AAAA,EACT;AAEA,WAAS,aAAa;AACpB,QAAI,MAAM;AACR,WAAK,QAAQ;AACb,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,YAAY;AAEhB,IAAI;AACJ,IAAI,iBAAiB,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,MAAM,eAAe,GAAG,IAAI,MAAM;AACpG,IAAI,2BAA4B,SAAU,OAAO;AAC/C,MAAI,cAAc,MAAM,OAAO,GAAG;AAChC,UAAM,eAAe;AAAA,EACvB;AACF;AAEA,IAAI,qBAAqB,WAAY;AACnC,MAAI,OAAO;AAEX,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC,MAAM,OAAO,MAAM,WAAW,MAAM,QAAQ,MAAM,MAAM,IAAI;AAC9E,MAAI,YAAY,KAAK,YAAY,SAAU,WAAW;AACpD,WAAO,OAAO,aAAa;AAAA,EAC7B,CAAC;AACD,SAAO,aAAa;AACtB,EAAE;AAEF,IAAI,gBAAgB;AACpB,IAAI,uBAAuB;AAE3B,SAAS,+BAA+B,UAAU,SAAS;AACzD,SAAO,KAAK,IAAI,QAAQ,IAAI,SAAS,CAAC,KAAK,wBAAwB,KAAK,IAAI,QAAQ,IAAI,SAAS,CAAC,KAAK;AACzG;AAEA,IAAI,SAAS;AAAA,EACX,MAAM;AACR;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAK,QACd,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,SAAS,MAAM,QACf,UAAU,MAAM,SAChB,UAAU,MAAM;AAEpB,UAAI,WAAW,eAAe;AAC5B;AAAA,MACF;AAEA,UAAI,QAAQ;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,YAAY;AAC7B,cAAM,eAAe;AACrB,cAAM,QAAQ,KAAK,KAAK;AACxB;AAAA,MACF;AAEA,QAAE,MAAM,SAAS,aAAa,OAAwCA,WAAU,OAAO,gBAAgB,IAAIA,WAAU,KAAK,IAAI;AAC9H,UAAI,UAAU,MAAM;AAEpB,UAAI,CAAC,+BAA+B,SAAS,KAAK,GAAG;AACnD;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,UAAI,UAAU,MAAM,QAAQ,UAAU,KAAK;AAC3C,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK;AAAA,QACjB,sBAAsB;AAAA,MACxB,CAAC;AACD,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,cAAM,eAAe;AAAA,MACvB;AAEA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,WAAW;AAC5B,eAAO;AACP;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AACrB,eAAO;AACP;AAAA,MACF;AAEA,+BAAyB,KAAK;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,IAAI,SAAS,KAAK;AAChB,UAAI,SAAS,EAAE,SAAS,WAAW;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AACrB,QAAE,MAAM,SAAS,UAAU,OAAwCA,WAAU,OAAO,kBAAkB,IAAIA,WAAU,KAAK,IAAI;AAE7H,UAAI,MAAM,QAAQ,wBAAwB,GAAG;AAC3C,eAAO;AACP;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,eAAW,sBAAO,MAAM;AAC5B,MAAI,sBAAkB,sBAAO,IAAI;AACjC,MAAI,sBAAsBiI,SAAQ,WAAY;AAC5C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,IAAI,SAAS,YAAY,OAAO;AAC9B,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AAEA,YAAI,MAAM,WAAW,eAAe;AAClC;AAAA,QACF;AAEA,YAAI,MAAM,WAAW,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ;AACpE;AAAA,QACF;AAEA,YAAI,cAAc,IAAI,uBAAuB,KAAK;AAElD,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAEA,YAAI,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,UAC9C,aAAa;AAAA,QACf,CAAC;AAED,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,cAAM,eAAe;AACrB,YAAI,QAAQ;AAAA,UACV,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,QACX;AACA,wBAAgB,QAAQ;AACxB,yBAAiB,SAAS,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,MAAI,2BAA2BA,SAAQ,WAAY;AACjD,WAAO;AAAA,MACL,WAAW;AAAA,MACX,IAAI,SAAS,GAAG,OAAO;AACrB,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AAEA,YAAI,KAAK,IAAI,uBAAuB,KAAK;AAEzC,YAAI,CAAC,IAAI;AACP;AAAA,QACF;AAEA,YAAI,UAAU,IAAI,wBAAwB,EAAE;AAE5C,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,YAAI,QAAQ,yBAAyB;AACnC;AAAA,QACF;AAEA,YAAI,CAAC,IAAI,WAAW,EAAE,GAAG;AACvB;AAAA,QACF;AAEA,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,MAAI,mBAAmB,YAAY,SAASY,oBAAmB;AAC7D,QAAI,UAAU;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,0BAA0B,mBAAmB,GAAG,OAAO;AAAA,EACvG,GAAG,CAAC,0BAA0B,mBAAmB,CAAC;AAClD,MAAI,OAAO,YAAY,WAAY;AACjC,QAAI,UAAU,SAAS;AAEvB,QAAI,QAAQ,SAAS,QAAQ;AAC3B;AAAA,IACF;AAEA,aAAS,UAAU;AACnB,oBAAgB,QAAQ;AACxB,qBAAiB;AAAA,EACnB,GAAG,CAAC,gBAAgB,CAAC;AACrB,MAAI,SAAS,YAAY,WAAY;AACnC,QAAI,QAAQ,SAAS;AACrB,SAAK;AAEL,QAAI,MAAM,SAAS,YAAY;AAC7B,YAAM,QAAQ,OAAO;AAAA,QACnB,sBAAsB;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,QAAI,MAAM,SAAS,WAAW;AAC5B,YAAM,QAAQ,MAAM;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,MAAI,sBAAsB,YAAY,SAASC,uBAAsB;AACnE,QAAI,UAAU;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,WAAW,mBAAmB;AAAA,MAChC;AAAA,MACA,WAAW;AAAA,MACX,UAAU,SAAS,WAAW;AAC5B,eAAO,SAAS;AAAA,MAClB;AAAA,MACA,UAAU,SAAS,SAAS,OAAO;AACjC,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF,CAAC;AACD,oBAAgB,UAAU,WAAW,QAAQ,UAAU,OAAO;AAAA,EAChE,GAAG,CAAC,QAAQ,IAAI,CAAC;AACjB,MAAI,mBAAmB,YAAY,SAASC,kBAAiB,SAAS,OAAO;AAC3E,MAAE,SAAS,QAAQ,SAAS,UAAU,OAAwC/I,WAAU,OAAO,4CAA4C,IAAIA,WAAU,KAAK,IAAI;AAClK,aAAS,UAAU;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AACA,wBAAoB;AAAA,EACtB,GAAG,CAAC,mBAAmB,CAAC;AACxB,EAAA8H,2BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACvB;AAEA,IAAI;AAEJ,SAAS,SAAS;AAAC;AAEnB,IAAI,kBAAkB,kBAAkB,CAAC,GAAG,gBAAgB,QAAQ,IAAI,MAAM,gBAAgB,MAAM,IAAI,MAAM,gBAAgB,IAAI,IAAI,MAAM,gBAAgB,GAAG,IAAI,MAAM;AAEzK,SAAS,oBAAoB,SAAS,MAAM;AAC1C,WAAS,SAAS;AAChB,SAAK;AACL,YAAQ,OAAO;AAAA,EACjB;AAEA,WAAS3C,QAAO;AACd,SAAK;AACL,YAAQ,KAAK;AAAA,EACf;AAEA,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AACrB,eAAO;AACP;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,OAAO;AAC3B,cAAM,eAAe;AACrB,QAAAA,MAAK;AACL;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,WAAW;AAC/B,cAAM,eAAe;AACrB,gBAAQ,SAAS;AACjB;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,SAAS;AAC7B,cAAM,eAAe;AACrB,gBAAQ,OAAO;AACf;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,YAAY;AAChC,cAAM,eAAe;AACrB,gBAAQ,UAAU;AAClB;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,WAAW;AAC/B,cAAM,eAAe;AACrB,gBAAQ,SAAS;AACjB;AAAA,MACF;AAEA,UAAI,eAAe,MAAM,OAAO,GAAG;AACjC,cAAM,eAAe;AACrB;AAAA,MACF;AAEA,+BAAyB,KAAK;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AAEA,SAAS,kBAAkB,KAAK;AAC9B,MAAI,sBAAkB,sBAAO,MAAM;AACnC,MAAI,sBAAsB8C,SAAQ,WAAY;AAC5C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,IAAI,SAAS,UAAU,OAAO;AAC5B,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AAEA,YAAI,MAAM,YAAY,OAAO;AAC3B;AAAA,QACF;AAEA,YAAI,cAAc,IAAI,uBAAuB,KAAK;AAElD,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAEA,YAAI,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,UAC9C,aAAa;AAAA,QACf,CAAC;AAED,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,cAAM,eAAe;AACrB,YAAI,cAAc;AAClB,YAAI,UAAU,QAAQ,SAAS;AAC/B,wBAAgB,QAAQ;AAExB,iBAAS,OAAO;AACd,WAAC,cAAc,OAAwCjI,WAAU,OAAO,0DAA0D,IAAIA,WAAU,KAAK,IAAI;AACzJ,wBAAc;AACd,0BAAgB,QAAQ;AACxB,2BAAiB;AAAA,QACnB;AAEA,wBAAgB,UAAU,WAAW,QAAQ,oBAAoB,SAAS,IAAI,GAAG;AAAA,UAC/E,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,MAAI,mBAAmB,YAAY,SAAS,kBAAkB;AAC5D,QAAI,UAAU;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,mBAAmB,GAAG,OAAO;AAAA,EAC7E,GAAG,CAAC,mBAAmB,CAAC;AACxB,EAAA8H,2BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACvB;AAEA,IAAI,SAAS;AAAA,EACX,MAAM;AACR;AACA,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAE1B,SAAS,kBAAkB,MAAM;AAC/B,MAAI,SAAS,KAAK,QACd,WAAW,KAAK;AACpB,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,eAAO;AACP;AAAA,MACF;AAEA,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AAAA,MACvB;AAEA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAO;AAChC,MAAI,SAAS,MAAM,QACf,YAAY,MAAM,WAClB,WAAW,MAAM;AACrB,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AAEA,YAAM,WAAW;AACjB,UAAI,kBAAkB,MAAM,QAAQ,CAAC,GACjC,UAAU,gBAAgB,SAC1B,UAAU,gBAAgB;AAC9B,UAAI,QAAQ;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK,KAAK;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK;AAAA,QACjB,sBAAsB;AAAA,MACxB,CAAC;AACD,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,eAAO;AACP;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,SAAS,GAAG,OAAO;AACrB,UAAI,QAAQ,SAAS;AACrB,QAAE,MAAM,SAAS,UAAU,OAAwC9H,WAAU,KAAK,IAAIA,WAAU,KAAK,IAAI;AACzG,UAAI,QAAQ,MAAM,QAAQ,CAAC;AAE3B,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AAEA,UAAI,eAAe,MAAM,SAAS;AAElC,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AAEA,UAAI,gBAAgB,MAAM,QAAQ,wBAAwB;AAE1D,UAAI,MAAM,SAAS,WAAW;AAC5B,YAAI,eAAe;AACjB,iBAAO;AAAA,QACT;AAEA;AAAA,MACF;AAEA,UAAI,eAAe;AACjB,YAAI,MAAM,UAAU;AAClB,gBAAM,eAAe;AACrB;AAAA,QACF;AAEA,eAAO;AACP;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,eAAW,sBAAO,MAAM;AAC5B,MAAI,sBAAkB,sBAAO,IAAI;AACjC,MAAI,WAAW,YAAY,SAASgJ,YAAW;AAC7C,WAAO,SAAS;AAAA,EAClB,GAAG,CAAC,CAAC;AACL,MAAI,WAAW,YAAY,SAASC,UAAS,OAAO;AAClD,aAAS,UAAU;AAAA,EACrB,GAAG,CAAC,CAAC;AACL,MAAI,sBAAsBhB,SAAQ,WAAY;AAC5C,WAAO;AAAA,MACL,WAAW;AAAA,MACX,IAAI,SAAS,aAAa,OAAO;AAC/B,YAAI,MAAM,kBAAkB;AAC1B;AAAA,QACF;AAEA,YAAI,cAAc,IAAI,uBAAuB,KAAK;AAElD,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAEA,YAAI,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,UAC9C,aAAa;AAAA,QACf,CAAC;AAED,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,YAAI,QAAQ,MAAM,QAAQ,CAAC;AAC3B,YAAI,UAAU,MAAM,SAChB,UAAU,MAAM;AACpB,YAAI,QAAQ;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,wBAAgB,QAAQ;AACxB,yBAAiB,SAAS,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,MAAI,mBAAmB,YAAY,SAASY,oBAAmB;AAC7D,QAAI,UAAU;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,mBAAmB,GAAG,OAAO;AAAA,EAC7E,GAAG,CAAC,mBAAmB,CAAC;AACxB,MAAI,OAAO,YAAY,WAAY;AACjC,QAAI,UAAU,SAAS;AAEvB,QAAI,QAAQ,SAAS,QAAQ;AAC3B;AAAA,IACF;AAEA,QAAI,QAAQ,SAAS,WAAW;AAC9B,mBAAa,QAAQ,gBAAgB;AAAA,IACvC;AAEA,aAAS,MAAM;AACf,oBAAgB,QAAQ;AACxB,qBAAiB;AAAA,EACnB,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAC/B,MAAI,SAAS,YAAY,WAAY;AACnC,QAAI,QAAQ,SAAS;AACrB,SAAK;AAEL,QAAI,MAAM,SAAS,YAAY;AAC7B,YAAM,QAAQ,OAAO;AAAA,QACnB,sBAAsB;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,QAAI,MAAM,SAAS,WAAW;AAC5B,YAAM,QAAQ,MAAM;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,MAAI,sBAAsB,YAAY,SAASC,uBAAsB;AACnE,QAAI,UAAU;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF;AACA,QAAI,eAAe,WAAW,QAAQ,kBAAkB,IAAI,GAAG,OAAO;AACtE,QAAI,eAAe,WAAW,QAAQ,kBAAkB,IAAI,GAAG,OAAO;AAEtE,oBAAgB,UAAU,SAAS,YAAY;AAC7C,mBAAa;AACb,mBAAa;AAAA,IACf;AAAA,EACF,GAAG,CAAC,QAAQ,UAAU,IAAI,CAAC;AAC3B,MAAI,gBAAgB,YAAY,SAASI,iBAAgB;AACvD,QAAI,QAAQ,SAAS;AACrB,MAAE,MAAM,SAAS,aAAa,OAAwClJ,WAAU,OAAO,sCAAsC,MAAM,IAAI,IAAIA,WAAU,KAAK,IAAI;AAC9J,QAAI,UAAU,MAAM,QAAQ,UAAU,MAAM,KAAK;AACjD,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,QAAQ,CAAC;AACvB,MAAI,mBAAmB,YAAY,SAAS+I,kBAAiB,SAAS,OAAO;AAC3E,MAAE,SAAS,EAAE,SAAS,UAAU,OAAwC/I,WAAU,OAAO,4CAA4C,IAAIA,WAAU,KAAK,IAAI;AAC5J,QAAI,mBAAmB,WAAW,eAAe,gBAAgB;AACjE,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,wBAAoB;AAAA,EACtB,GAAG,CAAC,qBAAqB,UAAU,UAAU,aAAa,CAAC;AAC3D,EAAA8H,2BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AACxB,UAAI,QAAQ,SAAS;AAErB,UAAI,MAAM,SAAS,WAAW;AAC5B,qBAAa,MAAM,gBAAgB;AACnC,iBAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,kBAAkB,QAAQ,CAAC;AACzC,EAAAA,2BAA0B,SAAS,aAAa;AAC9C,QAAI,SAAS,WAAW,QAAQ,CAAC;AAAA,MAC/B,WAAW;AAAA,MACX,IAAI,SAAS,KAAK;AAAA,MAAC;AAAA,MACnB,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,CAAC,CAAC;AACF,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,uBAAuB,aAAa;AAC3C,SAAO,WAAY;AACjB,QAAI,cAAc,YAAY,WAAW;AACzC,uBAAmB,WAAY;AAC7B,QAAE,YAAY,QAAQ,WAAW,YAAY,UAAU,OAAwC9H,WAAU,OAAO,yDAAyD,IAAIA,WAAU,KAAK,IAAI;AAAA,IAClM,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAI,sBAAsB;AAAA,EACxB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AACT;AAEA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,sBAAsB,QAAQ,oBAAoB,QAAQ,QAAQ,YAAY,CAAC,CAAC;AAEpF,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,QAAQ,aAAa,iBAAiB;AAEtD,MAAI,cAAc,UAAU,cAAc,IAAI;AAC5C,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,QAAQ;AACtB,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuB,QAAQ,QAAQ,aAAa;AAC7D;AAEA,SAAS,4BAA4B4B,YAAW,OAAO;AACrD,MAAI,SAAS,MAAM;AAEnB,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO,uBAAuBA,YAAW,MAAM;AACjD;AAEA,IAAI,6BAA8B,SAAU,IAAI;AAC9C,SAAO,QAAQ,GAAG,sBAAsB,CAAC,EAAE;AAC7C;AAEA,SAAS,UAAU,IAAI;AACrB,SAAO,cAAc,gBAAgB,EAAE,EAAE;AAC3C;AAEA,IAAI,uBAAuB,WAAY;AACrC,MAAI,OAAO;AAEX,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC,MAAM,qBAAqB,uBAAuB;AACpE,MAAI,QAAQ,KAAK,YAAY,SAAU,MAAM;AAC3C,WAAO,QAAQ,QAAQ;AAAA,EACzB,CAAC;AACD,SAAO,SAAS;AAClB,EAAE;AAEF,SAAS,gBAAgB,IAAI,UAAU;AACrC,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,oBAAoB,EAAE,QAAQ,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,eAAe,QAAQ;AACnD;AAEA,SAAS,UAAU,IAAI,UAAU;AAC/B,MAAI,GAAG,SAAS;AACd,WAAO,GAAG,QAAQ,QAAQ;AAAA,EAC5B;AAEA,SAAO,gBAAgB,IAAI,QAAQ;AACrC;AAEA,SAAS,YAAY,WAAW;AAC9B,SAAO,MAAM,WAAW,YAAY,OAAQ,YAAY;AAC1D;AAEA,SAAS,+BAA+B,WAAW,OAAO;AACxD,MAAI,SAAS,MAAM;AAEnB,MAAI,CAAC,UAAU,MAAM,GAAG;AACtB,WAAwC/B,SAAQ,gCAAgC,IAAI;AACpF,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,YAAY,SAAS;AACpC,MAAI,SAAS,UAAU,QAAQ,QAAQ;AAEvC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAwCA,SAAQ,mCAAmC,IAAI;AACvF,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,kCAAkC,WAAW,OAAO;AAC3D,MAAI,SAAS,+BAA+B,WAAW,KAAK;AAE5D,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,aAAa,WAAW,WAAW;AACnD;AAEA,SAAS,cAAc,WAAW,aAAa;AAC7C,MAAI,WAAW,MAAM,UAAU,YAAY,OAAQ,YAAY;AAC/D,MAAI,WAAW,QAAQ,SAAS,iBAAiB,QAAQ,CAAC;AAC1D,MAAI,cAAc,KAAK,UAAU,SAAU,IAAI;AAC7C,WAAO,GAAG,aAAa,UAAU,EAAE,MAAM;AAAA,EAC3C,CAAC;AAED,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,cAAc,WAAW,GAAG;AAC/B,WAAwCA,SAAQ,wCAAwC,IAAI;AAC5F,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO;AAC7B,QAAM,eAAe;AACvB;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,WAAW,KAAK,UAChB,QAAQ,KAAK,OACb,eAAe,KAAK,cACpB,aAAa,KAAK;AAEtB,MAAI,CAAC,aAAa,GAAG;AACnB,QAAI,YAAY;AACd,aAAwCA,SAAQ,+OAA+O,IAAI;AAAA,IACrS;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,OAAO;AACtB,QAAI,YAAY;AACd,aAAwCA,SAAQ,0HAA0H,WAAW,yDAAyD,QAAQ,8GAA8G,IAAI;AAAA,IAC1W;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,MAAM,SAChB,QAAQ,MAAM,OACd,WAAW,MAAM,UACjB,cAAc,MAAM;AAExB,MAAI,QAAQ,UAAU,GAAG;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,SAAS,UAAU,SAAS,WAAW;AAEnD,MAAI,CAAC,OAAO;AACV,WAAwCA,SAAQ,uCAAuC,WAAW,IAAI;AACtG,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,QAAQ,WAAW;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,aAAa,MAAM,SAAS,GAAG,WAAW,GAAG;AAChD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,MAAM,SAChB,YAAY,MAAM,WAClB,QAAQ,MAAM,OACd,WAAW,MAAM,UACjB,cAAc,MAAM,aACpB,kBAAkB,MAAM,iBACxB,cAAc,MAAM;AACxB,MAAI,cAAc,SAAS;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,SAAS,UAAU,QAAQ,WAAW;AAClD,MAAI,KAAK,cAAc,WAAW,MAAM,WAAW,EAAE;AAErD,MAAI,CAAC,IAAI;AACP,WAAwCA,SAAQ,+CAA+C,WAAW,IAAI;AAC9G,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,CAAC,MAAM,QAAQ,8BAA8B,4BAA4B,IAAI,WAAW,GAAG;AAC5G,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,QAAQ,MAAM,mBAAmB,IAAI;AAChD,MAAI,QAAQ;AAEZ,WAAS,6BAA6B;AACpC,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,WAAS,eAAe;AACtB,WAAO,QAAQ,SAAS,IAAI;AAAA,EAC9B;AAEA,WAAS,YAAY,UAAU,WAAW;AACxC,QAAI,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,CAAC,GAAG;AACF,YAAM,SAAS,UAAU,CAAC;AAAA,IAC5B;AAAA,EACF;AAEA,MAAI,0BAA0B,YAAY,KAAK,MAAM,UAAU;AAE/D,WAASsJ,QAAO,MAAM;AACpB,aAAS,YAAY;AACnB,cAAQ,QAAQ;AAChB,cAAQ;AAAA,IACV;AAEA,QAAI,UAAU,YAAY;AACxB,gBAAU;AACV,QAAE,UAAU,cAAc,OAAwCnJ,WAAU,OAAO,0BAA0B,KAAK,IAAIA,WAAU,KAAK,IAAI;AAAA,IAC3I;AAEA,UAAM,SAAS,KAAK,KAAK,cAAc,CAAC;AACxC,YAAQ;AAER,aAAS6D,QAAO,QAAQ,SAAS;AAC/B,UAAI,YAAY,QAAQ;AACtB,kBAAU;AAAA,UACR,sBAAsB;AAAA,QACxB;AAAA,MACF;AAEA,WAAK,QAAQ;AAEb,UAAI,QAAQ,sBAAsB;AAChC,YAAI,SAAS,WAAW,QAAQ,CAAC;AAAA,UAC/B,WAAW;AAAA,UACX,IAAI;AAAA,UACJ,SAAS;AAAA,YACP,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AAAA,QACF,CAAC,CAAC;AACF,mBAAW,MAAM;AAAA,MACnB;AAEA,gBAAU;AACV,YAAM,SAAS,KAAK;AAAA,QAClB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAEA,WAAO,SAAS;AAAA,MACd,UAAU,SAAS,WAAW;AAC5B,eAAO,UAAU;AAAA,UACf,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA,MACA,yBAAyB;AAAA,MACzB,MAAM,SAASsB,MAAK,SAAS;AAC3B,eAAOtB,QAAO,QAAQ,OAAO;AAAA,MAC/B;AAAA,MACA,QAAQ,SAAS,OAAO,SAAS;AAC/B,eAAOA,QAAO,UAAU,OAAO;AAAA,MACjC;AAAA,IACF,GAAG,KAAK,OAAO;AAAA,EACjB;AAEA,WAAS,UAAU,iBAAiB;AAClC,QAAI,SAAS,qBAAQ,SAAU,QAAQ;AACrC,8BAAwB,WAAY;AAClC,eAAO,KAAK;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,QAAI,MAAMsF,QAAO;AAAA,MACf,gBAAgB;AAAA,QACd,IAAI;AAAA,QACJ;AAAA,QACA,cAAc;AAAA,MAChB;AAAA,MACA,SAAS,SAAS,UAAU;AAC1B,eAAO,OAAO,OAAO;AAAA,MACvB;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AACD,WAAO,SAAS,CAAC,GAAG,KAAK;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAEA,WAAS,WAAW;AAClB,QAAI,UAAU;AAAA,MACZ,QAAQ,SAAS,WAAW;AAC1B,eAAO,wBAAwB,MAAM;AAAA,MACvC;AAAA,MACA,WAAW,SAAS,cAAc;AAChC,eAAO,wBAAwB,SAAS;AAAA,MAC1C;AAAA,MACA,UAAU,SAAS,aAAa;AAC9B,eAAO,wBAAwB,QAAQ;AAAA,MACzC;AAAA,MACA,UAAU,SAAS,aAAa;AAC9B,eAAO,wBAAwB,QAAQ;AAAA,MACzC;AAAA,IACF;AACA,WAAOA,QAAO;AAAA,MACZ,gBAAgB;AAAA,QACd,IAAI;AAAA,QACJ,iBAAiB,2BAA2B,EAAE;AAAA,QAC9C,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,eAAe;AACtB,QAAI,gBAAgB,UAAU;AAAA,MAC5B,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAED,QAAI,eAAe;AACjB,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,UAAU;AAAA,IACZ,UAAU,SAAS,WAAW;AAC5B,aAAO,UAAU;AAAA,QACf,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,yBAAyB;AAAA,IACzB;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB,CAAC,gBAAgB,mBAAmB,cAAc;AACvE,SAAS,iBAAiB,OAAO;AAC/B,MAAI,YAAY,MAAM,WAClB,QAAQ,MAAM,OACd,WAAW,MAAM,UACjB,gBAAgB,MAAM,eACtB,uBAAuB,MAAM;AACjC,MAAI,aAAa,CAAC,EAAE,OAAO,uBAAuB,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAC1F,MAAI,cAAU,wBAAS,WAAY;AACjC,WAAO,OAAO;AAAA,EAChB,CAAC,EAAE,CAAC;AACJ,MAAI,iBAAiB,YAAY,SAASC,gBAAe,UAAU,SAAS;AAC1E,QAAI,SAAS,cAAc,CAAC,QAAQ,YAAY;AAC9C,cAAQ,WAAW;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AACZ,EAAAtB,2BAA0B,SAAS,gBAAgB;AACjD,QAAI,WAAW,MAAM,SAAS;AAC9B,QAAI,cAAc,MAAM,UAAU,WAAY;AAC5C,UAAI,UAAU,MAAM,SAAS;AAC7B,qBAAe,UAAU,OAAO;AAChC,iBAAW;AAAA,IACb,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,SAAS,OAAO,cAAc,CAAC;AACnC,EAAAA,2BAA0B,WAAY;AACpC,WAAO,QAAQ;AAAA,EACjB,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,MAAI,aAAa,YAAY,SAAU,aAAa;AAClD,WAAO,SAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,SAAS,UAAU,KAAK,CAAC;AAC7B,MAAI,aAAa,YAAY,SAAU,aAAa,WAAW,SAAS;AACtE,WAAO,SAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB,aAAa,WAAW,QAAQ,cAAc,QAAQ,cAAc;AAAA,IACtE,CAAC;AAAA,EACH,GAAG,CAAC,WAAW,SAAS,UAAU,KAAK,CAAC;AACxC,MAAI,yBAAyB,YAAY,SAAU,OAAO;AACxD,WAAO,kCAAkC,WAAW,KAAK;AAAA,EAC3D,GAAG,CAAC,SAAS,CAAC;AACd,MAAI,0BAA0B,YAAY,SAAU,IAAI;AACtD,QAAI,QAAQ,SAAS,UAAU,SAAS,EAAE;AAC1C,WAAO,QAAQ,MAAM,UAAU;AAAA,EACjC,GAAG,CAAC,SAAS,SAAS,CAAC;AACvB,MAAI,iBAAiB,YAAY,SAASuB,kBAAiB;AACzD,QAAI,CAAC,QAAQ,UAAU,GAAG;AACxB;AAAA,IACF;AAEA,YAAQ,WAAW;AAEnB,QAAI,MAAM,SAAS,EAAE,UAAU,QAAQ;AACrC,YAAM,SAAS,MAAM,CAAC;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,SAAS,KAAK,CAAC;AACnB,MAAI,gBAAgB,YAAY,QAAQ,WAAW,CAAC,OAAO,CAAC;AAC5D,MAAI,MAAMpB,SAAQ,WAAY;AAC5B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,YAAY,YAAY,wBAAwB,yBAAyB,gBAAgB,aAAa,CAAC;AAC3G,yBAAuB,UAAU;AAEjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,eAAW,CAAC,EAAE,GAAG;AAAA,EACnB;AACF;AAEA,IAAI,mBAAmB,SAASqB,kBAAiB,OAAO;AACtD,SAAO;AAAA,IACL,iBAAiB,MAAM;AAAA,IACvB,mBAAmB,MAAM;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,MAAM;AAAA,IACjB,cAAc,MAAM;AAAA,EACtB;AACF;AAEA,SAAS,SAAS,SAAS;AACzB,GAAC,QAAQ,UAAU,OAAwCtJ,WAAU,OAAO,oCAAoC,IAAIA,WAAU,KAAK,IAAI;AACvI,SAAO,QAAQ;AACjB;AAEA,SAAS,IAAI,OAAO;AAClB,MAAI,YAAY,MAAM,WAClB,eAAe,MAAM,cACrB,UAAU,MAAM,SAChB,QAAQ,MAAM,OACduJ,+BAA8B,MAAM;AACxC,MAAI,mBAAe,sBAAO,IAAI;AAC9B,uBAAqB;AACrB,MAAI,eAAe,YAAY,KAAK;AACpC,MAAI,gBAAgB,YAAY,WAAY;AAC1C,WAAO,iBAAiB,aAAa,OAAO;AAAA,EAC9C,GAAG,CAAC,YAAY,CAAC;AACjB,MAAI,WAAW,aAAa,SAAS;AACrC,MAAI,gCAAgC,qBAAqB;AAAA,IACvD;AAAA,IACA,MAAMA;AAAA,EACR,CAAC;AACD,MAAI,eAAe,gBAAgB,WAAW,KAAK;AACnD,MAAI,eAAe,YAAY,SAAU,QAAQ;AAC/C,aAAS,YAAY,EAAE,SAAS,MAAM;AAAA,EACxC,GAAG,CAAC,CAAC;AACL,MAAI,mBAAmBtB,SAAQ,WAAY;AACzC,WAAO,mBAAmB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,YAAY;AAAA,EACjB,GAAG,CAAC,YAAY,CAAC;AACjB,MAAI,WAAW,YAAY;AAC3B,MAAI,mBAAmBA,SAAQ,WAAY;AACzC,WAAO,uBAAuB,UAAU,gBAAgB;AAAA,EAC1D,GAAG,CAAC,UAAU,gBAAgB,CAAC;AAC/B,MAAI,eAAeA,SAAQ,WAAY;AACrC,WAAO,mBAAmB,SAAS;AAAA,MACjC;AAAA,MACA,iBAAiB,iBAAiB;AAAA,IACpC,GAAG,mBAAmB;AAAA,MACpB;AAAA,IACF,GAAG,YAAY,CAAC,CAAC;AAAA,EACnB,GAAG,CAAC,iBAAiB,iBAAiB,YAAY,CAAC;AACnD,MAAI,eAAe,gBAAgB,SAAS;AAC5C,MAAI,QAAQA,SAAQ,WAAY;AAC9B,WAAO9B,aAAY;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,cAAc,kBAAkB,cAAc,eAAe,YAAY,CAAC;AAExF,MAAI,MAAuC;AACzC,QAAI,aAAa,WAAW,aAAa,YAAY,OAAO;AAC1D,aAAwCtG,SAAQ,yBAAyB,IAAI;AAAA,IAC/E;AAAA,EACF;AAEA,eAAa,UAAU;AACvB,MAAI,gBAAgB,YAAY,WAAY;AAC1C,QAAI,UAAU,SAAS,YAAY;AACnC,QAAI,QAAQ,QAAQ,SAAS;AAE7B,QAAI,MAAM,UAAU,QAAQ;AAC1B,cAAQ,SAAS,MAAM,CAAC;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,aAAa,YAAY,WAAY;AACvC,QAAI,QAAQ,SAAS,YAAY,EAAE,SAAS;AAC5C,WAAO,MAAM,cAAc,MAAM,UAAU;AAAA,EAC7C,GAAG,CAAC,CAAC;AACL,MAAI,eAAeoI,SAAQ,WAAY;AACrC,WAAO;AAAA,MACL;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAC9B,eAAa,YAAY;AACzB,MAAI,aAAa,YAAY,SAAU,IAAI;AACzC,WAAO,aAAa,SAAS,YAAY,EAAE,SAAS,GAAG,EAAE;AAAA,EAC3D,GAAG,CAAC,CAAC;AACL,MAAI,uBAAuB,YAAY,WAAY;AACjD,WAAO,kBAAkB,SAAS,YAAY,EAAE,SAAS,CAAC;AAAA,EAC5D,GAAG,CAAC,CAAC;AACL,MAAI,aAAaA,SAAQ,WAAY;AACnC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,kBAAkB,+BAA+B,cAAc,YAAY,sBAAsB,QAAQ,CAAC;AACzH,mBAAiB;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,sBAAsB,MAAM,yBAAyB;AAAA,EACvD,CAAC;AACD,+BAAU,WAAY;AACpB,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,CAAC;AAClB,SAAO,cAAA/H,QAAM,cAAc,WAAW,UAAU;AAAA,IAC9C,OAAO;AAAA,EACT,GAAG,cAAAA,QAAM,cAAc,kBAAU;AAAA,IAC/B,SAAS;AAAA,IACT;AAAA,EACF,GAAG,MAAM,QAAQ,CAAC;AACpB;AAEA,IAAI,UAAU;AACd,SAAS,UAAU;AACjB,YAAU;AACZ;AACA,SAAS,mBAAmB;AAC1B,SAAO+H,SAAQ,WAAY;AACzB,WAAO,KAAK;AAAA,EACd,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,qBAAqB;AAC5B,UAAQ;AACR,QAAM;AACR;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,YAAY,iBAAiB;AACjC,MAAIsB,+BAA8B,MAAM,+BAA+B,OAAO;AAC9E,SAAO,cAAArJ,QAAM,cAAc,eAAe,MAAM,SAAU,cAAc;AACtE,WAAO,cAAAA,QAAM,cAAc,KAAK;AAAA,MAC9B,OAAO,MAAM;AAAA,MACb;AAAA,MACA;AAAA,MACA,6BAA6BqJ;AAAA,MAC7B,sBAAsB,MAAM;AAAA,MAC5B,SAAS,MAAM;AAAA,MACf,iBAAiB,MAAM;AAAA,MACvB,mBAAmB,MAAM;AAAA,MACzB,aAAa,MAAM;AAAA,MACnB,cAAc,MAAM;AAAA,MACpB,WAAW,MAAM;AAAA,IACnB,GAAG,MAAM,QAAQ;AAAA,EACnB,CAAC;AACH;AAEA,IAAI,YAAY,SAASxI,SAAQ,MAAM;AACrC,SAAO,SAAU,OAAO;AACtB,WAAO,SAAS;AAAA,EAClB;AACF;AAEA,IAAI,WAAW,UAAU,QAAQ;AACjC,IAAI,SAAS,UAAU,MAAM;AAC7B,IAAI,cAAc,UAAU,SAAS;AAErC,IAAI,WAAW,SAASyI,UAAS,UAAU,IAAI;AAC7C,SAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AACxD;AAEA,IAAI,SAAS,SAASC,QAAO,UAAU,IAAI;AACzC,SAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AACxD;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,IAAI;AACzD,MAAIC,SAAQ,OAAO,iBAAiB,EAAE;AACtC,MAAI,WAAW;AAAA,IACb,WAAWA,OAAM;AAAA,IACjB,WAAWA,OAAM;AAAA,EACnB;AACA,SAAO,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,MAAM;AAClE;AAEA,IAAI,mBAAmB,SAASC,oBAAmB;AACjD,MAAI,OAAuC;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,eAAe;AAC1B,MAAI,OAAO,SAAS;AACpB,GAAC,OAAO,OAAwC5J,WAAU,KAAK,IAAIA,WAAU,KAAK,IAAI;AAEtF,MAAI,CAAC,oBAAoB,IAAI,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,OAAO,iBAAiB,IAAI;AAC5C,MAAI,eAAe;AAAA,IACjB,WAAW,UAAU;AAAA,IACrB,WAAW,UAAU;AAAA,EACvB;AAEA,MAAI,OAAO,cAAc,WAAW,GAAG;AACrC,WAAO;AAAA,EACT;AAEA,SAAwCH,SAAQ,ykBAAykB,IAAI;AAC7nB,SAAO;AACT;AAEA,IAAI,uBAAuB,SAASgK,sBAAqB,IAAI;AAC3D,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,MAAM;AACxB,WAAO,iBAAiB,IAAI,KAAK;AAAA,EACnC;AAEA,MAAI,OAAO,SAAS,iBAAiB;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,oBAAoB,EAAE,GAAG;AAC5B,WAAOA,sBAAqB,GAAG,aAAa;AAAA,EAC9C;AAEA,SAAO;AACT;AAEA,IAAI,iCAAkC,SAAU,YAAY;AAC1D,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AAEA,MAAI,sBAAsB,qBAAqB,WAAW,aAAa;AAEvE,MAAI,CAAC,qBAAqB;AACxB;AAAA,EACF;AAEA,SAAwChK,SAAQ,2TAA2T,IAAI;AACjX;AAEA,IAAI,cAAe,SAAU,IAAI;AAC/B,SAAO;AAAA,IACL,GAAG,GAAG;AAAA,IACN,GAAG,GAAG;AAAA,EACR;AACF;AAEA,IAAI,aAAa,SAASiK,YAAW,IAAI;AACvC,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAEA,MAAIH,SAAQ,OAAO,iBAAiB,EAAE;AAEtC,MAAIA,OAAM,aAAa,SAAS;AAC9B,WAAO;AAAA,EACT;AAEA,SAAOG,YAAW,GAAG,aAAa;AACpC;AAEA,IAAI,SAAU,SAAUzJ,QAAO;AAC7B,MAAI,oBAAoB,qBAAqBA,MAAK;AAClD,MAAI,gBAAgB,WAAWA,MAAK;AACpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,wBAAyB,SAAU,MAAM;AAC3C,MAAI,aAAa,KAAK,YAClB,YAAY,KAAK,WACjB,mBAAmB,KAAK,kBACxB,gBAAgB,KAAK,eACrB,YAAY,KAAK,WACjB,SAAS,KAAK,QACd,OAAO,KAAK,MACZc,WAAU,KAAK;AAEnB,MAAI,QAAQ,WAAY;AACtB,QAAI,CAACA,UAAS;AACZ,aAAO;AAAA,IACT;AAEA,QAAI,aAAaA,SAAQ,YACrB,cAAcA,SAAQ;AAC1B,QAAI,YAAY,aAAa;AAAA,MAC3B,cAAc,WAAW;AAAA,MACzB,aAAa,WAAW;AAAA,MACxB,QAAQ,YAAY,WAAW;AAAA,MAC/B,OAAO,YAAY,WAAW;AAAA,IAChC,CAAC;AACD,WAAO;AAAA,MACL,eAAeA,SAAQ,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,mBAAmBA,SAAQ;AAAA,MAC3B,QAAQ;AAAA,QACN,SAASA,SAAQ;AAAA,QACjB,SAASA,SAAQ;AAAA,QACjB,KAAK;AAAA,QACL,MAAM;AAAA,UACJ,OAAO;AAAA,UACP,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE;AAEF,MAAI,OAAO,cAAc,aAAa,WAAW;AACjD,MAAI,UAAU,WAAW;AAAA,IACvB;AAAA,IACA,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,YAAY,SAAS4I,WAAU,WAAW,mBAAmB;AAC/D,MAAI,OAAO,OAAO,SAAS;AAE3B,MAAI,CAAC,mBAAmB;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,mBAAmB;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK,WAAW,MAAM,kBAAkB;AAClD,MAAI,OAAO,KAAK,WAAW,OAAO,kBAAkB;AACpD,MAAI,SAAS,MAAM,kBAAkB;AACrC,MAAI,QAAQ,OAAO,kBAAkB;AACrC,MAAI,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,YAAY,OAAO,YAAY,KAAK,MAAM;AAC9C,MAAI,SAAS,UAAU;AAAA,IACrB;AAAA,IACA,QAAQ,KAAK;AAAA,IACb,QAAQ,KAAK;AAAA,IACb,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAEA,IAAI,eAAgB,SAAU,MAAM;AAClC,MAAI7B,OAAM,KAAK,KACX,aAAa,KAAK,YAClB,MAAM,KAAK,KACX,eAAe,KAAK,cACpB,YAAY,KAAK,WACjB,iBAAiB,KAAK,gBACtB,mBAAmB,KAAK,kBACxB,oBAAoB,KAAK;AAC7B,MAAI,oBAAoB,IAAI;AAC5B,MAAI,SAAS,UAAUA,MAAK,iBAAiB;AAC7C,MAAI,OAAO,WAAW,QAAQ,YAAY;AAE1C,MAAI/G,WAAU,WAAY;AACxB,QAAI,CAAC,mBAAmB;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,OAAO,iBAAiB;AAC1C,QAAI,aAAa;AAAA,MACf,cAAc,kBAAkB;AAAA,MAChC,aAAa,kBAAkB;AAAA,IACjC;AACA,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,MAAM,WAAW,aAAa,YAAY;AAAA,MAC1C,QAAQ,YAAY,iBAAiB;AAAA,MACrC;AAAA,MACA;AAAA,IACF;AAAA,EACF,EAAE;AAEF,MAAI,YAAY,sBAAsB;AAAA,IACpC;AAAA,IACA,WAAW,CAAC;AAAA,IACZ;AAAA,IACA,eAAe,IAAI;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAASA;AAAA,EACX,CAAC;AACD,SAAO;AACT;AAEA,IAAI,YAAY;AAAA,EACd,SAAS;AACX;AACA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AACA,IAAI,qBAAsB,SAAU,SAAS;AAC3C,SAAO,QAAQ,2BAA2B,YAAY;AACxD;AAEA,SAAS,mBAAmB,SAAS;AACnC,MAAI,aAAS,0BAAW,OAAO;AAC/B,GAAC,SAAS,OAAwCnB,WAAU,OAAO,iCAAiC,IAAIA,WAAU,KAAK,IAAI;AAC3H,SAAO;AACT;AAEA,IAAI,+BAA+B,SAASgK,8BAA6B,UAAU;AACjF,SAAO,YAAY,SAAS,IAAI,qBAAqB;AACvD;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,uBAAmB,sBAAO,IAAI;AAClC,MAAI,aAAa,mBAAmB,UAAU;AAC9C,MAAI,WAAW,YAAY,WAAW;AACtC,MAAI,WAAW,WAAW,UACtB,UAAU,WAAW;AACzB,MAAI,cAAc,YAAY,IAAI;AAClC,MAAI,aAAa/B,SAAQ,WAAY;AACnC,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,IACb;AAAA,EACF,GAAG,CAAC,KAAK,aAAa,KAAK,MAAM,KAAK,IAAI,CAAC;AAC3C,MAAI,6BAAyB,sBAAO,UAAU;AAC9C,MAAI,uBAAuBA,SAAQ,WAAY;AAC7C,WAAO,wBAAW,SAAU,GAAG,GAAG;AAChC,OAAC,iBAAiB,UAAU,OAAwCjI,WAAU,OAAO,sCAAsC,IAAIA,WAAU,KAAK,IAAI;AAClJ,UAAIwB,UAAS;AAAA,QACX;AAAA,QACA;AAAA,MACF;AACA,cAAQ,sBAAsB,WAAW,IAAIA,OAAM;AAAA,IACrD,CAAC;AAAA,EACH,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC;AAC3B,MAAI,mBAAmB,YAAY,WAAY;AAC7C,QAAI,WAAW,iBAAiB;AAEhC,QAAI,CAAC,YAAY,CAAC,SAAS,IAAI,mBAAmB;AAChD,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,SAAS,IAAI,iBAAiB;AAAA,EACnD,GAAG,CAAC,CAAC;AACL,MAAI,eAAe,YAAY,WAAY;AACzC,QAAIA,UAAS,iBAAiB;AAC9B,yBAAqBA,QAAO,GAAGA,QAAO,CAAC;AAAA,EACzC,GAAG,CAAC,kBAAkB,oBAAoB,CAAC;AAC3C,MAAI,uBAAuByG,SAAQ,WAAY;AAC7C,WAAO,qBAAQ,YAAY;AAAA,EAC7B,GAAG,CAAC,YAAY,CAAC;AACjB,MAAI,kBAAkB,YAAY,WAAY;AAC5C,QAAI,WAAW,iBAAiB;AAChC,QAAI9G,WAAU,6BAA6B,QAAQ;AACnD,MAAE,YAAYA,YAAW,OAAwCnB,WAAU,OAAO,+CAA+C,IAAIA,WAAU,KAAK,IAAI;AACxJ,QAAI,UAAU,SAAS;AAEvB,QAAI,QAAQ,0BAA0B;AACpC,mBAAa;AACb;AAAA,IACF;AAEA,yBAAqB;AAAA,EACvB,GAAG,CAAC,sBAAsB,YAAY,CAAC;AACvC,MAAI,6BAA6B,YAAY,SAAU,cAAc,SAAS;AAC5E,KAAC,CAAC,iBAAiB,UAAU,OAAwCA,WAAU,OAAO,sDAAsD,IAAIA,WAAU,KAAK,IAAI;AACnK,QAAI,WAAW,YAAY;AAC3B,QAAIkI,OAAM,SAAS,gBAAgB;AACnC,KAACA,OAAM,OAAwClI,WAAU,OAAO,wCAAwC,IAAIA,WAAU,KAAK,IAAI;AAC/H,QAAI,MAAM,OAAOkI,IAAG;AACpB,QAAI,WAAW;AAAA,MACb,KAAKA;AAAA,MACL;AAAA,MACA;AAAA,MACA,eAAe;AAAA,IACjB;AACA,qBAAiB,UAAU;AAC3B,QAAI,YAAY,aAAa;AAAA,MAC3B,KAAKA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,SAAS;AAAA,MACpB,gBAAgB,SAAS;AAAA,MACzB,kBAAkB,SAAS;AAAA,MAC3B,mBAAmB,CAAC,SAAS;AAAA,IAC/B,CAAC;AACD,QAAI,aAAa,IAAI;AAErB,QAAI,YAAY;AACd,iBAAW,aAAa,gBAAgB,WAAW,WAAW,SAAS;AACvE,iBAAW,iBAAiB,UAAU,iBAAiB,mBAAmB,SAAS,aAAa,CAAC;AAEjG,UAAI,MAAuC;AACzC,uCAA+B,UAAU;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,WAAW,YAAY,iBAAiB,WAAW,CAAC;AACnE,MAAI,yBAAyB,YAAY,WAAY;AACnD,QAAI,WAAW,iBAAiB;AAChC,QAAI/G,WAAU,6BAA6B,QAAQ;AACnD,MAAE,YAAYA,YAAW,OAAwCnB,WAAU,OAAO,iFAAiF,IAAIA,WAAU,KAAK,IAAI;AAC1L,WAAO,YAAYmB,QAAO;AAAA,EAC5B,GAAG,CAAC,CAAC;AACL,MAAI,cAAc,YAAY,WAAY;AACxC,QAAI,WAAW,iBAAiB;AAChC,KAAC,WAAW,OAAwCnB,WAAU,OAAO,sCAAsC,IAAIA,WAAU,KAAK,IAAI;AAClI,QAAImB,WAAU,6BAA6B,QAAQ;AACnD,qBAAiB,UAAU;AAE3B,QAAI,CAACA,UAAS;AACZ;AAAA,IACF;AAEA,yBAAqB,OAAO;AAC5B,IAAAA,SAAQ,gBAAgB,gBAAgB,SAAS;AACjD,IAAAA,SAAQ,oBAAoB,UAAU,iBAAiB,mBAAmB,SAAS,aAAa,CAAC;AAAA,EACnG,GAAG,CAAC,iBAAiB,oBAAoB,CAAC;AAC1C,MAAIK,UAAS,YAAY,SAAU,QAAQ;AACzC,QAAI,WAAW,iBAAiB;AAChC,KAAC,WAAW,OAAwCxB,WAAU,OAAO,qCAAqC,IAAIA,WAAU,KAAK,IAAI;AACjI,QAAImB,WAAU,6BAA6B,QAAQ;AACnD,KAACA,WAAU,OAAwCnB,WAAU,OAAO,sDAAsD,IAAIA,WAAU,KAAK,IAAI;AACjJ,IAAAmB,SAAQ,aAAa,OAAO;AAC5B,IAAAA,SAAQ,cAAc,OAAO;AAAA,EAC/B,GAAG,CAAC,CAAC;AACL,MAAI,YAAY8G,SAAQ,WAAY;AAClC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQzG;AAAA,IACV;AAAA,EACF,GAAG,CAAC,aAAa,4BAA4B,wBAAwBA,OAAM,CAAC;AAC5E,MAAI,QAAQyG,SAAQ,WAAY;AAC9B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,QAAQ,CAAC;AACpC,EAAAH,2BAA0B,WAAY;AACpC,2BAAuB,UAAU,MAAM;AACvC,aAAS,UAAU,SAAS,KAAK;AACjC,WAAO,WAAY;AACjB,UAAI,iBAAiB,SAAS;AAC5B,eAAwCjI,SAAQ,4EAA4E,IAAI;AAChI,oBAAY;AAAA,MACd;AAEA,eAAS,UAAU,WAAW,KAAK;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,aAAa,OAAO,SAAS,SAAS,SAAS,CAAC;AAC3E,EAAAiI,2BAA0B,WAAY;AACpC,QAAI,CAAC,iBAAiB,SAAS;AAC7B;AAAA,IACF;AAEA,YAAQ,yBAAyB,uBAAuB,QAAQ,IAAI,CAAC,KAAK,cAAc;AAAA,EAC1F,GAAG,CAAC,KAAK,gBAAgB,OAAO,CAAC;AACjC,EAAAA,2BAA0B,WAAY;AACpC,QAAI,CAAC,iBAAiB,SAAS;AAC7B;AAAA,IACF;AAEA,YAAQ,gCAAgC,uBAAuB,QAAQ,IAAI,KAAK,gBAAgB;AAAA,EAClG,GAAG,CAAC,KAAK,kBAAkB,OAAO,CAAC;AACrC;AAEA,SAAS,SAAS;AAAC;AAEnB,IAAI,QAAQ;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQvG;AACV;AAEA,IAAI,UAAU,SAAS0I,SAAQ,MAAM;AACnC,MAAI,yBAAyB,KAAK,wBAC9BC,eAAc,KAAK,aACnB,UAAU,KAAK;AAEnB,MAAI,wBAAwB;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,SAAS;AACvB,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,QAAQA,aAAY,OAAO,UAAU;AAAA,IACrC,OAAOA,aAAY,OAAO,UAAU;AAAA,IACpC,QAAQA,aAAY,OAAO;AAAA,EAC7B;AACF;AAEA,IAAI,WAAW,SAASC,UAAS,OAAO;AACtC,MAAI,yBAAyB,MAAM,wBAC/BD,eAAc,MAAM,aACpB,UAAU,MAAM;AACpB,MAAI,OAAO,QAAQ;AAAA,IACjB;AAAA,IACA,aAAaA;AAAA,IACb;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,SAASA,aAAY;AAAA,IACrB,WAAW;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,WAAW,KAAK,OAAO;AAAA,IACvB,aAAa,KAAK,OAAO;AAAA,IACzB,cAAc,KAAK,OAAO;AAAA,IAC1B,YAAY,KAAK,OAAO;AAAA,IACxB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY,YAAY,SAAS,YAAY,cAAc;AAAA,EAC7D;AACF;AAEA,SAAS,YAAY,OAAO;AAC1B,MAAI,0BAAsB,sBAAO,IAAI;AACrC,MAAI,2BAA2B,YAAY,WAAY;AACrD,QAAI,CAAC,oBAAoB,SAAS;AAChC;AAAA,IACF;AAEA,iBAAa,oBAAoB,OAAO;AACxC,wBAAoB,UAAU;AAAA,EAChC,GAAG,CAAC,CAAC;AACL,MAAI,UAAU,MAAM,SAChB,kBAAkB,MAAM,iBACxB,UAAU,MAAM,SAChB,YAAY,MAAM;AAEtB,MAAI,gBAAY,wBAAS,MAAM,YAAY,MAAM,GAC7C,yBAAyB,UAAU,CAAC,GACpC,4BAA4B,UAAU,CAAC;AAE3C,+BAAU,WAAY;AACpB,QAAI,CAAC,wBAAwB;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,QAAQ;AACtB,+BAAyB;AACzB,gCAA0B,KAAK;AAC/B,aAAO;AAAA,IACT;AAEA,QAAI,oBAAoB,SAAS;AAC/B,aAAO;AAAA,IACT;AAEA,wBAAoB,UAAU,WAAW,WAAY;AACnD,0BAAoB,UAAU;AAC9B,gCAA0B,KAAK;AAAA,IACjC,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,SAAS,wBAAwB,wBAAwB,CAAC;AAC9D,MAAI,kBAAkB,YAAY,SAAU,OAAO;AACjD,QAAI,MAAM,iBAAiB,UAAU;AACnC;AAAA,IACF;AAEA,oBAAgB;AAEhB,QAAI,YAAY,SAAS;AACvB,cAAQ;AAAA,IACV;AAAA,EACF,GAAG,CAAC,SAAS,SAAS,eAAe,CAAC;AACtC,MAAIP,SAAQ,SAAS;AAAA,IACnB;AAAA,IACA,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,EACrB,CAAC;AACD,SAAO,cAAAzJ,QAAM,cAAc,MAAM,YAAY,SAAS;AAAA,IACpD,OAAOyJ;AAAA,IACP,mCAAmC;AAAA,IACnC,iBAAiB;AAAA,IACjB,KAAK,MAAM;AAAA,EACb,CAAC;AACH;AAEA,IAAI,gBAAgB,cAAAzJ,QAAM,KAAK,WAAW;AAE1C,IAAI,mBAAmB,cAAAA,QAAM,cAAc,IAAI;AAE/C,SAAS,qBAAqB,IAAI;AAChC,IAAE,MAAM,cAAc,EAAE,KAAK,OAAwCF,WAAU,OAAO,iPAAiP,IAAIA,WAAU,KAAK,IAAI;AAChW;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,UAAU,MAAM,QAAQ;AAC/B,SAAO,QAAQ,SAAU,OAAO;AAC9B,WAAO,MAAM,IAAI;AAAA,EACnB,CAAC;AACH;AAEA,IAAI,SAAS,CAAC,SAAS,SAAS,MAAM;AACpC,MAAI,QAAQ,KAAK;AACjB,GAAC,MAAM,cAAc,OAAwCA,WAAU,OAAO,yCAAyC,IAAIA,WAAU,KAAK,IAAI;AAC9I,IAAE,OAAO,MAAM,gBAAgB,YAAY,OAAwCA,WAAU,OAAO,6DAA6D,OAAO,MAAM,cAAc,GAAG,IAAIA,WAAU,KAAK,IAAI;AACxN,GAAG,SAAS,SAAS,OAAO;AAC1B,MAAI,QAAQ,MAAM;AAClB,GAAC,UAAU,MAAM,cAAc,IAAI,OAAwCA,WAAU,OAAO,kCAAkC,IAAIA,WAAU,KAAK,IAAI;AACrJ,GAAC,UAAU,MAAM,gBAAgB,IAAI,OAAwCA,WAAU,OAAO,oCAAoC,IAAIA,WAAU,KAAK,IAAI;AACzJ,GAAC,UAAU,MAAM,uBAAuB,IAAI,OAAwCA,WAAU,OAAO,2CAA2C,IAAIA,WAAU,KAAK,IAAI;AACzK,GAAG,SAAS,IAAI,OAAO;AACrB,MAAI,kBAAkB,MAAM;AAC5B,uBAAqB,gBAAgB,CAAC;AACxC,CAAC;AACD,IAAI,WAAW,CAAC,SAAS,YAAY,OAAO;AAC1C,MAAI,QAAQ,MAAM,OACd,oBAAoB,MAAM;AAE9B,MAAI,CAAC,MAAM,aAAa;AACtB;AAAA,EACF;AAEA,MAAIkI,OAAM,kBAAkB;AAE5B,MAAIA,MAAK;AACP;AAAA,EACF;AAEA,SAAwCrI,SAAQ,kDAAmD,MAAM,cAAc,qRAAsR,IAAI;AACnZ,CAAC;AACD,IAAI,UAAU,CAAC,SAAS,SAAS,OAAO;AACtC,MAAI,QAAQ,MAAM;AAClB,GAAC,MAAM,cAAc,OAAwCG,WAAU,OAAO,sEAAsE,IAAIA,WAAU,KAAK,IAAI;AAC7K,GAAG,SAAS,iBAAiB,OAAO;AAClC,MAAI,oBAAoB,MAAM;AAC9B,GAAC,CAAC,kBAAkB,IAAI,OAAwCA,WAAU,OAAO,iDAAiD,IAAIA,WAAU,KAAK,IAAI;AAC3J,CAAC;AACD,SAAS,cAAc,MAAM;AAC3B,qBAAmB,WAAY;AAC7B,cAAU,MAAM,MAAM;AAEtB,QAAI,KAAK,MAAM,SAAS,YAAY;AAClC,gBAAU,MAAM,QAAQ;AAAA,IAC1B;AAEA,QAAI,KAAK,MAAM,SAAS,WAAW;AACjC,gBAAU,MAAM,OAAO;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,eAAe,SAAU,sBAAsB;AACjD,iBAAeoK,eAAc,oBAAoB;AAEjD,WAASA,gBAAe;AACtB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,qBAAqB,KAAK,MAAM,sBAAsB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AACtF,UAAM,QAAQ;AAAA,MACZ,WAAW,QAAQ,MAAM,MAAM,EAAE;AAAA,MACjC,MAAM,MAAM,MAAM;AAAA,MAClB,SAAS,MAAM,MAAM,iBAAiB,MAAM,MAAM,KAAK,SAAS;AAAA,IAClE;AAEA,UAAM,UAAU,WAAY;AAC1B,UAAI,MAAM,MAAM,YAAY,SAAS;AACnC;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAEA,EAAAA,cAAa,2BAA2B,SAAS,yBAAyB,OAAO,OAAO;AACtF,QAAI,CAAC,MAAM,eAAe;AACxB,aAAO;AAAA,QACL,WAAW,QAAQ,MAAM,EAAE;AAAA,QAC3B,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,MAAM,IAAI;AACZ,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,MAAM,WAAW;AACnB,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,SAASA,cAAa;AAE1B,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,MAAM,WAAW;AACzB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AAAA,MACb,SAAS,KAAK;AAAA,MACd,MAAM,KAAK,MAAM;AAAA,MACjB,SAAS,KAAK,MAAM;AAAA,IACtB;AACA,WAAO,KAAK,MAAM,SAAS,QAAQ;AAAA,EACrC;AAEA,SAAOA;AACT,EAAE,cAAAlK,QAAM,aAAa;AAErB,IAAI,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,eAAe;AACjB;AAEA,IAAI,wBAAwB,SAASmK,uBAAsB,2BAA2B,UAAU;AAC9F,MAAI,UAAU;AACZ,WAAO,YAAY,KAAK,SAAS,QAAQ;AAAA,EAC3C;AAEA,MAAI,2BAA2B;AAC7B,WAAO,YAAY;AAAA,EACrB;AAEA,SAAO,YAAY;AACrB;AAEA,IAAI,qBAAqB,SAASC,oBAAmB,aAAa,iBAAiB;AACjF,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,SAAO,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;AAClE;AAEA,IAAI,2BAA2B,SAASC,0BAAyB,UAAU;AACzE,MAAI,SAAS,sBAAsB,MAAM;AACvC,WAAO,SAAS;AAAA,EAClB;AAEA,SAAO,SAAS,SAAS;AAC3B;AAEA,SAAS,iBAAiB,UAAU;AAClC,MAAI,YAAY,SAAS;AACzB,MAAI,MAAM,UAAU;AACpB,MAAIvH,UAAS,SAAS,QAClB,cAAc,SAAS,aACvB,WAAW,SAAS;AACxB,MAAI,cAAc,QAAQ,WAAW;AACrC,MAAI,gBAAgB,yBAAyB,QAAQ;AACrD,MAAI,kBAAkB,QAAQ,QAAQ;AACtC,MAAI,YAAY,kBAAkB,WAAW,KAAKA,SAAQ,WAAW,IAAI,WAAW,OAAOA,OAAM;AACjG,MAAI2G,SAAQ;AAAA,IACV,UAAU;AAAA,IACV,KAAK,IAAI,UAAU;AAAA,IACnB,MAAM,IAAI,UAAU;AAAA,IACpB,WAAW;AAAA,IACX,OAAO,IAAI,UAAU;AAAA,IACrB,QAAQ,IAAI,UAAU;AAAA,IACtB,YAAY,sBAAsB,eAAe,QAAQ;AAAA,IACzD;AAAA,IACA,SAAS,mBAAmB,aAAa,eAAe;AAAA,IACxD,QAAQ,kBAAkB,cAAc,gBAAgB,cAAc;AAAA,IACtE,eAAe;AAAA,EACjB;AACA,SAAOA;AACT;AAEA,SAAS,kBAAkB,WAAW;AACpC,SAAO;AAAA,IACL,WAAW,WAAW,OAAO,UAAU,MAAM;AAAA,IAC7C,YAAY,UAAU,4BAA4B,OAAO;AAAA,EAC3D;AACF;AAEA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,SAAS,aAAa,iBAAiB,MAAM,IAAI,kBAAkB,MAAM;AACzF;AAEA,SAAS,eAAe,YAAY,IAAI,cAAc;AACpD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,iBAAiB,OAAO,iBAAiB,EAAE;AAC/C,MAAI,YAAY,GAAG,sBAAsB;AACzC,MAAI,SAAS,aAAa,WAAW,cAAc;AACnD,MAAI,OAAO,WAAW,QAAQ,YAAY;AAC1C,MAAIO,eAAc;AAAA,IAChB;AAAA,IACA,SAAS,GAAG,QAAQ,YAAY;AAAA,IAChC,SAAS,eAAe;AAAA,EAC1B;AACA,MAAI,aAAa;AAAA,IACf,GAAG,OAAO,UAAU;AAAA,IACpB,GAAG,OAAO,UAAU;AAAA,EACtB;AACA,MAAI,YAAY;AAAA,IACd;AAAA,IACA,aAAaA;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,WAAW,YAAY,WAAW;AACtC,MAAI,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,kBAAkB,KAAK,iBACvB,6BAA6B,KAAK,4BAClC,0BAA0B,KAAK,yBAC/B,YAAY,KAAK;AACrB,MAAI,UAAUjC,SAAQ,WAAY;AAChC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,4BAA4B,WAAW,uBAAuB,CAAC;AACnE,MAAIuC,gBAAe,YAAY,SAAU,cAAc;AACrD,QAAI,KAAK,gBAAgB;AACzB,KAAC,KAAK,OAAwCxK,WAAU,OAAO,yCAAyC,IAAIA,WAAU,KAAK,IAAI;AAC/H,WAAO,eAAe,YAAY,IAAI,YAAY;AAAA,EACpD,GAAG,CAAC,YAAY,eAAe,CAAC;AAChC,MAAI,QAAQiI,SAAQ,WAAY;AAC9B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAcuC;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,YAAYA,eAAc,SAAS,QAAQ,CAAC;AAChD,MAAI,mBAAe,sBAAO,KAAK;AAC/B,MAAI,wBAAoB,sBAAO,IAAI;AACnC,EAAA1C,2BAA0B,WAAY;AACpC,aAAS,UAAU,SAAS,aAAa,OAAO;AAChD,WAAO,WAAY;AACjB,aAAO,SAAS,UAAU,WAAW,aAAa,OAAO;AAAA,IAC3D;AAAA,EACF,GAAG,CAAC,SAAS,SAAS,CAAC;AACvB,EAAAA,2BAA0B,WAAY;AACpC,QAAI,kBAAkB,SAAS;AAC7B,wBAAkB,UAAU;AAC5B;AAAA,IACF;AAEA,QAAI,OAAO,aAAa;AACxB,iBAAa,UAAU;AACvB,aAAS,UAAU,OAAO,OAAO,IAAI;AAAA,EACvC,GAAG,CAAC,OAAO,SAAS,SAAS,CAAC;AAChC;AAEA,SAAS,gBAAgB,OAAO,WAAW,QAAQ;AACjD,qBAAmB,WAAY;AAC7B,aAAS/H,QAAO0K,KAAI;AAClB,aAAO,mBAAmBA,MAAK;AAAA,IACjC;AAEA,QAAI,KAAK,MAAM;AACf,KAAC,KAAK,OAAwCzK,WAAU,OAAO,kCAAkC,IAAIA,WAAU,KAAK,IAAI;AACxH,MAAE,OAAO,OAAO,YAAY,OAAwCA,WAAU,OAAO,wEAAwE,OAAO,KAAK,eAAe,KAAK,GAAG,IAAIA,WAAU,KAAK,IAAI;AACvN,KAAC,UAAU,MAAM,KAAK,IAAI,OAAwCA,WAAU,OAAOD,QAAO,EAAE,IAAI,iCAAiC,IAAIC,WAAU,KAAK,IAAI;AAExJ,QAAI,MAAM,OAAO,SAAS,YAAY;AACpC;AAAA,IACF;AAEA,yBAAqB,OAAO,CAAC;AAE7B,QAAI,MAAM,WAAW;AACnB,OAAC,eAAe,WAAW,EAAE,IAAI,OAAwCA,WAAU,OAAOD,QAAO,EAAE,IAAI,6BAA6B,IAAIC,WAAU,KAAK,IAAI;AAAA,IAC7J;AAAA,EACF,CAAC;AACH;AACA,SAAS,uBAAuB,SAAS;AACvC,SAAO,WAAY;AACjB,QAAI,iBAAa,sBAAO,OAAO;AAC/B,uBAAmB,WAAY;AAC7B,QAAE,YAAY,WAAW,WAAW,OAAwCA,WAAU,OAAO,4DAA4D,IAAIA,WAAU,KAAK,IAAI;AAAA,IAClL,GAAG,CAAC,OAAO,CAAC;AAAA,EACd,CAAC;AACH;AAEA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,eAAe;AACvB;AAEA,SAAS,UAAU,OAAO;AACxB,MAAIkI,WAAM,sBAAO,IAAI;AACrB,MAAI,SAAS,YAAY,SAAU,IAAI;AACrC,IAAAA,KAAI,UAAU;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,SAAS,YAAY,WAAY;AACnC,WAAOA,KAAI;AAAA,EACb,GAAG,CAAC,CAAC;AAEL,MAAI,sBAAsB,mBAAmB,UAAU,GACnD,YAAY,oBAAoB,WAChC,gCAAgC,oBAAoB,+BACpD,WAAW,oBAAoB;AAEnC,MAAI,uBAAuB,mBAAmB,gBAAgB,GAC1D,OAAO,qBAAqB,MAC5B,cAAc,qBAAqB;AAEvC,MAAI,aAAaD,SAAQ,WAAY;AACnC,WAAO;AAAA,MACL,IAAI,MAAM;AAAA,MACV,OAAO,MAAM;AAAA,MACb;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,aAAa,MAAM,OAAO,MAAM,WAAW,CAAC;AACtD,MAAI,WAAW,MAAM,UACjB,cAAc,MAAM,aACpB,YAAY,MAAM,WAClB,0BAA0B,MAAM,yBAChC,6BAA6B,MAAM,4BACnC,UAAU,MAAM,SAChB,SAAS,MAAM,QACf,8BAA8B,MAAM;AACxC,kBAAgB,OAAO,WAAW,MAAM;AACxC,yBAAuB,OAAO;AAE9B,MAAI,CAAC,SAAS;AACZ,QAAI,eAAeA,SAAQ,WAAY;AACrC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,GAAG,CAAC,YAAY,UAAU,QAAQ,4BAA4B,yBAAyB,SAAS,CAAC;AACjG,0BAAsB,YAAY;AAAA,EACpC;AAEA,MAAI,kBAAkBA,SAAQ,WAAY;AACxC,WAAO,YAAY;AAAA,MACjB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,oBAAoB;AAAA,MACpB,qCAAqC;AAAA,MACrC,mCAAmC;AAAA,MACnC,WAAW;AAAA,MACX,aAAa;AAAA,IACf,IAAI;AAAA,EACN,GAAG,CAAC,WAAW,+BAA+B,aAAa,SAAS,CAAC;AACrE,MAAI,YAAY,YAAY,SAAU,OAAO;AAC3C,QAAI,OAAO,SAAS,YAAY;AAC9B;AAAA,IACF;AAEA,QAAI,CAAC,OAAO,UAAU;AACpB;AAAA,IACF;AAEA,QAAI,MAAM,iBAAiB,aAAa;AACtC;AAAA,IACF;AAEA,gCAA4B;AAAA,EAC9B,GAAG,CAAC,6BAA6B,MAAM,CAAC;AACxC,MAAI,WAAWA,SAAQ,WAAY;AACjC,QAAI0B,SAAQ,WAAW,MAAM;AAC7B,QAAI,kBAAkB,OAAO,SAAS,cAAc,OAAO,WAAW,YAAY;AAClF,QAAI,SAAS;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,iCAAiC;AAAA,QACjC,yBAAyB;AAAA,QACzB,OAAOA;AAAA,QACP;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,iBAAiB,aAAa,QAAQ,WAAW,MAAM,CAAC;AACvE,MAAI,SAAS1B,SAAQ,WAAY;AAC/B,WAAO;AAAA,MACL,aAAa,WAAW;AAAA,MACxB,MAAM,WAAW;AAAA,MACjB,QAAQ;AAAA,QACN,OAAO,WAAW;AAAA,QAClB,aAAa,WAAW;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,aAAa,WAAW,IAAI,WAAW,OAAO,WAAW,IAAI,CAAC;AAC7E,SAAO,SAAS,UAAU,OAAO,UAAU,MAAM;AACnD;AAEA,IAAI,gBAAiB,SAAU,GAAG,GAAG;AACnC,SAAO,MAAM;AACf;AAEA,IAAI,8BAA+B,SAAU,QAAQ;AACnD,MAAIzH,WAAU,OAAO,SACjB,cAAc,OAAO;AAEzB,MAAI,aAAa;AACf,WAAO,YAAY;AAAA,EACrB;AAEA,MAAIA,UAAS;AACX,WAAOA,SAAQ;AAAA,EACjB;AAEA,SAAO;AACT;AAEA,IAAI,2BAA2B,SAASkK,0BAAyB,QAAQ;AACvE,SAAO,OAAO,UAAU,OAAO,QAAQ,cAAc;AACvD;AAEA,IAAI,2BAA2B,SAASC,0BAAyB,QAAQ;AACvE,SAAO,OAAO,MAAM,OAAO,GAAG,SAAS,YAAY,OAAO,GAAG,QAAQ,cAAc;AACrF;AAEA,SAAS,uBAAuB;AAC9B,MAAI,iBAAiB,wBAAW,SAAU,GAAG,GAAG;AAC9C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,sBAAsB,wBAAW,SAAU,MAAM,SAAS,cAAc,aAAa,UAAU;AACjG,WAAO;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,MACA,iBAAiB,QAAQ,QAAQ;AAAA,MACjC,eAAe;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,wBAAW,SAAU3H,SAAQ,MAAM,WAAW,SAAS,cAAc,aAAa,oBAAoB;AAC3H,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQA;AAAA,QACR;AAAA,QACA;AAAA,QACA,UAAU,oBAAoB,MAAM,SAAS,cAAc,aAAa,IAAI;AAAA,MAC9E;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,WAAW,SAAS4H,UAAS,OAAO,UAAU;AAChD,QAAI,MAAM,YAAY;AACpB,UAAI,MAAM,SAAS,UAAU,OAAO,SAAS,aAAa;AACxD,eAAO;AAAA,MACT;AAEA,UAAI5H,UAAS,MAAM,QAAQ,OAAO;AAClC,UAAI,YAAY,MAAM,WAAW,WAAW,SAAS,WAAW;AAChE,UAAI,eAAe,kBAAkB,MAAM,MAAM;AACjD,UAAI,cAAc,yBAAyB,MAAM,MAAM;AACvD,UAAI,qBAAqB,MAAM;AAC/B,aAAO,iBAAiB,eAAeA,QAAO,GAAGA,QAAO,CAAC,GAAG,MAAM,cAAc,WAAW,SAAS,SAAS,cAAc,aAAa,kBAAkB;AAAA,IAC5J;AAEA,QAAI,MAAM,UAAU,kBAAkB;AACpC,UAAI,YAAY,MAAM;AAEtB,UAAI,UAAU,OAAO,gBAAgB,SAAS,aAAa;AACzD,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,SAAS;AACvB,UAAI,aAAa,MAAM,WAAW,WAAW,SAAS,WAAW;AACjE,UAAI,SAAS,UAAU;AACvB,UAAI,OAAO,OAAO;AAElB,UAAI,gBAAgB,4BAA4B,MAAM;AAEtD,UAAI,eAAe,yBAAyB,MAAM;AAElD,UAAI,WAAW,MAAM;AACrB,UAAI,WAAW;AAAA,QACb;AAAA,QACA,OAAO,OAAO;AAAA,QACd,QAAQ,MAAM;AAAA,QACd,SAAS,eAAe,QAAQ,QAAQ,OAAO;AAAA,QAC/C,OAAO,eAAe,QAAQ,MAAM,OAAO;AAAA,MAC7C;AACA,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,UACd,WAAW;AAAA,UACX;AAAA,UACA,cAAc;AAAA,UACd,aAAa;AAAA,UACb;AAAA,UACA,oBAAoB;AAAA,UACpB,UAAU,oBAAoB,MAAM,SAAS,eAAe,cAAc,QAAQ;AAAA,QACpF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,kBAAkB;AAC9C,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,eAAe;AAAA,IACf,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA,aAAa;AAAA,EACf;AACF;AAEA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,2BAA2B;AAAA,IAC3B,UAAU,qBAAqB,IAAI;AAAA,EACrC;AACF;AAEA,SAAS,uBAAuB;AAC9B,MAAI,iBAAiB,wBAAW,SAAU,GAAG,GAAG;AAC9C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,sBAAsB,wBAAW,oBAAoB;AACzD,MAAI,mBAAmB,wBAAW,SAAUA,SAAQ,kBAAkB,2BAA2B;AAC/F,QAAI,qBAAqB,QAAQ;AAC/B,yBAAmB;AAAA,IACrB;AAEA,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQA;AAAA,QACR;AAAA,QACA;AAAA,QACA,UAAU,oBAAoB,gBAAgB;AAAA,MAChD;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,cAAc,SAAS6H,aAAY,kBAAkB;AACvD,WAAO,mBAAmB,iBAAiB,QAAQ,kBAAkB,IAAI,IAAI;AAAA,EAC/E;AAEA,MAAI,WAAW,SAASC,UAAS,OAAO,YAAY,QAAQ,eAAe;AACzE,QAAI,qBAAqB,OAAO,UAAU,QAAQ,KAAK;AACvD,QAAI,+BAA+B,QAAQ,cAAc,iBAAiB,cAAc,SAAS,KAAK,CAAC;AACvG,QAAItK,WAAU,cAAc,MAAM;AAClC,QAAI,mBAAmBA,YAAWA,SAAQ,gBAAgB,QAAQ,aAAa;AAE/E,QAAI,CAAC,oBAAoB;AACvB,UAAI,CAAC,8BAA8B;AACjC,eAAO,YAAY,gBAAgB;AAAA,MACrC;AAEA,UAAI,OAAO,UAAU,UAAU,KAAK,GAAG;AACrC,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,OAAO,cAAc,YAAY,KAAK;AAEnD,UAAI,UAAU,eAAe,OAAO,GAAG,OAAO,CAAC;AAE/C,aAAO,iBAAiB,SAAS,kBAAkB,IAAI;AAAA,IACzD;AAEA,QAAI,8BAA8B;AAChC,aAAO,YAAY,gBAAgB;AAAA,IACrC;AAEA,QAAI,aAAa,OAAO,YAAY;AACpC,QAAIwC,UAAS,eAAe,WAAW,GAAG,WAAW,CAAC;AACtD,WAAO,iBAAiBA,SAAQ,kBAAkB,mBAAmB,aAAa;AAAA,EACpF;AAEA,MAAI,WAAW,SAAS4H,UAAS,OAAO,UAAU;AAChD,QAAI,MAAM,YAAY;AACpB,UAAI,MAAM,SAAS,UAAU,OAAO,SAAS,aAAa;AACxD,eAAO;AAAA,MACT;AAEA,aAAO,SAAS,SAAS,aAAa,MAAM,SAAS,UAAU,IAAI,MAAM,QAAQ,MAAM,aAAa;AAAA,IACtG;AAEA,QAAI,MAAM,UAAU,kBAAkB;AACpC,UAAI,YAAY,MAAM;AAEtB,UAAI,UAAU,OAAO,gBAAgB,SAAS,aAAa;AACzD,eAAO;AAAA,MACT;AAEA,aAAO,SAAS,SAAS,aAAa,UAAU,OAAO,aAAa,UAAU,QAAQ,UAAU,aAAa;AAAA,IAC/G;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,sBAAsB,SAASG,uBAAsB;AACvD,MAAI,mBAAmB,qBAAqB;AAC5C,MAAI,oBAAoB,qBAAqB;AAE7C,MAAI,WAAW,SAASH,UAAS,OAAO,UAAU;AAChD,WAAO,iBAAiB,OAAO,QAAQ,KAAK,kBAAkB,OAAO,QAAQ,KAAK;AAAA,EACpF;AAEA,SAAO;AACT;AACA,IAAI,qBAAqB;AAAA,EACvB;AACF;AACA,IAAI,qBAAqB,gBAAQ,qBAAqB,oBAAoB,MAAM;AAAA,EAC9E,SAAS;AAAA,EACT,MAAM;AAAA,EACN,oBAAoB;AACtB,CAAC,EAAE,SAAS;AAEZ,SAAS,iBAAiB,OAAO;AAC/B,MAAI,mBAAmB,mBAAmB,gBAAgB;AAC1D,MAAI,kBAAkB,iBAAiB;AAEvC,MAAI,oBAAoB,MAAM,eAAe,CAAC,MAAM,SAAS;AAC3D,WAAO;AAAA,EACT;AAEA,SAAO,cAAA1K,QAAM,cAAc,oBAAoB,KAAK;AACtD;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,YAAY,OAAO,MAAM,mBAAmB,YAAY,CAAC,MAAM,iBAAiB;AACpF,MAAI,6BAA6B,QAAQ,MAAM,iCAAiC;AAChF,MAAI,0BAA0B,QAAQ,MAAM,uBAAuB;AACnE,SAAO,cAAAA,QAAM,cAAc,kBAAkB,SAAS,CAAC,GAAG,OAAO;AAAA,IAC/D,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,iBAAa,0BAAW,UAAU;AACtC,GAAC,aAAa,OAAwCF,WAAU,OAAO,4BAA4B,IAAIA,WAAU,KAAK,IAAI;AAC1H,MAAI,YAAY,WAAW,WACvBgL,qBAAoB,WAAW;AACnC,MAAI,mBAAe,sBAAO,IAAI;AAC9B,MAAI,qBAAiB,sBAAO,IAAI;AAChC,MAAI,WAAW,MAAM,UACjB,cAAc,MAAM,aACpB,OAAO,MAAM,MACb,OAAO,MAAM,MACb,YAAY,MAAM,WAClB,0BAA0B,MAAM,yBAChC,iBAAiB,MAAM,gBACvB,mBAAmB,MAAM,kBACzB,WAAW,MAAM,UACjB,WAAW,MAAM,UACjBrG,2BAA0B,MAAM,yBAChC,uBAAuB,MAAM;AACjC,MAAI,kBAAkB,YAAY,WAAY;AAC5C,WAAO,aAAa;AAAA,EACtB,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,YAAY,SAAU,OAAO;AACjD,iBAAa,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,oBAAoB,YAAY,WAAY;AAC9C,WAAO,eAAe;AAAA,EACxB,GAAG,CAAC,CAAC;AACL,MAAI,oBAAoB,YAAY,SAAU,OAAO;AACnD,mBAAe,UAAU;AAAA,EAC3B,GAAG,CAAC,CAAC;AACL,gBAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,6BAA6B,YAAY,WAAY;AACvD,QAAIqG,mBAAkB,GAAG;AACvB,MAAArG,yBAAwB;AAAA,QACtB,WAAW,mBAAmB;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAACqG,oBAAmBrG,wBAAuB,CAAC;AAC/C,wBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAIuF,eAAc,cAAAhK,QAAM,cAAc,cAAc;AAAA,IAClD,IAAI,MAAM;AAAA,IACV,eAAe,MAAM;AAAA,EACvB,GAAG,SAAU,MAAM;AACjB,QAAI,UAAU,KAAK,SACf,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,WAAO,cAAAA,QAAM,cAAc,eAAe;AAAA,MACxC,aAAa;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACD,MAAI,WAAW+H,SAAQ,WAAY;AACjC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,aAAaiC;AAAA,MACb,gBAAgB;AAAA,QACd,yBAAyB;AAAA,QACzB,iCAAiC;AAAA,MACnC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,aAAaA,cAAa,eAAe,CAAC;AACzD,MAAI,kBAAkB,WAAW,SAAS,SAAS,cAAc;AACjE,MAAI,mBAAmBjC,SAAQ,WAAY;AACzC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,iBAAiB,IAAI,CAAC;AAEvC,WAAS,WAAW;AAClB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,SAAS,UACpB,SAAS,SAAS;AACtB,QAAI,OAAO,cAAA/H,QAAM,cAAc,kBAAkB;AAAA,MAC/C,aAAa,SAAS;AAAA,MACtB,OAAO,SAAS,OAAO;AAAA,MACvB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,yBAAyB;AAAA,MACzB,4BAA4B;AAAA,IAC9B,GAAG,SAAU,mBAAmB,mBAAmB;AACjD,aAAO,OAAO,mBAAmB,mBAAmB,QAAQ;AAAA,IAC9D,CAAC;AACD,WAAO,kBAAA+K,QAAS,aAAa,MAAM,qBAAqB,CAAC;AAAA,EAC3D;AAEA,SAAO,cAAA/K,QAAM,cAAc,iBAAiB,UAAU;AAAA,IACpD,OAAO;AAAA,EACT,GAAG,SAAS,UAAU,QAAQ,GAAG,SAAS,CAAC;AAC7C;AAEA,IAAI,iBAAiB,SAASgL,gBAAe,MAAM,UAAU;AAC3D,SAAO,SAAS,SAAS,UAAU;AACrC;AAEA,IAAI,eAAe,SAASC,cAAa,UAAU,YAAY;AAC7D,SAAO,WAAW,WAAW,SAAS,UAAU,EAAE;AACpD;AAEA,IAAI,wBAAwB,SAASJ,uBAAsB;AACzD,MAAI,oBAAoB;AAAA,IACtB,aAAa;AAAA,IACb,0BAA0B;AAAA,IAC1B,UAAU;AAAA,MACR,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,IACtB;AAAA,IACA,UAAU;AAAA,EACZ;AAEA,MAAI,uBAAuB,SAAS,CAAC,GAAG,mBAAmB;AAAA,IACzD,0BAA0B;AAAA,EAC5B,CAAC;AAED,MAAI,qBAAqB,wBAAW,SAAU,YAAY;AACxD,WAAO;AAAA,MACL,aAAa,WAAW;AAAA,MACxB,MAAM,WAAW;AAAA,MACjB,QAAQ;AAAA,QACN,OAAO,WAAW;AAAA,QAClB,aAAa,WAAW;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,cAAc,wBAAW,SAAU,IAAI,WAAW,2BAA2B,yBAAyB,UAAU,aAAa;AAC/H,QAAI,cAAc,SAAS,WAAW;AACtC,QAAI,SAAS,SAAS,WAAW,gBAAgB;AAEjD,QAAI,QAAQ;AACV,UAAI,WAAW,cAAc;AAAA,QAC3B,QAAQ;AAAA,QACR,UAAU,mBAAmB,SAAS,UAAU;AAAA,MAClD,IAAI;AACJ,UAAI,YAAY;AAAA,QACd,gBAAgB;AAAA,QAChB,kBAAkB,4BAA4B,cAAc;AAAA,QAC5D,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,MACtB;AACA,aAAO;AAAA,QACL,aAAa,SAAS;AAAA,QACtB,0BAA0B;AAAA,QAC1B,UAAU;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,yBAAyB;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AAAA,MACb,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,IACtB;AACA,WAAO;AAAA,MACL,aAAa,SAAS;AAAA,MACtB,0BAA0B;AAAA,MAC1B;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AAED,MAAI,WAAW,SAASH,UAAS,OAAO,UAAU;AAChD,QAAI,KAAK,SAAS;AAClB,QAAI,OAAO,SAAS;AACpB,QAAI,YAAY,CAAC,SAAS;AAC1B,QAAI,cAAc,SAAS;AAE3B,QAAI,MAAM,YAAY;AACpB,UAAI,WAAW,MAAM;AAErB,UAAI,CAAC,eAAe,MAAM,QAAQ,GAAG;AACnC,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,aAAa,UAAU,MAAM,UAAU;AACtD,UAAI,iBAAiB,kBAAkB,MAAM,MAAM,MAAM;AACzD,aAAO,YAAY,IAAI,WAAW,gBAAgB,gBAAgB,UAAU,WAAW;AAAA,IACzF;AAEA,QAAI,MAAM,UAAU,kBAAkB;AACpC,UAAI,YAAY,MAAM;AAEtB,UAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG;AAC7C,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,aAAa,UAAU,UAAU,MAAM,UAAU;AAEjE,aAAO,YAAY,IAAI,WAAW,4BAA4B,UAAU,MAAM,MAAM,IAAI,kBAAkB,UAAU,MAAM,MAAM,IAAI,WAAW,WAAW;AAAA,IAC5J;AAEA,QAAI,MAAM,UAAU,UAAU,MAAM,aAAa,CAAC,MAAM,aAAa;AACnE,UAAI,aAAa,MAAM;AAEvB,UAAI,CAAC,eAAe,MAAM,WAAW,QAAQ,GAAG;AAC9C,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,kBAAkB,WAAW,MAAM,MAAM;AACvD,UAAI,eAAe,QAAQ,WAAW,OAAO,MAAM,WAAW,OAAO,GAAG,SAAS,SAAS;AAC1F,UAAI,SAAS,WAAW,SAAS,UAAU,OAAO;AAElD,UAAI,SAAS;AACX,eAAO,eAAe,oBAAoB;AAAA,MAC5C;AAEA,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AACA,IAAI,uBAAuB;AAAA,EACzB;AACF;AAEA,SAAS,UAAU;AACjB,GAAC,SAAS,OAAO,OAAwC5K,WAAU,OAAO,4BAA4B,IAAIA,WAAU,KAAK,IAAI;AAC7H,SAAO,SAAS;AAClB;AAEA,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,sBAAsB;AACxB;AACA,IAAI,qBAAqB,gBAAQ,uBAAuB,sBAAsB,MAAM;AAAA,EAClF,SAAS;AAAA,EACT,MAAM;AAAA,EACN,oBAAoB;AACtB,CAAC,EAAE,SAAS;AACZ,mBAAmB,eAAe;", "names": ["isValidElementType", "Element", "isContextConsumer", "isElement", "import_react", "t", "e", "import_react", "React", "setBatch", "getBatch", "batch", "notify", "get", "import_react", "React", "PropTypes", "import_react", "stringifyComponent", "checkForUpdates", "error", "unsubscribeWrapper", "initStateUpdates", "reactReduxForwardedRef", "wrapperProps", "React", "subscription", "notifyNestedSubs", "ref", "hoistStatics", "bindActionCreators", "_loop", "key", "mapDispatchToProps", "bindActionCreators", "mapDispatchToProps", "_excluded", "mapDispatchToProps", "_excluded", "mapDispatchToProps", "import_react", "import_react", "useReduxContext", "useStore", "useStore", "useDispatch", "import_react", "refEquality", "useReduxContext", "useSelector", "import_react", "useMemo", "getRect", "expand", "shrink", "shift", "createBox", "parse", "suffix", "getWindowScroll", "offset", "withScroll", "scroll", "calculateBox", "getBox", "areInputsEqual", "isEqual", "rafSchd", "wrapperFn", "import_react_dom", "isProduction", "clean", "getDevMessage", "getFormattedMessage", "warning", "shared", "prefix", "invariant", "Error<PERSON>ou<PERSON><PERSON>", "React", "position", "onDragStart", "start", "withLocation", "<PERSON><PERSON><PERSON><PERSON>", "combine", "onDragUpdate", "update", "returnedToStart", "onDragEnd", "add", "subtract", "isEqual", "negate", "patch", "distance", "closest", "apply", "offsetByPosition", "getCorners", "noSpacing", "scroll", "increase", "clip", "droppable", "draggable", "getDroppableDisplaced", "isVisibleInDroppable", "isVisibleInViewport", "isVisible", "withDroppableDisplacement", "isPartiallyVisible", "isTotallyVisible", "isTotallyVisibleOnAxis", "getShouldAnimate", "process", "match", "distanceFromStartToBorderBoxCenter", "distanceFromEndToBorderBoxCenter", "getCrossAxisBorderBoxCenter", "goAfter", "goBefore", "goIntoStart", "getResultWithoutDroppableDisplacement", "withDroppableScroll", "offset", "getKnownActive", "getCurrentPageBorderBoxCenter", "getCurrentPageBorderBox", "getDisplacedBy", "getRequiredGrowthForPlaceholder", "withMaxScroll", "addPlaceholder", "removePlaceholder", "getDroppableOver", "home", "offsetRectByPosition", "clearUnusedPlaceholder", "finish", "dropPending", "isSnapping", "postDroppableChange", "beforeInitialCapture", "lift", "initialPublish", "publishWhileDragging", "collectionStarting", "updateDroppableScroll", "updateDroppableIsEnabled", "updateDroppableIsCombineEnabled", "move", "moveByWindowScroll", "updateViewportMaxScroll", "moveUp", "moveDown", "moveRight", "moveLeft", "flush", "animateDrop", "completeDrop", "drop", "dropAnimationFinished", "moveTo", "getWindowScroll", "shouldEnd", "result", "execute", "areLocationsEqual", "isCombineEqual", "isCriticalEqual", "with<PERSON><PERSON><PERSON>", "getDragStart", "beforeCapture", "beforeStart", "abort", "shouldStop", "createStore", "collect", "remove", "stop", "scrollDroppable", "stopPublishing", "subscriber", "startPublishing", "getScrollableDroppableOver", "required", "get<PERSON><PERSON><PERSON>", "canPartiallyScroll", "canScrollWindow", "getWindowOverlap", "canScrollDroppable", "getDroppableOverlap", "scrollWindow", "tryScroll", "start$1", "fakeScrollCallback", "moveByOffset", "scrollDroppableAsMuchAsItCan", "scrollWindowAsMuchAsItCan", "jumpScroller", "makeGetSelector", "getStyles", "getSelector", "useIsomorphicLayoutEffect", "getHead", "createStyleEl", "useMemo", "ref", "register", "focus", "tryGiveFocus", "tryShiftRecord", "tryRestoreFocusRecorded", "tryRecordFoc<PERSON>", "notify", "getId", "getVersion", "isSatisfied", "listenForCapture", "bindCapturingEvents", "startPendingDrag", "getPhase", "setPhase", "startDragging", "lift$1", "tryAbandonLock", "tryReleaseLock", "createResponders", "dragHandleUsageInstructions", "is<PERSON><PERSON><PERSON>", "isBoth", "isElementScrollable", "style", "isBodyScrollable", "getClosestScrollable", "getIsFixed", "getClient", "getClosestScrollableFromDrag", "getSize", "placeholder", "getStyle", "AnimateInOut", "getDraggingTransition", "getDraggingOpacity", "getShouldDraggingAnimate", "getDimension", "id", "getCombineWithFromResult", "getCombineWithFromImpact", "selector", "get<PERSON>allback", "getProps", "makeMapStateToProps", "isMovementAllowed", "ReactDOM", "isMatchingType", "getDraggable"]}