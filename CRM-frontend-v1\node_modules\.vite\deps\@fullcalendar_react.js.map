{"version": 3, "sources": ["../../@fullcalendar/react/src/index.tsx"], "sourcesContent": ["import React, { Component, createRef, PureComponent } from 'react'\nimport { createPortal, flushSync } from 'react-dom'\nimport {\n  CalendarOptions,\n  CalendarApi,\n  Calendar,\n} from '@fullcalendar/core'\nimport {\n  CustomRendering,\n  CustomRenderingStore,\n} from '@fullcalendar/core/internal'\n\nconst reactMajorVersion = parseInt(String(React.version).split('.')[0])\nconst syncRenderingByDefault = reactMajorVersion < 18\n\ninterface CalendarState {\n  customRenderingMap: Map<string, CustomRendering<any>>\n}\n\nexport default class FullCalendar extends Component<CalendarOptions, CalendarState> {\n  static act = runNow // DEPRECATED. Not leveraged anymore\n\n  private elRef = createRef<HTMLDivElement>()\n  private calendar: Calendar\n  private handleCustomRendering: (customRendering: CustomRendering<any>) => void\n  private resizeId: number | undefined\n  private isUpdating = false\n  private isUnmounting = false\n\n  state: CalendarState = {\n    customRenderingMap: new Map<string, CustomRendering<any>>()\n  }\n\n  render() {\n    const customRenderingNodes: JSX.Element[] = []\n\n    for (const customRendering of this.state.customRenderingMap.values()) {\n      customRenderingNodes.push(\n        <CustomRenderingComponent\n          key={customRendering.id}\n          customRendering={customRendering}\n        />\n      )\n    }\n\n    return (\n      <div ref={this.elRef}>\n        {customRenderingNodes}\n      </div>\n    )\n  }\n\n  componentDidMount() {\n    // reset b/c react strict-mode calls componentWillUnmount/componentDidMount\n    this.isUnmounting = false\n\n    const customRenderingStore = new CustomRenderingStore<unknown>()\n    this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore)\n\n    this.calendar = new Calendar(this.elRef.current, {\n      ...this.props,\n      handleCustomRendering: this.handleCustomRendering,\n    })\n    this.calendar.render()\n\n    // attaching with .on() will cause this to fire AFTER internal preact rendering did flushSync\n    this.calendar.on('_beforeprint', () => {\n      flushSync(() => {\n        // our `customRenderingMap` state will be flushed at this point\n      })\n    })\n\n    let lastRequestTimestamp: number | undefined\n\n    customRenderingStore.subscribe((customRenderingMap) => {\n      const requestTimestamp = Date.now()\n      const isMounting = !lastRequestTimestamp\n      const runFunc = (\n        // don't call flushSync if React version already does sync rendering by default\n        // guards against fatal errors:\n        // https://github.com/fullcalendar/fullcalendar/issues/7448\n        syncRenderingByDefault ||\n        //\n        isMounting ||\n        this.isUpdating ||\n        this.isUnmounting ||\n        (requestTimestamp - lastRequestTimestamp) < 100 // rerendering frequently\n      ) ? runNow // either sync rendering (first-time or React 16/17) or async (React 18)\n        : flushSync // guaranteed sync rendering\n\n      runFunc(() => {\n        this.setState({ customRenderingMap }, () => {\n          lastRequestTimestamp = requestTimestamp\n          if (isMounting) {\n            this.doResize()\n          } else {\n            this.requestResize()\n          }\n        })\n      })\n    })\n  }\n\n  componentDidUpdate() {\n    this.isUpdating = true\n    this.calendar.resetOptions({\n      ...this.props,\n      handleCustomRendering: this.handleCustomRendering,\n    })\n    this.isUpdating = false\n  }\n\n  componentWillUnmount() {\n    this.isUnmounting = true\n    this.cancelResize()\n    this.calendar.destroy()\n  }\n\n  requestResize = () => {\n    if (!this.isUnmounting) {\n      this.cancelResize()\n      this.resizeId = requestAnimationFrame(() => {\n        this.doResize()\n      })\n    }\n  }\n\n  doResize() {\n    this.calendar.updateSize()\n  }\n\n  cancelResize() {\n    if (this.resizeId !== undefined) {\n      cancelAnimationFrame(this.resizeId)\n      this.resizeId = undefined\n    }\n  }\n\n  getApi(): CalendarApi {\n    return this.calendar\n  }\n}\n\n// Custom Rendering\n// -------------------------------------------------------------------------------------------------\n\ninterface CustomRenderingComponentProps {\n  customRendering: CustomRendering<any>\n}\n\nclass CustomRenderingComponent extends PureComponent<CustomRenderingComponentProps> {\n  render() {\n    const { customRendering } = this.props\n    const { generatorMeta } = customRendering\n    const vnode = typeof generatorMeta === 'function' ?\n      generatorMeta(customRendering.renderProps) :\n      generatorMeta\n\n    return createPortal(vnode, customRendering.containerEl)\n  }\n}\n\n// Util\n// -------------------------------------------------------------------------------------------------\n\nfunction runNow(f: () => void): void {\n  f()\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,mBAA2D;AAC3D,uBAAwC;AAWxC,IAAM,oBAAoB,SAAS,OAAO,aAAAA,QAAM,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACtE,IAAM,yBAAyB,oBAAoB;AAMnD,IAAqB,eAArB,cAA0C,uBAAyC;EAAnF,cAAA;;AAGU,SAAA,YAAQ,wBAAS;AAIjB,SAAA,aAAa;AACb,SAAA,eAAe;AAEvB,SAAA,QAAuB;MACrB,oBAAoB,oBAAI,IAAG;;AAwF7B,SAAA,gBAAgB,MAAK;AACnB,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,aAAY;AACjB,aAAK,WAAW,sBAAsB,MAAK;AACzC,eAAK,SAAQ;QACf,CAAC;;IAEL;EAgBF;EA5GE,SAAM;AACJ,UAAM,uBAAsC,CAAA;AAE5C,eAAW,mBAAmB,KAAK,MAAM,mBAAmB,OAAM,GAAI;AACpE,2BAAqB,KACnB,aAAAA,QAAA,cAAC,0BAAwB,EACvB,KAAK,gBAAgB,IACrB,gBAAgC,CAAA,CAChC;;AAIN,WACE,aAAAA,QAAA,cAAA,OAAA,EAAK,KAAK,KAAK,MAAK,GACjB,oBAAoB;EAG3B;EAEA,oBAAiB;AAEf,SAAK,eAAe;AAEpB,UAAM,uBAAuB,IAAI,qBAAoB;AACrD,SAAK,wBAAwB,qBAAqB,OAAO,KAAK,oBAAoB;AAElF,SAAK,WAAW,IAAI,SAAS,KAAK,MAAM,SAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAC1C,KAAK,KAAK,GAAA,EACb,uBAAuB,KAAK,sBAAqB,CAAA,CAAA;AAEnD,SAAK,SAAS,OAAM;AAGpB,SAAK,SAAS,GAAG,gBAAgB,MAAK;AACpC,sCAAU,MAAK;MAEf,CAAC;IACH,CAAC;AAED,QAAI;AAEJ,yBAAqB,UAAU,CAAC,uBAAsB;AACpD,YAAM,mBAAmB,KAAK,IAAG;AACjC,YAAM,aAAa,CAAC;AACpB,YAAM;;;;QAIJ;QAEA,cACA,KAAK,cACL,KAAK,gBACJ,mBAAmB,uBAAwB,MAC1C,SACA;;AAEJ,cAAQ,MAAK;AACX,aAAK,SAAS,EAAE,mBAAkB,GAAI,MAAK;AACzC,iCAAuB;AACvB,cAAI,YAAY;AACd,iBAAK,SAAQ;iBACR;AACL,iBAAK,cAAa;;QAEtB,CAAC;MACH,CAAC;IACH,CAAC;EACH;EAEA,qBAAkB;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS,aAAY,OAAA,OAAA,OAAA,OAAA,CAAA,GACrB,KAAK,KAAK,GAAA,EACb,uBAAuB,KAAK,sBAAqB,CAAA,CAAA;AAEnD,SAAK,aAAa;EACpB;EAEA,uBAAoB;AAClB,SAAK,eAAe;AACpB,SAAK,aAAY;AACjB,SAAK,SAAS,QAAO;EACvB;EAWA,WAAQ;AACN,SAAK,SAAS,WAAU;EAC1B;EAEA,eAAY;AACV,QAAI,KAAK,aAAa,QAAW;AAC/B,2BAAqB,KAAK,QAAQ;AAClC,WAAK,WAAW;;EAEpB;EAEA,SAAM;AACJ,WAAO,KAAK;EACd;;AAxHO,aAAA,MAAM;AAkIf,IAAM,2BAAN,cAAuC,2BAA4C;EACjF,SAAM;AACJ,UAAM,EAAE,gBAAe,IAAK,KAAK;AACjC,UAAM,EAAE,cAAa,IAAK;AAC1B,UAAM,QAAQ,OAAO,kBAAkB,aACrC,cAAc,gBAAgB,WAAW,IACzC;AAEF,eAAO,+BAAa,OAAO,gBAAgB,WAAW;EACxD;;AAMF,SAAS,OAAO,GAAa;AAC3B,IAAC;AACH;", "names": ["React"]}