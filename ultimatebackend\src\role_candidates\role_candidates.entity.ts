import { People } from 'src/people/people.entity';
import { Roles } from 'src/roles/roles.entity';
import { Users } from 'src/users/users.entity';
import { RoleCandidateLog } from './role_candidate_log.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('role_candidates')
export class RoleCandidate {
  @Index('role_candidates_id_index')
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ['LINKEDIN', 'CV', 'JOB_BOARD', 'JOB_POSTING'],
    nullable: true,
    default: 'LINKEDIN',
  })
  source_type: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  profile_url: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  profile_source: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  li_type: string;

  @Column({
    type: 'boolean',
    default: false,
    nullable: true,
  })
  is_willing_to_relocate: boolean;

  @Column({
    type: 'boolean',
    default: false,
  })
  is_accepted: boolean;

  @Column({
    type: 'enum',
    enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ISSUE', 'NEW_AWS'],
    default: 'PENDING',
  })
  candidate_status: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  radius_miles: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  source: string;
  @Column({
    type: 'varchar',
    nullable: true,
  })
  current_channel: string;

  @Column({
    type: 'enum',
    enum: [
      'NEW',
      'ENGAGED',
    ],
    nullable: true,
    default: 'NEW',
  })
  stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: [
      'INTERESTED',
      'NOT_INTERESTED',
      'AUTO_REPLY',
      'NO_RESPONSE',
      'WFR',
      'NCR',
    ],
  })
  screening_stage: string;

  @Column({
    type: 'enum',
    enum: [
      'SEND_JD',
      'SALARY_NEGOTIATION',
      'LOCATION_NEGOTIATION',
      'OFFICE_TIMING_NEGOTIATION',
      'SKILL_QUALIFICATION',
      'WFR',
      'READY_TO_SEND',
    ],
    nullable: true,
  })
  partially_interested_stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: [
      'INTERESTED',
      'SUBMITTED_TO_CLIENT',
      'CLIENT_INTERESTED',
      'CLIENT_REJECTED',
    ],
  })
  submission_stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: [
      'REJECTED_BY_QA',
      'REJECTED_BY_ACM',
      'REJECTED_BY_CANDIDATE',
      'REJECTED_BY_CLIENT',
      'REJECTED_BY_OTHER',
    ],
  })
  rejection_stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: [
      'INTERVIEW_SCHEDULED',
      'INTERVIEW_COMPLETED',
      'INTERVIEW_NOT_SCHEDULED',
      'INTERVIEW_CANCELLED',
      'INTERVIEW_RESCHEDULED',
      'INTERVIEW_POSTPONED',
      'INTERVIEW_REJECTED',
      'INTERVIEW_WITHDRAWN',
      'INTERVIEW_CONFLICT',
      'INTERVIEW_CONFLICT_RESOLVED',
      'INTERVIEW_CONFLICT_REJECTED',
      'INTERVIEW_CONFLICT_WITHDRAWN',
    ],
  })
  interview_stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: [
      'OFFER_PLANNED',
      'OFFER_MADE',
      'OFFER_ACCEPTED',
      'OFFER_REJECTED',
      'OFFER_WITHDRAWN',
    ],
  })
  offer_stage: string;

  @Column({
    type: 'enum',
    nullable: true,
    enum: ['HIRED', 'JOINED', 'NOT_HIRED'],
  })
  hiring_stage: string;

  @Column({
    type: 'enum',
    enum: [
      'NEW',
      'SCREENING',
      'SUBMISSION',
      'REJECTED',
      'INTERVIEW',
      'OFFERED',
      'HIRED',
    ],
    nullable: true,
    default: 'NEW',
  })
  pipeline: string;
  

  @Column({
    type: 'enum',
    enum: [
      'LESS_THAN_MONTH',
      '1MONTH',
      '3MONTHS',
      '6MONTHS',
      '9MONTHS',
      '12MONTHS',
      '15MONTHS',
      '18MONTHS',
      '24MONTHS',
      '36MONTHS',
      '48MONTHS',
      '60MONTHS',
      'MORE_THAN_60MONTHS',
    ],
    nullable: true,
  })
  months_back: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  salary: string;

  @ManyToOne(() => Roles, (role) => role.role_candidates)
  role: Roles;

  @Column({
    type: 'int',
    nullable: true,
  })
  roleId: number;

  @ManyToOne(() => People, (client) => client.role_candidates)
  client: People;

  @Column({
    type: 'int',
    nullable: true,
  })
  clientId: number;

  @ManyToOne(() => People, (candidate) => candidate.role_candidates)
  candidate: People;

  @Column({
    type: 'int',
    nullable: true,
  })
  candidateId: number;

  @ManyToOne(() => People, (prospect) => prospect.role_candidates)
  prospect: People;

  @Column({
    type: 'int',
    nullable: true,
  })
  prospectId: number;

  @ManyToOne(() => Users, (user) => user.role_candidates)
  user: Users;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  userId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @OneToMany(() => RoleCandidateLog, (log) => log.roleCandidate)
  logs: RoleCandidateLog[];
}
