{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/tiki.js"], "sourcesContent": ["function inBlock(style, terminator, returnTokenizer) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.match(terminator)) {\n        state.tokenize = inText;\n        break;\n      }\n      stream.next();\n    }\n\n    if (returnTokenizer) state.tokenize = returnTokenizer;\n\n    return style;\n  };\n}\n\nfunction inLine(style) {\n  return function(stream, state) {\n    while(!stream.eol()) {\n      stream.next();\n    }\n    state.tokenize = inText;\n    return style;\n  };\n}\n\nfunction inText(stream, state) {\n  function chain(parser) {\n    state.tokenize = parser;\n    return parser(stream, state);\n  }\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  //non start of line\n  switch (ch) { //switch is generally much faster than if, so it is used here\n  case \"{\": //plugin\n    stream.eat(\"/\");\n    stream.eatSpace();\n    stream.eatWhile(/[^\\s\\u00a0=\\\"\\'\\/?(}]/);\n    state.tokenize = inPlugin;\n    return \"tag\";\n  case \"_\": //bold\n    if (stream.eat(\"_\"))\n      return chain(inBlock(\"strong\", \"__\", inText));\n    break;\n  case \"'\": //italics\n    if (stream.eat(\"'\"))\n      return chain(inBlock(\"em\", \"''\", inText));\n    break;\n  case \"(\":// Wiki Link\n    if (stream.eat(\"(\"))\n      return chain(inBlock(\"link\", \"))\", inText));\n    break;\n  case \"[\":// Weblink\n    return chain(inBlock(\"url\", \"]\", inText));\n    break;\n  case \"|\": //table\n    if (stream.eat(\"|\"))\n      return chain(inBlock(\"comment\", \"||\"));\n    break;\n  case \"-\":\n    if (stream.eat(\"=\")) {//titleBar\n      return chain(inBlock(\"header string\", \"=-\", inText));\n    } else if (stream.eat(\"-\")) {//deleted\n      return chain(inBlock(\"error tw-deleted\", \"--\", inText));\n    }\n    break;\n  case \"=\": //underline\n    if (stream.match(\"==\"))\n      return chain(inBlock(\"tw-underline\", \"===\", inText));\n    break;\n  case \":\":\n    if (stream.eat(\":\"))\n      return chain(inBlock(\"comment\", \"::\"));\n    break;\n  case \"^\": //box\n    return chain(inBlock(\"tw-box\", \"^\"));\n    break;\n  case \"~\": //np\n    if (stream.match(\"np~\"))\n      return chain(inBlock(\"meta\", \"~/np~\"));\n    break;\n  }\n\n  //start of line types\n  if (sol) {\n    switch (ch) {\n    case \"!\": //header at start of line\n      if (stream.match('!!!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!')) {\n        return chain(inLine(\"header string\"));\n      } else {\n        return chain(inLine(\"header string\"));\n      }\n      break;\n    case \"*\": //unordered list line item, or <li /> at start of line\n    case \"#\": //ordered list line item, or <li /> at start of line\n    case \"+\": //ordered list line item, or <li /> at start of line\n      return chain(inLine(\"tw-listitem bracket\"));\n      break;\n    }\n  }\n\n  //stream.eatWhile(/[&{]/); was eating up plugins, turned off to act less like html and more like tiki\n  return null;\n}\n\n// Return variables for tokenizers\nvar pluginName, type;\nfunction inPlugin(stream, state) {\n  var ch = stream.next();\n  var peek = stream.peek();\n\n  if (ch == \"}\") {\n    state.tokenize = inText;\n    //type = ch == \")\" ? \"endPlugin\" : \"selfclosePlugin\"; inPlugin\n    return \"tag\";\n  } else if (ch == \"(\" || ch == \")\") {\n    return \"bracket\";\n  } else if (ch == \"=\") {\n    type = \"equals\";\n\n    if (peek == \">\") {\n      stream.next();\n      peek = stream.peek();\n    }\n\n    //here we detect values directly after equal character with no quotes\n    if (!/[\\'\\\"]/.test(peek)) {\n      state.tokenize = inAttributeNoQuote();\n    }\n    //end detect values\n\n    return \"operator\";\n  } else if (/[\\'\\\"]/.test(ch)) {\n    state.tokenize = inAttribute(ch);\n    return state.tokenize(stream, state);\n  } else {\n    stream.eatWhile(/[^\\s\\u00a0=\\\"\\'\\/?]/);\n    return \"keyword\";\n  }\n}\n\nfunction inAttribute(quote) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.next() == quote) {\n        state.tokenize = inPlugin;\n        break;\n      }\n    }\n    return \"string\";\n  };\n}\n\nfunction inAttributeNoQuote() {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      var ch = stream.next();\n      var peek = stream.peek();\n      if (ch == \" \" || ch == \",\" || /[ )}]/.test(peek)) {\n        state.tokenize = inPlugin;\n        break;\n      }\n    }\n    return \"string\";\n  };\n}\n\nvar curState, setStyle;\nfunction pass() {\n  for (var i = arguments.length - 1; i >= 0; i--) curState.cc.push(arguments[i]);\n}\n\nfunction cont() {\n  pass.apply(null, arguments);\n  return true;\n}\n\nfunction pushContext(pluginName, startOfLine) {\n  var noIndent = curState.context && curState.context.noIndent;\n  curState.context = {\n    prev: curState.context,\n    pluginName: pluginName,\n    indent: curState.indented,\n    startOfLine: startOfLine,\n    noIndent: noIndent\n  };\n}\n\nfunction popContext() {\n  if (curState.context) curState.context = curState.context.prev;\n}\n\nfunction element(type) {\n  if (type == \"openPlugin\") {curState.pluginName = pluginName; return cont(attributes, endplugin(curState.startOfLine));}\n  else if (type == \"closePlugin\") {\n    var err = false;\n    if (curState.context) {\n      err = curState.context.pluginName != pluginName;\n      popContext();\n    } else {\n      err = true;\n    }\n    if (err) setStyle = \"error\";\n    return cont(endcloseplugin(err));\n  }\n  else if (type == \"string\") {\n    if (!curState.context || curState.context.name != \"!cdata\") pushContext(\"!cdata\");\n    if (curState.tokenize == inText) popContext();\n    return cont();\n  }\n  else return cont();\n}\n\nfunction endplugin(startOfLine) {\n  return function(type) {\n    if (\n      type == \"selfclosePlugin\" ||\n        type == \"endPlugin\"\n    )\n      return cont();\n    if (type == \"endPlugin\") {pushContext(curState.pluginName, startOfLine); return cont();}\n    return cont();\n  };\n}\n\nfunction endcloseplugin(err) {\n  return function(type) {\n    if (err) setStyle = \"error\";\n    if (type == \"endPlugin\") return cont();\n    return pass();\n  };\n}\n\nfunction attributes(type) {\n  if (type == \"keyword\") {setStyle = \"attribute\"; return cont(attributes);}\n  if (type == \"equals\") return cont(attvalue, attributes);\n  return pass();\n}\nfunction attvalue(type) {\n  if (type == \"keyword\") {setStyle = \"string\"; return cont();}\n  if (type == \"string\") return cont(attvaluemaybe);\n  return pass();\n}\nfunction attvaluemaybe(type) {\n  if (type == \"string\") return cont(attvaluemaybe);\n  else return pass();\n}\nexport const tiki = {\n  name: \"tiki\",\n  startState: function() {\n    return {tokenize: inText, cc: [], indented: 0, startOfLine: true, pluginName: null, context: null};\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      state.startOfLine = true;\n      state.indented = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n\n    setStyle = type = pluginName = null;\n    var style = state.tokenize(stream, state);\n    if ((style || type) && style != \"comment\") {\n      curState = state;\n      while (true) {\n        var comb = state.cc.pop() || element;\n        if (comb(type || style)) break;\n      }\n    }\n    state.startOfLine = false;\n    return setStyle || style;\n  },\n  indent: function(state, textAfter, cx) {\n    var context = state.context;\n    if (context && context.noIndent) return 0;\n    if (context && /^{\\//.test(textAfter))\n      context = context.prev;\n    while (context && !context.startOfLine)\n      context = context.prev;\n    if (context) return context.indent + cx.unit;\n    else return 0;\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,QAAQ,OAAO,YAAY,iBAAiB;AACnD,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,cAAM,WAAW;AACjB;AAAA,MACF;AACA,aAAO,KAAK;AAAA,IACd;AAEA,QAAI;AAAiB,YAAM,WAAW;AAEtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAM,CAAC,OAAO,IAAI,GAAG;AACnB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,WAAS,MAAM,QAAQ;AACrB,UAAM,WAAW;AACjB,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC7B;AAEA,MAAI,MAAM,OAAO,IAAI;AACrB,MAAI,KAAK,OAAO,KAAK;AAGrB,UAAQ,IAAI;AAAA,IACZ,KAAK;AACH,aAAO,IAAI,GAAG;AACd,aAAO,SAAS;AAChB,aAAO,SAAS,uBAAuB;AACvC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,KAAK;AACH,UAAI,OAAO,IAAI,GAAG;AAChB,eAAO,MAAM,QAAQ,UAAU,MAAM,MAAM,CAAC;AAC9C;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,GAAG;AAChB,eAAO,MAAM,QAAQ,MAAM,MAAM,MAAM,CAAC;AAC1C;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,GAAG;AAChB,eAAO,MAAM,QAAQ,QAAQ,MAAM,MAAM,CAAC;AAC5C;AAAA,IACF,KAAK;AACH,aAAO,MAAM,QAAQ,OAAO,KAAK,MAAM,CAAC;AACxC;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,GAAG;AAChB,eAAO,MAAM,QAAQ,WAAW,IAAI,CAAC;AACvC;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAO,MAAM,QAAQ,iBAAiB,MAAM,MAAM,CAAC;AAAA,MACrD,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,eAAO,MAAM,QAAQ,oBAAoB,MAAM,MAAM,CAAC;AAAA,MACxD;AACA;AAAA,IACF,KAAK;AACH,UAAI,OAAO,MAAM,IAAI;AACnB,eAAO,MAAM,QAAQ,gBAAgB,OAAO,MAAM,CAAC;AACrD;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,GAAG;AAChB,eAAO,MAAM,QAAQ,WAAW,IAAI,CAAC;AACvC;AAAA,IACF,KAAK;AACH,aAAO,MAAM,QAAQ,UAAU,GAAG,CAAC;AACnC;AAAA,IACF,KAAK;AACH,UAAI,OAAO,MAAM,KAAK;AACpB,eAAO,MAAM,QAAQ,QAAQ,OAAO,CAAC;AACvC;AAAA,EACF;AAGA,MAAI,KAAK;AACP,YAAQ,IAAI;AAAA,MACZ,KAAK;AACH,YAAI,OAAO,MAAM,OAAO,GAAG;AACzB,iBAAO,MAAM,OAAO,eAAe,CAAC;AAAA,QACtC,WAAW,OAAO,MAAM,MAAM,GAAG;AAC/B,iBAAO,MAAM,OAAO,eAAe,CAAC;AAAA,QACtC,WAAW,OAAO,MAAM,KAAK,GAAG;AAC9B,iBAAO,MAAM,OAAO,eAAe,CAAC;AAAA,QACtC,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,iBAAO,MAAM,OAAO,eAAe,CAAC;AAAA,QACtC,OAAO;AACL,iBAAO,MAAM,OAAO,eAAe,CAAC;AAAA,QACtC;AACA;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,MAAM,OAAO,qBAAqB,CAAC;AAC1C;AAAA,IACF;AAAA,EACF;AAGA,SAAO;AACT;AAGA,IAAI;AAAJ,IAAgB;AAChB,SAAS,SAAS,QAAQ,OAAO;AAC/B,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,OAAO,OAAO,KAAK;AAEvB,MAAI,MAAM,KAAK;AACb,UAAM,WAAW;AAEjB,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO;AAEP,QAAI,QAAQ,KAAK;AACf,aAAO,KAAK;AACZ,aAAO,OAAO,KAAK;AAAA,IACrB;AAGA,QAAI,CAAC,SAAS,KAAK,IAAI,GAAG;AACxB,YAAM,WAAW,mBAAmB;AAAA,IACtC;AAGA,WAAO;AAAA,EACT,WAAW,SAAS,KAAK,EAAE,GAAG;AAC5B,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC,OAAO;AACL,WAAO,SAAS,qBAAqB;AACrC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,OAAO,KAAK,KAAK,OAAO;AAC1B,cAAM,WAAW;AACjB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,qBAAqB;AAC5B,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,KAAK,OAAO,KAAK;AACrB,UAAI,OAAO,OAAO,KAAK;AACvB,UAAI,MAAM,OAAO,MAAM,OAAO,QAAQ,KAAK,IAAI,GAAG;AAChD,cAAM,WAAW;AACjB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI;AAAJ,IAAc;AACd,SAAS,OAAO;AACd,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG;AAAK,aAAS,GAAG,KAAK,UAAU,CAAC,CAAC;AAC/E;AAEA,SAAS,OAAO;AACd,OAAK,MAAM,MAAM,SAAS;AAC1B,SAAO;AACT;AAEA,SAAS,YAAYA,aAAY,aAAa;AAC5C,MAAI,WAAW,SAAS,WAAW,SAAS,QAAQ;AACpD,WAAS,UAAU;AAAA,IACjB,MAAM,SAAS;AAAA,IACf,YAAYA;AAAA,IACZ,QAAQ,SAAS;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,aAAa;AACpB,MAAI,SAAS;AAAS,aAAS,UAAU,SAAS,QAAQ;AAC5D;AAEA,SAAS,QAAQC,OAAM;AACrB,MAAIA,SAAQ,cAAc;AAAC,aAAS,aAAa;AAAY,WAAO,KAAK,YAAY,UAAU,SAAS,WAAW,CAAC;AAAA,EAAE,WAC7GA,SAAQ,eAAe;AAC9B,QAAI,MAAM;AACV,QAAI,SAAS,SAAS;AACpB,YAAM,SAAS,QAAQ,cAAc;AACrC,iBAAW;AAAA,IACb,OAAO;AACL,YAAM;AAAA,IACR;AACA,QAAI;AAAK,iBAAW;AACpB,WAAO,KAAK,eAAe,GAAG,CAAC;AAAA,EACjC,WACSA,SAAQ,UAAU;AACzB,QAAI,CAAC,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAAU,kBAAY,QAAQ;AAChF,QAAI,SAAS,YAAY;AAAQ,iBAAW;AAC5C,WAAO,KAAK;AAAA,EACd;AACK,WAAO,KAAK;AACnB;AAEA,SAAS,UAAU,aAAa;AAC9B,SAAO,SAASA,OAAM;AACpB,QACEA,SAAQ,qBACNA,SAAQ;AAEV,aAAO,KAAK;AACd,QAAIA,SAAQ,aAAa;AAAC,kBAAY,SAAS,YAAY,WAAW;AAAG,aAAO,KAAK;AAAA,IAAE;AACvF,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,eAAe,KAAK;AAC3B,SAAO,SAASA,OAAM;AACpB,QAAI;AAAK,iBAAW;AACpB,QAAIA,SAAQ;AAAa,aAAO,KAAK;AACrC,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,WAAWA,OAAM;AACxB,MAAIA,SAAQ,WAAW;AAAC,eAAW;AAAa,WAAO,KAAK,UAAU;AAAA,EAAE;AACxE,MAAIA,SAAQ;AAAU,WAAO,KAAK,UAAU,UAAU;AACtD,SAAO,KAAK;AACd;AACA,SAAS,SAASA,OAAM;AACtB,MAAIA,SAAQ,WAAW;AAAC,eAAW;AAAU,WAAO,KAAK;AAAA,EAAE;AAC3D,MAAIA,SAAQ;AAAU,WAAO,KAAK,aAAa;AAC/C,SAAO,KAAK;AACd;AACA,SAAS,cAAcA,OAAM;AAC3B,MAAIA,SAAQ;AAAU,WAAO,KAAK,aAAa;AAAA;AAC1C,WAAO,KAAK;AACnB;AACO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO,EAAC,UAAU,QAAQ,IAAI,CAAC,GAAG,UAAU,GAAG,aAAa,MAAM,YAAY,MAAM,SAAS,KAAI;AAAA,EACnG;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,YAAM,cAAc;AACpB,YAAM,WAAW,OAAO,YAAY;AAAA,IACtC;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAE9B,eAAW,OAAO,aAAa;AAC/B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,SAAK,SAAS,SAAS,SAAS,WAAW;AACzC,iBAAW;AACX,aAAO,MAAM;AACX,YAAI,OAAO,MAAM,GAAG,IAAI,KAAK;AAC7B,YAAI,KAAK,QAAQ,KAAK;AAAG;AAAA,MAC3B;AAAA,IACF;AACA,UAAM,cAAc;AACpB,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,UAAU,MAAM;AACpB,QAAI,WAAW,QAAQ;AAAU,aAAO;AACxC,QAAI,WAAW,OAAO,KAAK,SAAS;AAClC,gBAAU,QAAQ;AACpB,WAAO,WAAW,CAAC,QAAQ;AACzB,gBAAU,QAAQ;AACpB,QAAI;AAAS,aAAO,QAAQ,SAAS,GAAG;AAAA;AACnC,aAAO;AAAA,EACd;AACF;", "names": ["pluginName", "type"]}