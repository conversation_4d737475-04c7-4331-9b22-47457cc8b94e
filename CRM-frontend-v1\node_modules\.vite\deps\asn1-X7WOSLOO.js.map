{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/asn1.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst defaults = {\n  keywords: words(\"DEFINITIONS OBJECTS IF DERIVED INFORMATION ACTION\" +\n                  \" REPLY ANY NAMED CHARACTERIZED BEHAVIOUR REGISTERED\" +\n                  \" WITH AS IDENTIFIED CONSTRAINED BY PRESENT BEGIN\" +\n                  \" IMPORTS FROM UNITS SYNTAX MIN-ACCESS MAX-ACCESS\" +\n                  \" MINACCESS MAXACCESS REVISION STATUS DESCRIPTION\" +\n                  \" SEQUENCE SET COMPONENTS OF CHOICE DistinguishedName\" +\n                  \" ENUMERATED SIZE MODULE END INDEX AUGMENTS EXTENSIBILITY\" +\n                  \" IMPLIED EXPORTS\"),\n  cmipVerbs: words(\"ACTIONS ADD GET NOTIFICATIONS REPLACE REMOVE\"),\n  compareTypes: words(\"OPTIONAL DEFAULT MANAGED MODULE-TYPE MODULE_IDENTITY\" +\n                      \" MODULE-COMPL<PERSON>NCE OBJECT-TYPE OBJECT-IDENTITY\" +\n                      \" OBJECT-COMPLIANCE MODE CONFIRMED CONDITIONAL\" +\n                      \" SUBORDINATE SUPERIOR CLASS TRUE FALSE NULL\" +\n                      \" TEXTUAL-CONVENTION\"),\n  status: words(\"current deprecated mandatory obsolete\"),\n  tags: words(\"APPLICATION AUTOMATIC EXPLICIT IMPLICIT PRIVATE TAGS\" +\n              \" UNIVERSAL\"),\n  storage: words(\"BOOLEAN INTEGER OBJECT IDENTIFIER BIT OCTET STRING\" +\n                 \" UTCTime InterfaceIndex IANAifType CMIP-Attribute\" +\n                 \" REAL PACKAGE PACKAGES IpAddress PhysAddress\" +\n                 \" NetworkAddress BITS BMPString TimeStamp TimeTicks\" +\n                 \" TruthValue RowStatus DisplayString GeneralString\" +\n                 \" GraphicString IA5String NumericString\" +\n                 \" PrintableString SnmpAdminString TeletexString\" +\n                 \" UTF8String VideotexString VisibleString StringStore\" +\n                 \" ISO646String T61String UniversalString Unsigned32\" +\n                 \" Integer32 Gauge Gauge32 Counter Counter32 Counter64\"),\n  modifier: words(\"ATTRIBUTE ATTRIBUTES MANDATORY-GROUP MANDATORY-GROUPS\" +\n                  \" GROUP GROUPS ELEMENTS EQUALITY ORDERING SUBSTRINGS\" +\n                  \" DEFINED\"),\n  accessTypes: words(\"not-accessible accessible-for-notify read-only\" +\n                     \" read-create read-write\"),\n  multiLineStrings: true\n}\n\nexport function asn1(parserConfig) {\n  var keywords = parserConfig.keywords || defaults.keywords,\n      cmipVerbs = parserConfig.cmipVerbs || defaults.cmipVerbs,\n      compareTypes = parserConfig.compareTypes || defaults.compareTypes,\n      status = parserConfig.status || defaults.status,\n      tags = parserConfig.tags || defaults.tags,\n      storage = parserConfig.storage || defaults.storage,\n      modifier = parserConfig.modifier || defaults.modifier,\n      accessTypes = parserConfig.accessTypes|| defaults.accessTypes,\n      multiLineStrings = parserConfig.multiLineStrings || defaults.multiLineStrings,\n      indentStatements = parserConfig.indentStatements !== false;\n  var isOperatorChar = /[\\|\\^]/;\n  var curPunc;\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    if (/[\\[\\]\\(\\){}:=,;]/.test(ch)) {\n      curPunc = ch;\n      return \"punctuation\";\n    }\n    if (ch == \"-\"){\n      if (stream.eat(\"-\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (/\\d/.test(ch)) {\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperatorChar.test(ch)) {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n\n    stream.eatWhile(/[\\w\\-]/);\n    var cur = stream.current();\n    if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n    if (cmipVerbs.propertyIsEnumerable(cur)) return \"variableName\";\n    if (compareTypes.propertyIsEnumerable(cur)) return \"atom\";\n    if (status.propertyIsEnumerable(cur)) return \"comment\";\n    if (tags.propertyIsEnumerable(cur)) return \"typeName\";\n    if (storage.propertyIsEnumerable(cur)) return \"modifier\";\n    if (modifier.propertyIsEnumerable(cur)) return \"modifier\";\n    if (accessTypes.propertyIsEnumerable(cur)) return \"modifier\";\n\n    return \"variableName\";\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped){\n          var afterNext = stream.peek();\n          //look if the character if the quote is like the B in '10100010'B\n          if (afterNext){\n            afterNext = afterNext.toLowerCase();\n            if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n              stream.next();\n          }\n          end = true; break;\n        }\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = null;\n      return \"string\";\n    };\n  }\n\n  function Context(indented, column, type, align, prev) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.align = align;\n    this.prev = prev;\n  }\n  function pushContext(state, col, type) {\n    var indent = state.indented;\n    if (state.context && state.context.type == \"statement\")\n      indent = state.context.indented;\n    return state.context = new Context(indent, col, type, null, state.context);\n  }\n  function popContext(state) {\n    var t = state.context.type;\n    if (t == \")\" || t == \"]\" || t == \"}\")\n      state.indented = state.context.indented;\n    return state.context = state.context.prev;\n  }\n\n  //Interface\n  return {\n    name: \"asn1\",\n    startState: function() {\n      return {\n        tokenize: null,\n        context: new Context(-2, 0, \"top\", false),\n        indented: 0,\n        startOfLine: true\n      };\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (stream.eatSpace()) return null;\n      curPunc = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n          && ctx.type == \"statement\"){\n        popContext(state);\n      }\n      else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n      else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n      else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n      else if (curPunc == \"}\") {\n        while (ctx.type == \"statement\") ctx = popContext(state);\n        if (ctx.type == \"}\") ctx = popContext(state);\n        while (ctx.type == \"statement\") ctx = popContext(state);\n      }\n      else if (curPunc == ctx.type) popContext(state);\n      else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                     && curPunc != ';') || (ctx.type == \"statement\"\n                                                            && curPunc == \"newstatement\")))\n        pushContext(state, stream.column(), \"statement\");\n\n      state.startOfLine = false;\n      return style;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*[{}]$/,\n      commentTokens: {line: \"--\"}\n    }\n  };\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE;AAAG,QAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAM,WAAW;AAAA,EACf,UAAU,MAAM,kXAOkB;AAAA,EAClC,WAAW,MAAM,8CAA8C;AAAA,EAC/D,cAAc,MAAM,+MAIqB;AAAA,EACzC,QAAQ,MAAM,uCAAuC;AAAA,EACrD,MAAM,MAAM,gEACY;AAAA,EACxB,SAAS,MAAM,keASsD;AAAA,EACrE,UAAU,MAAM,kHAEU;AAAA,EAC1B,aAAa,MAAM,uEACyB;AAAA,EAC5C,kBAAkB;AACpB;AAEO,SAAS,KAAK,cAAc;AACjC,MAAI,WAAW,aAAa,YAAY,SAAS,UAC7C,YAAY,aAAa,aAAa,SAAS,WAC/C,eAAe,aAAa,gBAAgB,SAAS,cACrD,SAAS,aAAa,UAAU,SAAS,QACzC,OAAO,aAAa,QAAQ,SAAS,MACrC,UAAU,aAAa,WAAW,SAAS,SAC3C,WAAW,aAAa,YAAY,SAAS,UAC7C,cAAc,aAAa,eAAc,SAAS,aAClD,mBAAmB,aAAa,oBAAoB,SAAS,kBAC7D,mBAAmB,aAAa,qBAAqB;AACzD,MAAI,iBAAiB;AACrB,MAAI;AAEJ,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,YAAM,WAAW,YAAY,EAAE;AAC/B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AACA,QAAI,mBAAmB,KAAK,EAAE,GAAG;AAC/B,gBAAU;AACV,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAI;AACZ,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,KAAK,KAAK,EAAE,GAAG;AACjB,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,aAAO,SAAS,cAAc;AAC9B,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,QAAQ;AACxB,QAAI,MAAM,OAAO,QAAQ;AACzB,QAAI,SAAS,qBAAqB,GAAG;AAAG,aAAO;AAC/C,QAAI,UAAU,qBAAqB,GAAG;AAAG,aAAO;AAChD,QAAI,aAAa,qBAAqB,GAAG;AAAG,aAAO;AACnD,QAAI,OAAO,qBAAqB,GAAG;AAAG,aAAO;AAC7C,QAAI,KAAK,qBAAqB,GAAG;AAAG,aAAO;AAC3C,QAAI,QAAQ,qBAAqB,GAAG;AAAG,aAAO;AAC9C,QAAI,SAAS,qBAAqB,GAAG;AAAG,aAAO;AAC/C,QAAI,YAAY,qBAAqB,GAAG;AAAG,aAAO;AAElD,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,UAAU,OAAO,MAAM,MAAM;AACjC,cAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,YAAI,QAAQ,SAAS,CAAC,SAAQ;AAC5B,cAAI,YAAY,OAAO,KAAK;AAE5B,cAAI,WAAU;AACZ,wBAAY,UAAU,YAAY;AAClC,gBAAG,aAAa,OAAO,aAAa,OAAO,aAAa;AACtD,qBAAO,KAAK;AAAA,UAChB;AACA,gBAAM;AAAM;AAAA,QACd;AACA,kBAAU,CAAC,WAAW,QAAQ;AAAA,MAChC;AACA,UAAI,OAAO,EAAE,WAAW;AACtB,cAAM,WAAW;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACA,WAAS,YAAY,OAAO,KAAK,MAAM;AACrC,QAAI,SAAS,MAAM;AACnB,QAAI,MAAM,WAAW,MAAM,QAAQ,QAAQ;AACzC,eAAS,MAAM,QAAQ;AACzB,WAAO,MAAM,UAAU,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAAA,EAC3E;AACA,WAAS,WAAW,OAAO;AACzB,QAAI,IAAI,MAAM,QAAQ;AACtB,QAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AAC/B,YAAM,WAAW,MAAM,QAAQ;AACjC,WAAO,MAAM,UAAU,MAAM,QAAQ;AAAA,EACvC;AAGA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY,WAAW;AACrB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,SAAS,IAAI,QAAQ,IAAI,GAAG,OAAO,KAAK;AAAA,QACxC,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,MAAM,MAAM;AAChB,UAAI,OAAO,IAAI,GAAG;AAChB,YAAI,IAAI,SAAS;AAAM,cAAI,QAAQ;AACnC,cAAM,WAAW,OAAO,YAAY;AACpC,cAAM,cAAc;AAAA,MACtB;AACA,UAAI,OAAO,SAAS;AAAG,eAAO;AAC9B,gBAAU;AACV,UAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,UAAI,SAAS;AAAW,eAAO;AAC/B,UAAI,IAAI,SAAS;AAAM,YAAI,QAAQ;AAEnC,WAAK,WAAW,OAAO,WAAW,OAAO,WAAW,QAC7C,IAAI,QAAQ,aAAY;AAC7B,mBAAW,KAAK;AAAA,MAClB,WACS,WAAW;AAAK,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,eACvD,WAAW;AAAK,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,eACvD,WAAW;AAAK,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,eACvD,WAAW,KAAK;AACvB,eAAO,IAAI,QAAQ;AAAa,gBAAM,WAAW,KAAK;AACtD,YAAI,IAAI,QAAQ;AAAK,gBAAM,WAAW,KAAK;AAC3C,eAAO,IAAI,QAAQ;AAAa,gBAAM,WAAW,KAAK;AAAA,MACxD,WACS,WAAW,IAAI;AAAM,mBAAW,KAAK;AAAA,eACrC,sBAAuB,IAAI,QAAQ,OAAO,IAAI,QAAQ,UAC7B,WAAW,OAAS,IAAI,QAAQ,eACT,WAAW;AAClE,oBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AAEjD,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe;AAAA,MACf,eAAe,EAAC,MAAM,KAAI;AAAA,IAC5B;AAAA,EACF;AACF;", "names": ["words"]}