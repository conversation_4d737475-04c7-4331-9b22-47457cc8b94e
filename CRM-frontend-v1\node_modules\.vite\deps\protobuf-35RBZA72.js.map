{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/protobuf.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n};\n\nvar keywordArray = [\n  \"package\", \"message\", \"import\", \"syntax\",\n  \"required\", \"optional\", \"repeated\", \"reserved\", \"default\", \"extensions\", \"packed\",\n  \"bool\", \"bytes\", \"double\", \"enum\", \"float\", \"string\",\n  \"int32\", \"int64\", \"uint32\", \"uint64\", \"sint32\", \"sint64\", \"fixed32\", \"fixed64\", \"sfixed32\", \"sfixed64\",\n  \"option\", \"service\", \"rpc\", \"returns\"\n];\nvar keywords = wordRegexp(keywordArray);\n\nvar identifiers = new RegExp(\"^[_A-Za-z\\xa1-\\uffff][_A-Za-z0-9\\xa1-\\uffff]*\");\n\nfunction tokenBase(stream) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+/))\n      return \"number\";\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?/))\n      return \"number\";\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?/))\n      return \"number\";\n  }\n\n  // Handle Strings\n  if (stream.match(/^\"([^\"]|(\"\"))*\"/)) { return \"string\"; }\n  if (stream.match(/^'([^']|(''))*'/)) { return \"string\"; }\n\n  // Handle words\n  if (stream.match(keywords)) { return \"keyword\"; }\n  if (stream.match(identifiers)) { return \"variable\"; } ;\n\n  // Handle non-detected items\n  stream.next();\n  return null;\n};\n\nexport const protobuf = {\n  name: \"protobuf\",\n  token: tokenBase,\n  languageData: {\n    autocomplete: keywordArray\n  }\n}\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5D;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAChC;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EAAc;AAAA,EACzE;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAC5F;AAAA,EAAU;AAAA,EAAW;AAAA,EAAO;AAC9B;AACA,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,cAAc,IAAI,OAAO,+BAA+C;AAE5E,SAAS,UAAU,QAAQ;AAEzB,MAAI,OAAO,SAAS;AAAG,WAAO;AAG9B,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AACrC,QAAI,OAAO,MAAM,sBAAsB;AACrC,aAAO;AACT,QAAI,OAAO,MAAM,iCAAiC;AAChD,aAAO;AACT,QAAI,OAAO,MAAM,4BAA4B;AAC3C,aAAO;AAAA,EACX;AAGA,MAAI,OAAO,MAAM,iBAAiB,GAAG;AAAE,WAAO;AAAA,EAAU;AACxD,MAAI,OAAO,MAAM,iBAAiB,GAAG;AAAE,WAAO;AAAA,EAAU;AAGxD,MAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAW;AAChD,MAAI,OAAO,MAAM,WAAW,GAAG;AAAE,WAAO;AAAA,EAAY;AAAE;AAGtD,SAAO,KAAK;AACZ,SAAO;AACT;AAEO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,cAAc;AAAA,IACZ,cAAc;AAAA,EAChB;AACF;", "names": []}