{"version": 3, "sources": ["../../@lezer/python/dist/index.js", "../../@codemirror/lang-python/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, ContextTracker, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst printKeyword = 1,\n  indent = 194,\n  dedent = 195,\n  newline$1 = 196,\n  blankLineStart = 197,\n  newlineBracketed = 198,\n  eof = 199,\n  stringContent = 200,\n  Escape = 2,\n  replacementStart = 3,\n  stringEnd = 201,\n  ParenL = 24,\n  ParenthesizedExpression = 25,\n  TupleExpression = 49,\n  ComprehensionExpression = 50,\n  BracketL = 55,\n  ArrayExpression = 56,\n  ArrayComprehensionExpression = 57,\n  BraceL = 59,\n  DictionaryExpression = 60,\n  DictionaryComprehensionExpression = 61,\n  SetExpression = 62,\n  SetComprehensionExpression = 63,\n  ArgList = 65,\n  subscript = 238,\n  String$1 = 71,\n  stringStart = 241,\n  stringStartD = 242,\n  stringStartL = 243,\n  stringStartLD = 244,\n  stringStartR = 245,\n  stringStartRD = 246,\n  stringStartRL = 247,\n  stringStartRLD = 248,\n  FormatString = 72,\n  stringStartF = 249,\n  stringStartFD = 250,\n  stringStartFL = 251,\n  stringStartFLD = 252,\n  stringStartFR = 253,\n  stringStartFRD = 254,\n  stringStartFRL = 255,\n  stringStartFRLD = 256,\n  FormatReplacement = 73,\n  nestedFormatReplacement = 77,\n  importList = 263,\n  TypeParamList = 112,\n  ParamList = 130,\n  SequencePattern = 151,\n  MappingPattern = 152,\n  PatternArgList = 155;\n\nconst newline = 10, carriageReturn = 13, space = 32, tab = 9, hash = 35, parenOpen = 40, dot = 46,\n      braceOpen = 123, braceClose = 125, singleQuote = 39, doubleQuote = 34, backslash = 92,\n      letter_o = 111, letter_x = 120, letter_N = 78, letter_u = 117, letter_U = 85;\n\nconst bracketed = new Set([\n  ParenthesizedExpression, TupleExpression, ComprehensionExpression, importList, ArgList, ParamList,\n  ArrayExpression, ArrayComprehensionExpression, subscript,\n  SetExpression, SetComprehensionExpression, FormatString, FormatReplacement, nestedFormatReplacement,\n  DictionaryExpression, DictionaryComprehensionExpression,\n  SequencePattern, MappingPattern, PatternArgList, TypeParamList\n]);\n\nfunction isLineBreak(ch) {\n  return ch == newline || ch == carriageReturn\n}\n\nfunction isHex(ch) {\n  return ch >= 48 && ch <= 57 || ch >= 65 && ch <= 70 || ch >= 97 && ch <= 102\n}\n\nconst newlines = new ExternalTokenizer((input, stack) => {\n  let prev;\n  if (input.next < 0) {\n    input.acceptToken(eof);\n  } else if (stack.context.flags & cx_Bracketed) {\n    if (isLineBreak(input.next)) input.acceptToken(newlineBracketed, 1);\n  } else if (((prev = input.peek(-1)) < 0 || isLineBreak(prev)) &&\n             stack.canShift(blankLineStart)) {\n    let spaces = 0;\n    while (input.next == space || input.next == tab) { input.advance(); spaces++; }\n    if (input.next == newline || input.next == carriageReturn || input.next == hash)\n      input.acceptToken(blankLineStart, -spaces);\n  } else if (isLineBreak(input.next)) {\n    input.acceptToken(newline$1, 1);\n  }\n}, {contextual: true});\n\nconst indentation = new ExternalTokenizer((input, stack) => {\n  let context = stack.context;\n  if (context.flags) return\n  let prev = input.peek(-1);\n  if (prev == newline || prev == carriageReturn) {\n    let depth = 0, chars = 0;\n    for (;;) {\n      if (input.next == space) depth++;\n      else if (input.next == tab) depth += 8 - (depth % 8);\n      else break\n      input.advance();\n      chars++;\n    }\n    if (depth != context.indent &&\n        input.next != newline && input.next != carriageReturn && input.next != hash) {\n      if (depth < context.indent) input.acceptToken(dedent, -chars);\n      else input.acceptToken(indent);\n    }\n  }\n});\n\n// Flags used in Context objects\nconst cx_Bracketed = 1, cx_String = 2, cx_DoubleQuote = 4, cx_Long = 8, cx_Raw = 16, cx_Format = 32;\n\nfunction Context(parent, indent, flags) {\n  this.parent = parent;\n  this.indent = indent;\n  this.flags = flags;\n  this.hash = (parent ? parent.hash + parent.hash << 8 : 0) + indent + (indent << 4) + flags + (flags << 6);\n}\n\nconst topIndent = new Context(null, 0, 0);\n\nfunction countIndent(space) {\n  let depth = 0;\n  for (let i = 0; i < space.length; i++)\n    depth += space.charCodeAt(i) == tab ? 8 - (depth % 8) : 1;\n  return depth\n}\n\nconst stringFlags = new Map([\n  [stringStart, 0],\n  [stringStartD, cx_DoubleQuote],\n  [stringStartL, cx_Long],\n  [stringStartLD, cx_Long | cx_DoubleQuote],\n  [stringStartR, cx_Raw],\n  [stringStartRD, cx_Raw | cx_DoubleQuote],\n  [stringStartRL, cx_Raw | cx_Long],\n  [stringStartRLD, cx_Raw | cx_Long | cx_DoubleQuote],\n  [stringStartF, cx_Format],\n  [stringStartFD, cx_Format | cx_DoubleQuote],\n  [stringStartFL, cx_Format | cx_Long],\n  [stringStartFLD, cx_Format | cx_Long | cx_DoubleQuote],\n  [stringStartFR, cx_Format | cx_Raw],\n  [stringStartFRD, cx_Format | cx_Raw | cx_DoubleQuote],\n  [stringStartFRL, cx_Format | cx_Raw | cx_Long],\n  [stringStartFRLD, cx_Format | cx_Raw | cx_Long | cx_DoubleQuote]\n].map(([term, flags]) => [term, flags | cx_String]));\n\nconst trackIndent = new ContextTracker({\n  start: topIndent,\n  reduce(context, term, _, input) {\n    if ((context.flags & cx_Bracketed) && bracketed.has(term) ||\n        (term == String$1 || term == FormatString) && (context.flags & cx_String))\n      return context.parent\n    return context\n  },\n  shift(context, term, stack, input) {\n    if (term == indent)\n      return new Context(context, countIndent(input.read(input.pos, stack.pos)), 0)\n    if (term == dedent)\n      return context.parent\n    if (term == ParenL || term == BracketL || term == BraceL || term == replacementStart)\n      return new Context(context, 0, cx_Bracketed)\n    if (stringFlags.has(term))\n      return new Context(context, 0, stringFlags.get(term) | (context.flags & cx_Bracketed))\n    return context\n  },\n  hash(context) { return context.hash }\n});\n\nconst legacyPrint = new ExternalTokenizer(input => {\n  for (let i = 0; i < 5; i++) {\n    if (input.next != \"print\".charCodeAt(i)) return\n    input.advance();\n  }\n  if (/\\w/.test(String.fromCharCode(input.next))) return\n  for (let off = 0;; off++) {\n    let next = input.peek(off);\n    if (next == space || next == tab) continue\n    if (next != parenOpen && next != dot && next != newline && next != carriageReturn && next != hash)\n      input.acceptToken(printKeyword);\n    return\n  }\n});\n\nconst strings = new ExternalTokenizer((input, stack) => {\n  let {flags} = stack.context;\n  let quote = (flags & cx_DoubleQuote) ? doubleQuote : singleQuote;\n  let long = (flags & cx_Long) > 0;\n  let escapes = !(flags & cx_Raw);\n  let format = (flags & cx_Format) > 0;\n\n  let start = input.pos;\n  for (;;) {\n    if (input.next < 0) {\n      break\n    } else if (format && input.next == braceOpen) {\n      if (input.peek(1) == braceOpen) {\n        input.advance(2);\n      } else {\n        if (input.pos == start) {\n          input.acceptToken(replacementStart, 1);\n          return\n        }\n        break\n      }\n    } else if (escapes && input.next == backslash) {\n      if (input.pos == start) {\n        input.advance();\n        let escaped = input.next;\n        if (escaped >= 0) {\n          input.advance();\n          skipEscape(input, escaped);\n        }\n        input.acceptToken(Escape);\n        return\n      }\n      break\n    } else if (input.next == quote && (!long || input.peek(1) == quote && input.peek(2) == quote)) {\n      if (input.pos == start) {\n        input.acceptToken(stringEnd, long ? 3 : 1);\n        return\n      }\n      break\n    } else if (input.next == newline) {\n      if (long) {\n        input.advance();\n      } else if (input.pos == start) {\n        input.acceptToken(stringEnd);\n        return\n      }\n      break\n    } else {\n      input.advance();\n    }\n  }\n  if (input.pos > start) input.acceptToken(stringContent);\n});\n\nfunction skipEscape(input, ch) {\n  if (ch == letter_o) {\n    for (let i = 0; i < 2 && input.next >= 48 && input.next <= 55; i++) input.advance();\n  } else if (ch == letter_x) {\n    for (let i = 0; i < 2 && isHex(input.next); i++) input.advance();\n  } else if (ch == letter_u) {\n    for (let i = 0; i < 4 && isHex(input.next); i++) input.advance();\n  } else if (ch == letter_U) {\n    for (let i = 0; i < 8 && isHex(input.next); i++) input.advance();\n  } else if (ch == letter_N) {\n    if (input.next == braceOpen) {\n      input.advance();\n      while (input.next >= 0 && input.next != braceClose && input.next != singleQuote &&\n             input.next != doubleQuote && input.next != newline) input.advance();\n      if (input.next == braceClose) input.advance();\n    }\n  }\n}\n\nconst pythonHighlighting = styleTags({\n  \"async \\\"*\\\" \\\"**\\\" FormatConversion FormatSpec\": tags.modifier,\n  \"for while if elif else try except finally return raise break continue with pass assert await yield match case\": tags.controlKeyword,\n  \"in not and or is del\": tags.operatorKeyword,\n  \"from def class global nonlocal lambda\": tags.definitionKeyword,\n  import: tags.moduleKeyword,\n  \"with as print\": tags.keyword,\n  Boolean: tags.bool,\n  None: tags.null,\n  VariableName: tags.variableName,\n  \"CallExpression/VariableName\": tags.function(tags.variableName),\n  \"FunctionDefinition/VariableName\": tags.function(tags.definition(tags.variableName)),\n  \"ClassDefinition/VariableName\": tags.definition(tags.className),\n  PropertyName: tags.propertyName,\n  \"CallExpression/MemberExpression/PropertyName\": tags.function(tags.propertyName),\n  Comment: tags.lineComment,\n  Number: tags.number,\n  String: tags.string,\n  FormatString: tags.special(tags.string),\n  Escape: tags.escape,\n  UpdateOp: tags.updateOperator,\n  \"ArithOp!\": tags.arithmeticOperator,\n  BitOp: tags.bitwiseOperator,\n  CompareOp: tags.compareOperator,\n  AssignOp: tags.definitionOperator,\n  Ellipsis: tags.punctuation,\n  At: tags.meta,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace,\n  \".\": tags.derefOperator,\n  \", ;\": tags.separator\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_identifier = {__proto__:null,await:44, or:54, and:56, in:60, not:62, is:64, if:70, else:72, lambda:76, yield:94, from:96, async:102, for:104, None:162, True:164, False:164, del:178, pass:182, break:186, continue:190, return:194, raise:202, import:206, as:208, global:212, nonlocal:214, assert:218, type:223, elif:236, while:240, try:246, except:248, finally:250, with:254, def:258, class:268, match:279, case:285};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"##jO`QeOOP$}OSOOO&WQtO'#HUOOQS'#Co'#CoOOQS'#Cp'#CpO'vQdO'#CnO*UQtO'#HTOOQS'#HU'#HUOOQS'#DU'#DUOOQS'#HT'#HTO*rQdO'#D_O+VQdO'#DfO+gQdO'#DjO+zOWO'#DuO,VOWO'#DvO.[QtO'#GuOOQS'#Gu'#GuO'vQdO'#GtO0ZQtO'#GtOOQS'#Eb'#EbO0rQdO'#EcOOQS'#Gs'#GsO0|QdO'#GrOOQV'#Gr'#GrO1XQdO'#FYOOQS'#G^'#G^O1^QdO'#FXOOQV'#IS'#ISOOQV'#Gq'#GqOOQV'#Fq'#FqQ`QeOOO'vQdO'#CqO1lQdO'#C}O1sQdO'#DRO2RQdO'#HYO2cQtO'#EVO'vQdO'#EWOOQS'#EY'#EYOOQS'#E['#E[OOQS'#E^'#E^O2wQdO'#E`O3_QdO'#EdO3rQdO'#EfO3zQtO'#EfO1XQdO'#EiO0rQdO'#ElO1XQdO'#EnO0rQdO'#EtO0rQdO'#EwO4VQdO'#EyO4^QdO'#FOO4iQdO'#EzO0rQdO'#FOO1XQdO'#FQO1XQdO'#FVO4nQdO'#F[P4uOdO'#GpPOOO)CBd)CBdOOQS'#Ce'#CeOOQS'#Cf'#CfOOQS'#Cg'#CgOOQS'#Ch'#ChOOQS'#Ci'#CiOOQS'#Cj'#CjOOQS'#Cl'#ClO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO5TQdO'#DoOOQS,5:Y,5:YO5hQdO'#HdOOQS,5:],5:]O5uQ!fO,5:]O5zQtO,59YO1lQdO,59bO1lQdO,59bO1lQdO,59bO8jQdO,59bO8oQdO,59bO8vQdO,59jO8}QdO'#HTO:TQdO'#HSOOQS'#HS'#HSOOQS'#D['#D[O:lQdO,59aO'vQdO,59aO:zQdO,59aOOQS,59y,59yO;PQdO,5:RO'vQdO,5:ROOQS,5:Q,5:QO;_QdO,5:QO;dQdO,5:XO'vQdO,5:XO'vQdO,5:VOOQS,5:U,5:UO;uQdO,5:UO;zQdO,5:WOOOW'#Fy'#FyO<POWO,5:aOOQS,5:a,5:aO<[QdO'#HwOOOW'#Dw'#DwOOOW'#Fz'#FzO<lOWO,5:bOOQS,5:b,5:bOOQS'#F}'#F}O<zQtO,5:iO?lQtO,5=`O@VQ#xO,5=`O@vQtO,5=`OOQS,5:},5:}OA_QeO'#GWOBqQdO,5;^OOQV,5=^,5=^OB|QtO'#IPOCkQdO,5;tOOQS-E:[-E:[OOQV,5;s,5;sO4dQdO'#FQOOQV-E9o-E9oOCsQtO,59]OEzQtO,59iOFeQdO'#HVOFpQdO'#HVO1XQdO'#HVOF{QdO'#DTOGTQdO,59mOGYQdO'#HZO'vQdO'#HZO0rQdO,5=tOOQS,5=t,5=tO0rQdO'#EROOQS'#ES'#ESOGwQdO'#GPOHXQdO,58|OHXQdO,58|O*xQdO,5:oOHgQtO'#H]OOQS,5:r,5:rOOQS,5:z,5:zOHzQdO,5;OOI]QdO'#IOO1XQdO'#H}OOQS,5;Q,5;QOOQS'#GT'#GTOIqQtO,5;QOJPQdO,5;QOJUQdO'#IQOOQS,5;T,5;TOJdQdO'#H|OOQS,5;W,5;WOJuQdO,5;YO4iQdO,5;`O4iQdO,5;cOJ}QtO'#ITO'vQdO'#ITOKXQdO,5;eO4VQdO,5;eO0rQdO,5;jO1XQdO,5;lOK^QeO'#EuOLjQgO,5;fO!!kQdO'#IUO4iQdO,5;jO!!vQdO,5;lO!#OQdO,5;qO!#ZQtO,5;vO'vQdO,5;vPOOO,5=[,5=[P!#bOSO,5=[P!#jOdO,5=[O!&bQtO1G.jO!&iQtO1G.jO!)YQtO1G.jO!)dQtO1G.jO!+}QtO1G.jO!,bQtO1G.jO!,uQdO'#HcO!-TQtO'#GuO0rQdO'#HcO!-_QdO'#HbOOQS,5:Z,5:ZO!-gQdO,5:ZO!-lQdO'#HeO!-wQdO'#HeO!.[QdO,5>OOOQS'#Ds'#DsOOQS1G/w1G/wOOQS1G.|1G.|O!/[QtO1G.|O!/cQtO1G.|O1lQdO1G.|O!0OQdO1G/UOOQS'#DZ'#DZO0rQdO,59tOOQS1G.{1G.{O!0VQdO1G/eO!0gQdO1G/eO!0oQdO1G/fO'vQdO'#H[O!0tQdO'#H[O!0yQtO1G.{O!1ZQdO,59iO!2aQdO,5=zO!2qQdO,5=zO!2yQdO1G/mO!3OQtO1G/mOOQS1G/l1G/lO!3`QdO,5=uO!4VQdO,5=uO0rQdO1G/qO!4tQdO1G/sO!4yQtO1G/sO!5ZQtO1G/qOOQS1G/p1G/pOOQS1G/r1G/rOOOW-E9w-E9wOOQS1G/{1G/{O!5kQdO'#HxO0rQdO'#HxO!5|QdO,5>cOOOW-E9x-E9xOOQS1G/|1G/|OOQS-E9{-E9{O!6[Q#xO1G2zO!6{QtO1G2zO'vQdO,5<jOOQS,5<j,5<jOOQS-E9|-E9|OOQS,5<r,5<rOOQS-E:U-E:UOOQV1G0x1G0xO1XQdO'#GRO!7dQtO,5>kOOQS1G1`1G1`O!8RQdO1G1`OOQS'#DV'#DVO0rQdO,5=qOOQS,5=q,5=qO!8WQdO'#FrO!8cQdO,59oO!8kQdO1G/XO!8uQtO,5=uOOQS1G3`1G3`OOQS,5:m,5:mO!9fQdO'#GtOOQS,5<k,5<kOOQS-E9}-E9}O!9wQdO1G.hOOQS1G0Z1G0ZO!:VQdO,5=wO!:gQdO,5=wO0rQdO1G0jO0rQdO1G0jO!:xQdO,5>jO!;ZQdO,5>jO1XQdO,5>jO!;lQdO,5>iOOQS-E:R-E:RO!;qQdO1G0lO!;|QdO1G0lO!<RQdO,5>lO!<aQdO,5>lO!<oQdO,5>hO!=VQdO,5>hO!=hQdO'#EpO0rQdO1G0tO!=sQdO1G0tO!=xQgO1G0zO!AvQgO1G0}O!EqQdO,5>oO!E{QdO,5>oO!FTQtO,5>oO0rQdO1G1PO!F_QdO1G1PO4iQdO1G1UO!!vQdO1G1WOOQV,5;a,5;aO!FdQfO,5;aO!FiQgO1G1QO!JjQdO'#GZO4iQdO1G1QO4iQdO1G1QO!JzQdO,5>pO!KXQdO,5>pO1XQdO,5>pOOQV1G1U1G1UO!KaQdO'#FSO!KrQ!fO1G1WO!KzQdO1G1WOOQV1G1]1G1]O4iQdO1G1]O!LPQdO1G1]O!LXQdO'#F^OOQV1G1b1G1bO!#ZQtO1G1bPOOO1G2v1G2vP!L^OSO1G2vOOQS,5=},5=}OOQS'#Dp'#DpO0rQdO,5=}O!LfQdO,5=|O!LyQdO,5=|OOQS1G/u1G/uO!MRQdO,5>PO!McQdO,5>PO!MkQdO,5>PO!NOQdO,5>PO!N`QdO,5>POOQS1G3j1G3jOOQS7+$h7+$hO!8kQdO7+$pO#!RQdO1G.|O#!YQdO1G.|OOQS1G/`1G/`OOQS,5<`,5<`O'vQdO,5<`OOQS7+%P7+%PO#!aQdO7+%POOQS-E9r-E9rOOQS7+%Q7+%QO#!qQdO,5=vO'vQdO,5=vOOQS7+$g7+$gO#!vQdO7+%PO##OQdO7+%QO##TQdO1G3fOOQS7+%X7+%XO##eQdO1G3fO##mQdO7+%XOOQS,5<_,5<_O'vQdO,5<_O##rQdO1G3aOOQS-E9q-E9qO#$iQdO7+%]OOQS7+%_7+%_O#$wQdO1G3aO#%fQdO7+%_O#%kQdO1G3gO#%{QdO1G3gO#&TQdO7+%]O#&YQdO,5>dO#&sQdO,5>dO#&sQdO,5>dOOQS'#Dx'#DxO#'UO&jO'#DzO#'aO`O'#HyOOOW1G3}1G3}O#'fQdO1G3}O#'nQdO1G3}O#'yQ#xO7+(fO#(jQtO1G2UP#)TQdO'#GOOOQS,5<m,5<mOOQS-E:P-E:POOQS7+&z7+&zOOQS1G3]1G3]OOQS,5<^,5<^OOQS-E9p-E9pOOQS7+$s7+$sO#)bQdO,5=`O#){QdO,5=`O#*^QtO,5<aO#*qQdO1G3cOOQS-E9s-E9sOOQS7+&U7+&UO#+RQdO7+&UO#+aQdO,5<nO#+uQdO1G4UOOQS-E:Q-E:QO#,WQdO1G4UOOQS1G4T1G4TOOQS7+&W7+&WO#,iQdO7+&WOOQS,5<p,5<pO#,tQdO1G4WOOQS-E:S-E:SOOQS,5<l,5<lO#-SQdO1G4SOOQS-E:O-E:OO1XQdO'#EqO#-jQdO'#EqO#-uQdO'#IRO#-}QdO,5;[OOQS7+&`7+&`O0rQdO7+&`O#.SQgO7+&fO!JmQdO'#GXO4iQdO7+&fO4iQdO7+&iO#2QQtO,5<tO'vQdO,5<tO#2[QdO1G4ZOOQS-E:W-E:WO#2fQdO1G4ZO4iQdO7+&kO0rQdO7+&kOOQV7+&p7+&pO!KrQ!fO7+&rO!KzQdO7+&rO`QeO1G0{OOQV-E:X-E:XO4iQdO7+&lO4iQdO7+&lOOQV,5<u,5<uO#2nQdO,5<uO!JmQdO,5<uOOQV7+&l7+&lO#2yQgO7+&lO#6tQdO,5<vO#7PQdO1G4[OOQS-E:Y-E:YO#7^QdO1G4[O#7fQdO'#IWO#7tQdO'#IWO1XQdO'#IWOOQS'#IW'#IWO#8PQdO'#IVOOQS,5;n,5;nO#8XQdO,5;nO0rQdO'#FUOOQV7+&r7+&rO4iQdO7+&rOOQV7+&w7+&wO4iQdO7+&wO#8^QfO,5;xOOQV7+&|7+&|POOO7+(b7+(bO#8cQdO1G3iOOQS,5<c,5<cO#8qQdO1G3hOOQS-E9u-E9uO#9UQdO,5<dO#9aQdO,5<dO#9tQdO1G3kOOQS-E9v-E9vO#:UQdO1G3kO#:^QdO1G3kO#:nQdO1G3kO#:UQdO1G3kOOQS<<H[<<H[O#:yQtO1G1zOOQS<<Hk<<HkP#;WQdO'#FtO8vQdO1G3bO#;eQdO1G3bO#;jQdO<<HkOOQS<<Hl<<HlO#;zQdO7+)QOOQS<<Hs<<HsO#<[QtO1G1yP#<{QdO'#FsO#=YQdO7+)RO#=jQdO7+)RO#=rQdO<<HwO#=wQdO7+({OOQS<<Hy<<HyO#>nQdO,5<bO'vQdO,5<bOOQS-E9t-E9tOOQS<<Hw<<HwOOQS,5<g,5<gO0rQdO,5<gO#>sQdO1G4OOOQS-E9y-E9yO#?^QdO1G4OO<[QdO'#H{OOOO'#D{'#D{OOOO'#F|'#F|O#?oO&jO,5:fOOOW,5>e,5>eOOOW7+)i7+)iO#?zQdO7+)iO#@SQdO1G2zO#@mQdO1G2zP'vQdO'#FuO0rQdO<<IpO1XQdO1G2YP1XQdO'#GSO#AOQdO7+)pO#AaQdO7+)pOOQS<<Ir<<IrP1XQdO'#GUP0rQdO'#GQOOQS,5;],5;]O#ArQdO,5>mO#BQQdO,5>mOOQS1G0v1G0vOOQS<<Iz<<IzOOQV-E:V-E:VO4iQdO<<JQOOQV,5<s,5<sO4iQdO,5<sOOQV<<JQ<<JQOOQV<<JT<<JTO#BYQtO1G2`P#BdQdO'#GYO#BkQdO7+)uO#BuQgO<<JVO4iQdO<<JVOOQV<<J^<<J^O4iQdO<<J^O!KrQ!fO<<J^O#FpQgO7+&gOOQV<<JW<<JWO#FzQgO<<JWOOQV1G2a1G2aO1XQdO1G2aO#JuQdO1G2aO4iQdO<<JWO1XQdO1G2bP0rQdO'#G[O#KQQdO7+)vO#K_QdO7+)vOOQS'#FT'#FTO0rQdO,5>rO#KgQdO,5>rOOQS,5>r,5>rO#KrQdO,5>qO#LTQdO,5>qOOQS1G1Y1G1YOOQS,5;p,5;pOOQV<<Jc<<JcO#L]QdO1G1dOOQS7+)T7+)TP#LbQdO'#FwO#LrQdO1G2OO#MVQdO1G2OO#MgQdO1G2OP#MrQdO'#FxO#NPQdO7+)VO#NaQdO7+)VO#NaQdO7+)VO#NiQdO7+)VO#NyQdO7+(|O8vQdO7+(|OOQSAN>VAN>VO$ dQdO<<LmOOQSAN>cAN>cO0rQdO1G1|O$ tQtO1G1|P$!OQdO'#FvOOQS1G2R1G2RP$!]QdO'#F{O$!jQdO7+)jO$#TQdO,5>gOOOO-E9z-E9zOOOW<<MT<<MTO$#cQdO7+(fOOQSAN?[AN?[OOQS7+'t7+'tO$#|QdO<<M[OOQS,5<q,5<qO$$_QdO1G4XOOQS-E:T-E:TOOQVAN?lAN?lOOQV1G2_1G2_O4iQdOAN?qO$$mQgOAN?qOOQVAN?xAN?xO4iQdOAN?xOOQV<<JR<<JRO4iQdOAN?rO4iQdO7+'{OOQV7+'{7+'{O1XQdO7+'{OOQVAN?rAN?rOOQS7+'|7+'|O$(hQdO<<MbOOQS1G4^1G4^O0rQdO1G4^OOQS,5<w,5<wO$(uQdO1G4]OOQS-E:Z-E:ZOOQU'#G_'#G_O$)WQfO7+'OO$)cQdO'#F_O$*jQdO7+'jO$*zQdO7+'jOOQS7+'j7+'jO$+VQdO<<LqO$+gQdO<<LqO$+gQdO<<LqO$+oQdO'#H^OOQS<<Lh<<LhO$+yQdO<<LhOOQS7+'h7+'hOOQS'#D|'#D|OOOO1G4R1G4RO$,dQdO1G4RO$,lQdO1G4RP!=hQdO'#GVOOQVG25]G25]O4iQdOG25]OOQVG25dG25dOOQVG25^G25^OOQV<<Kg<<KgO4iQdO<<KgOOQS7+)x7+)xP$,wQdO'#G]OOQU-E:]-E:]OOQV<<Jj<<JjO$-kQtO'#FaOOQS'#Fc'#FcO$-{QdO'#FbO$.mQdO'#FbOOQS'#Fb'#FbO$.rQdO'#IYO$)cQdO'#FiO$)cQdO'#FiO$/ZQdO'#FjO$)cQdO'#FkO$/bQdO'#IZOOQS'#IZ'#IZO$0PQdO,5;yOOQS<<KU<<KUO$0XQdO<<KUO$0iQdOANB]O$0yQdOANB]O$1RQdO'#H_OOQS'#H_'#H_O1sQdO'#DcO$1lQdO,5=xOOQSANBSANBSOOOO7+)m7+)mO$2TQdO7+)mOOQVLD*wLD*wOOQVANARANARO5uQ!fO'#GaO$2]QtO,5<SO$)cQdO'#FmOOQS,5<W,5<WOOQS'#Fd'#FdO$2}QdO,5;|O$3SQdO,5;|OOQS'#Fg'#FgO$)cQdO'#G`O$3tQdO,5<QO$4`QdO,5>tO$4pQdO,5>tO1XQdO,5<PO$5RQdO,5<TO$5WQdO,5<TO$)cQdO'#I[O$5]QdO'#I[O$5bQdO,5<UOOQS,5<V,5<VO'vQdO'#FpOOQU1G1e1G1eO4iQdO1G1eOOQSAN@pAN@pO$5gQdOG27wO$5wQdO,59}OOQS1G3d1G3dOOOO<<MX<<MXOOQS,5<{,5<{OOQS-E:_-E:_O$5|QtO'#FaO$6TQdO'#I]O$6cQdO'#I]O$6kQdO,5<XOOQS1G1h1G1hO$6pQdO1G1hO$6uQdO,5<zOOQS-E:^-E:^O$7aQdO,5=OO$7xQdO1G4`OOQS-E:b-E:bOOQS1G1k1G1kOOQS1G1o1G1oO$8YQdO,5>vO$)cQdO,5>vOOQS1G1p1G1pO$8hQtO,5<[OOQU7+'P7+'PO$+oQdO1G/iO$)cQdO,5<YO$8oQdO,5>wO$8vQdO,5>wOOQS1G1s1G1sOOQS7+'S7+'SP$)cQdO'#GdO$9OQdO1G4bO$9YQdO1G4bO$9bQdO1G4bOOQS7+%T7+%TO$9pQdO1G1tO$:OQtO'#FaO$:VQdO,5<}OOQS,5<},5<}O$:eQdO1G4cOOQS-E:a-E:aO$)cQdO,5<|O$:lQdO,5<|O$:qQdO7+)|OOQS-E:`-E:`O$:{QdO7+)|O$)cQdO,5<ZP$)cQdO'#GcO$;TQdO1G2hO$)cQdO1G2hP$;cQdO'#GbO$;jQdO<<MhO$;tQdO1G1uO$<SQdO7+(SO8vQdO'#C}O8vQdO,59bO8vQdO,59bO8vQdO,59bO$<bQtO,5=`O8vQdO1G.|O0rQdO1G/XO0rQdO7+$pP$<uQdO'#GOO'vQdO'#GtO$=SQdO,59bO$=XQdO,59bO$=`QdO,59mO$=eQdO1G/UO1sQdO'#DRO8vQdO,59j\",\n  stateData: \"$>O~O%cOS%^OSSOS%]PQ~OPdOVaOfoOhYOopOs!POvqO!PrO!Q{O!T!SO!U!RO!XZO!][O!h`O!r`O!s`O!t`O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO#l!QO#o!TO#s!UO#u!VO#z!WO#}hO$P!XO%oRO%pRO%tSO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O~O%]!YO~OV!aO_!aOa!bOh!iO!X!kO!f!mO%j![O%k!]O%l!^O%m!_O%n!_O%o!`O%p!`O%q!aO%r!aO%s!aO~Ok%xXl%xXm%xXn%xXo%xXp%xXs%xXz%xX{%xX!x%xX#g%xX%[%xX%_%xX%z%xXg%xX!T%xX!U%xX%{%xX!W%xX![%xX!Q%xX#[%xXt%xX!m%xX~P%SOfoOhYO!XZO!][O!h`O!r`O!s`O!t`O%oRO%pRO%tSO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O~Oz%wX{%wX#g%wX%[%wX%_%wX%z%wX~Ok!pOl!qOm!oOn!oOo!rOp!sOs!tO!x%wX~P)pOV!zOg!|Oo0cOv0qO!PrO~P'vOV#OOo0cOv0qO!W#PO~P'vOV#SOa#TOo0cOv0qO![#UO~P'vOQ#XO%`#XO%a#ZO~OQ#^OR#[O%`#^O%a#`O~OV%iX_%iXa%iXh%iXk%iXl%iXm%iXn%iXo%iXp%iXs%iXz%iX!X%iX!f%iX%j%iX%k%iX%l%iX%m%iX%n%iX%o%iX%p%iX%q%iX%r%iX%s%iXg%iX!T%iX!U%iX~O&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O{%iX!x%iX#g%iX%[%iX%_%iX%z%iX%{%iX!W%iX![%iX!Q%iX#[%iXt%iX!m%iX~P,eOz#dO{%hX!x%hX#g%hX%[%hX%_%hX%z%hX~Oo0cOv0qO~P'vO#g#gO%[#iO%_#iO~O%uWO~O!T#nO#u!VO#z!WO#}hO~OopO~P'vOV#sOa#tO%uWO{wP~OV#xOo0cOv0qO!Q#yO~P'vO{#{O!x$QO%z#|O#g!yX%[!yX%_!yX~OV#xOo0cOv0qO#g#SX%[#SX%_#SX~P'vOo0cOv0qO#g#WX%[#WX%_#WX~P'vOh$WO%uWO~O!f$YO!r$YO%uWO~OV$eO~P'vO!U$gO#s$hO#u$iO~O{$jO~OV$qO~P'vOS$sO%[$rO%_$rO%c$tO~OV$}Oa$}Og%POo0cOv0qO~P'vOo0cOv0qO{%SO~P'vO&Y%UO~Oa!bOh!iO!X!kO!f!mOVba_bakbalbambanbaobapbasbazba{ba!xba#gba%[ba%_ba%jba%kba%lba%mba%nba%oba%pba%qba%rba%sba%zbagba!Tba!Uba%{ba!Wba![ba!Qba#[batba!mba~On%ZO~Oo%ZO~P'vOo0cO~P'vOk0eOl0fOm0dOn0dOo0mOp0nOs0rOg%wX!T%wX!U%wX%{%wX!W%wX![%wX!Q%wX#[%wX!m%wX~P)pO%{%]Og%vXz%vX!T%vX!U%vX!W%vX{%vX~Og%_Oz%`O!T%dO!U%cO~Og%_O~Oz%gO!T%dO!U%cO!W&SX~O!W%kO~Oz%lO{%nO!T%dO!U%cO![%}X~O![%rO~O![%sO~OQ#XO%`#XO%a%uO~OV%wOo0cOv0qO!PrO~P'vOQ#^OR#[O%`#^O%a%zO~OV!qa_!qaa!qah!qak!qal!qam!qan!qao!qap!qas!qaz!qa{!qa!X!qa!f!qa!x!qa#g!qa%[!qa%_!qa%j!qa%k!qa%l!qa%m!qa%n!qa%o!qa%p!qa%q!qa%r!qa%s!qa%z!qag!qa!T!qa!U!qa%{!qa!W!qa![!qa!Q!qa#[!qat!qa!m!qa~P#yOz%|O{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~P%SOV&OOopOvqO{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~P'vOz%|O{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~OPdOVaOopOvqO!PrO!Q{O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO#g$zX%[$zX%_$zX~P'vO#g#gO%[&TO%_&TO~O!f&UOh&sX%[&sXz&sX#[&sX#g&sX%_&sX#Z&sXg&sX~Oh!iO%[&WO~Okealeameaneaoeapeaseazea{ea!xea#gea%[ea%_ea%zeagea!Tea!Uea%{ea!Wea![ea!Qea#[eatea!mea~P%SOsqazqa{qa#gqa%[qa%_qa%zqa~Ok!pOl!qOm!oOn!oOo!rOp!sO!xqa~PEcO%z&YOz%yX{%yX~O%uWOz%yX{%yX~Oz&]O{wX~O{&_O~Oz%lO#g%}X%[%}X%_%}Xg%}X{%}X![%}X!m%}X%z%}X~OV0lOo0cOv0qO!PrO~P'vO%z#|O#gUa%[Ua%_Ua~Oz&hO#g&PX%[&PX%_&PXn&PX~P%SOz&kO!Q&jO#g#Wa%[#Wa%_#Wa~Oz&lO#[&nO#g&rX%[&rX%_&rXg&rX~O!f$YO!r$YO#Z&qO%uWO~O#Z&qO~Oz&sO#g&tX%[&tX%_&tX~Oz&uO#g&pX%[&pX%_&pX{&pX~O!X&wO%z&xO~Oz&|On&wX~P%SOn'PO~OPdOVaOopOvqO!PrO!Q{O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO%['UO~P'vOt'YO#p'WO#q'XOP#naV#naf#nah#nao#nas#nav#na!P#na!Q#na!T#na!U#na!X#na!]#na!h#na!r#na!s#na!t#na!{#na!}#na#P#na#R#na#T#na#X#na#Z#na#^#na#_#na#a#na#c#na#l#na#o#na#s#na#u#na#z#na#}#na$P#na%X#na%o#na%p#na%t#na%u#na&Z#na&[#na&]#na&^#na&_#na&`#na&a#na&b#na&c#na&d#na&e#na&f#na&g#na&h#na&i#na&j#na%Z#na%_#na~Oz'ZO#[']O{&xX~Oh'_O!X&wO~Oh!iO{$jO!X&wO~O{'eO~P%SO%['hO%_'hO~OS'iO%['hO%_'hO~OV!aO_!aOa!bOh!iO!X!kO!f!mO%l!^O%m!_O%n!_O%o!`O%p!`O%q!aO%r!aO%s!aOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~O%k!]O~P!#uO%kWi~P!#uOV!aO_!aOa!bOh!iO!X!kO!f!mO%o!`O%p!`O%q!aO%r!aO%s!aOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%kWi%lWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~O%m!_O%n!_O~P!&pO%mWi%nWi~P!&pOa!bOh!iO!X!kO!f!mOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%kWi%lWi%mWi%nWi%oWi%pWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~OV!aO_!aO%q!aO%r!aO%s!aO~P!)nOVWi_Wi%qWi%rWi%sWi~P!)nO!T%dO!U%cOg&VXz&VX~O%z'kO%{'kO~P,eOz'mOg&UX~Og'oO~Oz'pO{'rO!W&XX~Oo0cOv0qOz'pO{'sO!W&XX~P'vO!W'uO~Om!oOn!oOo!rOp!sOkjisjizji{ji!xji#gji%[ji%_ji%zji~Ol!qO~P!.aOlji~P!.aOk0eOl0fOm0dOn0dOo0mOp0nO~Ot'wO~P!/jOV'|Og'}Oo0cOv0qO~P'vOg'}Oz(OO~Og(QO~O!U(SO~Og(TOz(OO!T%dO!U%cO~P%SOk0eOl0fOm0dOn0dOo0mOp0nOgqa!Tqa!Uqa%{qa!Wqa![qa!Qqa#[qatqa!mqa~PEcOV'|Oo0cOv0qO!W&Sa~P'vOz(WO!W&Sa~O!W(XO~Oz(WO!T%dO!U%cO!W&Sa~P%SOV(]Oo0cOv0qO![%}a#g%}a%[%}a%_%}ag%}a{%}a!m%}a%z%}a~P'vOz(^O![%}a#g%}a%[%}a%_%}ag%}a{%}a!m%}a%z%}a~O![(aO~Oz(^O!T%dO!U%cO![%}a~P%SOz(dO!T%dO!U%cO![&Ta~P%SOz(gO{&lX![&lX!m&lX%z&lX~O{(kO![(mO!m(nO%z(jO~OV&OOopOvqO{%hi!x%hi#g%hi%[%hi%_%hi%z%hi~P'vOz(pO{%hi!x%hi#g%hi%[%hi%_%hi%z%hi~O!f&UOh&sa%[&saz&sa#[&sa#g&sa%_&sa#Z&sag&sa~O%[(uO~OV#sOa#tO%uWO~Oz&]O{wa~OopOvqO~P'vOz(^O#g%}a%[%}a%_%}ag%}a{%}a![%}a!m%}a%z%}a~P%SOz(zO#g%hX%[%hX%_%hX%z%hX~O%z#|O#gUi%[Ui%_Ui~O#g&Pa%[&Pa%_&Pan&Pa~P'vOz(}O#g&Pa%[&Pa%_&Pan&Pa~O%uWO#g&ra%[&ra%_&rag&ra~Oz)SO#g&ra%[&ra%_&rag&ra~Og)VO~OV)WOh$WO%uWO~O#Z)XO~O%uWO#g&ta%[&ta%_&ta~Oz)ZO#g&ta%[&ta%_&ta~Oo0cOv0qO#g&pa%[&pa%_&pa{&pa~P'vOz)^O#g&pa%[&pa%_&pa{&pa~OV)`Oa)`O%uWO~O%z)eO~Ot)hO#j)gOP#hiV#hif#hih#hio#his#hiv#hi!P#hi!Q#hi!T#hi!U#hi!X#hi!]#hi!h#hi!r#hi!s#hi!t#hi!{#hi!}#hi#P#hi#R#hi#T#hi#X#hi#Z#hi#^#hi#_#hi#a#hi#c#hi#l#hi#o#hi#s#hi#u#hi#z#hi#}#hi$P#hi%X#hi%o#hi%p#hi%t#hi%u#hi&Z#hi&[#hi&]#hi&^#hi&_#hi&`#hi&a#hi&b#hi&c#hi&d#hi&e#hi&f#hi&g#hi&h#hi&i#hi&j#hi%Z#hi%_#hi~Ot)iOP#kiV#kif#kih#kio#kis#kiv#ki!P#ki!Q#ki!T#ki!U#ki!X#ki!]#ki!h#ki!r#ki!s#ki!t#ki!{#ki!}#ki#P#ki#R#ki#T#ki#X#ki#Z#ki#^#ki#_#ki#a#ki#c#ki#l#ki#o#ki#s#ki#u#ki#z#ki#}#ki$P#ki%X#ki%o#ki%p#ki%t#ki%u#ki&Z#ki&[#ki&]#ki&^#ki&_#ki&`#ki&a#ki&b#ki&c#ki&d#ki&e#ki&f#ki&g#ki&h#ki&i#ki&j#ki%Z#ki%_#ki~OV)kOn&wa~P'vOz)lOn&wa~Oz)lOn&wa~P%SOn)pO~O%Y)tO~Ot)wO#p'WO#q)vOP#niV#nif#nih#nio#nis#niv#ni!P#ni!Q#ni!T#ni!U#ni!X#ni!]#ni!h#ni!r#ni!s#ni!t#ni!{#ni!}#ni#P#ni#R#ni#T#ni#X#ni#Z#ni#^#ni#_#ni#a#ni#c#ni#l#ni#o#ni#s#ni#u#ni#z#ni#}#ni$P#ni%X#ni%o#ni%p#ni%t#ni%u#ni&Z#ni&[#ni&]#ni&^#ni&_#ni&`#ni&a#ni&b#ni&c#ni&d#ni&e#ni&f#ni&g#ni&h#ni&i#ni&j#ni%Z#ni%_#ni~OV)zOo0cOv0qO{$jO~P'vOo0cOv0qO{&xa~P'vOz*OO{&xa~OV*SOa*TOg*WO%q*UO%uWO~O{$jO&{*YO~Oh'_O~Oh!iO{$jO~O%[*_O~O%[*aO%_*aO~OV$}Oa$}Oo0cOv0qOg&Ua~P'vOz*dOg&Ua~Oo0cOv0qO{*gO!W&Xa~P'vOz*hO!W&Xa~Oo0cOv0qOz*hO{*kO!W&Xa~P'vOo0cOv0qOz*hO!W&Xa~P'vOz*hO{*kO!W&Xa~Om0dOn0dOo0mOp0nOgjikjisjizji!Tji!Uji%{ji!Wji{ji![ji#gji%[ji%_ji!Qji#[jitji!mji%zji~Ol0fO~P!NkOlji~P!NkOV'|Og*pOo0cOv0qO~P'vOn*rO~Og*pOz*tO~Og*uO~OV'|Oo0cOv0qO!W&Si~P'vOz*vO!W&Si~O!W*wO~OV(]Oo0cOv0qO![%}i#g%}i%[%}i%_%}ig%}i{%}i!m%}i%z%}i~P'vOz*zO!T%dO!U%cO![&Ti~Oz*}O![%}i#g%}i%[%}i%_%}ig%}i{%}i!m%}i%z%}i~O![+OO~Oa+QOo0cOv0qO![&Ti~P'vOz*zO![&Ti~O![+SO~OV+UOo0cOv0qO{&la![&la!m&la%z&la~P'vOz+VO{&la![&la!m&la%z&la~O!]+YO&n+[O![!nX~O![+^O~O{(kO![+_O~O{(kO![+_O!m+`O~OV&OOopOvqO{%hq!x%hq#g%hq%[%hq%_%hq%z%hq~P'vOz$ri{$ri!x$ri#g$ri%[$ri%_$ri%z$ri~P%SOV&OOopOvqO~P'vOV&OOo0cOv0qO#g%ha%[%ha%_%ha%z%ha~P'vOz+aO#g%ha%[%ha%_%ha%z%ha~Oz$ia#g$ia%[$ia%_$ian$ia~P%SO#g&Pi%[&Pi%_&Pin&Pi~P'vOz+dO#g#Wq%[#Wq%_#Wq~O#[+eOz$va#g$va%[$va%_$vag$va~O%uWO#g&ri%[&ri%_&rig&ri~Oz+gO#g&ri%[&ri%_&rig&ri~OV+iOh$WO%uWO~O%uWO#g&ti%[&ti%_&ti~Oo0cOv0qO#g&pi%[&pi%_&pi{&pi~P'vO{#{Oz#eX!W#eX~Oz+mO!W&uX~O!W+oO~Ot+rO#j)gOP#hqV#hqf#hqh#hqo#hqs#hqv#hq!P#hq!Q#hq!T#hq!U#hq!X#hq!]#hq!h#hq!r#hq!s#hq!t#hq!{#hq!}#hq#P#hq#R#hq#T#hq#X#hq#Z#hq#^#hq#_#hq#a#hq#c#hq#l#hq#o#hq#s#hq#u#hq#z#hq#}#hq$P#hq%X#hq%o#hq%p#hq%t#hq%u#hq&Z#hq&[#hq&]#hq&^#hq&_#hq&`#hq&a#hq&b#hq&c#hq&d#hq&e#hq&f#hq&g#hq&h#hq&i#hq&j#hq%Z#hq%_#hq~On$|az$|a~P%SOV)kOn&wi~P'vOz+yOn&wi~Oz,TO{$jO#[,TO~O#q,VOP#nqV#nqf#nqh#nqo#nqs#nqv#nq!P#nq!Q#nq!T#nq!U#nq!X#nq!]#nq!h#nq!r#nq!s#nq!t#nq!{#nq!}#nq#P#nq#R#nq#T#nq#X#nq#Z#nq#^#nq#_#nq#a#nq#c#nq#l#nq#o#nq#s#nq#u#nq#z#nq#}#nq$P#nq%X#nq%o#nq%p#nq%t#nq%u#nq&Z#nq&[#nq&]#nq&^#nq&_#nq&`#nq&a#nq&b#nq&c#nq&d#nq&e#nq&f#nq&g#nq&h#nq&i#nq&j#nq%Z#nq%_#nq~O#[,WOz%Oa{%Oa~Oo0cOv0qO{&xi~P'vOz,YO{&xi~O{#{O%z,[Og&zXz&zX~O%uWOg&zXz&zX~Oz,`Og&yX~Og,bO~O%Y,eO~O!T%dO!U%cOg&Viz&Vi~OV$}Oa$}Oo0cOv0qOg&Ui~P'vO{,hOz$la!W$la~Oo0cOv0qO{,iOz$la!W$la~P'vOo0cOv0qO{*gO!W&Xi~P'vOz,lO!W&Xi~Oo0cOv0qOz,lO!W&Xi~P'vOz,lO{,oO!W&Xi~Og$hiz$hi!W$hi~P%SOV'|Oo0cOv0qO~P'vOn,qO~OV'|Og,rOo0cOv0qO~P'vOV'|Oo0cOv0qO!W&Sq~P'vOz$gi![$gi#g$gi%[$gi%_$gig$gi{$gi!m$gi%z$gi~P%SOV(]Oo0cOv0qO~P'vOa+QOo0cOv0qO![&Tq~P'vOz,sO![&Tq~O![,tO~OV(]Oo0cOv0qO![%}q#g%}q%[%}q%_%}qg%}q{%}q!m%}q%z%}q~P'vO{,uO~OV+UOo0cOv0qO{&li![&li!m&li%z&li~P'vOz,zO{&li![&li!m&li%z&li~O!]+YO&n+[O![!na~O{(kO![,}O~OV&OOo0cOv0qO#g%hi%[%hi%_%hi%z%hi~P'vOz-OO#g%hi%[%hi%_%hi%z%hi~O%uWO#g&rq%[&rq%_&rqg&rq~Oz-RO#g&rq%[&rq%_&rqg&rq~OV)`Oa)`O%uWO!W&ua~Oz-TO!W&ua~On$|iz$|i~P%SOV)kO~P'vOV)kOn&wq~P'vOt-XOP#myV#myf#myh#myo#mys#myv#my!P#my!Q#my!T#my!U#my!X#my!]#my!h#my!r#my!s#my!t#my!{#my!}#my#P#my#R#my#T#my#X#my#Z#my#^#my#_#my#a#my#c#my#l#my#o#my#s#my#u#my#z#my#}#my$P#my%X#my%o#my%p#my%t#my%u#my&Z#my&[#my&]#my&^#my&_#my&`#my&a#my&b#my&c#my&d#my&e#my&f#my&g#my&h#my&i#my&j#my%Z#my%_#my~O%Z-]O%_-]O~P`O#q-^OP#nyV#nyf#nyh#nyo#nys#nyv#ny!P#ny!Q#ny!T#ny!U#ny!X#ny!]#ny!h#ny!r#ny!s#ny!t#ny!{#ny!}#ny#P#ny#R#ny#T#ny#X#ny#Z#ny#^#ny#_#ny#a#ny#c#ny#l#ny#o#ny#s#ny#u#ny#z#ny#}#ny$P#ny%X#ny%o#ny%p#ny%t#ny%u#ny&Z#ny&[#ny&]#ny&^#ny&_#ny&`#ny&a#ny&b#ny&c#ny&d#ny&e#ny&f#ny&g#ny&h#ny&i#ny&j#ny%Z#ny%_#ny~Oz-aO{$jO#[-aO~Oo0cOv0qO{&xq~P'vOz-dO{&xq~O%z,[Og&zaz&za~OV*SOa*TO%q*UO%uWOg&ya~Oz-hOg&ya~O$S-lO~OV$}Oa$}Oo0cOv0qO~P'vOo0cOv0qO{-mOz$li!W$li~P'vOo0cOv0qOz$li!W$li~P'vO{-mOz$li!W$li~Oo0cOv0qO{*gO~P'vOo0cOv0qO{*gO!W&Xq~P'vOz-pO!W&Xq~Oo0cOv0qOz-pO!W&Xq~P'vOs-sO!T%dO!U%cOg&Oq!W&Oq![&Oqz&Oq~P!/jOa+QOo0cOv0qO![&Ty~P'vOz$ji![$ji~P%SOa+QOo0cOv0qO~P'vOV+UOo0cOv0qO~P'vOV+UOo0cOv0qO{&lq![&lq!m&lq%z&lq~P'vO{(kO![-xO!m-yO%z-wO~OV&OOo0cOv0qO#g%hq%[%hq%_%hq%z%hq~P'vO%uWO#g&ry%[&ry%_&ryg&ry~OV)`Oa)`O%uWO!W&ui~Ot-}OP#m!RV#m!Rf#m!Rh#m!Ro#m!Rs#m!Rv#m!R!P#m!R!Q#m!R!T#m!R!U#m!R!X#m!R!]#m!R!h#m!R!r#m!R!s#m!R!t#m!R!{#m!R!}#m!R#P#m!R#R#m!R#T#m!R#X#m!R#Z#m!R#^#m!R#_#m!R#a#m!R#c#m!R#l#m!R#o#m!R#s#m!R#u#m!R#z#m!R#}#m!R$P#m!R%X#m!R%o#m!R%p#m!R%t#m!R%u#m!R&Z#m!R&[#m!R&]#m!R&^#m!R&_#m!R&`#m!R&a#m!R&b#m!R&c#m!R&d#m!R&e#m!R&f#m!R&g#m!R&h#m!R&i#m!R&j#m!R%Z#m!R%_#m!R~Oo0cOv0qO{&xy~P'vOV*SOa*TO%q*UO%uWOg&yi~O$S-lO%Z.VO%_.VO~OV.aOh._O!X.^O!].`O!h.YO!s.[O!t.[O%p.XO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O~Oo0cOv0qOz$lq!W$lq~P'vO{.fOz$lq!W$lq~Oo0cOv0qO{*gO!W&Xy~P'vOz.gO!W&Xy~Oo0cOv.kO~P'vOs-sO!T%dO!U%cOg&Oy!W&Oy![&Oyz&Oy~P!/jO{(kO![.nO~O{(kO![.nO!m.oO~OV*SOa*TO%q*UO%uWO~Oh.tO!f.rOz$TX#[$TX%j$TXg$TX~Os$TX{$TX!W$TX![$TX~P$-VO%o.vO%p.vOs$UXz$UX{$UX#[$UX%j$UX!W$UXg$UX![$UX~O!h.xO~Oz.|O#[/OO%j.yOs&|X{&|X!W&|Xg&|X~Oa/RO~P$)oOh.tOs&}Xz&}X{&}X#[&}X%j&}X!W&}Xg&}X![&}X~Os/VO{$jO~Oo0cOv0qOz$ly!W$ly~P'vOo0cOv0qO{*gO!W&X!R~P'vOz/ZO!W&X!R~Og&RXs&RX!T&RX!U&RX!W&RX![&RXz&RX~P!/jOs-sO!T%dO!U%cOg&Qa!W&Qa![&Qaz&Qa~O{(kO![/^O~O!f.rOh$[as$[az$[a{$[a#[$[a%j$[a!W$[ag$[a![$[a~O!h/eO~O%o.vO%p.vOs$Uaz$Ua{$Ua#[$Ua%j$Ua!W$Uag$Ua![$Ua~O%j.yOs$Yaz$Ya{$Ya#[$Ya!W$Yag$Ya![$Ya~Os&|a{&|a!W&|ag&|a~P$)cOz/jOs&|a{&|a!W&|ag&|a~O!W/mO~Og/mO~O{/oO~O![/pO~Oo0cOv0qO{*gO!W&X!Z~P'vO{/sO~O%z/tO~P$-VOz/uO#[/OO%j.yOg'PX~Oz/uOg'PX~Og/wO~O!h/xO~O#[/OOs%Saz%Sa{%Sa%j%Sa!W%Sag%Sa![%Sa~O#[/OO%j.yOs%Waz%Wa{%Wa!W%Wag%Wa~Os&|i{&|i!W&|ig&|i~P$)cOz/zO#[/OO%j.yO!['Oa~O{$da~P%SOg'Pa~P$)cOz0SOg'Pa~Oa0UO!['Oi~P$)oOz0WO!['Oi~Oz0WO#[/OO%j.yO!['Oi~O#[/OO%j.yOg$biz$bi~O%z0ZO~P$-VO#[/OO%j.yOg%Vaz%Va~Og'Pi~P$)cO{0^O~Oa0UO!['Oq~P$)oOz0`O!['Oq~O#[/OO%j.yOz%Ui![%Ui~Oa0UO~P$)oOa0UO!['Oy~P$)oO#[/OO%j.yOg$ciz$ci~O#[/OO%j.yOz%Uq![%Uq~Oz+aO#g%ha%[%ha%_%ha%z%ha~P%SOV&OOo0cOv0qO~P'vOn0hO~Oo0hO~P'vO{0iO~Ot0jO~P!/jO&]&Z&j&h&i&g&f&d&e&c&b&`&a&_&^&[%u~\",\n  goto: \"!=l'QPPPPPP'RP'Z*s+]+v,b,}-kP.YP'Z.y.y'ZPPP'Z2cPPPPPP2c5VPP5VP7g7p=xPP={>m>pPP'Z'ZPP?PPP'Z'ZPP'Z'Z'Z'Z'Z?T?}'ZP@QP@WD_G{HPPHSH^Hb'ZPPPHeHn'RP'R'RP'RP'RP'RP'RP'R'R'RP'RPP'RPP'RP'RPHtIQIYPIaIgPIaPIaIaPPPIaPKuPLOLYL`KuPIaLiPIaPLpLvPLzM`M}NhLzLzNnN{LzLzLzLz! a! g! j! o! r! |!!S!!`!!r!!x!#S!#Y!#v!#|!$S!$^!$d!$j!$|!%W!%^!%d!%n!%t!%z!&Q!&W!&^!&h!&n!&x!'O!'X!'_!'n!'v!(Q!(XPPPPPPPPPPP!(_!(b!(h!(q!({!)WPPPPPPPPPPPP!-z!/`!3`!6pPP!6x!7X!7b!8Z!8Q!8d!8j!8m!8p!8s!8{!9lPPPPPPPPPPPPPPPPP!9o!9s!9yP!:_!:c!:o!:x!;U!;l!;o!;r!;x!<O!<U!<XP!<a!<j!=f!=i]eOn#g$j)t,P'}`OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0r}!cQ#c#p$R$d$p%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!P!dQ#c#p$R$d$p$u%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!R!eQ#c#p$R$d$p$u$v%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!T!fQ#c#p$R$d$p$u$v$w%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!V!gQ#c#p$R$d$p$u$v$w$x%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!X!hQ#c#p$R$d$p$u$v$w$x$y%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g!]!hQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0g'}TOTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0r&cVOYZ[dnprxy}!P!Q!U!i!k!o!p!q!s!t#[#d#g#y#{#}$Q$h$j$}%S%Z%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/Z/s0c0d0e0f0h0i0j0k0n0r%mXOYZ[dnrxy}!P!Q!U!i!k#[#d#g#y#{#}$Q$h$j$}%S%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,s,u,w,y,z-O-d-f-m-p.f.g/Z0i0j0kQ#vqQ/[.kR0o0q't`OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rh#jhz{$W$Z&l&q)S)X+f+g-RW#rq&].k0qQ$]|Q$a!OQ$n!VQ$o!WW$|!i'm*d,gS&[#s#tQ'S$iQ(s&UQ)U&nU)Y&s)Z+jW)a&w+m-T-{Q*Q']W*R'_,`-h.TQ+l)`S,_*S*TQ-Q+eQ-_,TQ-c,WQ.R-al.W-l.^._.a.z.|/R/j/o/t/y0U0Z0^Q/S.`Q/a.tQ/l/OU0P/u0S0[X0V/z0W0_0`R&Z#r!_!wYZ!P!Q!k%S%`%g'p'r's(O(W)g*g*h*k*q*t*v,h,i,k,l,o-m-p.f.g/ZR%^!vQ!{YQ%x#[Q&d#}Q&g$QR,{+YT.j-s/s![!jQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0gQ&X#kQ'c$oR*^'dR'l$|Q%V!mR/_.r'|_OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rS#a_#b!P.[-l.^._.`.a.t.z.|/R/j/o/t/u/y/z0S0U0W0Z0[0^0_0`'|_OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rT#a_#bT#^^#_R(o%xa(l%x(n(o+`,{-y-z.oT+[(k+]R-z,{Q$PsQ+l)aR,^*RX#}s$O$P&fQ&y$aQ'a$nQ'd$oR)s'SQ)b&wV-S+m-T-{ZgOn$j)t,PXkOn)t,PQ$k!TQ&z$bQ&{$cQ'^$mQ'b$oQ)q'RQ)x'WQ){'XQ)|'YQ*Z'`S*]'c'dQ+s)gQ+u)hQ+v)iQ+z)oS+|)r*[Q,Q)vQ,R)wS,S)y)zQ,d*^Q-V+rQ-W+tQ-Y+{S-Z+},OQ-`,UQ-b,VQ-|-XQ.O-[Q.P-^Q.Q-_Q.p-}Q.q.RQ/W.dR/r/XWkOn)t,PR#mjQ'`$nS)r'S'aR,O)sQ,]*RR-f,^Q*['`Q+})rR-[,OZiOjn)t,PQ'f$pR*`'gT-j,e-ku.c-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^t.c-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^Q/S.`X0V/z0W0_0`!P.Z-l.^._.`.a.t.z.|/R/j/o/t/u/y/z0S0U0W0Z0[0^0_0`Q.w.YR/f.xg.z.].{/b/i/n/|0O0Q0]0a0bu.b-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^X.u.W.b/a0PR/c.tV0R/u0S0[R/X.dQnOS#on,PR,P)tQ&^#uR(x&^S%m#R#wS(_%m(bT(b%p&`Q%a!yQ%h!}W(P%a%h(U(YQ(U%eR(Y%jQ&i$RR)O&iQ(e%qQ*{(`T+R(e*{Q'n%OR*e'nS'q%R%SY*i'q*j,m-q.hU*j'r's'tU,m*k*l*mS-q,n,oR.h-rQ#Y]R%t#YQ#_^R%y#_Q(h%vS+W(h+XR+X(iQ+](kR,|+]Q#b_R%{#bQ#ebQ%}#cW&Q#e%}({+bQ({&cR+b0gQ$OsS&e$O&fR&f$PQ&v$_R)_&vQ&V#jR(t&VQ&m$VS)T&m+hR+h)UQ$Z{R&p$ZQ&t$]R)[&tQ+n)bR-U+nQ#hfR&S#hQ)f&zR+q)fQ&}$dS)m&})nR)n'OQ'V$kR)u'VQ'[$lS*P'[,ZR,Z*QQ,a*VR-i,aWjOn)t,PR#ljQ-k,eR.U-kd.{.]/b/i/n/|0O0Q0]0a0bR/h.{U.s.W/a0PR/`.sQ/{/nS0X/{0YR0Y/|S/v/b/cR0T/vQ.}.]R/k.}R!ZPXmOn)t,PWlOn)t,PR'T$jYfOn$j)t,PR&R#g[sOn#g$j)t,PR&d#}&bQOYZ[dnprxy}!P!Q!U!i!k!o!p!q!s!t#[#d#g#y#{#}$Q$h$j$}%S%Z%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/Z/s0c0d0e0f0h0i0j0k0n0rQ!nTQ#caQ#poU$Rt%c(SS$d!R$gQ$p!XQ$u!cQ$v!dQ$w!eQ$x!fQ$y!gQ$z!hQ%e!zQ%j#OQ%p#SQ%q#TQ&`#xQ'O$eQ'g$qQ(q&OU(|&h(}+cW)j&|)l+x+yQ*o'|Q*x(]Q+w)kQ,v+QQ/q/VR0g0lQ!yYQ!}ZQ$b!PQ$c!QQ%R!kQ't%S^'{%`%g(O(W*q*t*v^*f'p*h,k,l-p.g/ZQ*l'rQ*m'sQ+t)gQ,j*gQ,n*kQ-n,hQ-o,iQ-r,oQ.e-mR/Y.f[bOn#g$j)t,P!^!vYZ!P!Q!k%S%`%g'p'r's(O(W)g*g*h*k*q*t*v,h,i,k,l,o-m-p.f.g/ZQ#R[Q#fdS#wrxQ$UyW$_}$Q'P)pS$l!U$hW${!i'm*d,gS%v#[+Y`&P#d%|(p(r(z+a-O0kQ&a#yQ&b#{Q&c#}Q'j$}Q'z%^W([%l(^*y*}Q(`%nQ(i%wQ(v&ZS(y&_0iQ)P&jQ)Q&kU)]&u)^+kQ)d&xQ)y'WY)}'Z*O,X,Y-dQ*b'lS*n'w0jW+P(d*z,s,wW+T(g+V,y,zQ+p)eQ,U)zQ,c*YQ,x+UQ-P+dQ-e,]Q-v,uR.S-fhUOn#d#g$j%|&_'w(p(r)t,P%S!uYZ[drxy}!P!Q!U!i!k#[#y#{#}$Q$h$}%S%^%`%g%l%n%w&Z&j&k&u&x'P'W'Z'l'm'p'r's(O(W(^(d(g(z)^)e)g)p)z*O*Y*d*g*h*k*q*t*v*y*z*}+U+V+Y+a+d+k,X,Y,],g,h,i,k,l,o,s,u,w,y,z-O-d-f-m-p.f.g/Z0i0j0kQ#qpW%W!o!s0d0nQ%X!pQ%Y!qQ%[!tQ%f0cS'v%Z0hQ'x0eQ'y0fQ,p*rQ-u,qS.i-s/sR0p0rU#uq.k0qR(w&][cOn#g$j)t,PZ!xY#[#}$Q+YQ#W[Q#zrR$TxQ%b!yQ%i!}Q%o#RQ'j${Q(V%eQ(Z%jQ(c%pQ(f%qQ*|(`Q,f*bQ-t,pQ.m-uR/].lQ$StQ(R%cR*s(SQ.l-sR/}/sR#QZR#V[R%Q!iQ%O!iV*c'm*d,g!]!lQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v/q0gR%T!kT#]^#_Q%x#[R,{+YQ(m%xS+_(n(oQ,}+`Q-x,{S.n-y-zR/^.oT+Z(k+]Q$`}Q&g$QQ)o'PR+{)pQ$XzQ)W&qR+i)XQ$XzQ&o$WQ)W&qR+i)XQ#khW$Vz$W&q)XQ$[{Q&r$ZZ)R&l)S+f+g-RR$^|R)c&wXlOn)t,PQ$f!RR'Q$gQ$m!UR'R$hR*X'_Q*V'_V-g,`-h.TQ.d-lQ/P.^R/Q._U.]-l.^._Q/U.aQ/b.tQ/g.zU/i.|/j/yQ/n/RQ/|/oQ0O/tU0Q/u0S0[Q0]0UQ0a0ZR0b0^R/T.`R/d.t\",\n  nodeNames: \"⚠ print Escape { Comment Script AssignStatement * BinaryExpression BitOp BitOp BitOp BitOp ArithOp ArithOp @ ArithOp ** UnaryExpression ArithOp BitOp AwaitExpression await ) ( ParenthesizedExpression BinaryExpression or and CompareOp in not is UnaryExpression ConditionalExpression if else LambdaExpression lambda ParamList VariableName AssignOp , : NamedExpression AssignOp YieldExpression yield from TupleExpression ComprehensionExpression async for LambdaExpression ] [ ArrayExpression ArrayComprehensionExpression } { DictionaryExpression DictionaryComprehensionExpression SetExpression SetComprehensionExpression CallExpression ArgList AssignOp MemberExpression . PropertyName Number String FormatString FormatReplacement FormatSelfDoc FormatConversion FormatSpec FormatReplacement FormatSelfDoc ContinuedString Ellipsis None Boolean TypeDef AssignOp UpdateStatement UpdateOp ExpressionStatement DeleteStatement del PassStatement pass BreakStatement break ContinueStatement continue ReturnStatement return YieldStatement PrintStatement RaiseStatement raise ImportStatement import as ScopeStatement global nonlocal AssertStatement assert TypeDefinition type TypeParamList TypeParam StatementGroup ; IfStatement Body elif WhileStatement while ForStatement TryStatement try except finally WithStatement with FunctionDefinition def ParamList AssignOp TypeDef ClassDefinition class DecoratedStatement Decorator At MatchStatement match MatchBody MatchClause case CapturePattern LiteralPattern ArithOp ArithOp AsPattern OrPattern LogicOp AttributePattern SequencePattern MappingPattern StarPattern ClassPattern PatternArgList KeywordPattern KeywordPattern Guard\",\n  maxTerm: 277,\n  context: trackIndent,\n  nodeProps: [\n    [\"isolate\", -5,4,71,72,73,77,\"\"],\n    [\"group\", -15,6,85,87,88,90,92,94,96,98,99,100,102,105,108,110,\"Statement Statement\",-22,8,18,21,25,40,49,50,56,57,60,61,62,63,64,67,70,71,72,79,80,81,82,\"Expression\",-10,114,116,119,121,122,126,128,133,135,138,\"Statement\",-9,143,144,147,148,150,151,152,153,154,\"Pattern\"],\n    [\"openedBy\", 23,\"(\",54,\"[\",58,\"{\"],\n    [\"closedBy\", 24,\")\",55,\"]\",59,\"}\"]\n  ],\n  propSources: [pythonHighlighting],\n  skippedNodes: [0,4],\n  repeatNodeCount: 34,\n  tokenData: \"!2|~R!`OX%TXY%oY[%T[]%o]p%Tpq%oqr'ars)Yst*xtu%Tuv,dvw-hwx.Uxy/tyz0[z{0r{|2S|}2p}!O3W!O!P4_!P!Q:Z!Q!R;k!R![>_![!]Do!]!^Es!^!_FZ!_!`Gk!`!aHX!a!b%T!b!cIf!c!dJU!d!eK^!e!hJU!h!i!#f!i!tJU!t!u!,|!u!wJU!w!x!.t!x!}JU!}#O!0S#O#P&o#P#Q!0j#Q#R!1Q#R#SJU#S#T%T#T#UJU#U#VK^#V#YJU#Y#Z!#f#Z#fJU#f#g!,|#g#iJU#i#j!.t#j#oJU#o#p!1n#p#q!1s#q#r!2a#r#s!2f#s$g%T$g;'SJU;'S;=`KW<%lOJU`%YT&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T`%lP;=`<%l%To%v]&n`%c_OX%TXY%oY[%T[]%o]p%Tpq%oq#O%T#O#P&o#P#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To&tX&n`OY%TYZ%oZ]%T]^%o^#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc'f[&n`O!_%T!_!`([!`#T%T#T#U(r#U#f%T#f#g(r#g#h(r#h#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc(cTmR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc(yT!mR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk)aV&n`&[ZOr%Trs)vs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk){V&n`Or%Trs*bs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk*iT&n`&^ZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To+PZS_&n`OY*xYZ%TZ]*x]^%T^#o*x#o#p+r#p#q*x#q#r+r#r;'S*x;'S;=`,^<%lO*x_+wTS_OY+rZ]+r^;'S+r;'S;=`,W<%lO+r_,ZP;=`<%l+ro,aP;=`<%l*xj,kV%rQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj-XT!xY&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj-oV%lQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk.]V&n`&ZZOw%Twx.rx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk.wV&n`Ow%Twx/^x#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk/eT&n`&]ZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk/{ThZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc0cTgR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk0yXVZ&n`Oz%Tz{1f{!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk1mVaR&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk2ZV%oZ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc2wTzR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To3_W%pZ&n`O!_%T!_!`-Q!`!a3w!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Td4OT&{S&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk4fX!fQ&n`O!O%T!O!P5R!P!Q%T!Q![6T![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk5WV&n`O!O%T!O!P5m!P#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk5tT!rZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti6[a!hX&n`O!Q%T!Q![6T![!g%T!g!h7a!h!l%T!l!m9s!m#R%T#R#S6T#S#X%T#X#Y7a#Y#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti7fZ&n`O{%T{|8X|}%T}!O8X!O!Q%T!Q![8s![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti8^V&n`O!Q%T!Q![8s![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti8z]!hX&n`O!Q%T!Q![8s![!l%T!l!m9s!m#R%T#R#S8s#S#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti9zT!hX&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk:bX%qR&n`O!P%T!P!Q:}!Q!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj;UV%sQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti;ro!hX&n`O!O%T!O!P=s!P!Q%T!Q![>_![!d%T!d!e?q!e!g%T!g!h7a!h!l%T!l!m9s!m!q%T!q!rA]!r!z%T!z!{Bq!{#R%T#R#S>_#S#U%T#U#V?q#V#X%T#X#Y7a#Y#^%T#^#_9s#_#c%T#c#dA]#d#l%T#l#mBq#m#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti=xV&n`O!Q%T!Q![6T![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti>fc!hX&n`O!O%T!O!P=s!P!Q%T!Q![>_![!g%T!g!h7a!h!l%T!l!m9s!m#R%T#R#S>_#S#X%T#X#Y7a#Y#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti?vY&n`O!Q%T!Q!R@f!R!S@f!S#R%T#R#S@f#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti@mY!hX&n`O!Q%T!Q!R@f!R!S@f!S#R%T#R#S@f#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiAbX&n`O!Q%T!Q!YA}!Y#R%T#R#SA}#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiBUX!hX&n`O!Q%T!Q!YA}!Y#R%T#R#SA}#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiBv]&n`O!Q%T!Q![Co![!c%T!c!iCo!i#R%T#R#SCo#S#T%T#T#ZCo#Z#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiCv]!hX&n`O!Q%T!Q![Co![!c%T!c!iCo!i#R%T#R#SCo#S#T%T#T#ZCo#Z#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%ToDvV{_&n`O!_%T!_!`E]!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TcEdT%{R&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkEzT#gZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkFbXmR&n`O!^%T!^!_F}!_!`([!`!a([!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TjGUV%mQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkGrV%zZ&n`O!_%T!_!`([!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkH`WmR&n`O!_%T!_!`([!`!aHx!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TjIPV%nQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkIoV_Q#}P&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%ToJ_]&n`&YS%uZO!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUoKZP;=`<%lJUoKge&n`&YS%uZOr%Trs)Ysw%Twx.Ux!Q%T!Q![JU![!c%T!c!tJU!t!uLx!u!}JU!}#R%T#R#SJU#S#T%T#T#fJU#f#gLx#g#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUoMRa&n`&YS%uZOr%TrsNWsw%Twx! vx!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUkN_V&n`&`ZOr%TrsNts#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkNyV&n`Or%Trs! `s#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk! gT&n`&bZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk! }V&n`&_ZOw%Twx!!dx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!!iV&n`Ow%Twx!#Ox#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!#VT&n`&aZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!#oe&n`&YS%uZOr%Trs!%Qsw%Twx!&px!Q%T!Q![JU![!c%T!c!tJU!t!u!(`!u!}JU!}#R%T#R#SJU#S#T%T#T#fJU#f#g!(`#g#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!%XV&n`&dZOr%Trs!%ns#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!%sV&n`Or%Trs!&Ys#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!&aT&n`&fZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!&wV&n`&cZOw%Twx!'^x#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!'cV&n`Ow%Twx!'xx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!(PT&n`&eZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!(ia&n`&YS%uZOr%Trs!)nsw%Twx!+^x!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!)uV&n`&hZOr%Trs!*[s#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!*aV&n`Or%Trs!*vs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!*}T&n`&jZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!+eV&n`&gZOw%Twx!+zx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!,PV&n`Ow%Twx!,fx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!,mT&n`&iZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!-Vi&n`&YS%uZOr%TrsNWsw%Twx! vx!Q%T!Q![JU![!c%T!c!dJU!d!eLx!e!hJU!h!i!(`!i!}JU!}#R%T#R#SJU#S#T%T#T#UJU#U#VLx#V#YJU#Y#Z!(`#Z#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUo!.}a&n`&YS%uZOr%Trs)Ysw%Twx.Ux!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!0ZT!XZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc!0qT!WR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj!1XV%kQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T~!1sO!]~k!1zV%jR&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T~!2fO![~i!2mT%tX&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T\",\n  tokenizers: [legacyPrint, indentation, newlines, strings, 0, 1, 2, 3, 4],\n  topRules: {\"Script\":[0,5]},\n  specialized: [{term: 221, get: (value) => spec_identifier[value] || -1}],\n  tokenPrec: 7664\n});\n\nexport { parser };\n", "import { parser } from '@lezer/python';\nimport { syntax<PERSON>ree, LRLanguage, indentNodeProp, delimitedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { NodeWeakMap, IterMode } from '@lezer/common';\nimport { snippetCompletion, ifNotIn, completeFromList } from '@codemirror/autocomplete';\n\nconst cache = /*@__PURE__*/new NodeWeakMap();\nconst ScopeNodes = /*@__PURE__*/new Set([\n    \"Script\", \"Body\",\n    \"FunctionDefinition\", \"ClassDefinition\", \"LambdaExpression\",\n    \"ForStatement\", \"MatchClause\"\n]);\nfunction defID(type) {\n    return (node, def, outer) => {\n        if (outer)\n            return false;\n        let id = node.node.getChild(\"VariableName\");\n        if (id)\n            def(id, type);\n        return true;\n    };\n}\nconst gatherCompletions = {\n    FunctionDefinition: /*@__PURE__*/defID(\"function\"),\n    ClassDefinition: /*@__PURE__*/defID(\"class\"),\n    ForStatement(node, def, outer) {\n        if (outer)\n            for (let child = node.node.firstChild; child; child = child.nextSibling) {\n                if (child.name == \"VariableName\")\n                    def(child, \"variable\");\n                else if (child.name == \"in\")\n                    break;\n            }\n    },\n    ImportStatement(_node, def) {\n        var _a, _b;\n        let { node } = _node;\n        let isFrom = ((_a = node.firstChild) === null || _a === void 0 ? void 0 : _a.name) == \"from\";\n        for (let ch = node.getChild(\"import\"); ch; ch = ch.nextSibling) {\n            if (ch.name == \"VariableName\" && ((_b = ch.nextSibling) === null || _b === void 0 ? void 0 : _b.name) != \"as\")\n                def(ch, isFrom ? \"variable\" : \"namespace\");\n        }\n    },\n    AssignStatement(node, def) {\n        for (let child = node.node.firstChild; child; child = child.nextSibling) {\n            if (child.name == \"VariableName\")\n                def(child, \"variable\");\n            else if (child.name == \":\" || child.name == \"AssignOp\")\n                break;\n        }\n    },\n    ParamList(node, def) {\n        for (let prev = null, child = node.node.firstChild; child; child = child.nextSibling) {\n            if (child.name == \"VariableName\" && (!prev || !/\\*|AssignOp/.test(prev.name)))\n                def(child, \"variable\");\n            prev = child;\n        }\n    },\n    CapturePattern: /*@__PURE__*/defID(\"variable\"),\n    AsPattern: /*@__PURE__*/defID(\"variable\"),\n    __proto__: null\n};\nfunction getScope(doc, node) {\n    let cached = cache.get(node);\n    if (cached)\n        return cached;\n    let completions = [], top = true;\n    function def(node, type) {\n        let name = doc.sliceString(node.from, node.to);\n        completions.push({ label: name, type });\n    }\n    node.cursor(IterMode.IncludeAnonymous).iterate(node => {\n        if (node.name) {\n            let gather = gatherCompletions[node.name];\n            if (gather && gather(node, def, top) || !top && ScopeNodes.has(node.name))\n                return false;\n            top = false;\n        }\n        else if (node.to - node.from > 8192) {\n            // Allow caching for bigger internal nodes\n            for (let c of getScope(doc, node.node))\n                completions.push(c);\n            return false;\n        }\n    });\n    cache.set(node, completions);\n    return completions;\n}\nconst Identifier = /^[\\w\\xa1-\\uffff][\\w\\d\\xa1-\\uffff]*$/;\nconst dontComplete = [\"String\", \"FormatString\", \"Comment\", \"PropertyName\"];\n/**\nCompletion source that looks up locally defined names in\nPython code.\n*/\nfunction localCompletionSource(context) {\n    let inner = syntaxTree(context.state).resolveInner(context.pos, -1);\n    if (dontComplete.indexOf(inner.name) > -1)\n        return null;\n    let isWord = inner.name == \"VariableName\" ||\n        inner.to - inner.from < 20 && Identifier.test(context.state.sliceDoc(inner.from, inner.to));\n    if (!isWord && !context.explicit)\n        return null;\n    let options = [];\n    for (let pos = inner; pos; pos = pos.parent) {\n        if (ScopeNodes.has(pos.name))\n            options = options.concat(getScope(context.state.doc, pos));\n    }\n    return {\n        options,\n        from: isWord ? inner.from : context.pos,\n        validFor: Identifier\n    };\n}\nconst globals = /*@__PURE__*/[\n    \"__annotations__\", \"__builtins__\", \"__debug__\", \"__doc__\", \"__import__\", \"__name__\",\n    \"__loader__\", \"__package__\", \"__spec__\",\n    \"False\", \"None\", \"True\"\n].map(n => ({ label: n, type: \"constant\" })).concat(/*@__PURE__*/[\n    \"ArithmeticError\", \"AssertionError\", \"AttributeError\", \"BaseException\", \"BlockingIOError\",\n    \"BrokenPipeError\", \"BufferError\", \"BytesWarning\", \"ChildProcessError\", \"ConnectionAbortedError\",\n    \"ConnectionError\", \"ConnectionRefusedError\", \"ConnectionResetError\", \"DeprecationWarning\",\n    \"EOFError\", \"Ellipsis\", \"EncodingWarning\", \"EnvironmentError\", \"Exception\", \"FileExistsError\",\n    \"FileNotFoundError\", \"FloatingPointError\", \"FutureWarning\", \"GeneratorExit\", \"IOError\",\n    \"ImportError\", \"ImportWarning\", \"IndentationError\", \"IndexError\", \"InterruptedError\",\n    \"IsADirectoryError\", \"KeyError\", \"KeyboardInterrupt\", \"LookupError\", \"MemoryError\",\n    \"ModuleNotFoundError\", \"NameError\", \"NotADirectoryError\", \"NotImplemented\", \"NotImplementedError\",\n    \"OSError\", \"OverflowError\", \"PendingDeprecationWarning\", \"PermissionError\", \"ProcessLookupError\",\n    \"RecursionError\", \"ReferenceError\", \"ResourceWarning\", \"RuntimeError\", \"RuntimeWarning\",\n    \"StopAsyncIteration\", \"StopIteration\", \"SyntaxError\", \"SyntaxWarning\", \"SystemError\",\n    \"SystemExit\", \"TabError\", \"TimeoutError\", \"TypeError\", \"UnboundLocalError\", \"UnicodeDecodeError\",\n    \"UnicodeEncodeError\", \"UnicodeError\", \"UnicodeTranslateError\", \"UnicodeWarning\", \"UserWarning\",\n    \"ValueError\", \"Warning\", \"ZeroDivisionError\"\n].map(n => ({ label: n, type: \"type\" }))).concat(/*@__PURE__*/[\n    \"bool\", \"bytearray\", \"bytes\", \"classmethod\", \"complex\", \"float\", \"frozenset\", \"int\", \"list\",\n    \"map\", \"memoryview\", \"object\", \"range\", \"set\", \"staticmethod\", \"str\", \"super\", \"tuple\", \"type\"\n].map(n => ({ label: n, type: \"class\" }))).concat(/*@__PURE__*/[\n    \"abs\", \"aiter\", \"all\", \"anext\", \"any\", \"ascii\", \"bin\", \"breakpoint\", \"callable\", \"chr\",\n    \"compile\", \"delattr\", \"dict\", \"dir\", \"divmod\", \"enumerate\", \"eval\", \"exec\", \"exit\", \"filter\",\n    \"format\", \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\", \"input\", \"isinstance\",\n    \"issubclass\", \"iter\", \"len\", \"license\", \"locals\", \"max\", \"min\", \"next\", \"oct\", \"open\",\n    \"ord\", \"pow\", \"print\", \"property\", \"quit\", \"repr\", \"reversed\", \"round\", \"setattr\", \"slice\",\n    \"sorted\", \"sum\", \"vars\", \"zip\"\n].map(n => ({ label: n, type: \"function\" })));\nconst snippets = [\n    /*@__PURE__*/snippetCompletion(\"def ${name}(${params}):\\n\\t${}\", {\n        label: \"def\",\n        detail: \"function\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"for ${name} in ${collection}:\\n\\t${}\", {\n        label: \"for\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"while ${}:\\n\\t${}\", {\n        label: \"while\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"try:\\n\\t${}\\nexcept ${error}:\\n\\t${}\", {\n        label: \"try\",\n        detail: \"/ except block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"if ${}:\\n\\t\\n\", {\n        label: \"if\",\n        detail: \"block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"if ${}:\\n\\t${}\\nelse:\\n\\t${}\", {\n        label: \"if\",\n        detail: \"/ else block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"class ${name}:\\n\\tdef __init__(self, ${params}):\\n\\t\\t\\t${}\", {\n        label: \"class\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"import ${module}\", {\n        label: \"import\",\n        detail: \"statement\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/snippetCompletion(\"from ${module} import ${names}\", {\n        label: \"from\",\n        detail: \"import\",\n        type: \"keyword\"\n    })\n];\n/**\nAutocompletion for built-in Python globals and keywords.\n*/\nconst globalCompletion = /*@__PURE__*/ifNotIn(dontComplete, /*@__PURE__*/completeFromList(/*@__PURE__*/globals.concat(snippets)));\n\nfunction innerBody(context) {\n    let { node, pos } = context;\n    let lineIndent = context.lineIndent(pos, -1);\n    let found = null;\n    for (;;) {\n        let before = node.childBefore(pos);\n        if (!before) {\n            break;\n        }\n        else if (before.name == \"Comment\") {\n            pos = before.from;\n        }\n        else if (before.name == \"Body\" || before.name == \"MatchBody\") {\n            if (context.baseIndentFor(before) + context.unit <= lineIndent)\n                found = before;\n            node = before;\n        }\n        else if (before.name == \"MatchClause\") {\n            node = before;\n        }\n        else if (before.type.is(\"Statement\")) {\n            node = before;\n        }\n        else {\n            break;\n        }\n    }\n    return found;\n}\nfunction indentBody(context, node) {\n    let base = context.baseIndentFor(node);\n    let line = context.lineAt(context.pos, -1), to = line.from + line.text.length;\n    // Don't consider blank, deindented lines at the end of the\n    // block part of the block\n    if (/^\\s*($|#)/.test(line.text) &&\n        context.node.to < to + 100 &&\n        !/\\S/.test(context.state.sliceDoc(to, context.node.to)) &&\n        context.lineIndent(context.pos, -1) <= base)\n        return null;\n    // A normally deindenting keyword that appears at a higher\n    // indentation than the block should probably be handled by the next\n    // level\n    if (/^\\s*(else:|elif |except |finally:|case\\s+[^=:]+:)/.test(context.textAfter) && context.lineIndent(context.pos, -1) > base)\n        return null;\n    return base + context.unit;\n}\n/**\nA language provider based on the [Lezer Python\nparser](https://github.com/lezer-parser/python), extended with\nhighlighting and indentation information.\n*/\nconst pythonLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"python\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Body: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = indentBody(context, inner || context.node)) !== null && _a !== void 0 ? _a : context.continue();\n                },\n                MatchBody: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = indentBody(context, inner || context.node)) !== null && _a !== void 0 ? _a : context.continue();\n                },\n                IfStatement: cx => /^\\s*(else:|elif )/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                \"ForStatement WhileStatement\": cx => /^\\s*else:/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                TryStatement: cx => /^\\s*(except |finally:|else:)/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                MatchStatement: cx => {\n                    if (/^\\s*case /.test(cx.textAfter))\n                        return cx.baseIndent + cx.unit;\n                    return cx.continue();\n                },\n                \"TupleExpression ComprehensionExpression ParamList ArgList ParenthesizedExpression\": /*@__PURE__*/delimitedIndent({ closing: \")\" }),\n                \"DictionaryExpression DictionaryComprehensionExpression SetExpression SetComprehensionExpression\": /*@__PURE__*/delimitedIndent({ closing: \"}\" }),\n                \"ArrayExpression ArrayComprehensionExpression\": /*@__PURE__*/delimitedIndent({ closing: \"]\" }),\n                \"String FormatString\": () => null,\n                Script: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = (inner && indentBody(context, inner))) !== null && _a !== void 0 ? _a : context.continue();\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"ArrayExpression DictionaryExpression SetExpression TupleExpression\": foldInside,\n                Body: (node, state) => ({ from: node.from + 1, to: node.to - (node.to == state.doc.length ? 0 : 1) })\n            })\n        ],\n    }),\n    languageData: {\n        closeBrackets: {\n            brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"'],\n            stringPrefixes: [\"f\", \"fr\", \"rf\", \"r\", \"u\", \"b\", \"br\", \"rb\",\n                \"F\", \"FR\", \"RF\", \"R\", \"U\", \"B\", \"BR\", \"RB\"]\n        },\n        commentTokens: { line: \"#\" },\n        // Indent logic logic are triggered upon below input patterns\n        indentOnInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:|case\\s+[^:]*:?)$/,\n    }\n});\n/**\nPython language support.\n*/\nfunction python() {\n    return new LanguageSupport(pythonLanguage, [\n        pythonLanguage.data.of({ autocomplete: localCompletionSource }),\n        pythonLanguage.data.of({ autocomplete: globalCompletion }),\n    ]);\n}\n\nexport { globalCompletion, localCompletionSource, python, pythonLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,eAAe;AAArB,IACE,SAAS;AADX,IAEE,SAAS;AAFX,IAGE,YAAY;AAHd,IAIE,iBAAiB;AAJnB,IAKE,mBAAmB;AALrB,IAME,MAAM;AANR,IAOE,gBAAgB;AAPlB,IAQE,SAAS;AARX,IASE,mBAAmB;AATrB,IAUE,YAAY;AAVd,IAWE,SAAS;AAXX,IAYE,0BAA0B;AAZ5B,IAaE,kBAAkB;AAbpB,IAcE,0BAA0B;AAd5B,IAeE,WAAW;AAfb,IAgBE,kBAAkB;AAhBpB,IAiBE,+BAA+B;AAjBjC,IAkBE,SAAS;AAlBX,IAmBE,uBAAuB;AAnBzB,IAoBE,oCAAoC;AApBtC,IAqBE,gBAAgB;AArBlB,IAsBE,6BAA6B;AAtB/B,IAuBE,UAAU;AAvBZ,IAwBE,YAAY;AAxBd,IAyBE,WAAW;AAzBb,IA0BE,cAAc;AA1BhB,IA2BE,eAAe;AA3BjB,IA4BE,eAAe;AA5BjB,IA6BE,gBAAgB;AA7BlB,IA8BE,eAAe;AA9BjB,IA+BE,gBAAgB;AA/BlB,IAgCE,gBAAgB;AAhClB,IAiCE,iBAAiB;AAjCnB,IAkCE,eAAe;AAlCjB,IAmCE,eAAe;AAnCjB,IAoCE,gBAAgB;AApClB,IAqCE,gBAAgB;AArClB,IAsCE,iBAAiB;AAtCnB,IAuCE,gBAAgB;AAvClB,IAwCE,iBAAiB;AAxCnB,IAyCE,iBAAiB;AAzCnB,IA0CE,kBAAkB;AA1CpB,IA2CE,oBAAoB;AA3CtB,IA4CE,0BAA0B;AA5C5B,IA6CE,aAAa;AA7Cf,IA8CE,gBAAgB;AA9ClB,IA+CE,YAAY;AA/Cd,IAgDE,kBAAkB;AAhDpB,IAiDE,iBAAiB;AAjDnB,IAkDE,iBAAiB;AAEnB,IAAM,UAAU;AAAhB,IAAoB,iBAAiB;AAArC,IAAyC,QAAQ;AAAjD,IAAqD,MAAM;AAA3D,IAA8D,OAAO;AAArE,IAAyE,YAAY;AAArF,IAAyF,MAAM;AAA/F,IACM,YAAY;AADlB,IACuB,aAAa;AADpC,IACyC,cAAc;AADvD,IAC2D,cAAc;AADzE,IAC6E,YAAY;AADzF,IAEM,WAAW;AAFjB,IAEsB,WAAW;AAFjC,IAEsC,WAAW;AAFjD,IAEqD,WAAW;AAFhE,IAEqE,WAAW;AAEhF,IAAM,YAAY,oBAAI,IAAI;AAAA,EACxB;AAAA,EAAyB;AAAA,EAAiB;AAAA,EAAyB;AAAA,EAAY;AAAA,EAAS;AAAA,EACxF;AAAA,EAAiB;AAAA,EAA8B;AAAA,EAC/C;AAAA,EAAe;AAAA,EAA4B;AAAA,EAAc;AAAA,EAAmB;AAAA,EAC5E;AAAA,EAAsB;AAAA,EACtB;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAgB;AACnD,CAAC;AAED,SAAS,YAAY,IAAI;AACvB,SAAO,MAAM,WAAW,MAAM;AAChC;AAEA,SAAS,MAAM,IAAI;AACjB,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC3E;AAEA,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI;AACJ,MAAI,MAAM,OAAO,GAAG;AAClB,UAAM,YAAY,GAAG;AAAA,EACvB,WAAW,MAAM,QAAQ,QAAQ,cAAc;AAC7C,QAAI,YAAY,MAAM,IAAI;AAAG,YAAM,YAAY,kBAAkB,CAAC;AAAA,EACpE,aAAa,OAAO,MAAM,KAAK,EAAE,KAAK,KAAK,YAAY,IAAI,MAChD,MAAM,SAAS,cAAc,GAAG;AACzC,QAAI,SAAS;AACb,WAAO,MAAM,QAAQ,SAAS,MAAM,QAAQ,KAAK;AAAE,YAAM,QAAQ;AAAG;AAAA,IAAU;AAC9E,QAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ,kBAAkB,MAAM,QAAQ;AACzE,YAAM,YAAY,gBAAgB,CAAC,MAAM;AAAA,EAC7C,WAAW,YAAY,MAAM,IAAI,GAAG;AAClC,UAAM,YAAY,WAAW,CAAC;AAAA,EAChC;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,IAAM,cAAc,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC1D,MAAI,UAAU,MAAM;AACpB,MAAI,QAAQ;AAAO;AACnB,MAAI,OAAO,MAAM,KAAK,EAAE;AACxB,MAAI,QAAQ,WAAW,QAAQ,gBAAgB;AAC7C,QAAI,QAAQ,GAAG,QAAQ;AACvB,eAAS;AACP,UAAI,MAAM,QAAQ;AAAO;AAAA,eAChB,MAAM,QAAQ;AAAK,iBAAS,IAAK,QAAQ;AAAA;AAC7C;AACL,YAAM,QAAQ;AACd;AAAA,IACF;AACA,QAAI,SAAS,QAAQ,UACjB,MAAM,QAAQ,WAAW,MAAM,QAAQ,kBAAkB,MAAM,QAAQ,MAAM;AAC/E,UAAI,QAAQ,QAAQ;AAAQ,cAAM,YAAY,QAAQ,CAAC,KAAK;AAAA;AACvD,cAAM,YAAY,MAAM;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AAGD,IAAM,eAAe;AAArB,IAAwB,YAAY;AAApC,IAAuC,iBAAiB;AAAxD,IAA2D,UAAU;AAArE,IAAwE,SAAS;AAAjF,IAAqF,YAAY;AAEjG,SAAS,QAAQ,QAAQA,SAAQ,OAAO;AACtC,OAAK,SAAS;AACd,OAAK,SAASA;AACd,OAAK,QAAQ;AACb,OAAK,QAAQ,SAAS,OAAO,OAAO,OAAO,QAAQ,IAAI,KAAKA,WAAUA,WAAU,KAAK,SAAS,SAAS;AACzG;AAEA,IAAM,YAAY,IAAI,QAAQ,MAAM,GAAG,CAAC;AAExC,SAAS,YAAYC,QAAO;AAC1B,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ;AAChC,aAASA,OAAM,WAAW,CAAC,KAAK,MAAM,IAAK,QAAQ,IAAK;AAC1D,SAAO;AACT;AAEA,IAAM,cAAc,IAAI,IAAI;AAAA,EAC1B,CAAC,aAAa,CAAC;AAAA,EACf,CAAC,cAAc,cAAc;AAAA,EAC7B,CAAC,cAAc,OAAO;AAAA,EACtB,CAAC,eAAe,UAAU,cAAc;AAAA,EACxC,CAAC,cAAc,MAAM;AAAA,EACrB,CAAC,eAAe,SAAS,cAAc;AAAA,EACvC,CAAC,eAAe,SAAS,OAAO;AAAA,EAChC,CAAC,gBAAgB,SAAS,UAAU,cAAc;AAAA,EAClD,CAAC,cAAc,SAAS;AAAA,EACxB,CAAC,eAAe,YAAY,cAAc;AAAA,EAC1C,CAAC,eAAe,YAAY,OAAO;AAAA,EACnC,CAAC,gBAAgB,YAAY,UAAU,cAAc;AAAA,EACrD,CAAC,eAAe,YAAY,MAAM;AAAA,EAClC,CAAC,gBAAgB,YAAY,SAAS,cAAc;AAAA,EACpD,CAAC,gBAAgB,YAAY,SAAS,OAAO;AAAA,EAC7C,CAAC,iBAAiB,YAAY,SAAS,UAAU,cAAc;AACjE,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEnD,IAAM,cAAc,IAAI,eAAe;AAAA,EACrC,OAAO;AAAA,EACP,OAAO,SAAS,MAAM,GAAG,OAAO;AAC9B,QAAK,QAAQ,QAAQ,gBAAiB,UAAU,IAAI,IAAI,MACnD,QAAQ,YAAY,QAAQ,iBAAkB,QAAQ,QAAQ;AACjE,aAAO,QAAQ;AACjB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,QAAI,QAAQ;AACV,aAAO,IAAI,QAAQ,SAAS,YAAY,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AAC9E,QAAI,QAAQ;AACV,aAAO,QAAQ;AACjB,QAAI,QAAQ,UAAU,QAAQ,YAAY,QAAQ,UAAU,QAAQ;AAClE,aAAO,IAAI,QAAQ,SAAS,GAAG,YAAY;AAC7C,QAAI,YAAY,IAAI,IAAI;AACtB,aAAO,IAAI,QAAQ,SAAS,GAAG,YAAY,IAAI,IAAI,IAAK,QAAQ,QAAQ,YAAa;AACvF,WAAO;AAAA,EACT;AAAA,EACA,KAAK,SAAS;AAAE,WAAO,QAAQ;AAAA,EAAK;AACtC,CAAC;AAED,IAAM,cAAc,IAAI,kBAAkB,WAAS;AACjD,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,MAAM,QAAQ,QAAQ,WAAW,CAAC;AAAG;AACzC,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,KAAK,KAAK,OAAO,aAAa,MAAM,IAAI,CAAC;AAAG;AAChD,WAAS,MAAM,KAAI,OAAO;AACxB,QAAI,OAAO,MAAM,KAAK,GAAG;AACzB,QAAI,QAAQ,SAAS,QAAQ;AAAK;AAClC,QAAI,QAAQ,aAAa,QAAQ,OAAO,QAAQ,WAAW,QAAQ,kBAAkB,QAAQ;AAC3F,YAAM,YAAY,YAAY;AAChC;AAAA,EACF;AACF,CAAC;AAED,IAAM,UAAU,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACtD,MAAI,EAAC,MAAK,IAAI,MAAM;AACpB,MAAI,QAAS,QAAQ,iBAAkB,cAAc;AACrD,MAAI,QAAQ,QAAQ,WAAW;AAC/B,MAAI,UAAU,EAAE,QAAQ;AACxB,MAAI,UAAU,QAAQ,aAAa;AAEnC,MAAI,QAAQ,MAAM;AAClB,aAAS;AACP,QAAI,MAAM,OAAO,GAAG;AAClB;AAAA,IACF,WAAW,UAAU,MAAM,QAAQ,WAAW;AAC5C,UAAI,MAAM,KAAK,CAAC,KAAK,WAAW;AAC9B,cAAM,QAAQ,CAAC;AAAA,MACjB,OAAO;AACL,YAAI,MAAM,OAAO,OAAO;AACtB,gBAAM,YAAY,kBAAkB,CAAC;AACrC;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF,WAAW,WAAW,MAAM,QAAQ,WAAW;AAC7C,UAAI,MAAM,OAAO,OAAO;AACtB,cAAM,QAAQ;AACd,YAAI,UAAU,MAAM;AACpB,YAAI,WAAW,GAAG;AAChB,gBAAM,QAAQ;AACd,qBAAW,OAAO,OAAO;AAAA,QAC3B;AACA,cAAM,YAAY,MAAM;AACxB;AAAA,MACF;AACA;AAAA,IACF,WAAW,MAAM,QAAQ,UAAU,CAAC,QAAQ,MAAM,KAAK,CAAC,KAAK,SAAS,MAAM,KAAK,CAAC,KAAK,QAAQ;AAC7F,UAAI,MAAM,OAAO,OAAO;AACtB,cAAM,YAAY,WAAW,OAAO,IAAI,CAAC;AACzC;AAAA,MACF;AACA;AAAA,IACF,WAAW,MAAM,QAAQ,SAAS;AAChC,UAAI,MAAM;AACR,cAAM,QAAQ;AAAA,MAChB,WAAW,MAAM,OAAO,OAAO;AAC7B,cAAM,YAAY,SAAS;AAC3B;AAAA,MACF;AACA;AAAA,IACF,OAAO;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,MAAI,MAAM,MAAM;AAAO,UAAM,YAAY,aAAa;AACxD,CAAC;AAED,SAAS,WAAW,OAAO,IAAI;AAC7B,MAAI,MAAM,UAAU;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAK,YAAM,QAAQ;AAAA,EACpF,WAAW,MAAM,UAAU;AACzB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG;AAAK,YAAM,QAAQ;AAAA,EACjE,WAAW,MAAM,UAAU;AACzB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG;AAAK,YAAM,QAAQ;AAAA,EACjE,WAAW,MAAM,UAAU;AACzB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG;AAAK,YAAM,QAAQ;AAAA,EACjE,WAAW,MAAM,UAAU;AACzB,QAAI,MAAM,QAAQ,WAAW;AAC3B,YAAM,QAAQ;AACd,aAAO,MAAM,QAAQ,KAAK,MAAM,QAAQ,cAAc,MAAM,QAAQ,eAC7D,MAAM,QAAQ,eAAe,MAAM,QAAQ;AAAS,cAAM,QAAQ;AACzE,UAAI,MAAM,QAAQ;AAAY,cAAM,QAAQ;AAAA,IAC9C;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,UAAU;AAAA,EACnC,8CAAkD,KAAK;AAAA,EACvD,iHAAiH,KAAK;AAAA,EACtH,wBAAwB,KAAK;AAAA,EAC7B,yCAAyC,KAAK;AAAA,EAC9C,QAAQ,KAAK;AAAA,EACb,iBAAiB,KAAK;AAAA,EACtB,SAAS,KAAK;AAAA,EACd,MAAM,KAAK;AAAA,EACX,cAAc,KAAK;AAAA,EACnB,+BAA+B,KAAK,SAAS,KAAK,YAAY;AAAA,EAC9D,mCAAmC,KAAK,SAAS,KAAK,WAAW,KAAK,YAAY,CAAC;AAAA,EACnF,gCAAgC,KAAK,WAAW,KAAK,SAAS;AAAA,EAC9D,cAAc,KAAK;AAAA,EACnB,gDAAgD,KAAK,SAAS,KAAK,YAAY;AAAA,EAC/E,SAAS,KAAK;AAAA,EACd,QAAQ,KAAK;AAAA,EACb,QAAQ,KAAK;AAAA,EACb,cAAc,KAAK,QAAQ,KAAK,MAAM;AAAA,EACtC,QAAQ,KAAK;AAAA,EACb,UAAU,KAAK;AAAA,EACf,YAAY,KAAK;AAAA,EACjB,OAAO,KAAK;AAAA,EACZ,WAAW,KAAK;AAAA,EAChB,UAAU,KAAK;AAAA,EACf,UAAU,KAAK;AAAA,EACf,IAAI,KAAK;AAAA,EACT,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,KAAK,KAAK;AAAA,EACV,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,kBAAkB,EAAC,WAAU,MAAK,OAAM,IAAI,IAAG,IAAI,KAAI,IAAI,IAAG,IAAI,KAAI,IAAI,IAAG,IAAI,IAAG,IAAI,MAAK,IAAI,QAAO,IAAI,OAAM,IAAI,MAAK,IAAI,OAAM,KAAK,KAAI,KAAK,MAAK,KAAK,MAAK,KAAK,OAAM,KAAK,KAAI,KAAK,MAAK,KAAK,OAAM,KAAK,UAAS,KAAK,QAAO,KAAK,OAAM,KAAK,QAAO,KAAK,IAAG,KAAK,QAAO,KAAK,UAAS,KAAK,QAAO,KAAK,MAAK,KAAK,MAAK,KAAK,OAAM,KAAK,KAAI,KAAK,QAAO,KAAK,SAAQ,KAAK,MAAK,KAAK,KAAI,KAAK,OAAM,KAAK,OAAM,KAAK,MAAK,IAAG;AACva,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAAA,IAC/B,CAAC,SAAS,KAAI,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,uBAAsB,KAAI,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,cAAa,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,aAAY,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,SAAS;AAAA,IAC/Q,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,IACjC,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,EACnC;AAAA,EACA,aAAa,CAAC,kBAAkB;AAAA,EAChC,cAAc,CAAC,GAAE,CAAC;AAAA,EAClB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,aAAa,aAAa,UAAU,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACvE,UAAU,EAAC,UAAS,CAAC,GAAE,CAAC,EAAC;AAAA,EACzB,aAAa,CAAC,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,gBAAgB,KAAK,KAAK,GAAE,CAAC;AAAA,EACvE,WAAW;AACb,CAAC;;;AC3TD,IAAM,QAAqB,IAAI,YAAY;AAC3C,IAAM,aAA0B,oBAAI,IAAI;AAAA,EACpC;AAAA,EAAU;AAAA,EACV;AAAA,EAAsB;AAAA,EAAmB;AAAA,EACzC;AAAA,EAAgB;AACpB,CAAC;AACD,SAAS,MAAM,MAAM;AACjB,SAAO,CAAC,MAAM,KAAK,UAAU;AACzB,QAAI;AACA,aAAO;AACX,QAAI,KAAK,KAAK,KAAK,SAAS,cAAc;AAC1C,QAAI;AACA,UAAI,IAAI,IAAI;AAChB,WAAO;AAAA,EACX;AACJ;AACA,IAAM,oBAAoB;AAAA,EACtB,oBAAiC,MAAM,UAAU;AAAA,EACjD,iBAA8B,MAAM,OAAO;AAAA,EAC3C,aAAa,MAAM,KAAK,OAAO;AAC3B,QAAI;AACA,eAAS,QAAQ,KAAK,KAAK,YAAY,OAAO,QAAQ,MAAM,aAAa;AACrE,YAAI,MAAM,QAAQ;AACd,cAAI,OAAO,UAAU;AAAA,iBAChB,MAAM,QAAQ;AACnB;AAAA,MACR;AAAA,EACR;AAAA,EACA,gBAAgB,OAAO,KAAK;AACxB,QAAI,IAAI;AACR,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,WAAW,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACtF,aAAS,KAAK,KAAK,SAAS,QAAQ,GAAG,IAAI,KAAK,GAAG,aAAa;AAC5D,UAAI,GAAG,QAAQ,oBAAoB,KAAK,GAAG,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACrG,YAAI,IAAI,SAAS,aAAa,WAAW;AAAA,IACjD;AAAA,EACJ;AAAA,EACA,gBAAgB,MAAM,KAAK;AACvB,aAAS,QAAQ,KAAK,KAAK,YAAY,OAAO,QAAQ,MAAM,aAAa;AACrE,UAAI,MAAM,QAAQ;AACd,YAAI,OAAO,UAAU;AAAA,eAChB,MAAM,QAAQ,OAAO,MAAM,QAAQ;AACxC;AAAA,IACR;AAAA,EACJ;AAAA,EACA,UAAU,MAAM,KAAK;AACjB,aAAS,OAAO,MAAM,QAAQ,KAAK,KAAK,YAAY,OAAO,QAAQ,MAAM,aAAa;AAClF,UAAI,MAAM,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,cAAc,KAAK,KAAK,IAAI;AACvE,YAAI,OAAO,UAAU;AACzB,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,gBAA6B,MAAM,UAAU;AAAA,EAC7C,WAAwB,MAAM,UAAU;AAAA,EACxC,WAAW;AACf;AACA,SAAS,SAAS,KAAK,MAAM;AACzB,MAAI,SAAS,MAAM,IAAI,IAAI;AAC3B,MAAI;AACA,WAAO;AACX,MAAI,cAAc,CAAC,GAAG,MAAM;AAC5B,WAAS,IAAIC,OAAM,MAAM;AACrB,QAAI,OAAO,IAAI,YAAYA,MAAK,MAAMA,MAAK,EAAE;AAC7C,gBAAY,KAAK,EAAE,OAAO,MAAM,KAAK,CAAC;AAAA,EAC1C;AACA,OAAK,OAAO,SAAS,gBAAgB,EAAE,QAAQ,CAAAA,UAAQ;AACnD,QAAIA,MAAK,MAAM;AACX,UAAI,SAAS,kBAAkBA,MAAK,IAAI;AACxC,UAAI,UAAU,OAAOA,OAAM,KAAK,GAAG,KAAK,CAAC,OAAO,WAAW,IAAIA,MAAK,IAAI;AACpE,eAAO;AACX,YAAM;AAAA,IACV,WACSA,MAAK,KAAKA,MAAK,OAAO,MAAM;AAEjC,eAAS,KAAK,SAAS,KAAKA,MAAK,IAAI;AACjC,oBAAY,KAAK,CAAC;AACtB,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,QAAM,IAAI,MAAM,WAAW;AAC3B,SAAO;AACX;AACA,IAAM,aAAa;AACnB,IAAM,eAAe,CAAC,UAAU,gBAAgB,WAAW,cAAc;AAKzE,SAAS,sBAAsB,SAAS;AACpC,MAAI,QAAQ,WAAW,QAAQ,KAAK,EAAE,aAAa,QAAQ,KAAK,EAAE;AAClE,MAAI,aAAa,QAAQ,MAAM,IAAI,IAAI;AACnC,WAAO;AACX,MAAI,SAAS,MAAM,QAAQ,kBACvB,MAAM,KAAK,MAAM,OAAO,MAAM,WAAW,KAAK,QAAQ,MAAM,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAC9F,MAAI,CAAC,UAAU,CAAC,QAAQ;AACpB,WAAO;AACX,MAAI,UAAU,CAAC;AACf,WAAS,MAAM,OAAO,KAAK,MAAM,IAAI,QAAQ;AACzC,QAAI,WAAW,IAAI,IAAI,IAAI;AACvB,gBAAU,QAAQ,OAAO,SAAS,QAAQ,MAAM,KAAK,GAAG,CAAC;AAAA,EACjE;AACA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,SAAS,MAAM,OAAO,QAAQ;AAAA,IACpC,UAAU;AAAA,EACd;AACJ;AACA,IAAM,UAAuB;AAAA,EACzB;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAW;AAAA,EAAc;AAAA,EACzE;AAAA,EAAc;AAAA,EAAe;AAAA,EAC7B;AAAA,EAAS;AAAA,EAAQ;AACrB,EAAE,IAAI,QAAM,EAAE,OAAO,GAAG,MAAM,WAAW,EAAE,EAAE,OAAoB;AAAA,EAC7D;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAiB;AAAA,EACxE;AAAA,EAAmB;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAqB;AAAA,EACvE;AAAA,EAAmB;AAAA,EAA0B;AAAA,EAAwB;AAAA,EACrE;AAAA,EAAY;AAAA,EAAY;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAa;AAAA,EAC5E;AAAA,EAAqB;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAC7E;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAoB;AAAA,EAAc;AAAA,EAClE;AAAA,EAAqB;AAAA,EAAY;AAAA,EAAqB;AAAA,EAAe;AAAA,EACrE;AAAA,EAAuB;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAkB;AAAA,EAC5E;AAAA,EAAW;AAAA,EAAiB;AAAA,EAA6B;AAAA,EAAmB;AAAA,EAC5E;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAgB;AAAA,EACvE;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAiB;AAAA,EACvE;AAAA,EAAc;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAqB;AAAA,EAC5E;AAAA,EAAsB;AAAA,EAAgB;AAAA,EAAyB;AAAA,EAAkB;AAAA,EACjF;AAAA,EAAc;AAAA,EAAW;AAC7B,EAAE,IAAI,QAAM,EAAE,OAAO,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,OAAoB;AAAA,EAC1D;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAAe;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAO;AAAA,EACrF;AAAA,EAAO;AAAA,EAAc;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAC5F,EAAE,IAAI,QAAM,EAAE,OAAO,GAAG,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAoB;AAAA,EAC3D;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAY;AAAA,EACjF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAU;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAM;AAAA,EAAS;AAAA,EACjF;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAW;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAC/E;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EACnF;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAC7B,EAAE,IAAI,QAAM,EAAE,OAAO,GAAG,MAAM,WAAW,EAAE,CAAC;AAC5C,IAAM,WAAW;AAAA,EACA,kBAAkB,iCAAkC;AAAA,IAC7D,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,uCAAwC;AAAA,IACnE,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,oBAAqB;AAAA,IAChD,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,sCAAwC;AAAA,IACnE,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,gBAAiB;AAAA,IAC5C,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,8BAAgC;AAAA,IAC3D,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,2DAA+D;AAAA,IAC1F,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,oBAAoB;AAAA,IAC/C,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AAAA,EACY,kBAAkB,kCAAkC;AAAA,IAC7D,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AACL;AAIA,IAAM,mBAAgC,QAAQ,cAA2B,iBAA8B,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAEhI,SAAS,UAAU,SAAS;AACxB,MAAI,EAAE,MAAM,IAAI,IAAI;AACpB,MAAI,aAAa,QAAQ,WAAW,KAAK,EAAE;AAC3C,MAAI,QAAQ;AACZ,aAAS;AACL,QAAI,SAAS,KAAK,YAAY,GAAG;AACjC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ,WACS,OAAO,QAAQ,WAAW;AAC/B,YAAM,OAAO;AAAA,IACjB,WACS,OAAO,QAAQ,UAAU,OAAO,QAAQ,aAAa;AAC1D,UAAI,QAAQ,cAAc,MAAM,IAAI,QAAQ,QAAQ;AAChD,gBAAQ;AACZ,aAAO;AAAA,IACX,WACS,OAAO,QAAQ,eAAe;AACnC,aAAO;AAAA,IACX,WACS,OAAO,KAAK,GAAG,WAAW,GAAG;AAClC,aAAO;AAAA,IACX,OACK;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,SAAS,MAAM;AAC/B,MAAI,OAAO,QAAQ,cAAc,IAAI;AACrC,MAAI,OAAO,QAAQ,OAAO,QAAQ,KAAK,EAAE,GAAG,KAAK,KAAK,OAAO,KAAK,KAAK;AAGvE,MAAI,YAAY,KAAK,KAAK,IAAI,KAC1B,QAAQ,KAAK,KAAK,KAAK,OACvB,CAAC,KAAK,KAAK,QAAQ,MAAM,SAAS,IAAI,QAAQ,KAAK,EAAE,CAAC,KACtD,QAAQ,WAAW,QAAQ,KAAK,EAAE,KAAK;AACvC,WAAO;AAIX,MAAI,oDAAoD,KAAK,QAAQ,SAAS,KAAK,QAAQ,WAAW,QAAQ,KAAK,EAAE,IAAI;AACrH,WAAO;AACX,SAAO,OAAO,QAAQ;AAC1B;AAMA,IAAM,iBAA8B,WAAW,OAAO;AAAA,EAClD,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,MAAM,aAAW;AACb,cAAI;AACJ,cAAI,QAAQ,UAAU,OAAO;AAC7B,kBAAQ,KAAK,WAAW,SAAS,SAAS,QAAQ,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS;AAAA,QAC/G;AAAA,QACA,WAAW,aAAW;AAClB,cAAI;AACJ,cAAI,QAAQ,UAAU,OAAO;AAC7B,kBAAQ,KAAK,WAAW,SAAS,SAAS,QAAQ,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS;AAAA,QAC/G;AAAA,QACA,aAAa,QAAM,oBAAoB,KAAK,GAAG,SAAS,IAAI,GAAG,aAAa,GAAG,SAAS;AAAA,QACxF,+BAA+B,QAAM,YAAY,KAAK,GAAG,SAAS,IAAI,GAAG,aAAa,GAAG,SAAS;AAAA,QAClG,cAAc,QAAM,+BAA+B,KAAK,GAAG,SAAS,IAAI,GAAG,aAAa,GAAG,SAAS;AAAA,QACpG,gBAAgB,QAAM;AAClB,cAAI,YAAY,KAAK,GAAG,SAAS;AAC7B,mBAAO,GAAG,aAAa,GAAG;AAC9B,iBAAO,GAAG,SAAS;AAAA,QACvB;AAAA,QACA,qFAAkG,gBAAgB,EAAE,SAAS,IAAI,CAAC;AAAA,QAClI,mGAAgH,gBAAgB,EAAE,SAAS,IAAI,CAAC;AAAA,QAChJ,gDAA6D,gBAAgB,EAAE,SAAS,IAAI,CAAC;AAAA,QAC7F,uBAAuB,MAAM;AAAA,QAC7B,QAAQ,aAAW;AACf,cAAI;AACJ,cAAI,QAAQ,UAAU,OAAO;AAC7B,kBAAQ,KAAM,SAAS,WAAW,SAAS,KAAK,OAAQ,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS;AAAA,QAC1G;AAAA,MACJ,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,sEAAsE;AAAA,QACtE,MAAM,CAAC,MAAM,WAAW,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,IAAI,SAAS,IAAI,GAAG;AAAA,MACvG,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe;AAAA,MACX,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,MAChD,gBAAgB;AAAA,QAAC;AAAA,QAAK;AAAA,QAAM;AAAA,QAAM;AAAA,QAAK;AAAA,QAAK;AAAA,QAAK;AAAA,QAAM;AAAA,QACnD;AAAA,QAAK;AAAA,QAAM;AAAA,QAAM;AAAA,QAAK;AAAA,QAAK;AAAA,QAAK;AAAA,QAAM;AAAA,MAAI;AAAA,IAClD;AAAA,IACA,eAAe,EAAE,MAAM,IAAI;AAAA;AAAA,IAE3B,eAAe;AAAA,EACnB;AACJ,CAAC;AAID,SAAS,SAAS;AACd,SAAO,IAAI,gBAAgB,gBAAgB;AAAA,IACvC,eAAe,KAAK,GAAG,EAAE,cAAc,sBAAsB,CAAC;AAAA,IAC9D,eAAe,KAAK,GAAG,EAAE,cAAc,iBAAiB,CAAC;AAAA,EAC7D,CAAC;AACL;", "names": ["indent", "space", "node"]}