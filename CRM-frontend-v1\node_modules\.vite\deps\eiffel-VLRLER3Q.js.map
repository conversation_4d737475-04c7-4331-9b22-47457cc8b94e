{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/eiffel.js"], "sourcesContent": ["function wordObj(words) {\n  var o = {};\n  for (var i = 0, e = words.length; i < e; ++i) o[words[i]] = true;\n  return o;\n}\nvar keywords = wordObj([\n  'note',\n  'across',\n  'when',\n  'variant',\n  'until',\n  'unique',\n  'undefine',\n  'then',\n  'strip',\n  'select',\n  'retry',\n  'rescue',\n  'require',\n  'rename',\n  'reference',\n  'redefine',\n  'prefix',\n  'once',\n  'old',\n  'obsolete',\n  'loop',\n  'local',\n  'like',\n  'is',\n  'inspect',\n  'infix',\n  'include',\n  'if',\n  'frozen',\n  'from',\n  'external',\n  'export',\n  'ensure',\n  'end',\n  'elseif',\n  'else',\n  'do',\n  'creation',\n  'create',\n  'check',\n  'alias',\n  'agent',\n  'separate',\n  'invariant',\n  'inherit',\n  'indexing',\n  'feature',\n  'expanded',\n  'deferred',\n  'class',\n  'Void',\n  'True',\n  'Result',\n  'Precursor',\n  'False',\n  'Current',\n  'create',\n  'attached',\n  'detachable',\n  'as',\n  'and',\n  'implies',\n  'not',\n  'or'\n]);\nvar operators = wordObj([\":=\", \"and then\",\"and\", \"or\",\"<<\",\">>\"]);\n\nfunction chain(newtok, stream, state) {\n  state.tokenize.push(newtok);\n  return newtok(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n  var ch = stream.next();\n  if (ch == '\"'||ch == \"'\") {\n    return chain(readQuoted(ch, \"string\"), stream, state);\n  } else if (ch == \"-\"&&stream.eat(\"-\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \":\"&&stream.eat(\"=\")) {\n    return \"operator\";\n  } else if (/[0-9]/.test(ch)) {\n    stream.eatWhile(/[xXbBCc0-9\\.]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[a-zA-Z_0-9]/.test(ch)) {\n    stream.eatWhile(/[a-zA-Z_0-9]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[=+\\-\\/*^%<>~]/.test(ch)) {\n    stream.eatWhile(/[=+\\-\\/*^%<>~]/);\n    return \"operator\";\n  } else {\n    return null;\n  }\n}\n\nfunction readQuoted(quote, style,  unescaped) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && (unescaped || !escaped)) {\n        state.tokenize.pop();\n        break;\n      }\n      escaped = !escaped && ch == \"%\";\n    }\n    return style;\n  };\n}\n\nexport const eiffel = {\n  name: \"eiffel\",\n  startState: function() {\n    return {tokenize: [tokenBase]};\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize[state.tokenize.length-1](stream, state);\n    if (style == \"variable\") {\n      var word = stream.current();\n      style = keywords.propertyIsEnumerable(stream.current()) ? \"keyword\"\n        : operators.propertyIsEnumerable(stream.current()) ? \"operator\"\n        : /^[A-Z][A-Z_0-9]*$/g.test(word) ? \"tag\"\n        : /^0[bB][0-1]+$/g.test(word) ? \"number\"\n        : /^0[cC][0-7]+$/g.test(word) ? \"number\"\n        : /^0[xX][a-fA-F0-9]+$/g.test(word) ? \"number\"\n        : /^([0-9]+\\.[0-9]*)|([0-9]*\\.[0-9]+)$/g.test(word) ? \"number\"\n        : /^[0-9]+$/g.test(word) ? \"number\"\n        : \"variable\";\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n\n"], "mappings": ";;;AAAA,SAAS,QAAQ,OAAO;AACtB,MAAI,IAAI,CAAC;AACT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE;AAAG,MAAE,MAAM,CAAC,CAAC,IAAI;AAC5D,SAAO;AACT;AACA,IAAI,WAAW,QAAQ;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,YAAY,QAAQ,CAAC,MAAM,YAAW,OAAO,MAAK,MAAK,IAAI,CAAC;AAEhE,SAAS,MAAM,QAAQ,QAAQ,OAAO;AACpC,QAAM,SAAS,KAAK,MAAM;AAC1B,SAAO,OAAO,QAAQ,KAAK;AAC7B;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS;AAAG,WAAO;AAC9B,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAK,MAAM,KAAK;AACxB,WAAO,MAAM,WAAW,IAAI,QAAQ,GAAG,QAAQ,KAAK;AAAA,EACtD,WAAW,MAAM,OAAK,OAAO,IAAI,GAAG,GAAG;AACrC,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,OAAK,OAAO,IAAI,GAAG,GAAG;AACrC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,eAAe;AAC/B,WAAO,IAAI,QAAQ;AACnB,WAAO;AAAA,EACT,WAAW,eAAe,KAAK,EAAE,GAAG;AAClC,WAAO,SAAS,cAAc;AAC9B,WAAO,IAAI,QAAQ;AACnB,WAAO;AAAA,EACT,WAAW,iBAAiB,KAAK,EAAE,GAAG;AACpC,WAAO,SAAS,gBAAgB;AAChC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAW,OAAO,OAAQ,WAAW;AAC5C,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,UAAU,aAAa,CAAC,UAAU;AAC1C,cAAM,SAAS,IAAI;AACnB;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO,EAAC,UAAU,CAAC,SAAS,EAAC;AAAA,EAC/B;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AACjE,QAAI,SAAS,YAAY;AACvB,UAAI,OAAO,OAAO,QAAQ;AAC1B,cAAQ,SAAS,qBAAqB,OAAO,QAAQ,CAAC,IAAI,YACtD,UAAU,qBAAqB,OAAO,QAAQ,CAAC,IAAI,aACnD,qBAAqB,KAAK,IAAI,IAAI,QAClC,iBAAiB,KAAK,IAAI,IAAI,WAC9B,iBAAiB,KAAK,IAAI,IAAI,WAC9B,uBAAuB,KAAK,IAAI,IAAI,WACpC,uCAAuC,KAAK,IAAI,IAAI,WACpD,YAAY,KAAK,IAAI,IAAI,WACzB;AAAA,IACN;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,KAAI;AAAA,EAC5B;AACF;", "names": []}