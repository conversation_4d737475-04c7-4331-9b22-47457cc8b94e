import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
  Logger,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TwillioService } from './twillio.service';
import { Request, Response } from 'express';
import * as twilio from 'twilio';
import {
  SmsMessageDto,
  WhatsAppMessageDto,
  WhatsAppStatusDto,
} from './dto/whatsapp-message.dto';
import { WhatsAppMessage } from './whatsapp-message.entity';
import { ILike, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { TwillioGateway } from './twillio.gateway';
import { formatToE164 } from '../utils/phone-formatter.util';

@Controller('twillio')
@ApiTags('twillio')
export class TwillioController {
  private readonly logger = new Logger(TwillioController.name);
  constructor(
    private readonly twillioService: TwillioService,
    @InjectRepository(WhatsAppMessage)
    private readonly whatsappMessageRepo: Repository<WhatsAppMessage>,
    private readonly twillioGateway: TwillioGateway,
  ) {}

  @Post('whatsapp/send')
  @ApiOperation({ summary: 'Send a WhatsApp message (template or freeform)' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  @ApiBody({ type: WhatsAppMessageDto })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async sendWhatsAppMessage(@Body() messageDto: WhatsAppMessageDto) {
    try {
      // Automatically format phone number to E.164 format
      let to = messageDto.to.trim();

      try {
        to = formatToE164(to);
        this.logger.log(`Phone number formatted to E.164: ${to}`);
      } catch (formatError) {
        this.logger.error(`Failed to format phone number: ${formatError.message}`);
        throw new HttpException(
          `Invalid phone number format: ${formatError.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!to.startsWith('whatsapp:')) {
        to = `whatsapp:${to}`;
      }

      this.logger.log(`Sending WhatsApp message to ${to}...`);

      const result = await this.twillioService.sendWhatsAppMessage(
        to,
        messageDto.message,
        messageDto.mediaUrl,
        messageDto.region,
        messageDto.isTemplate || false,
        messageDto.templateData,
      );

      // Save sent message (move this to service - optional)
      const savedMessage = await this.twillioService.saveSentWhatsAppMessage(
        result,
        to,
        messageDto.message,
        messageDto.mediaUrl,
        messageDto.mediaType,
      );

      // Notify frontend via WebSocket
      this.twillioGateway.sendWhatsAppMessageNotification(savedMessage);

      return {
        success: true,
        messageSid: result.sid,
        status: result.status,
      };
    } catch (error) {
      this.logger.error(`Failed to send WhatsApp message: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

@Post('whatsapp/recieve')
@ApiOperation({
  summary: 'Receive incoming WhatsApp messages via Twilio Webhook',
})
@ApiResponse({ status: 200, description: 'Message received' })
async handleIncomingWhatsApp(@Req() req: Request, @Res() res: Response) {
  try {
    const body = req.body;
    this.logger.log(`Incoming WhatsApp message: ${JSON.stringify(body)}`);

    const from = body.From?.replace('whatsapp:', '');
    const to = body.To?.replace('whatsapp:', '');
    const messageSid = body.MessageSid;
    const messageType = body.MessageType;

    const messageToSave: any = {
      messageSid,
      sender: from,
      status: 'received',
      messageType,
      timestamp: new Date(),
    };

    if (messageType === 'document' || messageType === 'image') {
      messageToSave.text = 'Media';
      messageToSave.mediaType = body.MediaContentType0;
      messageToSave.mediaUrl = body.MediaUrl0;
    } else {
      messageToSave.text = body.Body;
    }

    await this.twillioService.saveMessage(to, from, messageToSave);
        this.twillioGateway.sendWhatsAppMessageNotification({
      messageSid: body.MessageSid,
      sender: body.From,
      to: body.To,
      text: body.Body,
      status: body.SmsStatus || 'received',
      messageType: body.MessageType,
      timestamp: new Date().toISOString(),
    });

    // ✅ Send plain text response (no JSON)
    return res.status(200).send('OK');
  } catch (error) {
    this.logger.error(
      `Error processing incoming WhatsApp message: ${error.message}`,
    );
    return res.status(500).send('Error');
  }
}



  // recieve WhatsApp message status updates
  @Post('whatsapp/status')
  @ApiOperation({ summary: 'Receive WhatsApp message status updates' })
  // @ApiBody({ type: WhatsAppStatusDto })
  // @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async receiveWhatsAppStatus(@Body() body: any) {
    try {
      this.logger.log(
        `WhatsApp status update received: ${JSON.stringify(body)}`,
      );

      const messageSid = body.MessageSid;
      const messageStatus = body.MessageStatus;
      const errorMessage = body.ErrorMessage || null;

      // Save the status update to the database
      const statusUpdate: WhatsAppStatusDto = {
        messageSid,
        messageStatus,
      };

      await this.twillioService.saveWhatsAppStatusUpdate(
        messageSid,
        messageStatus,
      );

      // Notify frontend via WebSocket
      this.twillioGateway.handleUpdateMessageStatus(statusUpdate);

      return { success: true, status: 'Status update received' };
    } catch (error) {
      this.logger.error(
        `Error processing WhatsApp status update: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('whatsapp/status/:messageSid')
  @ApiOperation({ summary: 'Get WhatsApp message status' })
  async getMessageStatus(@Param('messageSid') messageSid: string) {
    try {
      const status = await this.twillioService.getMessageStatus(messageSid);
      return {
        success: true,
        status,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('token')
  @ApiOperation({ summary: 'Generate Twilio Access Token' })
  getToken(@Query('identity') identity: string) {
    const userIdentity = identity || 'default_user';
    const token = this.twillioService.generateAccessToken(userIdentity);
    return { token };
  }

  @Post('webhook')
  handleWebhook(@Req() req: Request, @Res() res: Response) {
    const twiml = new twilio.twiml.VoiceResponse();
    const toNumber = req.body.To;

    console.log('Webhook received:', req.body);
    console.log('To Number:', toNumber);

    if (toNumber) {
      const dial = twiml.dial({
        callerId: process.env.TWILIO_PHONE_NUMBER,
      });
      dial.number(toNumber);
    } else {
      twiml.say('Thank you for calling!');
    }

    res.type('text/xml');
    res.send(twiml.toString());
  }

  @Get('whatsapp/history')
  @ApiOperation({ summary: 'Get WhatsApp message history for a number' })
  async getWhatsAppHistory(@Query('number') number: string) {
    // Fetch messages where 'to' or 'from' matches the number
    const messages = await this.whatsappMessageRepo.find({
      where: [{ to: number }, { from: number }],
      order: { createdAt: 'ASC' },
    });
    return { success: true, messages };
  }

  @Get('whatsapp/messages/:phoneNumber')
  @ApiOperation({ summary: 'Get WhatsApp messages by phone number' })
  async getWhatsAppMessagesByNumber(
    @Param('phoneNumber') phoneNumber: string,
  ): Promise<WhatsAppMessage[]> {
    try {
      const messages =
        await this.twillioService.getWhatsAppMessagesByNumber(phoneNumber);
      return messages;
    } catch (error) {
      this.logger.error(
        `Error fetching WhatsApp messages for ${phoneNumber}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('sms/send')
  @ApiOperation({ summary: 'Send an SMS message' })
  @ApiResponse({ status: 201, description: 'SMS sent successfully' })
  @ApiBody({ type: SmsMessageDto })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async sendSMSMessage(@Body() messageDto: SmsMessageDto) {
    try {
      // Automatically format phone number to E.164 format
      let to = messageDto.to.trim();

      try {
        to = formatToE164(to);
        this.logger.log(`Phone number formatted to E.164: ${to}`);
      } catch (formatError) {
        this.logger.error(`Failed to format phone number: ${formatError.message}`);
        throw new HttpException(
          `Invalid phone number format: ${formatError.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(`Sending SMS message to ${to}...`);

      const result = await this.twillioService.sendSMSMessage(
        to,
        messageDto.message,
      );
      // Save to DB
      const savedMessage = await this.twillioService.saveSentSMSMessage(
        result,
        to,
        messageDto.message,
      );
      // Emit real-time event
      this.twillioGateway.sendSMSMessageNotification(savedMessage);
      return {
        success: true,
        messageSid: result.sid,
        status: result.status,
      };
    } catch (error) {
      this.logger.error(`Failed to send SMS message: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('sms/messages/:phoneNumber')
  @ApiOperation({ summary: 'Get SMS messages by phone number' })
  async getSmsMessagesByPhoneNumber(@Param('phoneNumber') phoneNumber: string) {
    try {
      // Find messages where 'to' or 'from' matches the phone number (not WhatsApp)
      const formattedNumber = phoneNumber.startsWith('+')
        ? phoneNumber
        : `+${phoneNumber}`;
      const messages = await this.whatsappMessageRepo.find({
        where: [
          { to: ILike(`%${formattedNumber}`) },
          { from: ILike(`%${formattedNumber}`) },
        ],
        order: { createdAt: 'ASC' },
      });
      // Filter out WhatsApp messages (those with to/from containing 'whatsapp:')
      const smsMessages = messages.filter(
        (msg) =>
          !msg.to?.includes('whatsapp:') && !msg.from?.includes('whatsapp:'),
      );
      return smsMessages;
    } catch (error) {
      this.logger.error(
        `Error fetching SMS messages for ${phoneNumber}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // receive SMS message webhook
  @Post('sms/receive')
  @ApiOperation({ summary: 'Receive incoming SMS messages via Twilio Webhook' })
  async handleIncomingSMS(@Body() body: any) {
    try {
      this.logger.log(`Incoming SMS message: ${JSON.stringify(body)}`);

      const from = body.From;
      const to = body.To;
      const messageSid = body.MessageSid;

      const messageToSave: any = {
        messageSid,
        sender: from,
        status: 'received',
        text: body.Body,
        timestamp: new Date(),
      };

      await this.twillioService.saveMessage(to, from, messageToSave);
      // Emit real-time update to frontend
      this.twillioGateway.sendSMSMessageNotification(messageToSave);

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error processing incoming SMS message: ${error.message}`,
      );
      return { success: false, error: error.message };
    }
  }

  // receive SMS message status updates
  @Post('sms/status')
  @ApiOperation({ summary: 'Receive SMS message status updates' })
  async receiveSMSStatus(@Body() body: any) {
    try {
      this.logger.log(`SMS status update received: ${JSON.stringify(body)}`);

      const messageSid = body.MessageSid;
      const messageStatus = body.MessageStatus;
      const errorMessage = body.ErrorMessage || null;

      // Save the status update to the database
      await this.twillioService.saveWhatsAppStatusUpdate(
        messageSid,
        messageStatus,
      );

      // Notify frontend via WebSocket
      this.twillioGateway.handleUpdateMessageStatus({
        messageSid,
        messageStatus,
        errorMessage,
      });

      return { success: true, status: 'Status update received' };
    } catch (error) {
      this.logger.error(`Error processing SMS status update: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('error')
  @ApiOperation({ summary: 'Handle Twilio error webhook' })
  async handleTwilioError(@Body() body: any) {
    this.logger.error(`Twilio error received: ${JSON.stringify(body)}`);
    // Here you can log the error or take any necessary action
    return { success: true, message: 'Error received' };
  }

  // sendCombinedWhatsAppAndEmail
  @Post('sendWAEmailCampaign')
  @ApiOperation({ summary: 'Send combined WhatsApp and Email campaign' })
  async sendCampaign(@Body() candidates: any[]) {
    return this.twillioService.sendCombinedWhatsAppAndEmail(candidates);
  }

  @Post('test-phone-format')
  @ApiOperation({ summary: 'Test phone number formatting to E.164' })
  async testPhoneFormat(@Body() body: { phoneNumber: string; countryCode?: string }) {
    try {
      const { phoneNumber, countryCode = '+44' } = body;

      if (!phoneNumber) {
        throw new HttpException('Phone number is required', HttpStatus.BAD_REQUEST);
      }

      const formattedNumber = formatToE164(phoneNumber, countryCode);

      return {
        success: true,
        original: phoneNumber,
        formatted: formattedNumber,
        countryCode: countryCode,
        message: 'Phone number formatted successfully'
      };
    } catch (error) {
      this.logger.error(`Phone formatting test failed: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: error.message,
          original: body.phoneNumber
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
