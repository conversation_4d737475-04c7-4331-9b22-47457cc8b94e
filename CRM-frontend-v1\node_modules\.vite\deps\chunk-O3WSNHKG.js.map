{"version": 3, "sources": ["../../@ctrl/tinycolor/dist/module/util.js", "../../@ctrl/tinycolor/dist/module/conversion.js", "../../@ctrl/tinycolor/dist/module/css-color-names.js", "../../@ctrl/tinycolor/dist/module/format-input.js", "../../@ctrl/tinycolor/dist/module/index.js", "../../@ctrl/tinycolor/dist/module/readability.js", "../../@ctrl/tinycolor/dist/module/to-ms-filter.js", "../../@ctrl/tinycolor/dist/module/from-ratio.js", "../../@ctrl/tinycolor/dist/module/random.js", "../../@ctrl/tinycolor/dist/module/interfaces.js", "../../@ctrl/tinycolor/dist/module/public_api.js"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n", "import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        var rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round(bound01(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n", "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    var c1 = new TinyColor(color1);\n    var c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2) {\n    var _a, _b;\n    if (wcag2 === void 0) { wcag2 = { level: 'AA', size: 'small' }; }\n    var readabilityLevel = readability(color1, color2);\n    switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args) {\n    if (args === void 0) { args = { includeFallbackColors: false, level: 'AA', size: 'small' }; }\n    var bestColor = null;\n    var bestScore = 0;\n    var includeFallbackColors = args.includeFallbackColors, level = args.level, size = args.size;\n    for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n        var color = colorList_1[_i];\n        var score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level: level, size: size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n", "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    var color = new TinyColor(firstColor);\n    var hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    var secondHex8String = hex8String;\n    var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        var s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}\n", "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    var newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options) {\n    if (options === void 0) { options = {}; }\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        var totalColors = options.count;\n        var colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    var h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    var s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    var v = pickBrightness(h, s, options);\n    var res = { h: h, s: s, v: v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    var hueRange = getHueRange(hue);\n    var res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    var saturationRange = getColorInfo(hue).saturationRange;\n    var sMin = saturationRange[0];\n    var sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    var bMin = getMinimumBrightness(H, S);\n    var bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    var lowerBounds = getColorInfo(H).lowerBounds;\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n        var s1 = lowerBounds[i][0];\n        var v1 = lowerBounds[i][1];\n        var s2 = lowerBounds[i + 1][0];\n        var v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            var m = (v2 - v1) / (s2 - s1);\n            var b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    var num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        var namedColor = bounds.find(function (n) { return n.name === colorInput; });\n        if (namedColor) {\n            var color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        var parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            var hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (var _i = 0, bounds_1 = bounds; _i < bounds_1.length; _i++) {\n        var bound = bounds_1[_i];\n        var color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    var max = range[1] || 1;\n    var min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    var rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    var sMin = bound.lowerBounds[0][0];\n    var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    var bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport var bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n", "export {};\n", "import { tinycolor } from './index.js';\nexport * from './index.js';\nexport * from './css-color-names.js';\nexport * from './readability.js';\nexport * from './to-ms-filter.js';\nexport * from './from-ratio.js';\nexport * from './format-input.js';\nexport * from './random.js';\nexport * from './interfaces.js';\nexport * from './conversion.js';\n// kept for backwards compatability with v1\nexport default tinycolor;\n"], "mappings": ";;;;;AAIO,SAAS,QAAQ,GAAG,KAAK;AAC5B,MAAI,eAAe,CAAC,GAAG;AACnB,QAAI;AAAA,EACR;AACA,MAAI,YAAY,aAAa,CAAC;AAC9B,MAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACX,QAAI,SAAS,OAAO,IAAI,GAAG,GAAG,EAAE,IAAI;AAAA,EACxC;AAEA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAC9B,WAAO;AAAA,EACX;AAEA,MAAI,QAAQ,KAAK;AAIb,SAAK,IAAI,IAAK,IAAI,MAAO,MAAM,IAAI,OAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EACpE,OACK;AAGD,QAAK,IAAI,MAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EAC1C;AACA,SAAO;AACX;AAKO,SAAS,QAAQ,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AAMO,SAAS,eAAe,GAAG;AAC9B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC/E;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACvD;AAKO,SAAS,WAAW,GAAG;AAC1B,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC5B,QAAI;AAAA,EACR;AACA,SAAO;AACX;AAKO,SAAS,oBAAoB,GAAG;AACnC,MAAI,KAAK,GAAG;AACR,WAAO,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,GAAG;AAAA,EACzC;AACA,SAAO;AACX;AAKO,SAAS,KAAK,GAAG;AACpB,SAAO,EAAE,WAAW,IAAI,MAAM,IAAI,OAAO,CAAC;AAC9C;AAjFA;AAAA;AAAA;AAAA;;;ACSO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,SAAO;AAAA,IACH,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACzB;AACJ;AAMO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK,MAAM,OAAO;AACtB,MAAI,QAAQ,KAAK;AACb,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,SAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAM,GAAM,EAAK;AAC9B;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI;AAAA,EAC9B;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,GAAG;AAET,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,QAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC5C,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,QAAQ,IAAI,IAAI,IAAI;AAC5B,MAAI,QAAQ,KAAK;AACb,QAAI;AAAA,EACR,OACK;AACD,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,SAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAM,GAAM,EAAK;AAC9B;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,IAAI,KAAK,MAAM,CAAC;AACpB,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,KAAK,IAAI;AACjB,MAAI,IAAI,KAAK,IAAI,IAAI;AACrB,MAAI,IAAI,KAAK,KAAK,IAAI,KAAK;AAC3B,MAAI,MAAM,IAAI;AACd,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC9B,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AAC1C,MAAI,MAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAChE;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAQO,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,YAAY;AAC9C,MAAI,MAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,oBAAoB,CAAC,CAAC;AAAA,EAC/B;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACnF;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAeO,SAAS,oBAAoB,GAAG;AACnC,SAAO,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACtD;AAEO,SAAS,oBAAoB,GAAG;AACnC,SAAO,gBAAgB,CAAC,IAAI;AAChC;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAO,SAAS,KAAK,EAAE;AAC3B;AACO,SAAS,oBAAoB,OAAO;AACvC,SAAO;AAAA,IACH,GAAG,SAAS;AAAA,IACZ,IAAI,QAAQ,UAAW;AAAA,IACvB,GAAG,QAAQ;AAAA,EACf;AACJ;AA1OA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAIW;AAJX;AAAA;AAIO,IAAI,QAAQ;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,sBAAsB;AAAA,MACtB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,eAAe;AAAA,MACf,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,KAAK;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,aAAa;AAAA,IACjB;AAAA;AAAA;;;ACnIO,SAAS,WAAW,OAAO;AAC9B,MAAI,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC7B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,oBAAoB,KAAK;AAAA,EACrC;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AAC/E,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IAC3D,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AAClD,UAAI,MAAM;AAAA,IACd;AAAA,EACJ;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACJ;AACJ;AA6BO,SAAS,oBAAoB,OAAO;AACvC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AACd,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACZ,WACS,UAAU,eAAe;AAC9B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,OAAO;AAAA,EACpD;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,eAAe,OAAO;AAClC,SAAO,QAAQ,SAAS,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AACxD;AAtLA,IAoEI,aAEA,YAEA,UAIA,mBACA,mBACA;AA9EJ;AAAA;AACA;AACA;AACA;AAiEA,IAAI,cAAc;AAElB,IAAI,aAAa;AAEjB,IAAI,WAAW,MAAM,OAAO,YAAY,OAAO,EAAE,OAAO,aAAa,GAAG;AAIxE,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAChI,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAC/J,IAAI,WAAW;AAAA,MACX,UAAU,IAAI,OAAO,QAAQ;AAAA,MAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV;AAAA;AAAA;;;AC1FA,IAII;AAJJ;AAAA;AAAA;AACA;AACA;AACA;AACA,IAAI;AAAA,IAA2B,WAAY;AACvC,eAASA,WAAU,OAAO,MAAM;AAC5B,YAAI,UAAU,QAAQ;AAAE,kBAAQ;AAAA,QAAI;AACpC,YAAI,SAAS,QAAQ;AAAE,iBAAO,CAAC;AAAA,QAAG;AAClC,YAAI;AAEJ,YAAI,iBAAiBA,YAAW;AAE5B,iBAAO;AAAA,QACX;AACA,YAAI,OAAO,UAAU,UAAU;AAC3B,kBAAQ,oBAAoB,KAAK;AAAA,QACrC;AACA,aAAK,gBAAgB;AACrB,YAAI,MAAM,WAAW,KAAK;AAC1B,aAAK,gBAAgB;AACrB,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,aAAK,UAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,IAAI;AACtE,aAAK,eAAe,KAAK;AAKzB,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,aAAK,UAAU,IAAI;AAAA,MACvB;AACA,MAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,eAAO,KAAK,cAAc,IAAI;AAAA,MAClC;AACA,MAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,eAAO,CAAC,KAAK,OAAO;AAAA,MACxB;AAIA,MAAAA,WAAU,UAAU,gBAAgB,WAAY;AAE5C,YAAI,MAAM,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,MACvD;AAIA,MAAAA,WAAU,UAAU,eAAe,WAAY;AAE3C,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,eAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,MAC9C;AAIA,MAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,eAAO,KAAK;AAAA,MAChB;AAMA,MAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,aAAK,IAAI,WAAW,KAAK;AACzB,aAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,eAAO;AAAA,MACX;AAIA,MAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,YAAI,IAAI,KAAK,MAAM,EAAE;AACrB,eAAO,MAAM;AAAA,MACjB;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,eAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MAC3D;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,eAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACrK;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,eAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MAC3D;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,eAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACrK;AAKA,MAAAA,WAAU,UAAU,QAAQ,SAAU,YAAY;AAC9C,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,MACtD;AAKA,MAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACpD,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,MAAM,KAAK,MAAM,UAAU;AAAA,MACtC;AAKA,MAAAA,WAAU,UAAU,SAAS,SAAU,YAAY;AAC/C,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,MAC/D;AAKA,MAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,MAAM,KAAK,OAAO,UAAU;AAAA,MACvC;AAKA,MAAAA,WAAU,UAAU,mBAAmB,SAAU,gBAAgB;AAC7D,YAAI,mBAAmB,QAAQ;AAAE,2BAAiB;AAAA,QAAO;AACzD,eAAO,KAAK,MAAM,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,aAAa,cAAc;AAAA,MAC7F;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO;AAAA,UACH,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,YAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,YAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,eAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,GAAG,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACjK;AAIA,MAAAA,WAAU,UAAU,kBAAkB,WAAY;AAC9C,YAAI,MAAM,SAAU,GAAG;AAAE,iBAAO,GAAG,OAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,QAAG;AACnF,eAAO;AAAA,UACH,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ;AAIA,MAAAA,WAAU,UAAU,wBAAwB,WAAY;AACpD,YAAI,MAAM,SAAU,GAAG;AAAE,iBAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG;AAAA,QAAG;AACnE,eAAO,KAAK,MAAM,IACZ,OAAO,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,IACrF,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MAC1H;AAIA,MAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,YAAI,KAAK,MAAM,GAAG;AACd,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,iBAAO;AAAA,QACX;AACA,YAAI,MAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACtD,iBAAS,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC/D,cAAI,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAC1C,cAAI,QAAQ,OAAO;AACf,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,MAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,YAAI,YAAY,QAAQ,MAAM;AAC9B,iBAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,KAAK;AAC9D,YAAI,kBAAkB;AACtB,YAAI,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AACvC,YAAI,mBAAmB,CAAC,aAAa,aAAa,OAAO,WAAW,KAAK,KAAK,WAAW;AACzF,YAAI,kBAAkB;AAGlB,cAAI,WAAW,UAAU,KAAK,MAAM,GAAG;AACnC,mBAAO,KAAK,OAAO;AAAA,UACvB;AACA,iBAAO,KAAK,YAAY;AAAA,QAC5B;AACA,YAAI,WAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAI,WAAW,QAAQ;AACnB,4BAAkB,KAAK,sBAAsB;AAAA,QACjD;AACA,YAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAI,WAAW,QAAQ;AACnB,4BAAkB,KAAK,YAAY,IAAI;AAAA,QAC3C;AACA,YAAI,WAAW,QAAQ;AACnB,4BAAkB,KAAK,aAAa,IAAI;AAAA,QAC5C;AACA,YAAI,WAAW,QAAQ;AACnB,4BAAkB,KAAK,aAAa;AAAA,QACxC;AACA,YAAI,WAAW,QAAQ;AACnB,4BAAkB,KAAK,OAAO;AAAA,QAClC;AACA,YAAI,WAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAI,WAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,eAAO,mBAAmB,KAAK,YAAY;AAAA,MAC/C;AACA,MAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,gBAAQ,KAAK,MAAM,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,MACrF;AACA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO,IAAIA,WAAU,KAAK,SAAS,CAAC;AAAA,MACxC;AAKA,MAAAA,WAAU,UAAU,UAAU,SAAU,QAAQ;AAC5C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAMA,MAAAA,WAAU,UAAU,SAAS,SAAU,QAAQ;AAC3C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAMA,MAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,eAAO,KAAK,IAAI,SAAS,MAAM;AAAA,MACnC;AAMA,MAAAA,WAAU,UAAU,QAAQ,SAAU,QAAQ;AAC1C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,eAAO,KAAK,IAAI,SAAS,MAAM;AAAA,MACnC;AAMA,MAAAA,WAAU,UAAU,aAAa,SAAU,QAAQ;AAC/C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,YAAY,WAAY;AACxC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC9B;AAKA,MAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,YAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,MAAM,SAAU,OAAO,QAAQ;AAC/C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,OAAO,KAAK,MAAM;AACtB,YAAI,OAAO,IAAIA,WAAU,KAAK,EAAE,MAAM;AACtC,YAAI,IAAI,SAAS;AACjB,YAAI,OAAO;AAAA,UACP,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,QACpC;AACA,eAAO,IAAIA,WAAU,IAAI;AAAA,MAC7B;AACA,MAAAA,WAAU,UAAU,YAAY,SAAU,SAAS,QAAQ;AACvD,YAAI,YAAY,QAAQ;AAAE,oBAAU;AAAA,QAAG;AACvC,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,OAAO,MAAM;AACjB,YAAI,MAAM,CAAC,IAAI;AACf,aAAK,IAAI,KAAK,IAAI,KAAM,OAAO,WAAY,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,cAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,cAAI,KAAK,IAAIA,WAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACX;AAIA,MAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,IAAI,IAAI,OAAO;AACxB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AACA,MAAAA,WAAU,UAAU,gBAAgB,SAAU,SAAS;AACnD,YAAI,YAAY,QAAQ;AAAE,oBAAU;AAAA,QAAG;AACvC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI,IAAI;AACZ,YAAI,MAAM,CAAC;AACX,YAAI,eAAe,IAAI;AACvB,eAAO,WAAW;AACd,cAAI,KAAK,IAAIA,WAAU,EAAE,GAAM,GAAM,EAAK,CAAC,CAAC;AAC5C,eAAK,IAAI,gBAAgB;AAAA,QAC7B;AACA,eAAO;AAAA,MACX;AACA,MAAAA,WAAU,UAAU,kBAAkB,WAAY;AAC9C,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,IAAI,IAAI;AACZ,eAAO;AAAA,UACH;AAAA,UACA,IAAIA,WAAU,EAAE,IAAI,IAAI,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,UACvD,IAAIA,WAAU,EAAE,IAAI,IAAI,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,QAC5D;AAAA,MACJ;AAIA,MAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,YAAI,KAAK,KAAK,MAAM;AACpB,YAAI,KAAK,IAAIA,WAAU,UAAU,EAAE,MAAM;AACzC,YAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAClC,eAAO,IAAIA,WAAU;AAAA,UACjB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,GAAG;AAAA,QACP,CAAC;AAAA,MACL;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO,KAAK,OAAO,CAAC;AAAA,MACxB;AAIA,MAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,eAAO,KAAK,OAAO,CAAC;AAAA,MACxB;AAKA,MAAAA,WAAU,UAAU,SAAS,SAAU,GAAG;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,IAAI,IAAI;AACZ,YAAI,SAAS,CAAC,IAAI;AAClB,YAAI,YAAY,MAAM;AACtB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,iBAAO,KAAK,IAAIA,WAAU,EAAE,IAAI,IAAI,IAAI,aAAa,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AAAA,QACnF;AACA,eAAO;AAAA,MACX;AAIA,MAAAA,WAAU,UAAU,SAAS,SAAU,OAAO;AAC1C,eAAO,KAAK,YAAY,MAAM,IAAIA,WAAU,KAAK,EAAE,YAAY;AAAA,MACnE;AACA,aAAOA;AAAA,IACX,EAAE;AAAA;AAAA;;;ACpfF;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAGA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;", "names": ["TinyColor"]}