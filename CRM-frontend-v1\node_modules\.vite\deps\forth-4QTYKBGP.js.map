{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/forth.js"], "sourcesContent": ["function toWordList(words) {\n  var ret = [];\n  words.split(' ').forEach(function(e){\n    ret.push({name: e});\n  });\n  return ret;\n}\n\nvar coreWordList = toWordList(\n  'INVERT AND OR XOR\\\n 2* 2/ LSHIFT RSHIFT\\\n 0= = 0< < > U< MIN MAX\\\n 2DROP 2DUP 2OVER 2SWAP ?DUP DEPTH DROP DUP OVER ROT SWAP\\\n >R R> R@\\\n + - 1+ 1- ABS NEGATE\\\n S>D * M* UM*\\\n FM/MOD SM/REM UM/MOD */ */MOD / /MOD MOD\\\n HERE , @ ! CELL+ CELLS C, C@ C! CHARS 2@ 2!\\\n ALIGN ALIGNED +! ALLOT\\\n CHAR [CHAR] [ ] BL\\\n FIND EXECUTE IMMEDIATE COUNT LITERAL STATE\\\n ; DOES> >BODY\\\n EVALUATE\\\n SOURCE >IN\\\n <# # #S #> HOLD SIGN BASE >NUMBER HEX DECIMAL\\\n FILL MOVE\\\n . CR EMIT SPACE SPACES TYPE U. .R U.R\\\n ACCEPT\\\n TRUE FALSE\\\n <> U> 0<> 0>\\\n NIP TUCK ROLL PICK\\\n 2>R 2R@ 2R>\\\n WITHIN UNUSED MARKER\\\n I J\\\n TO\\\n COMPILE, [COMPILE]\\\n SAVE-INPUT RESTORE-INPUT\\\n PAD ERASE\\\n 2LITERAL DNEGATE\\\n D- D+ D0< D0= D2* D2/ D< D= DMAX DMIN D>S DABS\\\n M+ M*/ D. D.R 2ROT DU<\\\n CATCH THROW\\\n FREE RESIZE ALLOCATE\\\n CS-PICK CS-ROLL\\\n GET-CURRENT SET-CURRENT FORTH-WORDLIST GET-ORDER SET-ORDER\\\n PREVIOUS SEARCH-WORDLIST WORDLIST FIND ALSO ONLY FORTH DEFINITIONS ORDER\\\n -TRAILING /STRING SEARCH COMPARE CMOVE CMOVE> BLANK SLITERAL');\n\nvar immediateWordList = toWordList('IF ELSE THEN BEGIN WHILE REPEAT UNTIL RECURSE [IF] [ELSE] [THEN] ?DO DO LOOP +LOOP UNLOOP LEAVE EXIT AGAIN CASE OF ENDOF ENDCASE');\n\nfunction searchWordList (wordList, word) {\n  var i;\n  for (i = wordList.length - 1; i >= 0; i--) {\n    if (wordList[i].name === word.toUpperCase()) {\n      return wordList[i];\n    }\n  }\n  return undefined;\n}\nexport const forth = {\n  name: \"forth\",\n  startState: function() {\n    return {\n      state: '',\n      base: 10,\n      coreWordList: coreWordList,\n      immediateWordList: immediateWordList,\n      wordList: []\n    };\n  },\n  token: function (stream, stt) {\n    var mat;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    if (stt.state === '') { // interpretation\n      if (stream.match(/^(\\]|:NONAME)(\\s|$)/i)) {\n        stt.state = ' compilation';\n        return 'builtin';\n      }\n      mat = stream.match(/^(\\:)\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        stt.state = ' compilation';\n        return 'def';\n      }\n      mat = stream.match(/^(VARIABLE|2VARIABLE|CONSTANT|2CONSTANT|CREATE|POSTPONE|VALUE|WORD)\\s+(\\S+)(\\s|$)+/i);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        return 'def';\n      }\n      mat = stream.match(/^(\\'|\\[\\'\\])\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        return 'builtin'\n      }\n    } else { // compilation\n      // ; [\n      if (stream.match(/^(\\;|\\[)(\\s)/)) {\n        stt.state = '';\n        stream.backUp(1);\n        return 'builtin';\n      }\n      if (stream.match(/^(\\;|\\[)($)/)) {\n        stt.state = '';\n        return 'builtin';\n      }\n      if (stream.match(/^(POSTPONE)\\s+\\S+(\\s|$)+/)) {\n        return 'builtin';\n      }\n    }\n\n    // dynamic wordlist\n    mat = stream.match(/^(\\S+)(\\s+|$)/);\n    if (mat) {\n      if (searchWordList(stt.wordList, mat[1]) !== undefined) {\n        return 'variable';\n      }\n\n      // comments\n      if (mat[1] === '\\\\') {\n        stream.skipToEnd();\n        return 'comment';\n      }\n\n      // core words\n      if (searchWordList(stt.coreWordList, mat[1]) !== undefined) {\n        return 'builtin';\n      }\n      if (searchWordList(stt.immediateWordList, mat[1]) !== undefined) {\n        return 'keyword';\n      }\n\n      if (mat[1] === '(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'comment';\n      }\n\n      // // strings\n      if (mat[1] === '.(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'string';\n      }\n      if (mat[1] === 'S\"' || mat[1] === '.\"' || mat[1] === 'C\"') {\n        stream.eatWhile(function (s) { return s !== '\"'; });\n        stream.eat('\"');\n        return 'string';\n      }\n\n      // numbers\n      if (mat[1] - 0xfffffffff) {\n        return 'number';\n      }\n      // if (mat[1].match(/^[-+]?[0-9]+\\.[0-9]*/)) {\n      //     return 'number';\n      // }\n\n      return 'atom';\n    }\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM,CAAC;AACX,QAAM,MAAM,GAAG,EAAE,QAAQ,SAAS,GAAE;AAClC,QAAI,KAAK,EAAC,MAAM,EAAC,CAAC;AAAA,EACpB,CAAC;AACD,SAAO;AACT;AAEA,IAAI,eAAe;AAAA,EACjB;AAqC4D;AAE9D,IAAI,oBAAoB,WAAW,kIAAkI;AAErK,SAAS,eAAgB,UAAU,MAAM;AACvC,MAAI;AACJ,OAAK,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,QAAI,SAAS,CAAC,EAAE,SAAS,KAAK,YAAY,GAAG;AAC3C,aAAO,SAAS,CAAC;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,KAAK;AAC5B,QAAI;AACJ,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,UAAU,IAAI;AACpB,UAAI,OAAO,MAAM,sBAAsB,GAAG;AACxC,YAAI,QAAQ;AACZ,eAAO;AAAA,MACT;AACA,YAAM,OAAO,MAAM,sBAAsB;AACzC,UAAI,KAAK;AACP,YAAI,SAAS,KAAK,EAAC,MAAM,IAAI,CAAC,EAAE,YAAY,EAAC,CAAC;AAC9C,YAAI,QAAQ;AACZ,eAAO;AAAA,MACT;AACA,YAAM,OAAO,MAAM,qFAAqF;AACxG,UAAI,KAAK;AACP,YAAI,SAAS,KAAK,EAAC,MAAM,IAAI,CAAC,EAAE,YAAY,EAAC,CAAC;AAC9C,eAAO;AAAA,MACT;AACA,YAAM,OAAO,MAAM,6BAA6B;AAChD,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AAEL,UAAI,OAAO,MAAM,cAAc,GAAG;AAChC,YAAI,QAAQ;AACZ,eAAO,OAAO,CAAC;AACf,eAAO;AAAA,MACT;AACA,UAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,YAAI,QAAQ;AACZ,eAAO;AAAA,MACT;AACA,UAAI,OAAO,MAAM,0BAA0B,GAAG;AAC5C,eAAO;AAAA,MACT;AAAA,IACF;AAGA,UAAM,OAAO,MAAM,eAAe;AAClC,QAAI,KAAK;AACP,UAAI,eAAe,IAAI,UAAU,IAAI,CAAC,CAAC,MAAM,QAAW;AACtD,eAAO;AAAA,MACT;AAGA,UAAI,IAAI,CAAC,MAAM,MAAM;AACnB,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,IAAI,cAAc,IAAI,CAAC,CAAC,MAAM,QAAW;AAC1D,eAAO;AAAA,MACT;AACA,UAAI,eAAe,IAAI,mBAAmB,IAAI,CAAC,CAAC,MAAM,QAAW;AAC/D,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,eAAO,SAAS,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAK,CAAC;AAClD,eAAO,IAAI,GAAG;AACd,eAAO;AAAA,MACT;AAGA,UAAI,IAAI,CAAC,MAAM,MAAM;AACnB,eAAO,SAAS,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAK,CAAC;AAClD,eAAO,IAAI,GAAG;AACd,eAAO;AAAA,MACT;AACA,UAAI,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM;AACzD,eAAO,SAAS,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAK,CAAC;AAClD,eAAO,IAAI,GAAG;AACd,eAAO;AAAA,MACT;AAGA,UAAI,IAAI,CAAC,IAAI,aAAa;AACxB,eAAO;AAAA,MACT;AAKA,aAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}