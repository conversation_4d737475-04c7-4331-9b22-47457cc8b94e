import {
  createChainedFunction,
  debounce,
  deprecatedPropType,
  ownerDocument,
  ownerWindow,
  setRef
} from "./chunk-XUI66H36.js";
import {
  ClassNameGenerator_default
} from "./chunk-XINUQYHW.js";

// node_modules/@mui/material/utils/createChainedFunction.js
var createChainedFunction_default = createChainedFunction;

// node_modules/@mui/material/utils/debounce.js
var debounce_default = debounce;

// node_modules/@mui/material/utils/deprecatedPropType.js
var deprecatedPropType_default = deprecatedPropType;

// node_modules/@mui/material/utils/ownerDocument.js
var ownerDocument_default = ownerDocument;

// node_modules/@mui/material/utils/ownerWindow.js
var ownerWindow_default = ownerWindow;

// node_modules/@mui/material/utils/setRef.js
var setRef_default = setRef;

// node_modules/@mui/material/utils/index.js
var unstable_ClassNameGenerator = {
  configure: (generator) => {
    if (true) {
      console.warn(["MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.", "", "You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead", "", "The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401", "", "The updated documentation: https://mui.com/guides/classname-generator/"].join("\n"));
    }
    ClassNameGenerator_default.configure(generator);
  }
};

export {
  createChainedFunction_default,
  debounce_default,
  deprecatedPropType_default,
  ownerDocument_default,
  ownerWindow_default,
  setRef_default,
  unstable_ClassNameGenerator
};
//# sourceMappingURL=chunk-CBRJ3X2E.js.map
