{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/haxe.js"], "sourcesContent": ["// Tokenizer\n\nfunction kw(type) {return {type: type, style: \"keyword\"};}\nvar A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\");\nvar operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"}, attribute = {type:\"attribute\", style: \"attribute\"};\nvar type = kw(\"typedef\");\nvar keywords = {\n  \"if\": A, \"while\": A, \"else\": B, \"do\": B, \"try\": B,\n  \"return\": C, \"break\": C, \"continue\": C, \"new\": C, \"throw\": C,\n  \"var\": kw(\"var\"), \"inline\":attribute, \"static\": attribute, \"using\":kw(\"import\"),\n  \"public\": attribute, \"private\": attribute, \"cast\": kw(\"cast\"), \"import\": kw(\"import\"), \"macro\": kw(\"macro\"),\n  \"function\": kw(\"function\"), \"catch\": kw(\"catch\"), \"untyped\": kw(\"untyped\"), \"callback\": kw(\"cb\"),\n  \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n  \"in\": operator, \"never\": kw(\"property_access\"), \"trace\":kw(\"trace\"),\n  \"class\": type, \"abstract\":type, \"enum\":type, \"interface\":type, \"typedef\":type, \"extends\":type, \"implements\":type, \"dynamic\":type,\n  \"true\": atom, \"false\": atom, \"null\": atom\n};\n\nvar isOperatorChar = /[+\\-*&%=<>!?|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction toUnescaped(stream, end) {\n  var escaped = false, next;\n  while ((next = stream.next()) != null) {\n    if (next == end && !escaped)\n      return true;\n    escaped = !escaped && next == \"\\\\\";\n  }\n}\n\n// Used as scratch variables to communicate multiple values without\n// consing up tons of objects.\nvar type, content;\nfunction ret(tp, style, cont) {\n  type = tp; content = cont;\n  return style;\n}\n\nfunction haxeTokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    return chain(stream, state, haxeTokenString(ch));\n  } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    return ret(ch);\n  } else if (ch == \"0\" && stream.eat(/x/i)) {\n    stream.eatWhile(/[\\da-f]/i);\n    return ret(\"number\", \"number\");\n  } else if (/\\d/.test(ch) || ch == \"-\" && stream.eat(/\\d/)) {\n    stream.match(/^\\d*(?:\\.\\d*(?!\\.))?(?:[eE][+\\-]?\\d+)?/);\n    return ret(\"number\", \"number\");\n  } else if (state.reAllowed && (ch == \"~\" && stream.eat(/\\//))) {\n    toUnescaped(stream, \"/\");\n    stream.eatWhile(/[gimsu]/);\n    return ret(\"regexp\", \"string.special\");\n  } else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, haxeTokenComment);\n    } else if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return ret(\"comment\", \"comment\");\n    } else {\n      stream.eatWhile(isOperatorChar);\n      return ret(\"operator\", null, stream.current());\n    }\n  } else if (ch == \"#\") {\n    stream.skipToEnd();\n    return ret(\"conditional\", \"meta\");\n  } else if (ch == \"@\") {\n    stream.eat(/:/);\n    stream.eatWhile(/[\\w_]/);\n    return ret (\"metadata\", \"meta\");\n  } else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return ret(\"operator\", null, stream.current());\n  } else {\n    var word;\n    if(/[A-Z]/.test(ch)) {\n      stream.eatWhile(/[\\w_<>]/);\n      word = stream.current();\n      return ret(\"type\", \"type\", word);\n    } else {\n      stream.eatWhile(/[\\w_]/);\n      var word = stream.current(), known = keywords.propertyIsEnumerable(word) && keywords[word];\n      return (known && state.kwAllowed) ? ret(known.type, known.style, word) :\n        ret(\"variable\", \"variable\", word);\n    }\n  }\n}\n\nfunction haxeTokenString(quote) {\n  return function(stream, state) {\n    if (toUnescaped(stream, quote))\n      state.tokenize = haxeTokenBase;\n    return ret(\"string\", \"string\");\n  };\n}\n\nfunction haxeTokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = haxeTokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return ret(\"comment\", \"comment\");\n}\n\n// Parser\n\nvar atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true, \"regexp\": true};\n\nfunction HaxeLexical(indented, column, type, align, prev, info) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.prev = prev;\n  this.info = info;\n  if (align != null) this.align = align;\n}\n\nfunction inScope(state, varname) {\n  for (var v = state.localVars; v; v = v.next)\n    if (v.name == varname) return true;\n}\n\nfunction parseHaxe(state, style, type, content, stream) {\n  var cc = state.cc;\n  // Communicate our context to the combinators.\n  // (Less wasteful than consing up a hundred closures on every call.)\n  cx.state = state; cx.stream = stream; cx.marked = null, cx.cc = cc;\n\n  if (!state.lexical.hasOwnProperty(\"align\"))\n    state.lexical.align = true;\n\n  while(true) {\n    var combinator = cc.length ? cc.pop() : statement;\n    if (combinator(type, content)) {\n      while(cc.length && cc[cc.length - 1].lex)\n        cc.pop()();\n      if (cx.marked) return cx.marked;\n      if (type == \"variable\" && inScope(state, content)) return \"variableName.local\";\n      if (type == \"variable\" && imported(state, content)) return \"variableName.special\";\n      return style;\n    }\n  }\n}\n\nfunction imported(state, typename) {\n  if (/[a-z]/.test(typename.charAt(0)))\n    return false;\n  var len = state.importedtypes.length;\n  for (var i = 0; i<len; i++)\n    if(state.importedtypes[i]==typename) return true;\n}\n\nfunction registerimport(importname) {\n  var state = cx.state;\n  for (var t = state.importedtypes; t; t = t.next)\n    if(t.name == importname) return;\n  state.importedtypes = { name: importname, next: state.importedtypes };\n}\n// Combinator utils\n\nvar cx = {state: null, column: null, marked: null, cc: null};\nfunction pass() {\n  for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n}\nfunction cont() {\n  pass.apply(null, arguments);\n  return true;\n}\nfunction inList(name, list) {\n  for (var v = list; v; v = v.next)\n    if (v.name == name) return true;\n  return false;\n}\nfunction register(varname) {\n  var state = cx.state;\n  if (state.context) {\n    cx.marked = \"def\";\n    if (inList(varname, state.localVars)) return;\n    state.localVars = {name: varname, next: state.localVars};\n  } else if (state.globalVars) {\n    if (inList(varname, state.globalVars)) return;\n    state.globalVars = {name: varname, next: state.globalVars};\n  }\n}\n\n// Combinators\n\nvar defaultVars = {name: \"this\", next: null};\nfunction pushcontext() {\n  if (!cx.state.context) cx.state.localVars = defaultVars;\n  cx.state.context = {prev: cx.state.context, vars: cx.state.localVars};\n}\nfunction popcontext() {\n  cx.state.localVars = cx.state.context.vars;\n  cx.state.context = cx.state.context.prev;\n}\npopcontext.lex = true;\nfunction pushlex(type, info) {\n  var result = function() {\n    var state = cx.state;\n    state.lexical = new HaxeLexical(state.indented, cx.stream.column(), type, null, state.lexical, info);\n  };\n  result.lex = true;\n  return result;\n}\nfunction poplex() {\n  var state = cx.state;\n  if (state.lexical.prev) {\n    if (state.lexical.type == \")\")\n      state.indented = state.lexical.indented;\n    state.lexical = state.lexical.prev;\n  }\n}\npoplex.lex = true;\n\nfunction expect(wanted) {\n  function f(type) {\n    if (type == wanted) return cont();\n    else if (wanted == \";\") return pass();\n    else return cont(f);\n  }\n  return f;\n}\n\nfunction statement(type) {\n  if (type == \"@\") return cont(metadef);\n  if (type == \"var\") return cont(pushlex(\"vardef\"), vardef1, expect(\";\"), poplex);\n  if (type == \"keyword a\") return cont(pushlex(\"form\"), expression, statement, poplex);\n  if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n  if (type == \"{\") return cont(pushlex(\"}\"), pushcontext, block, poplex, popcontext);\n  if (type == \";\") return cont();\n  if (type == \"attribute\") return cont(maybeattribute);\n  if (type == \"function\") return cont(functiondef);\n  if (type == \"for\") return cont(pushlex(\"form\"), expect(\"(\"), pushlex(\")\"), forspec1, expect(\")\"),\n                                 poplex, statement, poplex);\n  if (type == \"variable\") return cont(pushlex(\"stat\"), maybelabel);\n  if (type == \"switch\") return cont(pushlex(\"form\"), expression, pushlex(\"}\", \"switch\"), expect(\"{\"),\n                                    block, poplex, poplex);\n  if (type == \"case\") return cont(expression, expect(\":\"));\n  if (type == \"default\") return cont(expect(\":\"));\n  if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, expect(\"(\"), funarg, expect(\")\"),\n                                   statement, poplex, popcontext);\n  if (type == \"import\") return cont(importdef, expect(\";\"));\n  if (type == \"typedef\") return cont(typedef);\n  return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n}\nfunction expression(type) {\n  if (atomicTypes.hasOwnProperty(type)) return cont(maybeoperator);\n  if (type == \"type\" ) return cont(maybeoperator);\n  if (type == \"function\") return cont(functiondef);\n  if (type == \"keyword c\") return cont(maybeexpression);\n  if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeoperator);\n  if (type == \"operator\") return cont(expression);\n  if (type == \"[\") return cont(pushlex(\"]\"), commasep(maybeexpression, \"]\"), poplex, maybeoperator);\n  if (type == \"{\") return cont(pushlex(\"}\"), commasep(objprop, \"}\"), poplex, maybeoperator);\n  return cont();\n}\nfunction maybeexpression(type) {\n  if (type.match(/[;\\}\\)\\],]/)) return pass();\n  return pass(expression);\n}\n\nfunction maybeoperator(type, value) {\n  if (type == \"operator\" && /\\+\\+|--/.test(value)) return cont(maybeoperator);\n  if (type == \"operator\" || type == \":\") return cont(expression);\n  if (type == \";\") return;\n  if (type == \"(\") return cont(pushlex(\")\"), commasep(expression, \")\"), poplex, maybeoperator);\n  if (type == \".\") return cont(property, maybeoperator);\n  if (type == \"[\") return cont(pushlex(\"]\"), expression, expect(\"]\"), poplex, maybeoperator);\n}\n\nfunction maybeattribute(type) {\n  if (type == \"attribute\") return cont(maybeattribute);\n  if (type == \"function\") return cont(functiondef);\n  if (type == \"var\") return cont(vardef1);\n}\n\nfunction metadef(type) {\n  if(type == \":\") return cont(metadef);\n  if(type == \"variable\") return cont(metadef);\n  if(type == \"(\") return cont(pushlex(\")\"), commasep(metaargs, \")\"), poplex, statement);\n}\nfunction metaargs(type) {\n  if(type == \"variable\") return cont();\n}\n\nfunction importdef (type, value) {\n  if(type == \"variable\" && /[A-Z]/.test(value.charAt(0))) { registerimport(value); return cont(); }\n  else if(type == \"variable\" || type == \"property\" || type == \".\" || value == \"*\") return cont(importdef);\n}\n\nfunction typedef (type, value)\n{\n  if(type == \"variable\" && /[A-Z]/.test(value.charAt(0))) { registerimport(value); return cont(); }\n  else if (type == \"type\" && /[A-Z]/.test(value.charAt(0))) { return cont(); }\n}\n\nfunction maybelabel(type) {\n  if (type == \":\") return cont(poplex, statement);\n  return pass(maybeoperator, expect(\";\"), poplex);\n}\nfunction property(type) {\n  if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n}\nfunction objprop(type) {\n  if (type == \"variable\") cx.marked = \"property\";\n  if (atomicTypes.hasOwnProperty(type)) return cont(expect(\":\"), expression);\n}\nfunction commasep(what, end) {\n  function proceed(type) {\n    if (type == \",\") return cont(what, proceed);\n    if (type == end) return cont();\n    return cont(expect(end));\n  }\n  return function(type) {\n    if (type == end) return cont();\n    else return pass(what, proceed);\n  };\n}\nfunction block(type) {\n  if (type == \"}\") return cont();\n  return pass(statement, block);\n}\nfunction vardef1(type, value) {\n  if (type == \"variable\"){register(value); return cont(typeuse, vardef2);}\n  return cont();\n}\nfunction vardef2(type, value) {\n  if (value == \"=\") return cont(expression, vardef2);\n  if (type == \",\") return cont(vardef1);\n}\nfunction forspec1(type, value) {\n  if (type == \"variable\") {\n    register(value);\n    return cont(forin, expression)\n  } else {\n    return pass()\n  }\n}\nfunction forin(_type, value) {\n  if (value == \"in\") return cont();\n}\nfunction functiondef(type, value) {\n  //function names starting with upper-case letters are recognised as types, so cludging them together here.\n  if (type == \"variable\" || type == \"type\") {register(value); return cont(functiondef);}\n  if (value == \"new\") return cont(functiondef);\n  if (type == \"(\") return cont(pushlex(\")\"), pushcontext, commasep(funarg, \")\"), poplex, typeuse, statement, popcontext);\n}\nfunction typeuse(type) {\n  if(type == \":\") return cont(typestring);\n}\nfunction typestring(type) {\n  if(type == \"type\") return cont();\n  if(type == \"variable\") return cont();\n  if(type == \"{\") return cont(pushlex(\"}\"), commasep(typeprop, \"}\"), poplex);\n}\nfunction typeprop(type) {\n  if(type == \"variable\") return cont(typeuse);\n}\nfunction funarg(type, value) {\n  if (type == \"variable\") {register(value); return cont(typeuse);}\n}\n\n// Interface\nexport const haxe = {\n  name: \"haxe\",\n  startState: function(indentUnit) {\n    var defaulttypes = [\"Int\", \"Float\", \"String\", \"Void\", \"Std\", \"Bool\", \"Dynamic\", \"Array\"];\n    var state = {\n      tokenize: haxeTokenBase,\n      reAllowed: true,\n      kwAllowed: true,\n      cc: [],\n      lexical: new HaxeLexical(-indentUnit, 0, \"block\", false),\n      importedtypes: defaulttypes,\n      context: null,\n      indented: 0\n    };\n    return state;\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (!state.lexical.hasOwnProperty(\"align\"))\n        state.lexical.align = false;\n      state.indented = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    if (type == \"comment\") return style;\n    state.reAllowed = !!(type == \"operator\" || type == \"keyword c\" || type.match(/^[\\[{}\\(,;:]$/));\n    state.kwAllowed = type != '.';\n    return parseHaxe(state, style, type, content, stream);\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != haxeTokenBase) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical;\n    if (lexical.type == \"stat\" && firstChar == \"}\") lexical = lexical.prev;\n    var type = lexical.type, closing = firstChar == type;\n    if (type == \"vardef\") return lexical.indented + 4;\n    else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n    else if (type == \"stat\" || type == \"form\") return lexical.indented + cx.unit;\n    else if (lexical.info == \"switch\" && !closing)\n      return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? cx.unit : 2 * cx.unit);\n    else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n    else return lexical.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\nexport const hxml = {\n  name: \"hxml\",\n  startState: function () {\n    return {\n      define: false,\n      inString: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    var sol = stream.sol();\n\n    ///* comments */\n    if (ch == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (sol && ch == \"-\") {\n      var style = \"variable-2\";\n\n      stream.eat(/-/);\n\n      if (stream.peek() == \"-\") {\n        stream.eat(/-/);\n        style = \"keyword a\";\n      }\n\n      if (stream.peek() == \"D\") {\n        stream.eat(/[D]/);\n        style = \"keyword c\";\n        state.define = true;\n      }\n\n      stream.eatWhile(/[A-Z]/i);\n      return style;\n    }\n\n    var ch = stream.peek();\n\n    if (state.inString == false && ch == \"'\") {\n      state.inString = true;\n      stream.next();\n    }\n\n    if (state.inString == true) {\n      if (stream.skipTo(\"'\")) {\n\n      } else {\n        stream.skipToEnd();\n      }\n\n      if (stream.peek() == \"'\") {\n        stream.next();\n        state.inString = false;\n      }\n\n      return \"string\";\n    }\n\n    stream.next();\n    return null;\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n}\n"], "mappings": ";;;AAEA,SAAS,GAAGA,OAAM;AAAC,SAAO,EAAC,MAAMA,OAAM,OAAO,UAAS;AAAE;AACzD,IAAI,IAAI,GAAG,WAAW;AAAtB,IAAyB,IAAI,GAAG,WAAW;AAA3C,IAA8C,IAAI,GAAG,WAAW;AAChE,IAAI,WAAW,GAAG,UAAU;AAA5B,IAA+B,OAAO,EAAC,MAAM,QAAQ,OAAO,OAAM;AAAlE,IAAqE,YAAY,EAAC,MAAK,aAAa,OAAO,YAAW;AACtH,IAAI,OAAO,GAAG,SAAS;AACvB,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EAAG,SAAS;AAAA,EAAG,QAAQ;AAAA,EAAG,MAAM;AAAA,EAAG,OAAO;AAAA,EAChD,UAAU;AAAA,EAAG,SAAS;AAAA,EAAG,YAAY;AAAA,EAAG,OAAO;AAAA,EAAG,SAAS;AAAA,EAC3D,OAAO,GAAG,KAAK;AAAA,EAAG,UAAS;AAAA,EAAW,UAAU;AAAA,EAAW,SAAQ,GAAG,QAAQ;AAAA,EAC9E,UAAU;AAAA,EAAW,WAAW;AAAA,EAAW,QAAQ,GAAG,MAAM;AAAA,EAAG,UAAU,GAAG,QAAQ;AAAA,EAAG,SAAS,GAAG,OAAO;AAAA,EAC1G,YAAY,GAAG,UAAU;AAAA,EAAG,SAAS,GAAG,OAAO;AAAA,EAAG,WAAW,GAAG,SAAS;AAAA,EAAG,YAAY,GAAG,IAAI;AAAA,EAC/F,OAAO,GAAG,KAAK;AAAA,EAAG,UAAU,GAAG,QAAQ;AAAA,EAAG,QAAQ,GAAG,MAAM;AAAA,EAAG,WAAW,GAAG,SAAS;AAAA,EACrF,MAAM;AAAA,EAAU,SAAS,GAAG,iBAAiB;AAAA,EAAG,SAAQ,GAAG,OAAO;AAAA,EAClE,SAAS;AAAA,EAAM,YAAW;AAAA,EAAM,QAAO;AAAA,EAAM,aAAY;AAAA,EAAM,WAAU;AAAA,EAAM,WAAU;AAAA,EAAM,cAAa;AAAA,EAAM,WAAU;AAAA,EAC5H,QAAQ;AAAA,EAAM,SAAS;AAAA,EAAM,QAAQ;AACvC;AAEA,IAAI,iBAAiB;AAErB,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,MAAI,UAAU,OAAO;AACrB,UAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,QAAI,QAAQ,OAAO,CAAC;AAClB,aAAO;AACT,cAAU,CAAC,WAAW,QAAQ;AAAA,EAChC;AACF;AAIA,IAAI;AAAJ,IAAU;AACV,SAAS,IAAI,IAAI,OAAOC,OAAM;AAC5B,SAAO;AAAI,YAAUA;AACrB,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,WAAO,MAAM,QAAQ,OAAO,gBAAgB,EAAE,CAAC;AAAA,EACjD,WAAW,qBAAqB,KAAK,EAAE,GAAG;AACxC,WAAO,IAAI,EAAE;AAAA,EACf,WAAW,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACxC,WAAO,SAAS,UAAU;AAC1B,WAAO,IAAI,UAAU,QAAQ;AAAA,EAC/B,WAAW,KAAK,KAAK,EAAE,KAAK,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACzD,WAAO,MAAM,wCAAwC;AACrD,WAAO,IAAI,UAAU,QAAQ;AAAA,EAC/B,WAAW,MAAM,cAAc,MAAM,OAAO,OAAO,IAAI,IAAI,IAAI;AAC7D,gBAAY,QAAQ,GAAG;AACvB,WAAO,SAAS,SAAS;AACzB,WAAO,IAAI,UAAU,gBAAgB;AAAA,EACvC,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,MAAM,QAAQ,OAAO,gBAAgB;AAAA,IAC9C,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,UAAU;AACjB,aAAO,IAAI,WAAW,SAAS;AAAA,IACjC,OAAO;AACL,aAAO,SAAS,cAAc;AAC9B,aAAO,IAAI,YAAY,MAAM,OAAO,QAAQ,CAAC;AAAA,IAC/C;AAAA,EACF,WAAW,MAAM,KAAK;AACpB,WAAO,UAAU;AACjB,WAAO,IAAI,eAAe,MAAM;AAAA,EAClC,WAAW,MAAM,KAAK;AACpB,WAAO,IAAI,GAAG;AACd,WAAO,SAAS,OAAO;AACvB,WAAO,IAAK,YAAY,MAAM;AAAA,EAChC,WAAW,eAAe,KAAK,EAAE,GAAG;AAClC,WAAO,SAAS,cAAc;AAC9B,WAAO,IAAI,YAAY,MAAM,OAAO,QAAQ,CAAC;AAAA,EAC/C,OAAO;AACL,QAAI;AACJ,QAAG,QAAQ,KAAK,EAAE,GAAG;AACnB,aAAO,SAAS,SAAS;AACzB,aAAO,OAAO,QAAQ;AACtB,aAAO,IAAI,QAAQ,QAAQ,IAAI;AAAA,IACjC,OAAO;AACL,aAAO,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO,QAAQ,GAAG,QAAQ,SAAS,qBAAqB,IAAI,KAAK,SAAS,IAAI;AACzF,aAAQ,SAAS,MAAM,YAAa,IAAI,MAAM,MAAM,MAAM,OAAO,IAAI,IACnE,IAAI,YAAY,YAAY,IAAI;AAAA,IACpC;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,YAAY,QAAQ,KAAK;AAC3B,YAAM,WAAW;AACnB,WAAO,IAAI,UAAU,QAAQ;AAAA,EAC/B;AACF;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO,IAAI,WAAW,SAAS;AACjC;AAIA,IAAI,cAAc,EAAC,QAAQ,MAAM,UAAU,MAAM,YAAY,MAAM,UAAU,MAAM,UAAU,KAAI;AAEjG,SAAS,YAAY,UAAU,QAAQD,OAAM,OAAO,MAAM,MAAM;AAC9D,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAOA;AACZ,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,MAAI,SAAS;AAAM,SAAK,QAAQ;AAClC;AAEA,SAAS,QAAQ,OAAO,SAAS;AAC/B,WAAS,IAAI,MAAM,WAAW,GAAG,IAAI,EAAE;AACrC,QAAI,EAAE,QAAQ;AAAS,aAAO;AAClC;AAEA,SAAS,UAAU,OAAO,OAAOA,OAAME,UAAS,QAAQ;AACtD,MAAI,KAAK,MAAM;AAGf,KAAG,QAAQ;AAAO,KAAG,SAAS;AAAQ,KAAG,SAAS,MAAM,GAAG,KAAK;AAEhE,MAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,UAAM,QAAQ,QAAQ;AAExB,SAAM,MAAM;AACV,QAAI,aAAa,GAAG,SAAS,GAAG,IAAI,IAAI;AACxC,QAAI,WAAWF,OAAME,QAAO,GAAG;AAC7B,aAAM,GAAG,UAAU,GAAG,GAAG,SAAS,CAAC,EAAE;AACnC,WAAG,IAAI,EAAE;AACX,UAAI,GAAG;AAAQ,eAAO,GAAG;AACzB,UAAIF,SAAQ,cAAc,QAAQ,OAAOE,QAAO;AAAG,eAAO;AAC1D,UAAIF,SAAQ,cAAc,SAAS,OAAOE,QAAO;AAAG,eAAO;AAC3D,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAO,UAAU;AACjC,MAAI,QAAQ,KAAK,SAAS,OAAO,CAAC,CAAC;AACjC,WAAO;AACT,MAAI,MAAM,MAAM,cAAc;AAC9B,WAAS,IAAI,GAAG,IAAE,KAAK;AACrB,QAAG,MAAM,cAAc,CAAC,KAAG;AAAU,aAAO;AAChD;AAEA,SAAS,eAAe,YAAY;AAClC,MAAI,QAAQ,GAAG;AACf,WAAS,IAAI,MAAM,eAAe,GAAG,IAAI,EAAE;AACzC,QAAG,EAAE,QAAQ;AAAY;AAC3B,QAAM,gBAAgB,EAAE,MAAM,YAAY,MAAM,MAAM,cAAc;AACtE;AAGA,IAAI,KAAK,EAAC,OAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,IAAI,KAAI;AAC3D,SAAS,OAAO;AACd,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG;AAAK,OAAG,GAAG,KAAK,UAAU,CAAC,CAAC;AACzE;AACA,SAAS,OAAO;AACd,OAAK,MAAM,MAAM,SAAS;AAC1B,SAAO;AACT;AACA,SAAS,OAAO,MAAM,MAAM;AAC1B,WAAS,IAAI,MAAM,GAAG,IAAI,EAAE;AAC1B,QAAI,EAAE,QAAQ;AAAM,aAAO;AAC7B,SAAO;AACT;AACA,SAAS,SAAS,SAAS;AACzB,MAAI,QAAQ,GAAG;AACf,MAAI,MAAM,SAAS;AACjB,OAAG,SAAS;AACZ,QAAI,OAAO,SAAS,MAAM,SAAS;AAAG;AACtC,UAAM,YAAY,EAAC,MAAM,SAAS,MAAM,MAAM,UAAS;AAAA,EACzD,WAAW,MAAM,YAAY;AAC3B,QAAI,OAAO,SAAS,MAAM,UAAU;AAAG;AACvC,UAAM,aAAa,EAAC,MAAM,SAAS,MAAM,MAAM,WAAU;AAAA,EAC3D;AACF;AAIA,IAAI,cAAc,EAAC,MAAM,QAAQ,MAAM,KAAI;AAC3C,SAAS,cAAc;AACrB,MAAI,CAAC,GAAG,MAAM;AAAS,OAAG,MAAM,YAAY;AAC5C,KAAG,MAAM,UAAU,EAAC,MAAM,GAAG,MAAM,SAAS,MAAM,GAAG,MAAM,UAAS;AACtE;AACA,SAAS,aAAa;AACpB,KAAG,MAAM,YAAY,GAAG,MAAM,QAAQ;AACtC,KAAG,MAAM,UAAU,GAAG,MAAM,QAAQ;AACtC;AACA,WAAW,MAAM;AACjB,SAAS,QAAQF,OAAM,MAAM;AAC3B,MAAI,SAAS,WAAW;AACtB,QAAI,QAAQ,GAAG;AACf,UAAM,UAAU,IAAI,YAAY,MAAM,UAAU,GAAG,OAAO,OAAO,GAAGA,OAAM,MAAM,MAAM,SAAS,IAAI;AAAA,EACrG;AACA,SAAO,MAAM;AACb,SAAO;AACT;AACA,SAAS,SAAS;AAChB,MAAI,QAAQ,GAAG;AACf,MAAI,MAAM,QAAQ,MAAM;AACtB,QAAI,MAAM,QAAQ,QAAQ;AACxB,YAAM,WAAW,MAAM,QAAQ;AACjC,UAAM,UAAU,MAAM,QAAQ;AAAA,EAChC;AACF;AACA,OAAO,MAAM;AAEb,SAAS,OAAO,QAAQ;AACtB,WAAS,EAAEA,OAAM;AACf,QAAIA,SAAQ;AAAQ,aAAO,KAAK;AAAA,aACvB,UAAU;AAAK,aAAO,KAAK;AAAA;AAC/B,aAAO,KAAK,CAAC;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,UAAUA,OAAM;AACvB,MAAIA,SAAQ;AAAK,WAAO,KAAK,OAAO;AACpC,MAAIA,SAAQ;AAAO,WAAO,KAAK,QAAQ,QAAQ,GAAG,SAAS,OAAO,GAAG,GAAG,MAAM;AAC9E,MAAIA,SAAQ;AAAa,WAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,WAAW,MAAM;AACnF,MAAIA,SAAQ;AAAa,WAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,MAAM;AACvE,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,aAAa,OAAO,QAAQ,UAAU;AACjF,MAAIA,SAAQ;AAAK,WAAO,KAAK;AAC7B,MAAIA,SAAQ;AAAa,WAAO,KAAK,cAAc;AACnD,MAAIA,SAAQ;AAAY,WAAO,KAAK,WAAW;AAC/C,MAAIA,SAAQ;AAAO,WAAO;AAAA,MAAK,QAAQ,MAAM;AAAA,MAAG,OAAO,GAAG;AAAA,MAAG,QAAQ,GAAG;AAAA,MAAG;AAAA,MAAU,OAAO,GAAG;AAAA,MAChE;AAAA,MAAQ;AAAA,MAAW;AAAA,IAAM;AACxD,MAAIA,SAAQ;AAAY,WAAO,KAAK,QAAQ,MAAM,GAAG,UAAU;AAC/D,MAAIA,SAAQ;AAAU,WAAO;AAAA,MAAK,QAAQ,MAAM;AAAA,MAAG;AAAA,MAAY,QAAQ,KAAK,QAAQ;AAAA,MAAG,OAAO,GAAG;AAAA,MAC/D;AAAA,MAAO;AAAA,MAAQ;AAAA,IAAM;AACvD,MAAIA,SAAQ;AAAQ,WAAO,KAAK,YAAY,OAAO,GAAG,CAAC;AACvD,MAAIA,SAAQ;AAAW,WAAO,KAAK,OAAO,GAAG,CAAC;AAC9C,MAAIA,SAAQ;AAAS,WAAO;AAAA,MAAK,QAAQ,MAAM;AAAA,MAAG;AAAA,MAAa,OAAO,GAAG;AAAA,MAAG;AAAA,MAAQ,OAAO,GAAG;AAAA,MAC7D;AAAA,MAAW;AAAA,MAAQ;AAAA,IAAU;AAC9D,MAAIA,SAAQ;AAAU,WAAO,KAAK,WAAW,OAAO,GAAG,CAAC;AACxD,MAAIA,SAAQ;AAAW,WAAO,KAAK,OAAO;AAC1C,SAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,OAAO,GAAG,GAAG,MAAM;AAC9D;AACA,SAAS,WAAWA,OAAM;AACxB,MAAI,YAAY,eAAeA,KAAI;AAAG,WAAO,KAAK,aAAa;AAC/D,MAAIA,SAAQ;AAAS,WAAO,KAAK,aAAa;AAC9C,MAAIA,SAAQ;AAAY,WAAO,KAAK,WAAW;AAC/C,MAAIA,SAAQ;AAAa,WAAO,KAAK,eAAe;AACpD,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,QAAQ,aAAa;AAC9F,MAAIA,SAAQ;AAAY,WAAO,KAAK,UAAU;AAC9C,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,iBAAiB,GAAG,GAAG,QAAQ,aAAa;AAChG,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,SAAS,GAAG,GAAG,QAAQ,aAAa;AACxF,SAAO,KAAK;AACd;AACA,SAAS,gBAAgBA,OAAM;AAC7B,MAAIA,MAAK,MAAM,YAAY;AAAG,WAAO,KAAK;AAC1C,SAAO,KAAK,UAAU;AACxB;AAEA,SAAS,cAAcA,OAAM,OAAO;AAClC,MAAIA,SAAQ,cAAc,UAAU,KAAK,KAAK;AAAG,WAAO,KAAK,aAAa;AAC1E,MAAIA,SAAQ,cAAcA,SAAQ;AAAK,WAAO,KAAK,UAAU;AAC7D,MAAIA,SAAQ;AAAK;AACjB,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,YAAY,GAAG,GAAG,QAAQ,aAAa;AAC3F,MAAIA,SAAQ;AAAK,WAAO,KAAK,UAAU,aAAa;AACpD,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,YAAY,OAAO,GAAG,GAAG,QAAQ,aAAa;AAC3F;AAEA,SAAS,eAAeA,OAAM;AAC5B,MAAIA,SAAQ;AAAa,WAAO,KAAK,cAAc;AACnD,MAAIA,SAAQ;AAAY,WAAO,KAAK,WAAW;AAC/C,MAAIA,SAAQ;AAAO,WAAO,KAAK,OAAO;AACxC;AAEA,SAAS,QAAQA,OAAM;AACrB,MAAGA,SAAQ;AAAK,WAAO,KAAK,OAAO;AACnC,MAAGA,SAAQ;AAAY,WAAO,KAAK,OAAO;AAC1C,MAAGA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,SAAS;AACtF;AACA,SAAS,SAASA,OAAM;AACtB,MAAGA,SAAQ;AAAY,WAAO,KAAK;AACrC;AAEA,SAAS,UAAWA,OAAM,OAAO;AAC/B,MAAGA,SAAQ,cAAc,QAAQ,KAAK,MAAM,OAAO,CAAC,CAAC,GAAG;AAAE,mBAAe,KAAK;AAAG,WAAO,KAAK;AAAA,EAAG,WACxFA,SAAQ,cAAcA,SAAQ,cAAcA,SAAQ,OAAO,SAAS;AAAK,WAAO,KAAK,SAAS;AACxG;AAEA,SAAS,QAASA,OAAM,OACxB;AACE,MAAGA,SAAQ,cAAc,QAAQ,KAAK,MAAM,OAAO,CAAC,CAAC,GAAG;AAAE,mBAAe,KAAK;AAAG,WAAO,KAAK;AAAA,EAAG,WACvFA,SAAQ,UAAU,QAAQ,KAAK,MAAM,OAAO,CAAC,CAAC,GAAG;AAAE,WAAO,KAAK;AAAA,EAAG;AAC7E;AAEA,SAAS,WAAWA,OAAM;AACxB,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,SAAS;AAC9C,SAAO,KAAK,eAAe,OAAO,GAAG,GAAG,MAAM;AAChD;AACA,SAAS,SAASA,OAAM;AACtB,MAAIA,SAAQ,YAAY;AAAC,OAAG,SAAS;AAAY,WAAO,KAAK;AAAA,EAAE;AACjE;AACA,SAAS,QAAQA,OAAM;AACrB,MAAIA,SAAQ;AAAY,OAAG,SAAS;AACpC,MAAI,YAAY,eAAeA,KAAI;AAAG,WAAO,KAAK,OAAO,GAAG,GAAG,UAAU;AAC3E;AACA,SAAS,SAAS,MAAM,KAAK;AAC3B,WAAS,QAAQA,OAAM;AACrB,QAAIA,SAAQ;AAAK,aAAO,KAAK,MAAM,OAAO;AAC1C,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,WAAO,KAAK,OAAO,GAAG,CAAC;AAAA,EACzB;AACA,SAAO,SAASA,OAAM;AACpB,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAAA;AACxB,aAAO,KAAK,MAAM,OAAO;AAAA,EAChC;AACF;AACA,SAAS,MAAMA,OAAM;AACnB,MAAIA,SAAQ;AAAK,WAAO,KAAK;AAC7B,SAAO,KAAK,WAAW,KAAK;AAC9B;AACA,SAAS,QAAQA,OAAM,OAAO;AAC5B,MAAIA,SAAQ,YAAW;AAAC,aAAS,KAAK;AAAG,WAAO,KAAK,SAAS,OAAO;AAAA,EAAE;AACvE,SAAO,KAAK;AACd;AACA,SAAS,QAAQA,OAAM,OAAO;AAC5B,MAAI,SAAS;AAAK,WAAO,KAAK,YAAY,OAAO;AACjD,MAAIA,SAAQ;AAAK,WAAO,KAAK,OAAO;AACtC;AACA,SAAS,SAASA,OAAM,OAAO;AAC7B,MAAIA,SAAQ,YAAY;AACtB,aAAS,KAAK;AACd,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,MAAM,OAAO,OAAO;AAC3B,MAAI,SAAS;AAAM,WAAO,KAAK;AACjC;AACA,SAAS,YAAYA,OAAM,OAAO;AAEhC,MAAIA,SAAQ,cAAcA,SAAQ,QAAQ;AAAC,aAAS,KAAK;AAAG,WAAO,KAAK,WAAW;AAAA,EAAE;AACrF,MAAI,SAAS;AAAO,WAAO,KAAK,WAAW;AAC3C,MAAIA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,aAAa,SAAS,QAAQ,GAAG,GAAG,QAAQ,SAAS,WAAW,UAAU;AACvH;AACA,SAAS,QAAQA,OAAM;AACrB,MAAGA,SAAQ;AAAK,WAAO,KAAK,UAAU;AACxC;AACA,SAAS,WAAWA,OAAM;AACxB,MAAGA,SAAQ;AAAQ,WAAO,KAAK;AAC/B,MAAGA,SAAQ;AAAY,WAAO,KAAK;AACnC,MAAGA,SAAQ;AAAK,WAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,MAAM;AAC3E;AACA,SAAS,SAASA,OAAM;AACtB,MAAGA,SAAQ;AAAY,WAAO,KAAK,OAAO;AAC5C;AACA,SAAS,OAAOA,OAAM,OAAO;AAC3B,MAAIA,SAAQ,YAAY;AAAC,aAAS,KAAK;AAAG,WAAO,KAAK,OAAO;AAAA,EAAE;AACjE;AAGO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,QAAI,eAAe,CAAC,OAAO,SAAS,UAAU,QAAQ,OAAO,QAAQ,WAAW,OAAO;AACvF,QAAI,QAAQ;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,IAAI,CAAC;AAAA,MACL,SAAS,IAAI,YAAY,CAAC,YAAY,GAAG,SAAS,KAAK;AAAA,MACvD,eAAe;AAAA,MACf,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,cAAM,QAAQ,QAAQ;AACxB,YAAM,WAAW,OAAO,YAAY;AAAA,IACtC;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,QAAQ;AAAW,aAAO;AAC9B,UAAM,YAAY,CAAC,EAAE,QAAQ,cAAc,QAAQ,eAAe,KAAK,MAAM,eAAe;AAC5F,UAAM,YAAY,QAAQ;AAC1B,WAAO,UAAU,OAAO,OAAO,MAAM,SAAS,MAAM;AAAA,EACtD;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAWG,KAAI;AACrC,QAAI,MAAM,YAAY;AAAe,aAAO;AAC5C,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,UAAU,MAAM;AAClE,QAAI,QAAQ,QAAQ,UAAU,aAAa;AAAK,gBAAU,QAAQ;AAClE,QAAIH,QAAO,QAAQ,MAAM,UAAU,aAAaA;AAChD,QAAIA,SAAQ;AAAU,aAAO,QAAQ,WAAW;AAAA,aACvCA,SAAQ,UAAU,aAAa;AAAK,aAAO,QAAQ;AAAA,aACnDA,SAAQ,UAAUA,SAAQ;AAAQ,aAAO,QAAQ,WAAWG,IAAG;AAAA,aAC/D,QAAQ,QAAQ,YAAY,CAAC;AACpC,aAAO,QAAQ,YAAY,sBAAsB,KAAK,SAAS,IAAIA,IAAG,OAAO,IAAIA,IAAG;AAAA,aAC7E,QAAQ;AAAO,aAAO,QAAQ,UAAU,UAAU,IAAI;AAAA;AAC1D,aAAO,QAAQ,YAAY,UAAU,IAAIA,IAAG;AAAA,EACnD;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,OAAO,IAAI;AAGrB,QAAI,MAAM,KAAK;AACb,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,KAAK;AACpB,UAAI,QAAQ;AAEZ,aAAO,IAAI,GAAG;AAEd,UAAI,OAAO,KAAK,KAAK,KAAK;AACxB,eAAO,IAAI,GAAG;AACd,gBAAQ;AAAA,MACV;AAEA,UAAI,OAAO,KAAK,KAAK,KAAK;AACxB,eAAO,IAAI,KAAK;AAChB,gBAAQ;AACR,cAAM,SAAS;AAAA,MACjB;AAEA,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO,KAAK;AAErB,QAAI,MAAM,YAAY,SAAS,MAAM,KAAK;AACxC,YAAM,WAAW;AACjB,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,MAAM,YAAY,MAAM;AAC1B,UAAI,OAAO,OAAO,GAAG,GAAG;AAAA,MAExB,OAAO;AACL,eAAO,UAAU;AAAA,MACnB;AAEA,UAAI,OAAO,KAAK,KAAK,KAAK;AACxB,eAAO,KAAK;AACZ,cAAM,WAAW;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": ["type", "cont", "content", "cx"]}