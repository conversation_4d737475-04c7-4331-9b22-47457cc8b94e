// twillio.gateway.ts
import { WebSocketGateway, WebSocketServer, SubscribeMessage, MessageBody, ConnectedSocket, OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TwillioService } from './twillio.service';
import { MessagesService } from 'src/messages/messages.service';
import { normalizePhoneNumber } from '../utils/phone-formatter.util';

@WebSocketGateway({ cors: { origin: '*', methods: ['GET', 'POST'] } })
export class TwillioGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;

  constructor(
    private readonly twilioService: TwillioService,
    private readonly messagesService: MessagesService,
  ) {}

  private normalizeNumber(number: string) {
    return normalizePhoneNumber(number);
  }

  handleConnection(client: Socket) {
    console.log('Client connected:', client.id);
  }

  handleDisconnect(client: Socket) {
    console.log('Client disconnected:', client.id);
  }

  @SubscribeMessage('whatsapp-message')
  sendWhatsAppMessageNotification(message: any) {
    console.log('Sending WhatsApp message notification:', message);
    const normalized = {
      ...message,
      sender: this.normalizeNumber(message.sender),
      from: this.normalizeNumber(message.from),
      to: this.normalizeNumber(message.to),
    };
    this.server.emit('whatsapp-message', normalized);
  }

  // Emit SMS message to all clients
  sendSMSMessageNotification(message: any) {
    const normalized = {
      ...message,
      sender: this.normalizeNumber(message.sender || message.from),
      from: this.normalizeNumber(message.from),
      to: this.normalizeNumber(message.to),
    };
    this.server.emit('sms-message', normalized);
  }

  @SubscribeMessage('update-message-status')
  async handleUpdateMessageStatus(@MessageBody() data: any) {
    const { messageSid, messageStatus } = data;
    console.log(`Updating message status for SID ${messageSid} to ${messageStatus}`);
    try {
      const updatedMessage = await this.twilioService.saveWhatsAppStatusUpdate(messageSid, messageStatus);
      this.server.emit('message-status-updated', {
        messageSid: updatedMessage.messageSid,
        status: updatedMessage.status,
      });
    } catch (error) {
      console.error('Error updating message status:', error);
    }
  }

  @SubscribeMessage('send-message')
  async handleSendMessage(@MessageBody() data: any, @ConnectedSocket() client: Socket) {
    const { from, to, message, templateData } = data;
    try {
      const normalizedFrom = this.normalizeNumber(from);
      const normalizedTo = this.normalizeNumber(to);

      const chat = await this.messagesService.getOrCreateChat(normalizedFrom, normalizedTo);
      const isWithin24h = chat.lastInboundTimestamp && Date.now() - new Date(chat.lastInboundTimestamp).getTime() < 24 * 60 * 60 * 1000;

      let response:any;
      if (isWithin24h) {
        response = await this.twilioService.sendWhatsAppMessage(normalizedTo, message);
      } else {
        response = await this.twilioService.sendWhatsAppMessage(normalizedTo, '', undefined, 'UK', true, templateData);
      }

      const saved = await this.messagesService.saveMessage(normalizedFrom, normalizedTo, {
        messageSid: response.sid,
        sender: normalizedFrom,
        text: message || 'Template sent',
        status: response.status,
        timestamp: new Date(),
      });

      this.sendWhatsAppMessageNotification(saved);
      client.emit('message-sent', { status: 'success', response: saved });
    } catch (error) {
      client.emit('send-error', { message: error.message });
    }
  }

  @SubscribeMessage('send-media-message')
  async handleSendMediaMessage(@MessageBody() data: any, @ConnectedSocket() client: Socket) {
    const { from, to, mediaUrl } = data;
    try {
      const response = await this.twilioService.sendMediaMessage(to, from, mediaUrl);
      const saved = await this.messagesService.saveMessage(from, to, {
        messageSid: response.sid,
        sender: from,
        mediaUrl,
        status: response.status,
        timestamp: new Date(),
      });

      this.sendWhatsAppMessageNotification(saved);
      client.emit('media-sent', { status: 'success', response: saved });
    } catch (error) {
      client.emit('send-error', { message: error.message });
    }
  }

  @SubscribeMessage('get-message-status')
  async handleGetMessageStatus(@MessageBody() data: any, @ConnectedSocket() client: Socket) {
    const { messageSid } = data;
    try {
      const status = await this.twilioService.getMessageStatus(messageSid);
      client.emit('message-status', { status });
    } catch (error) {
      client.emit('status-error', { message: error.message });
    }
  }

  @SubscribeMessage('send-sms-message')
  async handleSendSMSMessage(@MessageBody() data: any, @ConnectedSocket() client: Socket) {
    const { from, to, message } = data;
    try {
      const normalizedFrom = this.normalizeNumber(from);
      const normalizedTo = this.normalizeNumber(to);
      const response = await this.twilioService.sendSMSMessage(normalizedTo, message);
      const saved = await this.messagesService.saveMessage(normalizedFrom, normalizedTo, {
        messageSid: response.sid,
        sender: normalizedFrom,
        text: message,
        status: response.status,
        timestamp: new Date(),
      });
      this.sendSMSMessageNotification(saved);
      client.emit('sms-message-sent', { status: 'success', response: saved });
    } catch (error) {
      client.emit('send-error', { message: error.message });
    }
  }
}
