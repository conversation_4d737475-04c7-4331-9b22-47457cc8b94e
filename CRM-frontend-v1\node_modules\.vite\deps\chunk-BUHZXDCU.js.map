{"version": 3, "sources": ["../../@codesandbox/sandpack-client/dist/base-80a1f760.mjs"], "sourcesContent": ["import { dequal } from 'dequal';\n\nvar SandpackClient = /** @class */ (function () {\n    function SandpackClient(iframeSelector, sandboxSetup, options) {\n        if (options === void 0) { options = {}; }\n        this.status = \"idle\";\n        this.options = options;\n        this.sandboxSetup = sandboxSetup;\n        this.iframeSelector = iframeSelector;\n    }\n    /**\n     * Clients handles\n     */\n    SandpackClient.prototype.updateOptions = function (options) {\n        if (!dequal(this.options, options)) {\n            this.options = options;\n            this.updateSandbox();\n        }\n    };\n    SandpackClient.prototype.updateSandbox = function (_sandboxSetup, _isInitializationCompile) {\n        if (_sandboxSetup === void 0) { _sandboxSetup = this.sandboxSetup; }\n        throw Error(\"Method not implemented\");\n    };\n    SandpackClient.prototype.destroy = function () {\n        throw Error(\"Method not implemented\");\n    };\n    /**\n     * Bundler communication\n     */\n    SandpackClient.prototype.dispatch = function (_message) {\n        throw Error(\"Method not implemented\");\n    };\n    SandpackClient.prototype.listen = function (_listener) {\n        throw Error(\"Method not implemented\");\n    };\n    return SandpackClient;\n}());\n\nexport { SandpackClient as S };\n"], "mappings": ";;;;;AAEA,IAAI;AAAA;AAAA,EAAgC,WAAY;AAC5C,aAASA,gBAAe,gBAAgB,cAAc,SAAS;AAC3D,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IAC1B;AAIA,IAAAA,gBAAe,UAAU,gBAAgB,SAAU,SAAS;AACxD,UAAI,CAAC,OAAO,KAAK,SAAS,OAAO,GAAG;AAChC,aAAK,UAAU;AACf,aAAK,cAAc;AAAA,MACvB;AAAA,IACJ;AACA,IAAAA,gBAAe,UAAU,gBAAgB,SAAU,eAAe,0BAA0B;AACxF,UAAI,kBAAkB,QAAQ;AAAE,wBAAgB,KAAK;AAAA,MAAc;AACnE,YAAM,MAAM,wBAAwB;AAAA,IACxC;AACA,IAAAA,gBAAe,UAAU,UAAU,WAAY;AAC3C,YAAM,MAAM,wBAAwB;AAAA,IACxC;AAIA,IAAAA,gBAAe,UAAU,WAAW,SAAU,UAAU;AACpD,YAAM,MAAM,wBAAwB;AAAA,IACxC;AACA,IAAAA,gBAAe,UAAU,SAAS,SAAU,WAAW;AACnD,YAAM,MAAM,wBAAwB;AAAA,IACxC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;", "names": ["SandpackClient"]}