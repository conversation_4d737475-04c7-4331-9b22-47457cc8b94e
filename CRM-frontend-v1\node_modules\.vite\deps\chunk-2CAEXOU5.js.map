{"version": 3, "sources": ["../../@mui/material/CardHeader/CardHeader.js", "../../@mui/material/CardHeader/cardHeaderClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"avatar\", \"className\", \"component\", \"disableTypography\", \"subheader\", \"subheaderTypographyProps\", \"title\", \"titleTypographyProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '../Typography';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport cardHeaderClasses, { getCardHeaderUtilityClass } from './cardHeaderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar'],\n    action: ['action'],\n    content: ['content'],\n    title: ['title'],\n    subheader: ['subheader']\n  };\n  return composeClasses(slots, getCardHeaderUtilityClass, classes);\n};\nconst CardHeaderRoot = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${cardHeaderClasses.title}`]: styles.title,\n    [`& .${cardHeaderClasses.subheader}`]: styles.subheader\n  }, styles.root)\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 16\n});\nconst CardHeaderAvatar = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Avatar',\n  overridesResolver: (props, styles) => styles.avatar\n})({\n  display: 'flex',\n  flex: '0 0 auto',\n  marginRight: 16\n});\nconst CardHeaderAction = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  flex: '0 0 auto',\n  alignSelf: 'flex-start',\n  marginTop: -4,\n  marginRight: -8,\n  marginBottom: -4\n});\nconst CardHeaderContent = styled('div', {\n  name: 'MuiCardHeader',\n  slot: 'Content',\n  overridesResolver: (props, styles) => styles.content\n})({\n  flex: '1 1 auto'\n});\nconst CardHeader = /*#__PURE__*/React.forwardRef(function CardHeader(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiCardHeader'\n  });\n  const {\n      action,\n      avatar,\n      className,\n      component = 'div',\n      disableTypography = false,\n      subheader: subheaderProp,\n      subheaderTypographyProps,\n      title: titleProp,\n      titleTypographyProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    disableTypography\n  });\n  const classes = useUtilityClasses(ownerState);\n  let title = titleProp;\n  if (title != null && title.type !== Typography && !disableTypography) {\n    title = /*#__PURE__*/_jsx(Typography, _extends({\n      variant: avatar ? 'body2' : 'h5',\n      className: classes.title,\n      component: \"span\",\n      display: \"block\"\n    }, titleTypographyProps, {\n      children: title\n    }));\n  }\n  let subheader = subheaderProp;\n  if (subheader != null && subheader.type !== Typography && !disableTypography) {\n    subheader = /*#__PURE__*/_jsx(Typography, _extends({\n      variant: avatar ? 'body2' : 'body1',\n      className: classes.subheader,\n      color: \"text.secondary\",\n      component: \"span\",\n      display: \"block\"\n    }, subheaderTypographyProps, {\n      children: subheader\n    }));\n  }\n  return /*#__PURE__*/_jsxs(CardHeaderRoot, _extends({\n    className: clsx(classes.root, className),\n    as: component,\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [avatar && /*#__PURE__*/_jsx(CardHeaderAvatar, {\n      className: classes.avatar,\n      ownerState: ownerState,\n      children: avatar\n    }), /*#__PURE__*/_jsxs(CardHeaderContent, {\n      className: classes.content,\n      ownerState: ownerState,\n      children: [title, subheader]\n    }), action && /*#__PURE__*/_jsx(CardHeaderAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardHeader.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display in the card header.\n   */\n  action: PropTypes.node,\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.node,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, `subheader` and `title` won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `title` text, and optional `subheader` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  subheader: PropTypes.node,\n  /**\n   * These props will be forwarded to the subheader\n   * (as long as disableTypography is not `true`).\n   */\n  subheaderTypographyProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The content of the component.\n   */\n  title: PropTypes.node,\n  /**\n   * These props will be forwarded to the title\n   * (as long as disableTypography is not `true`).\n   */\n  titleTypographyProps: PropTypes.object\n} : void 0;\nexport default CardHeader;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardHeaderUtilityClass(slot) {\n  return generateUtilityClass('MuiCardHeader', slot);\n}\nconst cardHeaderClasses = generateUtilityClasses('MuiCardHeader', ['root', 'avatar', 'action', 'content', 'title', 'subheader']);\nexport default cardHeaderClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACA,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,UAAU,UAAU,WAAW,SAAS,WAAW,CAAC;AAC/H,IAAO,4BAAQ;;;ADOf,yBAA4B;AAC5B,IAAAA,sBAA8B;AAV9B,IAAM,YAAY,CAAC,UAAU,UAAU,aAAa,aAAa,qBAAqB,aAAa,4BAA4B,SAAS,sBAAsB;AAW9J,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,QAAQ,CAAC,QAAQ;AAAA,IACjB,QAAQ,CAAC,QAAQ;AAAA,IACjB,SAAS,CAAC,SAAS;AAAA,IACnB,OAAO,CAAC,OAAO;AAAA,IACf,WAAW,CAAC,WAAW;AAAA,EACzB;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACA,IAAM,iBAAiB,eAAO,OAAO;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,SAAS;AAAA,IAC7C,CAAC,MAAM,0BAAkB,KAAK,EAAE,GAAG,OAAO;AAAA,IAC1C,CAAC,MAAM,0BAAkB,SAAS,EAAE,GAAG,OAAO;AAAA,EAChD,GAAG,OAAO,IAAI;AAChB,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AACD,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,MAAM;AAAA,EACN,aAAa;AACf,CAAC;AACD,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAChB,CAAC;AACD,IAAM,oBAAoB,eAAO,OAAO;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,MAAM;AACR,CAAC;AACD,IAAM,aAAgC,iBAAW,SAASC,YAAW,SAAS,KAAK;AACjF,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,MAAI,QAAQ;AACZ,MAAI,SAAS,QAAQ,MAAM,SAAS,sBAAc,CAAC,mBAAmB;AACpE,gBAAqB,mBAAAC,KAAK,oBAAY,SAAS;AAAA,MAC7C,SAAS,SAAS,UAAU;AAAA,MAC5B,WAAW,QAAQ;AAAA,MACnB,WAAW;AAAA,MACX,SAAS;AAAA,IACX,GAAG,sBAAsB;AAAA,MACvB,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACA,MAAI,YAAY;AAChB,MAAI,aAAa,QAAQ,UAAU,SAAS,sBAAc,CAAC,mBAAmB;AAC5E,oBAAyB,mBAAAA,KAAK,oBAAY,SAAS;AAAA,MACjD,SAAS,SAAS,UAAU;AAAA,MAC5B,WAAW,QAAQ;AAAA,MACnB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,IACX,GAAG,0BAA0B;AAAA,MAC3B,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACA,aAAoB,oBAAAC,MAAM,gBAAgB,SAAS;AAAA,IACjD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR,UAAU,CAAC,cAAuB,mBAAAD,KAAK,kBAAkB;AAAA,MACvD,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC,OAAgB,oBAAAC,MAAM,mBAAmB;AAAA,MACxC,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,OAAO,SAAS;AAAA,IAC7B,CAAC,GAAG,cAAuB,mBAAAD,KAAK,kBAAkB;AAAA,MAChD,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,WAAW,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpF,QAAQ,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,0BAA0B,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,sBAAsB,kBAAAA,QAAU;AAClC,IAAI;AACJ,IAAO,qBAAQ;", "names": ["import_jsx_runtime", "<PERSON><PERSON><PERSON><PERSON>", "_jsx", "_jsxs", "PropTypes"]}