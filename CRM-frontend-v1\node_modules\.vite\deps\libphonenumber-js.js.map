{"version": 3, "sources": ["../../libphonenumber-js/min/exports/withMetadataArgument.js", "../../libphonenumber-js/min/exports/parsePhoneNumberWithError.js", "../../libphonenumber-js/min/exports/parsePhoneNumber.js", "../../libphonenumber-js/min/exports/isValidPhoneNumber.js", "../../libphonenumber-js/min/exports/isPossiblePhoneNumber.js", "../../libphonenumber-js/min/exports/validatePhoneNumberLength.js", "../../libphonenumber-js/min/exports/findNumbers.js", "../../libphonenumber-js/min/exports/searchNumbers.js", "../../libphonenumber-js/min/exports/findPhoneNumbersInText.js", "../../libphonenumber-js/min/exports/searchPhoneNumbersInText.js", "../../libphonenumber-js/min/exports/PhoneNumberMatcher.js", "../../libphonenumber-js/min/exports/AsYouType.js", "../../libphonenumber-js/min/exports/isSupportedCountry.js", "../../libphonenumber-js/min/exports/getCountries.js", "../../libphonenumber-js/min/exports/getCountryCallingCode.js", "../../libphonenumber-js/min/exports/getExtPrefix.js", "../../libphonenumber-js/min/exports/Metadata.js", "../../libphonenumber-js/min/exports/getExampleNumber.js", "../../libphonenumber-js/min/exports/formatIncompletePhoneNumber.js", "../../libphonenumber-js/min/exports/PhoneNumber.js", "../../libphonenumber-js/source/legacy/parse.js", "../../libphonenumber-js/index.es6.exports/parse.js", "../../libphonenumber-js/source/legacy/format.js", "../../libphonenumber-js/index.es6.exports/format.js", "../../libphonenumber-js/source/legacy/getNumberType.js", "../../libphonenumber-js/index.es6.exports/getNumberType.js", "../../libphonenumber-js/source/legacy/isPossibleNumber.js", "../../libphonenumber-js/index.es6.exports/isPossibleNumber.js", "../../libphonenumber-js/source/legacy/isValidNumber.js", "../../libphonenumber-js/index.es6.exports/isValidNumber.js", "../../libphonenumber-js/source/legacy/isValidNumberForRegion_.js", "../../libphonenumber-js/source/legacy/isValidNumberForRegion.js", "../../libphonenumber-js/index.es6.exports/isValidNumberForRegion.js", "../../libphonenumber-js/source/legacy/findPhoneNumbersInitialImplementation.js", "../../libphonenumber-js/source/legacy/findPhoneNumbers.js", "../../libphonenumber-js/index.es6.exports/findPhoneNumbers.js", "../../libphonenumber-js/index.es6.exports/searchPhoneNumbers.js", "../../libphonenumber-js/index.es6.exports/PhoneNumberSearch.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nexport default function withMetadataArgument(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { parsePhoneNumberWithError as _parsePhoneNumberWithError } from '../../core/index.js'\r\n\r\nexport function parsePhoneNumberWithError() {\r\n\treturn withMetadataArgument(_parsePhoneNumberWithError, arguments)\r\n}\r\n", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { default as _parsePhoneNumber } from '../../core/index.js'\r\n\r\nexport function parsePhoneNumber() {\r\n\treturn withMetadataArgument(_parsePhoneNumber, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isValidPhoneNumber as _isValidPhoneNumber } from '../../core/index.js'\r\n\r\nexport function isValidPhoneNumber() {\r\n\treturn withMetadataArgument(_isValidPhoneNumber, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isPossiblePhoneNumber as _isPossiblePhoneNumber } from '../../core/index.js'\r\n\r\nexport function isPossiblePhoneNumber() {\r\n\treturn withMetadataArgument(_isPossiblePhoneNumber, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { validatePhoneNumberLength as _validatePhoneNumberLength } from '../../core/index.js'\r\n\r\nexport function validatePhoneNumberLength() {\r\n\treturn withMetadataArgument(_validatePhoneNumberLength, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { findNumbers as _findNumbers } from '../../core/index.js'\r\n\r\nexport function findNumbers() {\r\n\treturn withMetadataArgument(_findNumbers, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { searchNumbers as _searchNumbers } from '../../core/index.js'\r\n\r\nexport function searchNumbers() {\r\n\treturn withMetadataArgument(_searchNumbers, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { findPhoneNumbersInText as _findPhoneNumbersInText } from '../../core/index.js'\r\n\r\nexport function findPhoneNumbersInText() {\r\n\treturn withMetadataArgument(_findPhoneNumbersInText, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { searchPhoneNumbersInText as _searchPhoneNumbersInText } from '../../core/index.js'\r\n\r\nexport function searchPhoneNumbersInText() {\r\n\treturn withMetadataArgument(_searchPhoneNumbersInText, arguments)\r\n}", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nimport { PhoneNumberMatcher as _PhoneNumberMatcher } from '../../core/index.js'\r\n\r\nexport function PhoneNumberMatcher(text, options) {\r\n\treturn _PhoneNumberMatcher.call(this, text, options, metadata)\r\n}\r\nPhoneNumberMatcher.prototype = Object.create(_PhoneNumberMatcher.prototype, {})\r\nPhoneNumberMatcher.prototype.constructor = PhoneNumberMatcher\r\n", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nimport { AsYouType as _AsYouType } from '../../core/index.js'\r\n\r\nexport function AsYouType(country) {\r\n\treturn _AsYouType.call(this, country, metadata)\r\n}\r\n\r\nAsYouType.prototype = Object.create(_AsYouType.prototype, {})\r\nAsYouType.prototype.constructor = AsYouType", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isSupportedCountry as _isSupportedCountry } from '../../core/index.js'\r\n\r\nexport function isSupportedCountry() {\r\n\treturn withMetadataArgument(_isSupportedCountry, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountries as _getCountries } from '../../core/index.js'\r\n\r\nexport function getCountries() {\r\n\treturn withMetadataArgument(_getCountries, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountryCallingCode as _getCountryCallingCode } from '../../core/index.js'\r\n\r\nexport function getCountryCallingCode() {\r\n\treturn withMetadataArgument(_getCountryCallingCode, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getExtPrefix as _getExtPrefix } from '../../core/index.js'\r\n\r\nexport function getExtPrefix() {\r\n\treturn withMetadataArgument(_getExtPrefix, arguments)\r\n}", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nimport { Metadata as _Metadata } from '../../core/index.js'\r\n\r\nexport function Metadata() {\r\n\treturn _Metadata.call(this, metadata)\r\n}\r\n\r\nMetadata.prototype = Object.create(_Metadata.prototype, {})\r\nMetadata.prototype.constructor = Metadata", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getExampleNumber as _getExampleNumber } from '../../core/index.js'\r\n\r\nexport function getExampleNumber() {\r\n\treturn withMetadataArgument(_getExampleNumber, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { formatIncompletePhoneNumber as _formatIncompletePhoneNumber } from '../../core/index.js'\r\n\r\nexport function formatIncompletePhoneNumber() {\r\n\treturn withMetadataArgument(_formatIncompletePhoneNumber, arguments)\r\n}", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nimport { PhoneNumber as _PhoneNumber } from '../../core/index.js'\r\n\r\nexport function PhoneNumber(number) {\r\n\treturn _PhoneNumber.call(this, number, metadata)\r\n}\r\nPhoneNumber.prototype = Object.create(_PhoneNumber.prototype, {})\r\nPhoneNumber.prototype.constructor = PhoneNumber\r\n", "import _parseNumber from '../parse.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function parseNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _parseNumber(text, options, metadata)\r\n}", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _parse from '../es6/legacy/parse.js'\r\n\r\nexport function parse() {\r\n\treturn withMetadataArgument(_parse, arguments)\r\n}\r\n", "import _formatNumber from '../format.js'\r\nimport parse from '../parse.js'\r\nimport isObject from '../helpers/isObject.js'\r\n\r\nexport default function formatNumber() {\r\n\tconst {\r\n\t\tinput,\r\n\t\tformat,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t} = normalizeArguments(arguments)\r\n\r\n\treturn _formatNumber(input, format, options, metadata)\r\n}\r\n\r\n// Sort out arguments\r\nfunction normalizeArguments(args)\r\n{\r\n\tconst [arg_1, arg_2, arg_3, arg_4, arg_5] = Array.prototype.slice.call(args)\r\n\r\n\tlet input\r\n\tlet format\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// Sort out arguments.\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `format('8005553535', ...)`.\r\n\tif (typeof arg_1 === 'string')\r\n\t{\r\n\t\t// If country code is supplied.\r\n\t\t// `format('8005553535', 'RU', 'NATIONAL', [options], metadata)`.\r\n\t\tif (typeof arg_3 === 'string')\r\n\t\t{\r\n\t\t\tformat = arg_3\r\n\r\n\t\t\tif (arg_5)\r\n\t\t\t{\r\n\t\t\t\toptions  = arg_4\r\n\t\t\t\tmetadata = arg_5\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\r\n\t\t\tinput = parse(arg_1, { defaultCountry: arg_2, extended: true }, metadata)\r\n\t\t}\r\n\t\t// Just an international phone number is supplied\r\n\t\t// `format('+78005553535', 'NATIONAL', [options], metadata)`.\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (typeof arg_2 !== 'string')\r\n\t\t\t{\r\n\t\t\t\tthrow new Error('`format` argument not passed to `formatNumber(number, format)`')\r\n\t\t\t}\r\n\r\n\t\t\tformat = arg_2\r\n\r\n\t\t\tif (arg_4)\r\n\t\t\t{\r\n\t\t\t\toptions  = arg_3\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\r\n\t\t\tinput = parse(arg_1, { extended: true }, metadata)\r\n\t\t}\r\n\t}\r\n\t// If the phone number is passed as a parsed number object.\r\n\t// `format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', [options], metadata)`.\r\n\telse if (isObject(arg_1))\r\n\t{\r\n\t\tinput  = arg_1\r\n\t\tformat = arg_2\r\n\r\n\t\tif (arg_4)\r\n\t\t{\r\n\t\t\toptions  = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\t}\r\n\telse throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t// Legacy lowercase formats.\r\n\tif (format === 'International') {\r\n\t\tformat = 'INTERNATIONAL'\r\n\t} else if (format === 'National') {\r\n\t\tformat = 'NATIONAL'\r\n\t}\r\n\r\n\treturn {\r\n\t\tinput,\r\n\t\tformat,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _format from '../es6/legacy/format.js'\r\n\r\nexport function format() {\r\n\treturn withMetadataArgument(_format, arguments)\r\n}\r\n", "import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js'\r\nimport _getNumberType from '../helpers/getNumberType.js'\r\nimport isObject from '../helpers/isObject.js'\r\nimport parse from '../parse.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function getNumberType() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn\r\n\t}\r\n\treturn _getNumberType(input, options, metadata)\r\n}\r\n\r\n// Sort out arguments\r\nexport function normalizeArguments(args)\r\n{\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet input\r\n\tlet options = {}\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `getNumberType('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string')\r\n\t{\r\n\t\t// If \"default country\" argument is being passed\r\n\t\t// then convert it to an `options` object.\r\n\t\t// `getNumberType('88005553535', 'RU', metadata)`.\r\n\t\tif (!isObject(arg_2))\r\n\t\t{\r\n\t\t\tif (arg_4)\r\n\t\t\t{\r\n\t\t\t\toptions = arg_3\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\r\n\t\t\t// `parse` extracts phone numbers from raw text,\r\n\t\t\t// therefore it will cut off all \"garbage\" characters,\r\n\t\t\t// while this `validate` function needs to verify\r\n\t\t\t// that the phone number contains no \"garbage\"\r\n\t\t\t// therefore the explicit `isViablePhoneNumber` check.\r\n\t\t\tif (isViablePhoneNumber(arg_1))\r\n\t\t\t{\r\n\t\t\t\tinput = parse(arg_1, { defaultCountry: arg_2 }, metadata)\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tinput = {}\r\n\t\t\t}\r\n\t\t}\r\n\t\t// No \"resrict country\" argument is being passed.\r\n\t\t// International phone number is passed.\r\n\t\t// `getNumberType('+78005553535', metadata)`.\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (arg_3)\r\n\t\t\t{\r\n\t\t\t\toptions = arg_2\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_2\r\n\t\t\t}\r\n\r\n\t\t\t// `parse` extracts phone numbers from raw text,\r\n\t\t\t// therefore it will cut off all \"garbage\" characters,\r\n\t\t\t// while this `validate` function needs to verify\r\n\t\t\t// that the phone number contains no \"garbage\"\r\n\t\t\t// therefore the explicit `isViablePhoneNumber` check.\r\n\t\t\tif (isViablePhoneNumber(arg_1))\r\n\t\t\t{\r\n\t\t\t\tinput = parse(arg_1, undefined, metadata)\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tinput = {}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// If the phone number is passed as a parsed phone number.\r\n\t// `getNumberType({ phone: '88005553535', country: 'RU' }, ...)`.\r\n\telse if (isObject(arg_1))\r\n\t{\r\n\t\tinput = arg_1\r\n\r\n\t\tif (arg_3)\r\n\t\t{\r\n\t\t\toptions = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\treturn {\r\n\t\tinput,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _getNumberType from '../es6/legacy/getNumberType.js'\r\n\r\nexport function getNumberType() {\r\n\treturn withMetadataArgument(_getNumberType, arguments)\r\n}\r\n", "import { normalizeArguments } from './getNumberType.js'\r\nimport _isPossibleNumber from '../isPossible.js'\r\n\r\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isPossibleNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone && !(options && options.v2)) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isPossibleNumber(input, options, metadata)\r\n}", "// Deprecated.\r\n\r\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isPossibleNumber from '../es6/legacy/isPossibleNumber.js'\r\n\r\nexport function isPossibleNumber() {\r\n\treturn withMetadataArgument(_isPossibleNumber, arguments)\r\n}\r\n", "import _isValidNumber from '../isValid.js'\r\nimport { normalizeArguments } from './getNumberType.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function isValidNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isValidNumber(input, options, metadata)\r\n}", "// Deprecated.\r\n\r\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isValidNumber from '../es6/legacy/isValidNumber.js'\r\n\r\nexport function isValidNumber() {\r\n\treturn withMetadataArgument(_isValidNumber, arguments)\r\n}\r\n", "import isValidNumber from '../isValid.js'\r\n\r\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\r\nexport default function isValidNumberForRegion(input, country, options, metadata) {\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\treturn input.country === country && isValidNumber(input, options, metadata)\r\n}", "import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js'\r\nimport parseNumber from '../parse.js'\r\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'\r\n\r\n// This function has been deprecated and is not exported as\r\n// `isValidPhoneNumberForCountry()` or `isValidPhoneNumberForRegion()`.\r\n//\r\n// The rationale is:\r\n//\r\n// * We don't use the \"region\" word, so \"country\" would be better.\r\n//\r\n// * It could be substituted with:\r\n//\r\n// ```js\r\n// export default function isValidPhoneNumberForCountry(phoneNumberString, country) {\r\n// \tconst phoneNumber = parsePhoneNumber(phoneNumberString, {\r\n// \t\tdefaultCountry: country,\r\n// \t\t// Demand that the entire input string must be a phone number.\r\n// \t\t// Otherwise, it would \"extract\" a phone number from an input string.\r\n// \t\textract: false\r\n// \t})\r\n// \tif (!phoneNumber) {\r\n// \t\treturn false\r\n// \t}\r\n// \tif (phoneNumber.country !== country) {\r\n// \t\treturn false\r\n// \t}\r\n// \treturn phoneNumber.isValid()\r\n// }\r\n// ```\r\n//\r\n// * Same function could be used for `isPossiblePhoneNumberForCountry()`\r\n//   by replacing `isValid()` with `isPossible()`.\r\n//\r\n// * The reason why this function is not exported is because its result is ambiguous.\r\n//   Suppose `false` is returned. It could mean any of:\r\n//   * Not a phone number.\r\n//   * The phone number is valid but belongs to another country or another calling code.\r\n//   * The phone number belongs to the correct country but is not valid digit-wise.\r\n//   All those three cases should be handled separately from a \"User Experience\" standpoint.\r\n//   Simply showing \"Invalid phone number\" error in all of those cases would be lazy UX.\r\n\r\nexport default function isValidNumberForRegion(number, country, metadata) {\r\n\tif (typeof number !== 'string') {\r\n\t\tthrow new TypeError('number must be a string')\r\n\t}\r\n\tif (typeof country !== 'string') {\r\n\t\tthrow new TypeError('country must be a string')\r\n\t}\r\n\t// `parse` extracts phone numbers from raw text,\r\n\t// therefore it will cut off all \"garbage\" characters,\r\n\t// while this `validate` function needs to verify\r\n\t// that the phone number contains no \"garbage\"\r\n\t// therefore the explicit `isViablePhoneNumber` check.\r\n\tlet input\r\n\tif (isViablePhoneNumber(number)) {\r\n\t\tinput = parseNumber(number, { defaultCountry: country }, metadata)\r\n\t} else {\r\n\t\tinput = {}\r\n\t}\r\n\treturn _isValidNumberForRegion(input, country, undefined, metadata)\r\n}", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isValidNumberForRegion from '../es6/legacy/isValidNumberForRegion.js'\r\n\r\nexport function isValidNumberForRegion() {\r\n\treturn withMetadataArgument(_isValidNumberForRegion, arguments)\r\n}\r\n", "// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport {\r\n\tPLUS_CHARS,\r\n\tVALID_PUNCTUATION,\r\n\tVALID_DIGITS,\r\n\tWHITESPACE\r\n} from '../constants.js'\r\n\r\nimport parse from '../parse.js'\r\nimport { VALID_PHONE_NUMBER_WITH_EXTENSION } from '../helpers/isViablePhoneNumber.js'\r\nimport createExtensionPattern from '../helpers/extension/createExtensionPattern.js'\r\n\r\nimport parsePreCandidate from '../findNumbers/parsePreCandidate.js'\r\nimport isValidPreCandidate from '../findNumbers/isValidPreCandidate.js'\r\nimport isValidCandidate from '../findNumbers/isValidCandidate.js'\r\n\r\n/**\r\n * Regexp of all possible ways to write extensions, for use when parsing. This\r\n * will be run as a case-insensitive regexp match. Wide character versions are\r\n * also provided after each ASCII version. There are three regular expressions\r\n * here. The first covers RFC 3966 format, where the extension is added using\r\n * ';ext='. The second more generic one starts with optional white space and\r\n * ends with an optional full stop (.), followed by zero or more spaces/tabs\r\n * /commas and then the numbers themselves. The other one covers the special\r\n * case of American numbers where the extension is written with a hash at the\r\n * end, such as '- 503#'. Note that the only capturing groups should be around\r\n * the digits that you want to capture as part of the extension, or else parsing\r\n * will fail! We allow two options for representing the accented o - the\r\n * character itself, and one in the unicode decomposed form with the combining\r\n * acute accent.\r\n */\r\nexport const EXTN_PATTERNS_FOR_PARSING = createExtensionPattern('parsing')\r\n\r\nconst WHITESPACE_IN_THE_BEGINNING_PATTERN = new RegExp('^[' + WHITESPACE + ']+')\r\nconst PUNCTUATION_IN_THE_END_PATTERN = new RegExp('[' + VALID_PUNCTUATION + ']+$')\r\n\r\n// // Regular expression for getting opening brackets for a valid number\r\n// // found using `PHONE_NUMBER_START_PATTERN` for prepending those brackets to the number.\r\n// const BEFORE_NUMBER_DIGITS_PUNCTUATION = new RegExp('[' + OPENING_BRACKETS + ']+' + '[' + WHITESPACE + ']*' + '$')\r\n\r\nconst VALID_PRECEDING_CHARACTER_PATTERN = /[^a-zA-Z0-9]/\r\n\r\nexport default function findPhoneNumbers(text, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\tconst search = new PhoneNumberSearch(text, options, metadata)\r\n\tconst phones = []\r\n\twhile (search.hasNext()) {\r\n\t\tphones.push(search.next())\r\n\t}\r\n\treturn phones\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers(text, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\tconst search = new PhoneNumberSearch(text, options, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (search.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: search.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts a parseable phone number including any opening brackets, etc.\r\n * @param  {string} text - Input.\r\n * @return {object} `{ ?number, ?startsAt, ?endsAt }`.\r\n */\r\nexport class PhoneNumberSearch {\r\n\tconstructor(text, options, metadata) {\r\n\t\tthis.text = text\r\n\t\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t\t// code coverage would decrease for some weird reason.\r\n\t\tthis.options = options || {}\r\n\t\tthis.metadata = metadata\r\n\r\n\t\t// Iteration tristate.\r\n\t\tthis.state = 'NOT_READY'\r\n\r\n\t\tthis.regexp = new RegExp(VALID_PHONE_NUMBER_WITH_EXTENSION, 'ig')\r\n\t}\r\n\r\n\tfind() {\r\n\t\tconst matches = this.regexp.exec(this.text)\r\n\t\tif (!matches) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tlet number = matches[0]\r\n\t\tlet startsAt = matches.index\r\n\r\n\t\tnumber = number.replace(WHITESPACE_IN_THE_BEGINNING_PATTERN, '')\r\n\t\tstartsAt += matches[0].length - number.length\r\n\t\t// Fixes not parsing numbers with whitespace in the end.\r\n\t\t// Also fixes not parsing numbers with opening parentheses in the end.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tnumber = number.replace(PUNCTUATION_IN_THE_END_PATTERN, '')\r\n\r\n\t\tnumber = parsePreCandidate(number)\r\n\r\n\t\tconst result = this.parseCandidate(number, startsAt)\r\n\t\tif (result) {\r\n\t\t\treturn result\r\n\t\t}\r\n\r\n\t\t// Tail recursion.\r\n\t\t// Try the next one if this one is not a valid phone number.\r\n\t\treturn this.find()\r\n\t}\r\n\r\n\tparseCandidate(number, startsAt) {\r\n\t\tif (!isValidPreCandidate(number, startsAt, this.text)) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// Don't parse phone numbers which are non-phone numbers\r\n\t\t// due to being part of something else (e.g. a UUID).\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/213\r\n\t\t// Copy-pasted from Google's `PhoneNumberMatcher.js` (`.parseAndValidate()`).\r\n\t\tif (!isValidCandidate(number, startsAt, this.text, this.options.extended ? 'POSSIBLE' : 'VALID')) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// // Prepend any opening brackets left behind by the\r\n\t\t// // `PHONE_NUMBER_START_PATTERN` regexp.\r\n\t\t// const text_before_number = text.slice(this.searching_from, startsAt)\r\n\t\t// const full_number_starts_at = text_before_number.search(BEFORE_NUMBER_DIGITS_PUNCTUATION)\r\n\t\t// if (full_number_starts_at >= 0)\r\n\t\t// {\r\n\t\t// \tnumber   = text_before_number.slice(full_number_starts_at) + number\r\n\t\t// \tstartsAt = full_number_starts_at\r\n\t\t// }\r\n\t\t//\r\n\t\t// this.searching_from = matches.lastIndex\r\n\r\n\t\tconst result = parse(number, this.options, this.metadata)\r\n\t\tif (!result.phone) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tresult.startsAt = startsAt\r\n\t\tresult.endsAt = startsAt + number.length\r\n\t\treturn result\r\n\t}\r\n\r\n\thasNext() {\r\n\t\tif (this.state === 'NOT_READY') {\r\n\t\t\tthis.last_match = this.find()\r\n\t\t\tif (this.last_match) {\r\n\t\t\t\tthis.state = 'READY'\r\n\t\t\t} else {\r\n\t\t\t\tthis.state = 'DONE'\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.state === 'READY'\r\n\t}\r\n\r\n\tnext() {\r\n\t\t// Check the state and find the next match as a side-effect if necessary.\r\n\t\tif (!this.hasNext()) {\r\n\t\t\tthrow new Error('No next element')\r\n\t\t}\r\n\t\t// Don't retain that memory any longer than necessary.\r\n\t\tconst result = this.last_match\r\n\t\tthis.last_match = null\r\n\t\tthis.state = 'NOT_READY'\r\n\t\treturn result\r\n\t}\r\n}", "// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _findPhoneNumbers(text, options, metadata)\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _searchPhoneNumbers(text, options, metadata)\r\n}", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _findPhoneNumbers from '../es6/legacy/findPhoneNumbers.js'\r\n\r\nexport function findPhoneNumbers() {\r\n\treturn withMetadataArgument(_findPhoneNumbers, arguments)\r\n}\r\n", "import withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport { searchPhoneNumbers as _searchPhoneNumbers } from '../es6/legacy/findPhoneNumbers.js'\r\n\r\nexport function searchPhoneNumbers() {\r\n\treturn withMetadataArgument(_searchPhoneNumbers, arguments)\r\n}\r\n", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../metadata.min.json.js'\r\n\r\nimport { PhoneNumberSearch as _PhoneNumberSearch } from '../es6/legacy/findPhoneNumbersInitialImplementation.js'\r\n\r\nexport function PhoneNumberSearch(text, options) {\r\n\t_PhoneNumberSearch.call(this, text, options, metadata)\r\n}\r\n\r\n// Deprecated.\r\nPhoneNumberSearch.prototype = Object.create(_PhoneNumberSearch.prototype, {})\r\nPhoneNumberSearch.prototype.constructor = PhoneNumberSearch\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIe,SAAR,qBAAsC,MAAM,YAAY;AAC9D,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,UAAU;AAChD,OAAK,KAAK,yBAAQ;AAClB,SAAO,KAAK,MAAM,MAAM,IAAI;AAC7B;;;ACLO,SAASA,6BAA4B;AAC3C,SAAO,qBAAqB,2BAA4B,SAAS;AAClE;;;ACFO,SAASC,oBAAmB;AAClC,SAAO,qBAAqB,kBAAmB,SAAS;AACzD;;;ACFO,SAASC,sBAAqB;AACpC,SAAO,qBAAqB,oBAAqB,SAAS;AAC3D;;;ACFO,SAASC,yBAAwB;AACvC,SAAO,qBAAqBA,wBAAwB,SAAS;AAC9D;;;ACFO,SAASC,6BAA4B;AAC3C,SAAO,qBAAqB,2BAA4B,SAAS;AAClE;;;ACFO,SAASC,eAAc;AAC7B,SAAO,qBAAqB,aAAc,SAAS;AACpD;;;ACFO,SAASC,iBAAgB;AAC/B,SAAO,qBAAqB,eAAgB,SAAS;AACtD;;;ACFO,SAASC,0BAAyB;AACxC,SAAO,qBAAqB,wBAAyB,SAAS;AAC/D;;;ACFO,SAASC,4BAA2B;AAC1C,SAAO,qBAAqB,0BAA2B,SAAS;AACjE;;;ACCO,SAASC,oBAAmB,MAAM,SAAS;AACjD,SAAO,mBAAoB,KAAK,MAAM,MAAM,SAAS,yBAAQ;AAC9D;AACAA,oBAAmB,YAAY,OAAO,OAAO,mBAAoB,WAAW,CAAC,CAAC;AAC9EA,oBAAmB,UAAU,cAAcA;;;ACJpC,SAASC,WAAU,SAAS;AAClC,SAAO,UAAW,KAAK,MAAM,SAAS,yBAAQ;AAC/C;AAEAA,WAAU,YAAY,OAAO,OAAO,UAAW,WAAW,CAAC,CAAC;AAC5DA,WAAU,UAAU,cAAcA;;;ACR3B,SAASC,sBAAqB;AACpC,SAAO,qBAAqB,oBAAqB,SAAS;AAC3D;;;ACFO,SAASC,gBAAe;AAC9B,SAAO,qBAAqB,cAAe,SAAS;AACrD;;;ACFO,SAASC,yBAAwB;AACvC,SAAO,qBAAqB,uBAAwB,SAAS;AAC9D;;;ACFO,SAASC,gBAAe;AAC9B,SAAO,qBAAqB,cAAe,SAAS;AACrD;;;ACCO,SAASC,YAAW;AAC1B,SAAO,SAAU,KAAK,MAAM,yBAAQ;AACrC;AAEAA,UAAS,YAAY,OAAO,OAAO,SAAU,WAAW,CAAC,CAAC;AAC1DA,UAAS,UAAU,cAAcA;;;ACR1B,SAASC,oBAAmB;AAClC,SAAO,qBAAqB,kBAAmB,SAAS;AACzD;;;ACFO,SAASC,+BAA8B;AAC7C,SAAO,qBAAqB,6BAA8B,SAAS;AACpE;;;ACCO,SAASC,aAAY,QAAQ;AACnC,SAAO,YAAa,KAAK,MAAM,QAAQ,yBAAQ;AAChD;AACAA,aAAY,YAAY,OAAO,OAAO,YAAa,WAAW,CAAC,CAAC;AAChEA,aAAY,UAAU,cAAcA;;;ACPrB,SAAf,cAAsC;AACrC,MAAA,sBAAoCC,mBAAmBC,SAAD,GAA9CC,OAAR,oBAAQA,MAAMC,UAAd,oBAAcA,SAASC,WAAvB,oBAAuBA;AACvB,SAAOC,MAAaH,MAAMC,SAASC,QAAhB;AACnB;;;ACFM,SAASE,SAAQ;AACvB,SAAO,qBAAqB,aAAQ,SAAS;AAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFe,SAAfC,gBAAuC;AACtC,MAAA,sBAKIC,oBAAmBC,SAAD,GAJrBC,QADD,oBACCA,OACAC,UAFD,oBAECA,QACAC,UAHD,oBAGCA,SACAC,WAJD,oBAICA;AAGD,SAAOC,aAAcJ,OAAOC,SAAQC,SAASC,QAAzB;AACpB;AAGD,SAASL,oBAAmBO,MAC5B;AACC,MAAA,wBAA4CC,MAAMC,UAAUC,MAAMC,KAAKJ,IAA3B,GAA5C,yBAAA,eAAA,uBAAA,CAAA,GAAOK,QAAP,uBAAA,CAAA,GAAcC,QAAd,uBAAA,CAAA,GAAqBC,QAArB,uBAAA,CAAA,GAA4BC,QAA5B,uBAAA,CAAA,GAAmCC,QAAnC,uBAAA,CAAA;AAEA,MAAId;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AAMJ,MAAI,OAAOO,UAAU,UACrB;AAGC,QAAI,OAAOE,UAAU,UACrB;AACCX,MAAAA,UAASW;AAET,UAAIE,OACJ;AACCZ,kBAAWW;AACXV,mBAAWW;MACX,OAED;AACCX,mBAAWU;MACX;AAEDb,cAAQe,MAAML,OAAO;QAAEM,gBAAgBL;QAAOM,UAAU;MAAnC,GAA2Cd,QAAnD;IACb,OAID;AACC,UAAI,OAAOQ,UAAU,UACrB;AACC,cAAM,IAAIO,MAAM,gEAAV;MACN;AAEDjB,MAAAA,UAASU;AAET,UAAIE,OACJ;AACCX,kBAAWU;AACXT,mBAAWU;MACX,OAED;AACCV,mBAAWS;MACX;AAEDZ,cAAQe,MAAML,OAAO;QAAEO,UAAU;MAAZ,GAAoBd,QAA5B;IACb;EACD,WAGQgB,SAAST,KAAD,GACjB;AACCV,YAASU;AACTT,IAAAA,UAASU;AAET,QAAIE,OACJ;AACCX,gBAAWU;AACXT,iBAAWU;IACX,OAED;AACCV,iBAAWS;IACX;EACD;AACI,UAAM,IAAIQ,UAAU,oFAAd;AAGX,MAAInB,YAAW,iBAAiB;AAC/BA,IAAAA,UAAS;EACT,WAAUA,YAAW,YAAY;AACjCA,IAAAA,UAAS;EACT;AAED,SAAO;IACND;IACAC,QAAAA;IACAC;IACAC;EAJM;AAMP;;;ACrGM,SAAS,SAAS;AACxB,SAAO,qBAAqBkB,eAAS,SAAS;AAC/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAe,SAAfC,iBAAwC;AACvC,MAAA,sBAAqCC,oBAAmBC,SAAD,GAA/CC,QAAR,oBAAQA,OAAOC,UAAf,oBAAeA,SAASC,WAAxB,oBAAwBA;AAExB,MAAI,CAACF,MAAMG,OAAO;AACjB;EACA;AACD,SAAOC,cAAeJ,OAAOC,SAASC,QAAjB;AACrB;AAGM,SAASJ,oBAAmBO,MACnC;AACC,MAAA,wBAAqCC,MAAMC,UAAUC,MAAMC,KAAKJ,IAA3B,GAArC,yBAAAK,gBAAA,uBAAA,CAAA,GAAOC,QAAP,uBAAA,CAAA,GAAcC,QAAd,uBAAA,CAAA,GAAqBC,QAArB,uBAAA,CAAA,GAA4BC,QAA5B,uBAAA,CAAA;AAEA,MAAId;AACJ,MAAIC,UAAU,CAAA;AACd,MAAIC;AAIJ,MAAI,OAAOS,UAAU,UACrB;AAIC,QAAI,CAACI,SAASH,KAAD,GACb;AACC,UAAIE,OACJ;AACCb,kBAAUY;AACVX,mBAAWY;MACX,OAED;AACCZ,mBAAWW;MACX;AAOD,UAAIG,oBAAoBL,KAAD,GACvB;AACCX,gBAAQiB,MAAMN,OAAO;UAAEO,gBAAgBN;QAAlB,GAA2BV,QAAnC;MACb,OAED;AACCF,gBAAQ,CAAA;MACR;IACD,OAKD;AACC,UAAIa,OACJ;AACCZ,kBAAUW;AACVV,mBAAWW;MACX,OAED;AACCX,mBAAWU;MACX;AAOD,UAAII,oBAAoBL,KAAD,GACvB;AACCX,gBAAQiB,MAAMN,OAAOQ,QAAWjB,QAAnB;MACb,OAED;AACCF,gBAAQ,CAAA;MACR;IACD;EACD,WAGQe,SAASJ,KAAD,GACjB;AACCX,YAAQW;AAER,QAAIE,OACJ;AACCZ,gBAAUW;AACVV,iBAAWW;IACX,OAED;AACCX,iBAAWU;IACX;EACD;AACI,UAAM,IAAIQ,UAAU,oFAAd;AAEX,SAAO;IACNpB;IACAC;IACAC;EAHM;AAKP;;;AC1GM,SAASmB,iBAAgB;AAC/B,SAAO,qBAAqBA,gBAAgB,SAAS;AACtD;;;ACWe,SAAf,mBAA2C;AAC1C,MAAA,sBAAqCC,oBAAmBC,SAAD,GAA/CC,QAAR,oBAAQA,OAAOC,UAAf,oBAAeA,SAASC,WAAxB,oBAAwBA;AAExB,MAAI,CAACF,MAAMG,SAAS,EAAEF,WAAWA,QAAQG,KAAK;AAC7C,WAAO;EACP;AACD,SAAOC,sBAAkBL,OAAOC,SAASC,QAAjB;AACxB;;;AClBM,SAASI,oBAAmB;AAClC,SAAO,qBAAqB,kBAAmB,SAAS;AACzD;;;ACJe,SAAfC,iBAAwC;AACvC,MAAA,sBAAqCC,oBAAmBC,SAAD,GAA/CC,QAAR,oBAAQA,OAAOC,UAAf,oBAAeA,SAASC,WAAxB,oBAAwBA;AAExB,MAAI,CAACF,MAAMG,OAAO;AACjB,WAAO;EACP;AACD,SAAOC,cAAeJ,OAAOC,SAASC,QAAjB;AACrB;;;ACLM,SAASG,iBAAgB;AAC/B,SAAO,qBAAqBA,gBAAgB,SAAS;AACtD;;;ACDe,SAAf,uBAA+CC,OAAOC,SAASC,SAASC,UAAU;AAGjFD,YAAUA,WAAW,CAAA;AACrB,SAAOF,MAAMC,YAAYA,WAAWG,cAAcJ,OAAOE,SAASC,QAAjB;AACjD;;;AC8Bc,SAAfE,wBAA+CC,QAAQC,SAASC,UAAU;AACzE,MAAI,OAAOF,WAAW,UAAU;AAC/B,UAAM,IAAIG,UAAU,yBAAd;EACN;AACD,MAAI,OAAOF,YAAY,UAAU;AAChC,UAAM,IAAIE,UAAU,0BAAd;EACN;AAMD,MAAIC;AACJ,MAAIC,oBAAoBL,MAAD,GAAU;AAChCI,YAAQE,MAAYN,QAAQ;MAAEO,gBAAgBN;IAAlB,GAA6BC,QAAtC;EACnB,OAAM;AACNE,YAAQ,CAAA;EACR;AACD,SAAOI,uBAAwBJ,OAAOH,SAASQ,QAAWP,QAA5B;AAC9B;;;ACzDM,SAASQ,0BAAyB;AACxC,SAAO,qBAAqBA,yBAAyB,SAAS;AAC/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2BO,IAAMC,4BAA4BC,uBAAuB,SAAD;AAE/D,IAAMC,sCAAsC,IAAIC,OAAO,OAAOC,aAAa,IAA/B;AAC5C,IAAMC,iCAAiC,IAAIF,OAAO,MAAMG,oBAAoB,KAArC;AAQxB,SAAf,iBAAyCC,MAAMC,SAASC,UAAU;AAEjE,MAAID,YAAYE,QAAW;AAC1BF,cAAU,CAAA;EACV;AACD,MAAMG,SAAS,IAAIC,kBAAkBL,MAAMC,SAASC,QAArC;AACf,MAAMI,SAAS,CAAA;AACf,SAAOF,OAAOG,QAAP,GAAkB;AACxBD,WAAOE,KAAKJ,OAAOK,KAAP,CAAZ;EACA;AACD,SAAOH;AACP;AAKM,SAASI,mBAAmBV,MAAMC,SAASC,UAAU;AAE3D,MAAID,YAAYE,QAAW;AAC1BF,cAAU,CAAA;EACV;AACD,MAAMG,SAAS,IAAIC,kBAAkBL,MAAMC,SAASC,QAArC;AACf,SAAA,gBAAA,CAAA,GACES,OAAOC,UADT,WACqB;AACnB,WAAO;MACHH,MAAM,SAAA,OAAM;AACX,YAAIL,OAAOG,QAAP,GAAkB;AACxB,iBAAO;YACNM,MAAM;YACNC,OAAOV,OAAOK,KAAP;UAFD;QAIP;AACD,eAAO;UACNI,MAAM;QADA;MAGJ;IAXE;EAaP,CAfF;AAiBA;AAOD,IAAaR,oBAAb,WAAA;AACC,WAAAA,mBAAYL,MAAMC,SAASC,UAAU;AAAA,oBAAA,MAAAG,kBAAA;AACpC,SAAKL,OAAOA;AAGZ,SAAKC,UAAUA,WAAW,CAAA;AAC1B,SAAKC,WAAWA;AAGhB,SAAKa,QAAQ;AAEb,SAAKC,SAAS,IAAIC,OAAOC,mCAAmC,IAA9C;EACd;AAZF,eAAAb,oBAAA,CAAA;IAAA,KAAA;IAAA,OAcC,SAAA,OAAO;AACN,UAAMc,UAAU,KAAKH,OAAOI,KAAK,KAAKpB,IAAtB;AAChB,UAAI,CAACmB,SAAS;AACb;MACA;AAED,UAAIE,SAASF,QAAQ,CAAD;AACpB,UAAIG,WAAWH,QAAQI;AAEvBF,eAASA,OAAOG,QAAQC,qCAAqC,EAApD;AACTH,kBAAYH,QAAQ,CAAD,EAAIO,SAASL,OAAOK;AAIvCL,eAASA,OAAOG,QAAQG,gCAAgC,EAA/C;AAETN,eAASO,kBAAkBP,MAAD;AAE1B,UAAMQ,SAAS,KAAKC,eAAeT,QAAQC,QAA5B;AACf,UAAIO,QAAQ;AACX,eAAOA;MACP;AAID,aAAO,KAAKE,KAAL;IACP;EAxCF,GAAA;IAAA,KAAA;IAAA,OA0CC,SAAA,eAAeV,QAAQC,UAAU;AAChC,UAAI,CAACU,oBAAoBX,QAAQC,UAAU,KAAKtB,IAAxB,GAA+B;AACtD;MACA;AAMD,UAAI,CAACiC,iBAAiBZ,QAAQC,UAAU,KAAKtB,MAAM,KAAKC,QAAQiC,WAAW,aAAa,OAAnE,GAA6E;AACjG;MACA;AAcD,UAAML,SAASM,MAAMd,QAAQ,KAAKpB,SAAS,KAAKC,QAA5B;AACpB,UAAI,CAAC2B,OAAOO,OAAO;AAClB;MACA;AAEDP,aAAOP,WAAWA;AAClBO,aAAOQ,SAASf,WAAWD,OAAOK;AAClC,aAAOG;IACP;EA3EF,GAAA;IAAA,KAAA;IAAA,OA6EC,SAAA,UAAU;AACT,UAAI,KAAKd,UAAU,aAAa;AAC/B,aAAKuB,aAAa,KAAKP,KAAL;AAClB,YAAI,KAAKO,YAAY;AACpB,eAAKvB,QAAQ;QACb,OAAM;AACN,eAAKA,QAAQ;QACb;MACD;AACD,aAAO,KAAKA,UAAU;IACtB;EAvFF,GAAA;IAAA,KAAA;IAAA,OAyFC,SAAA,OAAO;AAEN,UAAI,CAAC,KAAKR,QAAL,GAAgB;AACpB,cAAM,IAAIgC,MAAM,iBAAV;MACN;AAED,UAAMV,SAAS,KAAKS;AACpB,WAAKA,aAAa;AAClB,WAAKvB,QAAQ;AACb,aAAOc;IACP;EAnGF,CAAA,CAAA;AAAA,SAAAxB;AAAA,EAAA;;;ACpFe,SAAfmC,oBACA;AACC,MAAA,sBAAoCC,mBAAmBC,SAAD,GAA9CC,OAAR,oBAAQA,MAAMC,UAAd,oBAAcA,SAASC,WAAvB,oBAAuBA;AACvB,SAAOC,iBAAkBH,MAAMC,SAASC,QAAhB;AACxB;AAKM,SAASE,sBAChB;AACC,MAAA,uBAAoCN,mBAAmBC,SAAD,GAA9CC,OAAR,qBAAQA,MAAMC,UAAd,qBAAcA,SAASC,WAAvB,qBAAuBA;AACvB,SAAOG,mBAAoBL,MAAMC,SAASC,QAAhB;AAC1B;;;ACfM,SAASI,oBAAmB;AAClC,SAAO,qBAAqBA,mBAAmB,SAAS;AACzD;;;ACFO,SAASC,sBAAqB;AACpC,SAAO,qBAAqBA,qBAAqB,SAAS;AAC3D;;;ACAO,SAASC,mBAAkB,MAAM,SAAS;AAChD,oBAAmB,KAAK,MAAM,MAAM,SAAS,yBAAQ;AACtD;AAGAA,mBAAkB,YAAY,OAAO,OAAO,kBAAmB,WAAW,CAAC,CAAC;AAC5EA,mBAAkB,UAAU,cAAcA;", "names": ["parsePhoneNumberWithError", "parsePhoneNumber", "isValidPhoneNumber", "isPossiblePhoneNumber", "validatePhoneNumberLength", "findNumbers", "searchNumbers", "findPhoneNumbersInText", "searchPhoneNumbersInText", "PhoneNumberMatcher", "AsYouType", "isSupportedCountry", "getCountries", "getCountryCallingCode", "getExtPrefix", "<PERSON><PERSON><PERSON>", "getExampleNumber", "formatIncompletePhoneNumber", "PhoneNumber", "normalizeArguments", "arguments", "text", "options", "metadata", "_parseNumber", "parse", "formatNumber", "normalizeArguments", "arguments", "input", "format", "options", "metadata", "_formatNumber", "args", "Array", "prototype", "slice", "call", "arg_1", "arg_2", "arg_3", "arg_4", "arg_5", "parse", "defaultCountry", "extended", "Error", "isObject", "TypeError", "formatNumber", "getNumberType", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "_getNumberType", "args", "Array", "prototype", "slice", "call", "_slicedToArray", "arg_1", "arg_2", "arg_3", "arg_4", "isObject", "isViablePhoneNumber", "parse", "defaultCountry", "undefined", "TypeError", "getNumberType", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "v2", "_isPossibleNumber", "isPossibleNumber", "isValidNumber", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "_isValidNumber", "isValidNumber", "input", "country", "options", "metadata", "isValidNumber", "isValidNumberForRegion", "number", "country", "metadata", "TypeError", "input", "isViablePhoneNumber", "parseNumber", "defaultCountry", "_isValidNumberForRegion", "undefined", "isValidNumberForRegion", "EXTN_PATTERNS_FOR_PARSING", "createExtensionPattern", "WHITESPACE_IN_THE_BEGINNING_PATTERN", "RegExp", "WHITESPACE", "PUNCTUATION_IN_THE_END_PATTERN", "VALID_PUNCTUATION", "text", "options", "metadata", "undefined", "search", "PhoneNumberSearch", "phones", "hasNext", "push", "next", "searchPhoneNumbers", "Symbol", "iterator", "done", "value", "state", "regexp", "RegExp", "VALID_PHONE_NUMBER_WITH_EXTENSION", "matches", "exec", "number", "startsAt", "index", "replace", "WHITESPACE_IN_THE_BEGINNING_PATTERN", "length", "PUNCTUATION_IN_THE_END_PATTERN", "parsePreCandidate", "result", "parseCandidate", "find", "isValidPreCandidate", "isValidCandidate", "extended", "parse", "phone", "endsAt", "last_match", "Error", "findPhoneNumbers", "normalizeArguments", "arguments", "text", "options", "metadata", "_findPhoneNumbers", "searchPhoneNumbers", "_searchPhoneNumbers", "findPhoneNumbers", "searchPhoneNumbers", "PhoneNumberSearch"]}