{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/r.js"], "sourcesContent": ["function wordObj(words) {\n  var res = {};\n  for (var i = 0; i < words.length; ++i) res[words[i]] = true;\n  return res;\n}\nvar commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\nvar commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\nvar commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\nvar commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\n\nvar atoms = wordObj(commonAtoms);\nvar builtins = wordObj(commonBuiltins);\nvar keywords = wordObj(commonKeywords);\nvar blockkeywords = wordObj(commonBlockKeywords);\nvar opChars = /[+\\-*\\/^<>=!&|~$:]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"0\" && stream.eat(\"x\")) {\n    stream.eatWhile(/[\\da-f]/i);\n    return \"number\";\n  } else if (ch == \".\" && stream.eat(/\\d/)) {\n    stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n    return \"number\";\n  } else if (ch == \"'\" || ch == '\"') {\n    state.tokenize = tokenString(ch);\n    return \"string\";\n  } else if (ch == \"`\") {\n    stream.match(/[^`]+`/);\n    return \"string.special\";\n  } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n    return \"keyword\";\n  } else if (/[a-zA-Z\\.]/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    var word = stream.current();\n    if (atoms.propertyIsEnumerable(word)) return \"atom\";\n    if (keywords.propertyIsEnumerable(word)) {\n      // Block keywords start new blocks, except 'else if', which only starts\n      // one new block for the 'if', no block for the 'else'.\n      if (blockkeywords.propertyIsEnumerable(word) &&\n          !stream.match(/\\s*if(\\s+|$)/, false))\n        curPunc = \"block\";\n      return \"keyword\";\n    }\n    if (builtins.propertyIsEnumerable(word)) return \"builtin\";\n    return \"variable\";\n  } else if (ch == \"%\") {\n    if (stream.skipTo(\"%\")) stream.next();\n    return \"variableName.special\";\n  } else if (\n    (ch == \"<\" && stream.eat(\"-\")) ||\n      (ch == \"<\" && stream.match(\"<-\")) ||\n      (ch == \"-\" && stream.match(/>>?/))\n  ) {\n    return \"operator\";\n  } else if (ch == \"=\" && state.ctx.argList) {\n    return \"operator\";\n  } else if (opChars.test(ch)) {\n    if (ch == \"$\") return \"operator\";\n    stream.eatWhile(opChars);\n    return \"operator\";\n  } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n    curPunc = ch;\n    if (ch == \";\") return \"punctuation\";\n    return null;\n  } else {\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    if (stream.eat(\"\\\\\")) {\n      var ch = stream.next();\n      if (ch == \"x\") stream.match(/^[a-f0-9]{2}/i);\n      else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\")) stream.next();\n      else if (ch == \"u\") stream.match(/^[a-f0-9]{4}/i);\n      else if (ch == \"U\") stream.match(/^[a-f0-9]{8}/i);\n      else if (/[0-7]/.test(ch)) stream.match(/^[0-7]{1,2}/);\n      return \"string.special\";\n    } else {\n      var next;\n      while ((next = stream.next()) != null) {\n        if (next == quote) { state.tokenize = tokenBase; break; }\n        if (next == \"\\\\\") { stream.backUp(1); break; }\n      }\n      return \"string\";\n    }\n  };\n}\n\nvar ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4\n\nfunction push(state, type, stream) {\n  state.ctx = {type: type,\n               indent: state.indent,\n               flags: 0,\n               column: stream.column(),\n               prev: state.ctx};\n}\nfunction setFlag(state, flag) {\n  var ctx = state.ctx\n  state.ctx = {type: ctx.type,\n               indent: ctx.indent,\n               flags: ctx.flags | flag,\n               column: ctx.column,\n               prev: ctx.prev}\n}\nfunction pop(state) {\n  state.indent = state.ctx.indent;\n  state.ctx = state.ctx.prev;\n}\n\nexport const r = {\n  name: \"r\",\n  startState: function(indentUnit) {\n    return {tokenize: tokenBase,\n            ctx: {type: \"top\",\n                  indent: -indentUnit,\n                  flags: ALIGN_NO},\n            indent: 0,\n            afterIdent: false};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if ((state.ctx.flags & 3) == 0) state.ctx.flags |= ALIGN_NO\n      if (state.ctx.flags & BRACELESS) pop(state)\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0) setFlag(state, ALIGN_YES)\n\n    if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\") pop(state);\n    if (curPunc == \"{\") push(state, \"}\", stream);\n    else if (curPunc == \"(\") {\n      push(state, \")\", stream);\n      if (state.afterIdent) state.ctx.argList = true;\n    }\n    else if (curPunc == \"[\") push(state, \"]\", stream);\n    else if (curPunc == \"block\") push(state, \"block\", stream);\n    else if (curPunc == state.ctx.type) pop(state);\n    else if (state.ctx.type == \"block\" && style != \"comment\") setFlag(state, BRACELESS)\n    state.afterIdent = style == \"variable\" || style == \"keyword\";\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx,\n        closing = firstChar == ctx.type;\n    if (ctx.flags & BRACELESS) ctx = ctx.prev\n    if (ctx.type == \"block\") return ctx.indent + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.flags & ALIGN_YES) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    wordChars: \".\",\n    commentTokens: {line: \"#\"},\n    autocomplete: commonAtoms.concat(commonBuiltins, commonKeywords)\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,QAAQ,OAAO;AACtB,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAAG,QAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AACA,IAAI,cAAc,CAAC,QAAQ,MAAM,OAAO,OAAO,eAAe,YAAY,eAAe,iBAAiB,QAAQ,OAAO;AACzH,IAAI,iBAAiB,CAAC,QAAQ,SAAS,UAAU,QAAQ,UAAU,QAAQ,SAAS,SAAS;AAC7F,IAAI,iBAAiB,CAAC,MAAM,QAAQ,UAAU,SAAS,YAAY,OAAO,MAAM,QAAQ,OAAO;AAC/F,IAAI,sBAAsB,CAAC,MAAM,QAAQ,UAAU,SAAS,YAAY,KAAK;AAE7E,IAAI,QAAQ,QAAQ,WAAW;AAC/B,IAAI,WAAW,QAAQ,cAAc;AACrC,IAAI,WAAW,QAAQ,cAAc;AACrC,IAAI,gBAAgB,QAAQ,mBAAmB;AAC/C,IAAI,UAAU;AACd,IAAI;AAEJ,SAAS,UAAU,QAAQ,OAAO;AAChC,YAAU;AACV,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,KAAK;AACb,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,WAAO,SAAS,UAAU;AAC1B,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACxC,WAAO,MAAM,oBAAoB;AACjC,WAAO;AAAA,EACT,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,WAAO,MAAM,+BAA+B;AAC5C,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO,MAAM,QAAQ;AACrB,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,OAAO,MAAM,cAAc,GAAG;AACpD,WAAO;AAAA,EACT,WAAW,aAAa,KAAK,EAAE,GAAG;AAChC,WAAO,SAAS,QAAQ;AACxB,QAAI,OAAO,OAAO,QAAQ;AAC1B,QAAI,MAAM,qBAAqB,IAAI;AAAG,aAAO;AAC7C,QAAI,SAAS,qBAAqB,IAAI,GAAG;AAGvC,UAAI,cAAc,qBAAqB,IAAI,KACvC,CAAC,OAAO,MAAM,gBAAgB,KAAK;AACrC,kBAAU;AACZ,aAAO;AAAA,IACT;AACA,QAAI,SAAS,qBAAqB,IAAI;AAAG,aAAO;AAChD,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,OAAO,GAAG;AAAG,aAAO,KAAK;AACpC,WAAO;AAAA,EACT,WACG,MAAM,OAAO,OAAO,IAAI,GAAG,KACzB,MAAM,OAAO,OAAO,MAAM,IAAI,KAC9B,MAAM,OAAO,OAAO,MAAM,KAAK,GAClC;AACA,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,MAAM,IAAI,SAAS;AACzC,WAAO;AAAA,EACT,WAAW,QAAQ,KAAK,EAAE,GAAG;AAC3B,QAAI,MAAM;AAAK,aAAO;AACtB,WAAO,SAAS,OAAO;AACvB,WAAO;AAAA,EACT,WAAW,gBAAgB,KAAK,EAAE,GAAG;AACnC,cAAU;AACV,QAAI,MAAM;AAAK,aAAO;AACtB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,IAAI,GAAG;AACpB,UAAI,KAAK,OAAO,KAAK;AACrB,UAAI,MAAM;AAAK,eAAO,MAAM,eAAe;AAAA,gBACjC,MAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,GAAG,KAAK,OAAO,OAAO,GAAG;AAAG,eAAO,KAAK;AAAA,eAC/E,MAAM;AAAK,eAAO,MAAM,eAAe;AAAA,eACvC,MAAM;AAAK,eAAO,MAAM,eAAe;AAAA,eACvC,QAAQ,KAAK,EAAE;AAAG,eAAO,MAAM,aAAa;AACrD,aAAO;AAAA,IACT,OAAO;AACL,UAAI;AACJ,cAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,YAAI,QAAQ,OAAO;AAAE,gBAAM,WAAW;AAAW;AAAA,QAAO;AACxD,YAAI,QAAQ,MAAM;AAAE,iBAAO,OAAO,CAAC;AAAG;AAAA,QAAO;AAAA,MAC/C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,YAAY;AAAhB,IAAmB,WAAW;AAA9B,IAAiC,YAAY;AAE7C,SAAS,KAAK,OAAO,MAAM,QAAQ;AACjC,QAAM,MAAM;AAAA,IAAC;AAAA,IACA,QAAQ,MAAM;AAAA,IACd,OAAO;AAAA,IACP,QAAQ,OAAO,OAAO;AAAA,IACtB,MAAM,MAAM;AAAA,EAAG;AAC9B;AACA,SAAS,QAAQ,OAAO,MAAM;AAC5B,MAAI,MAAM,MAAM;AAChB,QAAM,MAAM;AAAA,IAAC,MAAM,IAAI;AAAA,IACV,QAAQ,IAAI;AAAA,IACZ,OAAO,IAAI,QAAQ;AAAA,IACnB,QAAQ,IAAI;AAAA,IACZ,MAAM,IAAI;AAAA,EAAI;AAC7B;AACA,SAAS,IAAI,OAAO;AAClB,QAAM,SAAS,MAAM,IAAI;AACzB,QAAM,MAAM,MAAM,IAAI;AACxB;AAEO,IAAM,IAAI;AAAA,EACf,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MAAC,UAAU;AAAA,MACV,KAAK;AAAA,QAAC,MAAM;AAAA,QACN,QAAQ,CAAC;AAAA,QACT,OAAO;AAAA,MAAQ;AAAA,MACrB,QAAQ;AAAA,MACR,YAAY;AAAA,IAAK;AAAA,EAC3B;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,WAAK,MAAM,IAAI,QAAQ,MAAM;AAAG,cAAM,IAAI,SAAS;AACnD,UAAI,MAAM,IAAI,QAAQ;AAAW,YAAI,KAAK;AAC1C,YAAM,SAAS,OAAO,YAAY;AAAA,IACpC;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,SAAS,cAAc,MAAM,IAAI,QAAQ,aAAa;AAAG,cAAQ,OAAO,SAAS;AAErF,SAAK,WAAW,OAAO,WAAW,OAAO,WAAW,QAAQ,MAAM,IAAI,QAAQ;AAAS,UAAI,KAAK;AAChG,QAAI,WAAW;AAAK,WAAK,OAAO,KAAK,MAAM;AAAA,aAClC,WAAW,KAAK;AACvB,WAAK,OAAO,KAAK,MAAM;AACvB,UAAI,MAAM;AAAY,cAAM,IAAI,UAAU;AAAA,IAC5C,WACS,WAAW;AAAK,WAAK,OAAO,KAAK,MAAM;AAAA,aACvC,WAAW;AAAS,WAAK,OAAO,SAAS,MAAM;AAAA,aAC/C,WAAW,MAAM,IAAI;AAAM,UAAI,KAAK;AAAA,aACpC,MAAM,IAAI,QAAQ,WAAW,SAAS;AAAW,cAAQ,OAAO,SAAS;AAClF,UAAM,aAAa,SAAS,cAAc,SAAS;AACnD,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,YAAY;AAAW,aAAO;AACxC,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,MAAM,MAAM,KAC1D,UAAU,aAAa,IAAI;AAC/B,QAAI,IAAI,QAAQ;AAAW,YAAM,IAAI;AACrC,QAAI,IAAI,QAAQ;AAAS,aAAO,IAAI,UAAU,aAAa,MAAM,IAAI,GAAG;AAAA,aAC/D,IAAI,QAAQ;AAAW,aAAO,IAAI,UAAU,UAAU,IAAI;AAAA;AAC9D,aAAO,IAAI,UAAU,UAAU,IAAI,GAAG;AAAA,EAC7C;AAAA,EAEA,cAAc;AAAA,IACZ,WAAW;AAAA,IACX,eAAe,EAAC,MAAM,IAAG;AAAA,IACzB,cAAc,YAAY,OAAO,gBAAgB,cAAc;AAAA,EACjE;AACF;", "names": []}