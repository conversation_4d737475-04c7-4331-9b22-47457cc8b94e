{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/spreadsheet.js"], "sourcesContent": ["export const spreadsheet = {\n  name: \"spreadsheet\",\n\n  startState: function () {\n    return {\n      stringType: null,\n      stack: []\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(\"string\");\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case \"string\":\n      while (state.stack[0] === \"string\" && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return \"string\";\n\n    case \"characterClass\":\n      while (state.stack[0] === \"characterClass\" && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match(/^\\\\./)))\n          state.stack.shift();\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(\"characterClass\");\n      return \"bracket\";\n    case \":\":\n      stream.next();\n      return \"operator\";\n    case \"\\\\\":\n      if (stream.match(/\\\\[a-z]+/)) return \"string.special\";\n      else {\n        stream.next();\n        return \"atom\";\n      }\n    case \".\":\n    case \",\":\n    case \";\":\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n    case \"<\":\n    case \"/\":\n    case \"=\":\n      stream.next();\n      return \"atom\";\n    case \"$\":\n      stream.next();\n      return \"builtin\";\n    }\n\n    if (stream.match(/\\d+/)) {\n      if (stream.match(/^\\w+/)) return \"error\";\n      return \"number\";\n    } else if (stream.match(/^[a-zA-Z_]\\w*/)) {\n      if (stream.match(/(?=[\\(.])/, false)) return \"keyword\";\n      return \"variable\";\n    } else if ([\"[\", \"]\", \"(\", \")\", \"{\", \"}\"].indexOf(peek) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "mappings": ";;;AAAO,IAAM,cAAc;AAAA,EACzB,MAAM;AAAA,EAEN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,OAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,CAAC;AAAQ;AAGb,QAAI,MAAM,MAAM,WAAW,GAAG;AAE5B,UAAK,OAAO,KAAK,KAAK,OAAS,OAAO,KAAK,KAAK,KAAM;AACpD,cAAM,aAAa,OAAO,KAAK;AAC/B,eAAO,KAAK;AACZ,cAAM,MAAM,QAAQ,QAAQ;AAAA,MAC9B;AAAA,IACF;AAIA,YAAQ,MAAM,MAAM,CAAC,GAAG;AAAA,MACxB,KAAK;AACH,eAAO,MAAM,MAAM,CAAC,MAAM,YAAY,CAAC,OAAO,IAAI,GAAG;AACnD,cAAI,OAAO,KAAK,MAAM,MAAM,YAAY;AACtC,mBAAO,KAAK;AACZ,kBAAM,MAAM,MAAM;AAAA,UACpB,WAAW,OAAO,KAAK,MAAM,MAAM;AACjC,mBAAO,KAAK;AACZ,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,mBAAO,MAAM,cAAc;AAAA,UAC7B;AAAA,QACF;AACA,eAAO;AAAA,MAET,KAAK;AACH,eAAO,MAAM,MAAM,CAAC,MAAM,oBAAoB,CAAC,OAAO,IAAI,GAAG;AAC3D,cAAI,EAAE,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM,MAAM;AACpD,kBAAM,MAAM,MAAM;AAAA,QACtB;AACA,eAAO;AAAA,IACT;AAEA,QAAI,OAAO,OAAO,KAAK;AAGvB,YAAQ,MAAM;AAAA,MACd,KAAK;AACH,eAAO,KAAK;AACZ,cAAM,MAAM,QAAQ,gBAAgB;AACpC,eAAO;AAAA,MACT,KAAK;AACH,eAAO,KAAK;AACZ,eAAO;AAAA,MACT,KAAK;AACH,YAAI,OAAO,MAAM,UAAU;AAAG,iBAAO;AAAA,aAChC;AACH,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK;AACZ,eAAO;AAAA,MACT,KAAK;AACH,eAAO,KAAK;AACZ,eAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,UAAI,OAAO,MAAM,MAAM;AAAG,eAAO;AACjC,aAAO;AAAA,IACT,WAAW,OAAO,MAAM,eAAe,GAAG;AACxC,UAAI,OAAO,MAAM,aAAa,KAAK;AAAG,eAAO;AAC7C,aAAO;AAAA,IACT,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,IAAI,KAAK,IAAI;AAC7D,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAW,CAAC,OAAO,SAAS,GAAG;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACF;", "names": []}