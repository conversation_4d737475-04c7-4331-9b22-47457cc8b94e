{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/velocity.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = parseWords(\"#end #else #break #stop #[[ #]] \" +\n                          \"#{end} #{else} #{break} #{stop}\");\nvar functions = parseWords(\"#if #elseif #foreach #set #include #parse #macro #define #evaluate \" +\n                           \"#{if} #{elseif} #{foreach} #{set} #{include} #{parse} #{macro} #{define} #{evaluate}\");\nvar specials = parseWords(\"$foreach.count $foreach.hasNext $foreach.first $foreach.last $foreach.topmost $foreach.parent.count $foreach.parent.hasNext $foreach.parent.first $foreach.parent.last $foreach.parent $velocityCount $!bodyContent $bodyContent\");\nvar isOperatorChar = /[+\\-*&%=<>!?:\\/|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  // start of unparsed string?\n  if ((ch == \"'\") && !state.inString && state.inParams) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenString(ch));\n  }\n  // start of parsed string?\n  else if ((ch == '\"')) {\n    state.lastTokenWasBuiltin = false;\n    if (state.inString) {\n      state.inString = false;\n      return \"string\";\n    }\n    else if (state.inParams)\n      return chain(stream, state, tokenString(ch));\n  }\n  // is it one of the special signs []{}().,;? Separator?\n  else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams)\n      state.inParams = true;\n    else if (ch == \")\") {\n      state.inParams = false;\n      state.lastTokenWasBuiltin = true;\n    }\n    return null;\n  }\n  // start of a number value?\n  else if (/\\d/.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  // multi line comment?\n  else if (ch == \"#\" && stream.eat(\"*\")) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenComment);\n  }\n  // unparsed content?\n  else if (ch == \"#\" && stream.match(/ *\\[ *\\[/)) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenUnparsed);\n  }\n  // single line comment?\n  else if (ch == \"#\" && stream.eat(\"#\")) {\n    state.lastTokenWasBuiltin = false;\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // variable?\n  else if (ch == \"$\") {\n    stream.eat(\"!\");\n    stream.eatWhile(/[\\w\\d\\$_\\.{}-]/);\n    // is it one of the specials?\n    if (specials && specials.propertyIsEnumerable(stream.current())) {\n      return \"keyword\";\n    }\n    else {\n      state.lastTokenWasBuiltin = true;\n      state.beforeParams = true;\n      return \"builtin\";\n    }\n  }\n  // is it a operator?\n  else if (isOperatorChar.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  else {\n    // get the whole word\n    stream.eatWhile(/[\\w\\$_{}@]/);\n    var word = stream.current();\n    // is it one of the listed keywords?\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    // is it one of the listed functions?\n    if (functions && functions.propertyIsEnumerable(word) ||\n        (stream.current().match(/^#@?[a-z0-9_]+ *$/i) && stream.peek()==\"(\") &&\n        !(functions && functions.propertyIsEnumerable(word.toLowerCase()))) {\n      state.beforeParams = true;\n      state.lastTokenWasBuiltin = false;\n      return \"keyword\";\n    }\n    if (state.inString) {\n      state.lastTokenWasBuiltin = false;\n      return \"string\";\n    }\n    if (stream.pos > word.length && stream.string.charAt(stream.pos-word.length-1)==\".\" && state.lastTokenWasBuiltin)\n      return \"builtin\";\n    // default: just a \"word\"\n    state.lastTokenWasBuiltin = false;\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if ((next == quote) && !escaped) {\n        end = true;\n        break;\n      }\n      if (quote=='\"' && stream.peek() == '$' && !escaped) {\n        state.inString = true;\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\n// Interface\n\nexport const velocity = {\n  name: \"velocity\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false,\n      inString: false,\n      lastTokenWasBuiltin: false\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"##\", block: {open: \"#*\", close: \"*#\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;AACvB,MAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAAG,QAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,WAAW,WAAW,iEACiC;AAC3D,IAAI,YAAY,WAAW,yJACsF;AACjH,IAAI,WAAW,WAAW,kOAAkO;AAC5P,IAAI,iBAAiB;AAErB,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AACA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,eAAe,MAAM;AACzB,QAAM,eAAe;AACrB,MAAI,KAAK,OAAO,KAAK;AAErB,MAAK,MAAM,OAAQ,CAAC,MAAM,YAAY,MAAM,UAAU;AACpD,UAAM,sBAAsB;AAC5B,WAAO,MAAM,QAAQ,OAAO,YAAY,EAAE,CAAC;AAAA,EAC7C,WAEU,MAAM,KAAM;AACpB,UAAM,sBAAsB;AAC5B,QAAI,MAAM,UAAU;AAClB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,WACS,MAAM;AACb,aAAO,MAAM,QAAQ,OAAO,YAAY,EAAE,CAAC;AAAA,EAC/C,WAES,mBAAmB,KAAK,EAAE,GAAG;AACpC,QAAI,MAAM,OAAO;AACf,YAAM,WAAW;AAAA,aACV,MAAM,KAAK;AAClB,YAAM,WAAW;AACjB,YAAM,sBAAsB;AAAA,IAC9B;AACA,WAAO;AAAA,EACT,WAES,KAAK,KAAK,EAAE,GAAG;AACtB,UAAM,sBAAsB;AAC5B,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT,WAES,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACrC,UAAM,sBAAsB;AAC5B,WAAO,MAAM,QAAQ,OAAO,YAAY;AAAA,EAC1C,WAES,MAAM,OAAO,OAAO,MAAM,UAAU,GAAG;AAC9C,UAAM,sBAAsB;AAC5B,WAAO,MAAM,QAAQ,OAAO,aAAa;AAAA,EAC3C,WAES,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACrC,UAAM,sBAAsB;AAC5B,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAES,MAAM,KAAK;AAClB,WAAO,IAAI,GAAG;AACd,WAAO,SAAS,gBAAgB;AAEhC,QAAI,YAAY,SAAS,qBAAqB,OAAO,QAAQ,CAAC,GAAG;AAC/D,aAAO;AAAA,IACT,OACK;AACH,YAAM,sBAAsB;AAC5B,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AAAA,EACF,WAES,eAAe,KAAK,EAAE,GAAG;AAChC,UAAM,sBAAsB;AAC5B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT,OACK;AAEH,WAAO,SAAS,YAAY;AAC5B,QAAI,OAAO,OAAO,QAAQ;AAE1B,QAAI,YAAY,SAAS,qBAAqB,IAAI;AAChD,aAAO;AAET,QAAI,aAAa,UAAU,qBAAqB,IAAI,KAC/C,OAAO,QAAQ,EAAE,MAAM,oBAAoB,KAAK,OAAO,KAAK,KAAG,OAChE,EAAE,aAAa,UAAU,qBAAqB,KAAK,YAAY,CAAC,IAAI;AACtE,YAAM,eAAe;AACrB,YAAM,sBAAsB;AAC5B,aAAO;AAAA,IACT;AACA,QAAI,MAAM,UAAU;AAClB,YAAM,sBAAsB;AAC5B,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,KAAK,UAAU,OAAO,OAAO,OAAO,OAAO,MAAI,KAAK,SAAO,CAAC,KAAG,OAAO,MAAM;AAC3F,aAAO;AAET,UAAM,sBAAsB;AAC5B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAK,QAAQ,SAAU,CAAC,SAAS;AAC/B,cAAM;AACN;AAAA,MACF;AACA,UAAI,SAAO,OAAO,OAAO,KAAK,KAAK,OAAO,CAAC,SAAS;AAClD,cAAM,WAAW;AACjB,cAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI;AAAK,YAAM,WAAW;AAC1B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,GAAG;AAClB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,YAAY,GAAG;AAC9B,YAAM,WAAW;AACjB;AAAA,IACF;AACA,QAAI,MAAM;AACR;AAAA,aACO,MAAM;AACb,iBAAW;AAAA,EACf;AACA,SAAO;AACT;AAGO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": []}