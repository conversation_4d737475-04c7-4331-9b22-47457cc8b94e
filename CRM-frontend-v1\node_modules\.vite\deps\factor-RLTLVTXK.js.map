{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/factor.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\n\nexport const factor = simpleMode({\n    start: [\n      // comments\n      {regex: /#?!.*/, token: \"comment\"},\n      // strings \"\"\", multiline --> state\n      {regex: /\"\"\"/, token: \"string\", next: \"string3\"},\n      {regex: /(STRING:)(\\s)/, token: [\"keyword\", null], next: \"string2\"},\n      {regex: /\\S*?\"/, token: \"string\", next: \"string\"},\n      // numbers: dec, hex, unicode, bin, fractional, complex\n      {regex: /(?:0x[\\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\\-?\\d+.?\\d*)(?=\\s)/, token: \"number\"},\n      //{regex: /[+-]?/} //fractional\n      // definition: defining word, defined word, etc\n      {regex: /((?:GENERIC)|\\:?\\:)(\\s+)(\\S+)(\\s+)(\\()/, token: [\"keyword\", null, \"def\", null, \"bracket\"], next: \"stack\"},\n      // method definition: defining word, type, defined word, etc\n      {regex: /(M\\:)(\\s+)(\\S+)(\\s+)(\\S+)/, token: [\"keyword\", null, \"def\", null, \"tag\"]},\n      // vocabulary using --> state\n      {regex: /USING\\:/, token: \"keyword\", next: \"vocabulary\"},\n      // vocabulary definition/use\n      {regex: /(USE\\:|IN\\:)(\\s+)(\\S+)(?=\\s|$)/, token: [\"keyword\", null, \"tag\"]},\n      // definition: a defining word, defined word\n      {regex: /(\\S+\\:)(\\s+)(\\S+)(?=\\s|$)/, token: [\"keyword\", null, \"def\"]},\n      // \"keywords\", incl. ; t f . [ ] { } defining words\n      {regex: /(?:;|\\\\|t|f|if|loop|while|until|do|PRIVATE>|<PRIVATE|\\.|\\S*\\[|\\]|\\S*\\{|\\})(?=\\s|$)/, token: \"keyword\"},\n      // <constructors> and the like\n      {regex: /\\S+[\\)>\\.\\*\\?]+(?=\\s|$)/, token: \"builtin\"},\n      {regex: /[\\)><]+\\S+(?=\\s|$)/, token: \"builtin\"},\n      // operators\n      {regex: /(?:[\\+\\-\\=\\/\\*<>])(?=\\s|$)/, token: \"keyword\"},\n      // any id (?)\n      {regex: /\\S+/, token: \"variable\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    vocabulary: [\n      {regex: /;/, token: \"keyword\", next: \"start\"},\n      {regex: /\\S+/, token: \"tag\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    string: [\n      {regex: /(?:[^\\\\]|\\\\.)*?\"/, token: \"string\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    string2: [\n      {regex: /^;/, token: \"keyword\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    string3: [\n      {regex: /(?:[^\\\\]|\\\\.)*?\"\"\"/, token: \"string\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    stack: [\n      {regex: /\\)/, token: \"bracket\", next: \"start\"},\n      {regex: /--/, token: \"bracket\"},\n      {regex: /\\S+/, token: \"meta\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    languageData: {\n      name: \"factor\",\n      dontIndentStates: [\"start\", \"vocabulary\", \"string\", \"string3\", \"stack\"],\n      commentTokens: {line: \"!\"}\n    }\n  });\n"], "mappings": ";;;;;;AAEO,IAAM,SAAS,WAAW;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEL,EAAC,OAAO,SAAS,OAAO,UAAS;AAAA;AAAA,IAEjC,EAAC,OAAO,OAAO,OAAO,UAAU,MAAM,UAAS;AAAA,IAC/C,EAAC,OAAO,iBAAiB,OAAO,CAAC,WAAW,IAAI,GAAG,MAAM,UAAS;AAAA,IAClE,EAAC,OAAO,SAAS,OAAO,UAAU,MAAM,SAAQ;AAAA;AAAA,IAEhD,EAAC,OAAO,mEAAmE,OAAO,SAAQ;AAAA;AAAA;AAAA,IAG1F,EAAC,OAAO,0CAA0C,OAAO,CAAC,WAAW,MAAM,OAAO,MAAM,SAAS,GAAG,MAAM,QAAO;AAAA;AAAA,IAEjH,EAAC,OAAO,6BAA6B,OAAO,CAAC,WAAW,MAAM,OAAO,MAAM,KAAK,EAAC;AAAA;AAAA,IAEjF,EAAC,OAAO,WAAW,OAAO,WAAW,MAAM,aAAY;AAAA;AAAA,IAEvD,EAAC,OAAO,kCAAkC,OAAO,CAAC,WAAW,MAAM,KAAK,EAAC;AAAA;AAAA,IAEzE,EAAC,OAAO,6BAA6B,OAAO,CAAC,WAAW,MAAM,KAAK,EAAC;AAAA;AAAA,IAEpE,EAAC,OAAO,sFAAsF,OAAO,UAAS;AAAA;AAAA,IAE9G,EAAC,OAAO,2BAA2B,OAAO,UAAS;AAAA,IACnD,EAAC,OAAO,sBAAsB,OAAO,UAAS;AAAA;AAAA,IAE9C,EAAC,OAAO,8BAA8B,OAAO,UAAS;AAAA;AAAA,IAEtD,EAAC,OAAO,OAAO,OAAO,WAAU;AAAA,IAChC,EAAC,OAAO,SAAS,OAAO,KAAI;AAAA,EAC9B;AAAA,EACA,YAAY;AAAA,IACV,EAAC,OAAO,KAAK,OAAO,WAAW,MAAM,QAAO;AAAA,IAC5C,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,IAC3B,EAAC,OAAO,SAAS,OAAO,KAAI;AAAA,EAC9B;AAAA,EACA,QAAQ;AAAA,IACN,EAAC,OAAO,oBAAoB,OAAO,UAAU,MAAM,QAAO;AAAA,IAC1D,EAAC,OAAO,MAAM,OAAO,SAAQ;AAAA,EAC/B;AAAA,EACA,SAAS;AAAA,IACP,EAAC,OAAO,MAAM,OAAO,WAAW,MAAM,QAAO;AAAA,IAC7C,EAAC,OAAO,MAAM,OAAO,SAAQ;AAAA,EAC/B;AAAA,EACA,SAAS;AAAA,IACP,EAAC,OAAO,sBAAsB,OAAO,UAAU,MAAM,QAAO;AAAA,IAC5D,EAAC,OAAO,MAAM,OAAO,SAAQ;AAAA,EAC/B;AAAA,EACA,OAAO;AAAA,IACL,EAAC,OAAO,MAAM,OAAO,WAAW,MAAM,QAAO;AAAA,IAC7C,EAAC,OAAO,MAAM,OAAO,UAAS;AAAA,IAC9B,EAAC,OAAO,OAAO,OAAO,OAAM;AAAA,IAC5B,EAAC,OAAO,SAAS,OAAO,KAAI;AAAA,EAC9B;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,kBAAkB,CAAC,SAAS,cAAc,UAAU,WAAW,OAAO;AAAA,IACtE,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF,CAAC;", "names": []}