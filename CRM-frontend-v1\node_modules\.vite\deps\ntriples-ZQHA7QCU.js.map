{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/ntriples.js"], "sourcesContent": ["var Location = {\n  PRE_SUBJECT         : 0,\n  WRITING_SUB_URI     : 1,\n  WRITING_BNODE_URI   : 2,\n  PRE_PRED            : 3,\n  WRITING_PRED_URI    : 4,\n  PRE_OBJ             : 5,\n  WRITING_OBJ_URI     : 6,\n  WRITING_OBJ_BNODE   : 7,\n  WRITING_OBJ_LITERAL : 8,\n  WRITING_LIT_LANG    : 9,\n  WRITING_LIT_TYPE    : 10,\n  POST_OBJ            : 11,\n  ERROR               : 12\n};\nfunction transitState(currState, c) {\n  var currLocation = currState.location;\n  var ret;\n\n  // Opening.\n  if     (currLocation == Location.PRE_SUBJECT && c == '<') ret = Location.WRITING_SUB_URI;\n  else if(currLocation == Location.PRE_SUBJECT && c == '_') ret = Location.WRITING_BNODE_URI;\n  else if(currLocation == Location.PRE_PRED    && c == '<') ret = Location.WRITING_PRED_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '<') ret = Location.WRITING_OBJ_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '_') ret = Location.WRITING_OBJ_BNODE;\n  else if(currLocation == Location.PRE_OBJ     && c == '\"') ret = Location.WRITING_OBJ_LITERAL;\n\n  // Closing.\n  else if(currLocation == Location.WRITING_SUB_URI     && c == '>') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_BNODE_URI   && c == ' ') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_PRED_URI    && c == '>') ret = Location.PRE_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_URI     && c == '>') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_BNODE   && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '\"') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_LANG && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_TYPE && c == '>') ret = Location.POST_OBJ;\n\n  // Closing typed and language literal.\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '@') ret = Location.WRITING_LIT_LANG;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '^') ret = Location.WRITING_LIT_TYPE;\n\n  // Spaces.\n  else if( c == ' ' &&\n           (\n             currLocation == Location.PRE_SUBJECT ||\n               currLocation == Location.PRE_PRED    ||\n               currLocation == Location.PRE_OBJ     ||\n               currLocation == Location.POST_OBJ\n           )\n         ) ret = currLocation;\n\n  // Reset.\n  else if(currLocation == Location.POST_OBJ && c == '.') ret = Location.PRE_SUBJECT;\n\n  // Error\n  else ret = Location.ERROR;\n\n  currState.location=ret;\n}\n\nexport const ntriples = {\n  name: \"ntriples\",\n  startState: function() {\n    return {\n      location : Location.PRE_SUBJECT,\n      uris     : [],\n      anchors  : [],\n      bnodes   : [],\n      langs    : [],\n      types    : []\n    };\n  },\n  token: function(stream, state) {\n    var ch = stream.next();\n    if(ch == '<') {\n      transitState(state, ch);\n      var parsedURI = '';\n      stream.eatWhile( function(c) { if( c != '#' && c != '>' ) { parsedURI += c; return true; } return false;} );\n      state.uris.push(parsedURI);\n      if( stream.match('#', false) ) return 'variable';\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '#') {\n      var parsedAnchor = '';\n      stream.eatWhile(function(c) { if(c != '>' && c != ' ') { parsedAnchor+= c; return true; } return false;});\n      state.anchors.push(parsedAnchor);\n      return 'url';\n    }\n    if(ch == '>') {\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '_') {\n      transitState(state, ch);\n      var parsedBNode = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedBNode += c; return true; } return false;});\n      state.bnodes.push(parsedBNode);\n      stream.next();\n      transitState(state, ' ');\n      return 'builtin';\n    }\n    if(ch == '\"') {\n      transitState(state, ch);\n      stream.eatWhile( function(c) { return c != '\"'; } );\n      stream.next();\n      if( stream.peek() != '@' && stream.peek() != '^' ) {\n        transitState(state, '\"');\n      }\n      return 'string';\n    }\n    if( ch == '@' ) {\n      transitState(state, '@');\n      var parsedLang = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedLang += c; return true; } return false;});\n      state.langs.push(parsedLang);\n      stream.next();\n      transitState(state, ' ');\n      return 'string.special';\n    }\n    if( ch == '^' ) {\n      stream.next();\n      transitState(state, '^');\n      var parsedType = '';\n      stream.eatWhile(function(c) { if( c != '>' ) { parsedType += c; return true; } return false;} );\n      state.types.push(parsedType);\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if( ch == ' ' ) {\n      transitState(state, ch);\n    }\n    if( ch == '.' ) {\n      transitState(state, ch);\n    }\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,WAAW;AAAA,EACb,aAAsB;AAAA,EACtB,iBAAsB;AAAA,EACtB,mBAAsB;AAAA,EACtB,UAAsB;AAAA,EACtB,kBAAsB;AAAA,EACtB,SAAsB;AAAA,EACtB,iBAAsB;AAAA,EACtB,mBAAsB;AAAA,EACtB,qBAAsB;AAAA,EACtB,kBAAsB;AAAA,EACtB,kBAAsB;AAAA,EACtB,UAAsB;AAAA,EACtB,OAAsB;AACxB;AACA,SAAS,aAAa,WAAW,GAAG;AAClC,MAAI,eAAe,UAAU;AAC7B,MAAI;AAGJ,MAAQ,gBAAgB,SAAS,eAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WACjE,gBAAgB,SAAS,eAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WACjE,gBAAgB,SAAS,YAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WACjE,gBAAgB,SAAS,WAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WACjE,gBAAgB,SAAS,WAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WACjE,gBAAgB,SAAS,WAAe,KAAK;AAAK,UAAM,SAAS;AAAA,WAGjE,gBAAgB,SAAS,mBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,qBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,oBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,mBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,qBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,uBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,oBAAoB,KAAK;AAAK,UAAM,SAAS;AAAA,WACtE,gBAAgB,SAAS,oBAAoB,KAAK;AAAK,UAAM,SAAS;AAAA,WAGtE,gBAAgB,SAAS,uBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WACzE,gBAAgB,SAAS,uBAAuB,KAAK;AAAK,UAAM,SAAS;AAAA,WAGxE,KAAK,QAEH,gBAAgB,SAAS,eACvB,gBAAgB,SAAS,YACzB,gBAAgB,SAAS,WACzB,gBAAgB,SAAS;AAE7B,UAAM;AAAA,WAGP,gBAAgB,SAAS,YAAY,KAAK;AAAK,UAAM,SAAS;AAAA;AAGjE,UAAM,SAAS;AAEpB,YAAU,WAAS;AACrB;AAEO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAW,SAAS;AAAA,MACpB,MAAW,CAAC;AAAA,MACZ,SAAW,CAAC;AAAA,MACZ,QAAW,CAAC;AAAA,MACZ,OAAW,CAAC;AAAA,MACZ,OAAW,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,KAAK,OAAO,KAAK;AACrB,QAAG,MAAM,KAAK;AACZ,mBAAa,OAAO,EAAE;AACtB,UAAI,YAAY;AAChB,aAAO,SAAU,SAAS,GAAG;AAAE,YAAI,KAAK,OAAO,KAAK,KAAM;AAAE,uBAAa;AAAG,iBAAO;AAAA,QAAM;AAAE,eAAO;AAAA,MAAM,CAAE;AAC1G,YAAM,KAAK,KAAK,SAAS;AACzB,UAAI,OAAO,MAAM,KAAK,KAAK;AAAI,eAAO;AACtC,aAAO,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAG,MAAM,KAAK;AACZ,UAAI,eAAe;AACnB,aAAO,SAAS,SAAS,GAAG;AAAE,YAAG,KAAK,OAAO,KAAK,KAAK;AAAE,0BAAe;AAAG,iBAAO;AAAA,QAAM;AAAE,eAAO;AAAA,MAAM,CAAC;AACxG,YAAM,QAAQ,KAAK,YAAY;AAC/B,aAAO;AAAA,IACT;AACA,QAAG,MAAM,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAG,MAAM,KAAK;AACZ,mBAAa,OAAO,EAAE;AACtB,UAAI,cAAc;AAClB,aAAO,SAAS,SAAS,GAAG;AAAE,YAAI,KAAK,KAAM;AAAE,yBAAe;AAAG,iBAAO;AAAA,QAAM;AAAE,eAAO;AAAA,MAAM,CAAC;AAC9F,YAAM,OAAO,KAAK,WAAW;AAC7B,aAAO,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAG,MAAM,KAAK;AACZ,mBAAa,OAAO,EAAE;AACtB,aAAO,SAAU,SAAS,GAAG;AAAE,eAAO,KAAK;AAAA,MAAK,CAAE;AAClD,aAAO,KAAK;AACZ,UAAI,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,KAAM;AACjD,qBAAa,OAAO,GAAG;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAM;AACd,mBAAa,OAAO,GAAG;AACvB,UAAI,aAAa;AACjB,aAAO,SAAS,SAAS,GAAG;AAAE,YAAI,KAAK,KAAM;AAAE,wBAAc;AAAG,iBAAO;AAAA,QAAM;AAAE,eAAO;AAAA,MAAM,CAAC;AAC7F,YAAM,MAAM,KAAK,UAAU;AAC3B,aAAO,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAM;AACd,aAAO,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,UAAI,aAAa;AACjB,aAAO,SAAS,SAAS,GAAG;AAAE,YAAI,KAAK,KAAM;AAAE,wBAAc;AAAG,iBAAO;AAAA,QAAM;AAAE,eAAO;AAAA,MAAM,CAAE;AAC9F,YAAM,MAAM,KAAK,UAAU;AAC3B,aAAO,KAAK;AACZ,mBAAa,OAAO,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAM;AACd,mBAAa,OAAO,EAAE;AAAA,IACxB;AACA,QAAI,MAAM,KAAM;AACd,mBAAa,OAAO,EAAE;AAAA,IACxB;AAAA,EACF;AACF;", "names": []}