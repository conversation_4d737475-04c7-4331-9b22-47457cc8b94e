{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/properties.js"], "sourcesContent": ["export const properties = {\n  name: \"properties\",\n\n  token: function(stream, state) {\n    var sol = stream.sol() || state.afterSection;\n    var eol = stream.eol();\n\n    state.afterSection = false;\n\n    if (sol) {\n      if (state.nextMultiline) {\n        state.inMultiline = true;\n        state.nextMultiline = false;\n      } else {\n        state.position = \"def\";\n      }\n    }\n\n    if (eol && ! state.nextMultiline) {\n      state.inMultiline = false;\n      state.position = \"def\";\n    }\n\n    if (sol) {\n      while(stream.eatSpace()) {}\n    }\n\n    var ch = stream.next();\n\n    if (sol && (ch === \"#\" || ch === \"!\" || ch === \";\")) {\n      state.position = \"comment\";\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (sol && ch === \"[\") {\n      state.afterSection = true;\n      stream.skipTo(\"]\"); stream.eat(\"]\");\n      return \"header\";\n    } else if (ch === \"=\" || ch === \":\") {\n      state.position = \"quote\";\n      return null;\n    } else if (ch === \"\\\\\" && state.position === \"quote\") {\n      if (stream.eol()) {  // end of line?\n        // Multiline value\n        state.nextMultiline = true;\n      }\n    }\n\n    return state.position;\n  },\n\n  startState: function() {\n    return {\n      position : \"def\",       // Current position, \"def\", \"quote\" or \"comment\"\n      nextMultiline : false,  // Is the next line multiline value\n      inMultiline : false,    // Is the current line a multiline value\n      afterSection : false    // Did we just open a section\n    };\n  }\n\n};\n"], "mappings": ";;;AAAO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EAEN,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,OAAO,IAAI,KAAK,MAAM;AAChC,QAAI,MAAM,OAAO,IAAI;AAErB,UAAM,eAAe;AAErB,QAAI,KAAK;AACP,UAAI,MAAM,eAAe;AACvB,cAAM,cAAc;AACpB,cAAM,gBAAgB;AAAA,MACxB,OAAO;AACL,cAAM,WAAW;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,OAAO,CAAE,MAAM,eAAe;AAChC,YAAM,cAAc;AACpB,YAAM,WAAW;AAAA,IACnB;AAEA,QAAI,KAAK;AACP,aAAM,OAAO,SAAS,GAAG;AAAA,MAAC;AAAA,IAC5B;AAEA,QAAI,KAAK,OAAO,KAAK;AAErB,QAAI,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM;AACnD,YAAM,WAAW;AACjB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAW,OAAO,OAAO,KAAK;AAC5B,YAAM,eAAe;AACrB,aAAO,OAAO,GAAG;AAAG,aAAO,IAAI,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,OAAO,OAAO,OAAO,KAAK;AACnC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,WAAW,OAAO,QAAQ,MAAM,aAAa,SAAS;AACpD,UAAI,OAAO,IAAI,GAAG;AAEhB,cAAM,gBAAgB;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAW;AAAA;AAAA,MACX,eAAgB;AAAA;AAAA,MAChB,aAAc;AAAA;AAAA,MACd,cAAe;AAAA;AAAA,IACjB;AAAA,EACF;AAEF;", "names": []}