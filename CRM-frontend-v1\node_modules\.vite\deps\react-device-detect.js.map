{"version": 3, "sources": ["../../ua-parser-js/dist/ua-parser.min.js", "../../react-device-detect/dist/lib.js"], "sourcesContent": ["/* UAParser.js v1.0.37\n   Copyright © 2012-2021 <PERSON><PERSON><PERSON> <<EMAIL>>\n   MIT License */\n(function(window,undefined){\"use strict\";var LIBVERSION=\"1.0.37\",EMPTY=\"\",UNKNOWN=\"?\",FUNC_TYPE=\"function\",UNDEF_TYPE=\"undefined\",OBJ_TYPE=\"object\",STR_TYPE=\"string\",MAJOR=\"major\",MODEL=\"model\",NAME=\"name\",TYPE=\"type\",VENDOR=\"vendor\",VERSION=\"version\",ARCHITECTURE=\"architecture\",CONSOLE=\"console\",MOBILE=\"mobile\",TABLET=\"tablet\",SMARTTV=\"smarttv\",WEARABLE=\"wearable\",EMBEDDED=\"embedded\",UA_MAX_LENGTH=500;var AMAZON=\"Amazon\",APPLE=\"Apple\",ASUS=\"ASUS\",BLACKBERRY=\"BlackBerry\",BROWSER=\"Browser\",CHROME=\"Chrome\",EDGE=\"Edge\",FIREFOX=\"Firefox\",GOOGLE=\"Google\",HUAWEI=\"Huawei\",LG=\"LG\",MICROSOFT=\"Microsoft\",MOTOROLA=\"Motorola\",OPERA=\"Opera\",SAMSUNG=\"Samsung\",SHARP=\"Sharp\",SONY=\"Sony\",XIAOMI=\"Xiaomi\",ZEBRA=\"Zebra\",FACEBOOK=\"Facebook\",CHROMIUM_OS=\"Chromium OS\",MAC_OS=\"Mac OS\";var extend=function(regexes,extensions){var mergedRegexes={};for(var i in regexes){if(extensions[i]&&extensions[i].length%2===0){mergedRegexes[i]=extensions[i].concat(regexes[i])}else{mergedRegexes[i]=regexes[i]}}return mergedRegexes},enumerize=function(arr){var enums={};for(var i=0;i<arr.length;i++){enums[arr[i].toUpperCase()]=arr[i]}return enums},has=function(str1,str2){return typeof str1===STR_TYPE?lowerize(str2).indexOf(lowerize(str1))!==-1:false},lowerize=function(str){return str.toLowerCase()},majorize=function(version){return typeof version===STR_TYPE?version.replace(/[^\\d\\.]/g,EMPTY).split(\".\")[0]:undefined},trim=function(str,len){if(typeof str===STR_TYPE){str=str.replace(/^\\s\\s*/,EMPTY);return typeof len===UNDEF_TYPE?str:str.substring(0,UA_MAX_LENGTH)}};var rgxMapper=function(ua,arrays){var i=0,j,k,p,q,matches,match;while(i<arrays.length&&!matches){var regex=arrays[i],props=arrays[i+1];j=k=0;while(j<regex.length&&!matches){if(!regex[j]){break}matches=regex[j++].exec(ua);if(!!matches){for(p=0;p<props.length;p++){match=matches[++k];q=props[p];if(typeof q===OBJ_TYPE&&q.length>0){if(q.length===2){if(typeof q[1]==FUNC_TYPE){this[q[0]]=q[1].call(this,match)}else{this[q[0]]=q[1]}}else if(q.length===3){if(typeof q[1]===FUNC_TYPE&&!(q[1].exec&&q[1].test)){this[q[0]]=match?q[1].call(this,match,q[2]):undefined}else{this[q[0]]=match?match.replace(q[1],q[2]):undefined}}else if(q.length===4){this[q[0]]=match?q[3].call(this,match.replace(q[1],q[2])):undefined}}else{this[q]=match?match:undefined}}}}i+=2}},strMapper=function(str,map){for(var i in map){if(typeof map[i]===OBJ_TYPE&&map[i].length>0){for(var j=0;j<map[i].length;j++){if(has(map[i][j],str)){return i===UNKNOWN?undefined:i}}}else if(has(map[i],str)){return i===UNKNOWN?undefined:i}}return str};var oldSafariMap={\"1.0\":\"/8\",1.2:\"/1\",1.3:\"/3\",\"2.0\":\"/412\",\"2.0.2\":\"/416\",\"2.0.3\":\"/417\",\"2.0.4\":\"/419\",\"?\":\"/\"},windowsVersionMap={ME:\"4.90\",\"NT 3.11\":\"NT3.51\",\"NT 4.0\":\"NT4.0\",2e3:\"NT 5.0\",XP:[\"NT 5.1\",\"NT 5.2\"],Vista:\"NT 6.0\",7:\"NT 6.1\",8:\"NT 6.2\",8.1:\"NT 6.3\",10:[\"NT 6.4\",\"NT 10.0\"],RT:\"ARM\"};var regexes={browser:[[/\\b(?:crmo|crios)\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Chrome\"]],[/edg(?:e|ios|a)?\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Edge\"]],[/(opera mini)\\/([-\\w\\.]+)/i,/(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,/(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i],[NAME,VERSION],[/opios[\\/ ]+([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Mini\"]],[/\\bopr\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA]],[/\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i],[VERSION,[NAME,\"Baidu\"]],[/(kindle)\\/([\\w\\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,/(avant|iemobile|slim)\\s?(?:browser)?[\\/ ]?([\\w\\.]*)/i,/(?:ms|\\()(ie) ([\\w\\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,/(heytap|ovi)browser\\/([\\d\\.]+)/i,/(weibo)__([\\d\\.]+)/i],[NAME,VERSION],[/(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i],[VERSION,[NAME,\"UC\"+BROWSER]],[/microm.+\\bqbcore\\/([\\w\\.]+)/i,/\\bqbcore\\/([\\w\\.]+).+microm/i,/micromessenger\\/([\\w\\.]+)/i],[VERSION,[NAME,\"WeChat\"]],[/konqueror\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Konqueror\"]],[/trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i],[VERSION,[NAME,\"IE\"]],[/ya(?:search)?browser\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Yandex\"]],[/slbrowser\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Smart Lenovo \"+BROWSER]],[/(avast|avg)\\/([\\w\\.]+)/i],[[NAME,/(.+)/,\"$1 Secure \"+BROWSER],VERSION],[/\\bfocus\\/([\\w\\.]+)/i],[VERSION,[NAME,FIREFOX+\" Focus\"]],[/\\bopt\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Touch\"]],[/coc_coc\\w+\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Coc Coc\"]],[/dolfin\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Dolphin\"]],[/coast\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Coast\"]],[/miuibrowser\\/([\\w\\.]+)/i],[VERSION,[NAME,\"MIUI \"+BROWSER]],[/fxios\\/([-\\w\\.]+)/i],[VERSION,[NAME,FIREFOX]],[/\\bqihu|(qi?ho?o?|360)browser/i],[[NAME,\"360 \"+BROWSER]],[/(oculus|sailfish|huawei|vivo)browser\\/([\\w\\.]+)/i],[[NAME,/(.+)/,\"$1 \"+BROWSER],VERSION],[/samsungbrowser\\/([\\w\\.]+)/i],[VERSION,[NAME,SAMSUNG+\" Internet\"]],[/(comodo_dragon)\\/([\\w\\.]+)/i],[[NAME,/_/g,\" \"],VERSION],[/metasr[\\/ ]?([\\d\\.]+)/i],[VERSION,[NAME,\"Sogou Explorer\"]],[/(sogou)mo\\w+\\/([\\d\\.]+)/i],[[NAME,\"Sogou Mobile\"],VERSION],[/(electron)\\/([\\w\\.]+) safari/i,/(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,/m?(qqbrowser|2345Explorer)[\\/ ]?([\\w\\.]+)/i],[NAME,VERSION],[/(lbbrowser)/i,/\\[(linkedin)app\\]/i],[NAME],[/((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i],[[NAME,FACEBOOK],VERSION],[/(Klarna)\\/([\\w\\.]+)/i,/(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,/(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,/safari (line)\\/([\\w\\.]+)/i,/\\b(line)\\/([\\w\\.]+)\\/iab/i,/(alipay)client\\/([\\w\\.]+)/i,/(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i],[NAME,VERSION],[/\\bgsa\\/([\\w\\.]+) .*safari\\//i],[VERSION,[NAME,\"GSA\"]],[/musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i],[VERSION,[NAME,\"TikTok\"]],[/headlesschrome(?:\\/([\\w\\.]+)| )/i],[VERSION,[NAME,CHROME+\" Headless\"]],[/ wv\\).+(chrome)\\/([\\w\\.]+)/i],[[NAME,CHROME+\" WebView\"],VERSION],[/droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i],[VERSION,[NAME,\"Android \"+BROWSER]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i],[NAME,VERSION],[/version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i],[VERSION,[NAME,\"Mobile Safari\"]],[/version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i],[VERSION,NAME],[/webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i],[NAME,[VERSION,strMapper,oldSafariMap]],[/(webkit|khtml)\\/([\\w\\.]+)/i],[NAME,VERSION],[/(navigator|netscape\\d?)\\/([-\\w\\.]+)/i],[[NAME,\"Netscape\"],VERSION],[/mobile vr; rv:([\\w\\.]+)\\).+firefox/i],[VERSION,[NAME,FIREFOX+\" Reality\"]],[/ekiohf.+(flow)\\/([\\w\\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,/(firefox)\\/([\\w\\.]+)/i,/(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,/(links) \\(([\\w\\.]+)/i,/panasonic;(viera)/i],[NAME,VERSION],[/(cobalt)\\/([\\w\\.]+)/i],[NAME,[VERSION,/master.|lts./,\"\"]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i],[[ARCHITECTURE,\"amd64\"]],[/(ia32(?=;))/i],[[ARCHITECTURE,lowerize]],[/((?:i[346]|x)86)[;\\)]/i],[[ARCHITECTURE,\"ia32\"]],[/\\b(aarch64|arm(v?8e?l?|_?64))\\b/i],[[ARCHITECTURE,\"arm64\"]],[/\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i],[[ARCHITECTURE,\"armhf\"]],[/windows (ce|mobile); ppc;/i],[[ARCHITECTURE,\"arm\"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i],[[ARCHITECTURE,/ower/,EMPTY,lowerize]],[/(sun4\\w)[;\\)]/i],[[ARCHITECTURE,\"sparc\"]],[/((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i],[[ARCHITECTURE,lowerize]]],device:[[/\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i],[MODEL,[VENDOR,SAMSUNG],[TYPE,TABLET]],[/\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,/samsung[- ]([-\\w]+)/i,/sec-(sgh\\w+)/i],[MODEL,[VENDOR,SAMSUNG],[TYPE,MOBILE]],[/(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i],[MODEL,[VENDOR,APPLE],[TYPE,MOBILE]],[/\\((ipad);[-\\w\\),; ]+apple/i,/applecoremedia\\/[\\w\\.]+ \\((ipad)/i,/\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i],[MODEL,[VENDOR,APPLE],[TYPE,TABLET]],[/(macintosh);/i],[MODEL,[VENDOR,APPLE]],[/\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i],[MODEL,[VENDOR,SHARP],[TYPE,MOBILE]],[/\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i],[MODEL,[VENDOR,HUAWEI],[TYPE,TABLET]],[/(?:huawei|honor)([-\\w ]+)[;\\)]/i,/\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i],[MODEL,[VENDOR,HUAWEI],[TYPE,MOBILE]],[/\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,/\\b; (\\w+) build\\/hm\\1/i,/\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,/\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,/oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,/\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i],[[MODEL,/_/g,\" \"],[VENDOR,XIAOMI],[TYPE,MOBILE]],[/oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,/\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i],[[MODEL,/_/g,\" \"],[VENDOR,XIAOMI],[TYPE,TABLET]],[/; (\\w+) bui.+ oppo/i,/\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i],[MODEL,[VENDOR,\"OPPO\"],[TYPE,MOBILE]],[/vivo (\\w+)(?: bui|\\))/i,/\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i],[MODEL,[VENDOR,\"Vivo\"],[TYPE,MOBILE]],[/\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i],[MODEL,[VENDOR,\"Realme\"],[TYPE,MOBILE]],[/\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,/\\bmot(?:orola)?[- ](\\w*)/i,/((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i],[MODEL,[VENDOR,MOTOROLA],[TYPE,MOBILE]],[/\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i],[MODEL,[VENDOR,MOTOROLA],[TYPE,TABLET]],[/((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i],[MODEL,[VENDOR,LG],[TYPE,TABLET]],[/(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,/\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,/\\blg-?([\\d\\w]+) bui/i],[MODEL,[VENDOR,LG],[TYPE,MOBILE]],[/(ideatab[-\\w ]+)/i,/lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i],[MODEL,[VENDOR,\"Lenovo\"],[TYPE,TABLET]],[/(?:maemo|nokia).*(n900|lumia \\d+)/i,/nokia[-_ ]?([-\\w\\.]*)/i],[[MODEL,/_/g,\" \"],[VENDOR,\"Nokia\"],[TYPE,MOBILE]],[/(pixel c)\\b/i],[MODEL,[VENDOR,GOOGLE],[TYPE,TABLET]],[/droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i],[MODEL,[VENDOR,GOOGLE],[TYPE,MOBILE]],[/droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i],[MODEL,[VENDOR,SONY],[TYPE,MOBILE]],[/sony tablet [ps]/i,/\\b(?:sony)?sgp\\w+(?: bui|\\))/i],[[MODEL,\"Xperia Tablet\"],[VENDOR,SONY],[TYPE,TABLET]],[/ (kb2005|in20[12]5|be20[12][59])\\b/i,/(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i],[MODEL,[VENDOR,\"OnePlus\"],[TYPE,MOBILE]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,/(kf[a-z]+)( bui|\\)).+silk\\//i],[MODEL,[VENDOR,AMAZON],[TYPE,TABLET]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i],[[MODEL,/(.+)/g,\"Fire Phone $1\"],[VENDOR,AMAZON],[TYPE,MOBILE]],[/(playbook);[-\\w\\),; ]+(rim)/i],[MODEL,VENDOR,[TYPE,TABLET]],[/\\b((?:bb[a-f]|st[hv])100-\\d)/i,/\\(bb10; (\\w+)/i],[MODEL,[VENDOR,BLACKBERRY],[TYPE,MOBILE]],[/(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i],[MODEL,[VENDOR,ASUS],[TYPE,TABLET]],[/ (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i],[MODEL,[VENDOR,ASUS],[TYPE,MOBILE]],[/(nexus 9)/i],[MODEL,[VENDOR,\"HTC\"],[TYPE,TABLET]],[/(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,/(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i],[VENDOR,[MODEL,/_/g,\" \"],[TYPE,MOBILE]],[/droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i],[MODEL,[VENDOR,\"Acer\"],[TYPE,TABLET]],[/droid.+; (m[1-5] note) bui/i,/\\bmz-([-\\w]{2,})/i],[MODEL,[VENDOR,\"Meizu\"],[TYPE,MOBILE]],[/; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i],[MODEL,[VENDOR,\"Ulefone\"],[TYPE,MOBILE]],[/(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\\w]*)/i,/(hp) ([\\w ]+\\w)/i,/(asus)-?(\\w+)/i,/(microsoft); (lumia[\\w ]+)/i,/(lenovo)[-_ ]?([-\\w]+)/i,/(jolla)/i,/(oppo) ?([\\w ]+) bui/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(kobo)\\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\\/([\\w\\.]+)/i,/(nook)[\\w ]+build\\/(\\w+)/i,/(dell) (strea[kpr\\d ]*[\\dko])/i,/(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,/(trinity)[- ]*(t\\d{3}) bui/i,/(gigaset)[- ]+(q\\w{1,9}) bui/i,/(vodafone) ([\\w ]+)(?:\\)| bui)/i],[VENDOR,MODEL,[TYPE,TABLET]],[/(surface duo)/i],[MODEL,[VENDOR,MICROSOFT],[TYPE,TABLET]],[/droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i],[MODEL,[VENDOR,\"Fairphone\"],[TYPE,MOBILE]],[/(u304aa)/i],[MODEL,[VENDOR,\"AT&T\"],[TYPE,MOBILE]],[/\\bsie-(\\w*)/i],[MODEL,[VENDOR,\"Siemens\"],[TYPE,MOBILE]],[/\\b(rct\\w+) b/i],[MODEL,[VENDOR,\"RCA\"],[TYPE,TABLET]],[/\\b(venue[\\d ]{2,7}) b/i],[MODEL,[VENDOR,\"Dell\"],[TYPE,TABLET]],[/\\b(q(?:mv|ta)\\w+) b/i],[MODEL,[VENDOR,\"Verizon\"],[TYPE,TABLET]],[/\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i],[MODEL,[VENDOR,\"Barnes & Noble\"],[TYPE,TABLET]],[/\\b(tm\\d{3}\\w+) b/i],[MODEL,[VENDOR,\"NuVision\"],[TYPE,TABLET]],[/\\b(k88) b/i],[MODEL,[VENDOR,\"ZTE\"],[TYPE,TABLET]],[/\\b(nx\\d{3}j) b/i],[MODEL,[VENDOR,\"ZTE\"],[TYPE,MOBILE]],[/\\b(gen\\d{3}) b.+49h/i],[MODEL,[VENDOR,\"Swiss\"],[TYPE,MOBILE]],[/\\b(zur\\d{3}) b/i],[MODEL,[VENDOR,\"Swiss\"],[TYPE,TABLET]],[/\\b((zeki)?tb.*\\b) b/i],[MODEL,[VENDOR,\"Zeki\"],[TYPE,TABLET]],[/\\b([yr]\\d{2}) b/i,/\\b(dragon[- ]+touch |dt)(\\w{5}) b/i],[[VENDOR,\"Dragon Touch\"],MODEL,[TYPE,TABLET]],[/\\b(ns-?\\w{0,9}) b/i],[MODEL,[VENDOR,\"Insignia\"],[TYPE,TABLET]],[/\\b((nxa|next)-?\\w{0,9}) b/i],[MODEL,[VENDOR,\"NextBook\"],[TYPE,TABLET]],[/\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[VENDOR,\"Voice\"],MODEL,[TYPE,MOBILE]],[/\\b(lvtel\\-)?(v1[12]) b/i],[[VENDOR,\"LvTel\"],MODEL,[TYPE,MOBILE]],[/\\b(ph-1) /i],[MODEL,[VENDOR,\"Essential\"],[TYPE,MOBILE]],[/\\b(v(100md|700na|7011|917g).*\\b) b/i],[MODEL,[VENDOR,\"Envizen\"],[TYPE,TABLET]],[/\\b(trio[-\\w\\. ]+) b/i],[MODEL,[VENDOR,\"MachSpeed\"],[TYPE,TABLET]],[/\\btu_(1491) b/i],[MODEL,[VENDOR,\"Rotor\"],[TYPE,TABLET]],[/(shield[\\w ]+) b/i],[MODEL,[VENDOR,\"Nvidia\"],[TYPE,TABLET]],[/(sprint) (\\w+)/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(kin\\.[onetw]{3})/i],[[MODEL,/\\./g,\" \"],[VENDOR,MICROSOFT],[TYPE,MOBILE]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,TABLET]],[/droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,MOBILE]],[/smart-tv.+(samsung)/i],[VENDOR,[TYPE,SMARTTV]],[/hbbtv.+maple;(\\d+)/i],[[MODEL,/^/,\"SmartTV\"],[VENDOR,SAMSUNG],[TYPE,SMARTTV]],[/(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i],[[VENDOR,LG],[TYPE,SMARTTV]],[/(apple) ?tv/i],[VENDOR,[MODEL,APPLE+\" TV\"],[TYPE,SMARTTV]],[/crkey/i],[[MODEL,CHROME+\"cast\"],[VENDOR,GOOGLE],[TYPE,SMARTTV]],[/droid.+aft(\\w+)( bui|\\))/i],[MODEL,[VENDOR,AMAZON],[TYPE,SMARTTV]],[/\\(dtv[\\);].+(aquos)/i,/(aquos-tv[\\w ]+)\\)/i],[MODEL,[VENDOR,SHARP],[TYPE,SMARTTV]],[/(bravia[\\w ]+)( bui|\\))/i],[MODEL,[VENDOR,SONY],[TYPE,SMARTTV]],[/(mitv-\\w{5}) bui/i],[MODEL,[VENDOR,XIAOMI],[TYPE,SMARTTV]],[/Hbbtv.*(technisat) (.*);/i],[VENDOR,MODEL,[TYPE,SMARTTV]],[/\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,/hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i],[[VENDOR,trim],[MODEL,trim],[TYPE,SMARTTV]],[/\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i],[[TYPE,SMARTTV]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[VENDOR,MODEL,[TYPE,CONSOLE]],[/droid.+; (shield) bui/i],[MODEL,[VENDOR,\"Nvidia\"],[TYPE,CONSOLE]],[/(playstation [345portablevi]+)/i],[MODEL,[VENDOR,SONY],[TYPE,CONSOLE]],[/\\b(xbox(?: one)?(?!; xbox))[\\); ]/i],[MODEL,[VENDOR,MICROSOFT],[TYPE,CONSOLE]],[/((pebble))app/i],[VENDOR,MODEL,[TYPE,WEARABLE]],[/(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i],[MODEL,[VENDOR,APPLE],[TYPE,WEARABLE]],[/droid.+; (glass) \\d/i],[MODEL,[VENDOR,GOOGLE],[TYPE,WEARABLE]],[/droid.+; (wt63?0{2,3})\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,WEARABLE]],[/(quest( 2| pro)?)/i],[MODEL,[VENDOR,FACEBOOK],[TYPE,WEARABLE]],[/(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i],[VENDOR,[TYPE,EMBEDDED]],[/(aeobc)\\b/i],[MODEL,[VENDOR,AMAZON],[TYPE,EMBEDDED]],[/droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i],[MODEL,[TYPE,MOBILE]],[/droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i],[MODEL,[TYPE,TABLET]],[/\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i],[[TYPE,TABLET]],[/(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i],[[TYPE,MOBILE]],[/(android[-\\w\\. ]{0,9});.+buil/i],[MODEL,[VENDOR,\"Generic\"]]],engine:[[/windows.+ edge\\/([\\w\\.]+)/i],[VERSION,[NAME,EDGE+\"HTML\"]],[/webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i],[VERSION,[NAME,\"Blink\"]],[/(presto)\\/([\\w\\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i,/ekioh(flow)\\/([\\w\\.]+)/i,/(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,/(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,/\\b(libweb)/i],[NAME,VERSION],[/rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i],[VERSION,NAME]],os:[[/microsoft (windows) (vista|xp)/i],[NAME,VERSION],[/(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i],[NAME,[VERSION,strMapper,windowsVersionMap]],[/windows nt 6\\.2; (arm)/i,/windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i],[[VERSION,strMapper,windowsVersionMap],[NAME,\"Windows\"]],[/ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,/(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,/cfnetwork\\/.+darwin/i],[[VERSION,/_/g,\".\"],[NAME,\"iOS\"]],[/(mac os x) ?([\\w\\. ]*)/i,/(macintosh|mac_powerpc\\b)(?!.+haiku)/i],[[NAME,MAC_OS],[VERSION,/_/g,\".\"]],[/droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i],[VERSION,NAME],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,/(blackberry)\\w*\\/([\\w\\.]*)/i,/(tizen|kaios)[\\/ ]([\\w\\.]+)/i,/\\((series40);/i],[NAME,VERSION],[/\\(bb(10);/i],[VERSION,[NAME,BLACKBERRY]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i],[VERSION,[NAME,\"Symbian\"]],[/mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i],[VERSION,[NAME,FIREFOX+\" OS\"]],[/web0s;.+rt(tv)/i,/\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i],[VERSION,[NAME,\"webOS\"]],[/watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i],[VERSION,[NAME,\"watchOS\"]],[/crkey\\/([\\d\\.]+)/i],[VERSION,[NAME,CHROME+\"cast\"]],[/(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i],[[NAME,CHROMIUM_OS],VERSION],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\\/(\\d+\\.[\\w\\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\\);]+)/i,/\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,/(mint)[\\/\\(\\) ]?(\\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,/(hurd|linux) ?([\\w\\.]*)/i,/(gnu) ?([\\w\\.]*)/i,/\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i,/(haiku) (\\w+)/i],[NAME,VERSION],[/(sunos) ?([\\w\\.\\d]*)/i],[[NAME,\"Solaris\"],VERSION],[/((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,/(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,/\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\\w\\.]*)/i],[NAME,VERSION]]};var UAParser=function(ua,extensions){if(typeof ua===OBJ_TYPE){extensions=ua;ua=undefined}if(!(this instanceof UAParser)){return new UAParser(ua,extensions).getResult()}var _navigator=typeof window!==UNDEF_TYPE&&window.navigator?window.navigator:undefined;var _ua=ua||(_navigator&&_navigator.userAgent?_navigator.userAgent:EMPTY);var _uach=_navigator&&_navigator.userAgentData?_navigator.userAgentData:undefined;var _rgxmap=extensions?extend(regexes,extensions):regexes;var _isSelfNav=_navigator&&_navigator.userAgent==_ua;this.getBrowser=function(){var _browser={};_browser[NAME]=undefined;_browser[VERSION]=undefined;rgxMapper.call(_browser,_ua,_rgxmap.browser);_browser[MAJOR]=majorize(_browser[VERSION]);if(_isSelfNav&&_navigator&&_navigator.brave&&typeof _navigator.brave.isBrave==FUNC_TYPE){_browser[NAME]=\"Brave\"}return _browser};this.getCPU=function(){var _cpu={};_cpu[ARCHITECTURE]=undefined;rgxMapper.call(_cpu,_ua,_rgxmap.cpu);return _cpu};this.getDevice=function(){var _device={};_device[VENDOR]=undefined;_device[MODEL]=undefined;_device[TYPE]=undefined;rgxMapper.call(_device,_ua,_rgxmap.device);if(_isSelfNav&&!_device[TYPE]&&_uach&&_uach.mobile){_device[TYPE]=MOBILE}if(_isSelfNav&&_device[MODEL]==\"Macintosh\"&&_navigator&&typeof _navigator.standalone!==UNDEF_TYPE&&_navigator.maxTouchPoints&&_navigator.maxTouchPoints>2){_device[MODEL]=\"iPad\";_device[TYPE]=TABLET}return _device};this.getEngine=function(){var _engine={};_engine[NAME]=undefined;_engine[VERSION]=undefined;rgxMapper.call(_engine,_ua,_rgxmap.engine);return _engine};this.getOS=function(){var _os={};_os[NAME]=undefined;_os[VERSION]=undefined;rgxMapper.call(_os,_ua,_rgxmap.os);if(_isSelfNav&&!_os[NAME]&&_uach&&_uach.platform!=\"Unknown\"){_os[NAME]=_uach.platform.replace(/chrome os/i,CHROMIUM_OS).replace(/macos/i,MAC_OS)}return _os};this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};this.getUA=function(){return _ua};this.setUA=function(ua){_ua=typeof ua===STR_TYPE&&ua.length>UA_MAX_LENGTH?trim(ua,UA_MAX_LENGTH):ua;return this};this.setUA(_ua);return this};UAParser.VERSION=LIBVERSION;UAParser.BROWSER=enumerize([NAME,VERSION,MAJOR]);UAParser.CPU=enumerize([ARCHITECTURE]);UAParser.DEVICE=enumerize([MODEL,VENDOR,TYPE,CONSOLE,MOBILE,SMARTTV,TABLET,WEARABLE,EMBEDDED]);UAParser.ENGINE=UAParser.OS=enumerize([NAME,VERSION]);if(typeof exports!==UNDEF_TYPE){if(typeof module!==UNDEF_TYPE&&module.exports){exports=module.exports=UAParser}exports.UAParser=UAParser}else{if(typeof define===FUNC_TYPE&&define.amd){define(function(){return UAParser})}else if(typeof window!==UNDEF_TYPE){window.UAParser=UAParser}}var $=typeof window!==UNDEF_TYPE&&(window.jQuery||window.Zepto);if($&&!$.ua){var parser=new UAParser;$.ua=parser.getResult();$.ua.get=function(){return parser.getUA()};$.ua.set=function(ua){parser.setUA(ua);var result=parser.getResult();for(var prop in result){$.ua[prop]=result[prop]}}}})(typeof window===\"object\"?window:this);", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = require('react');\nvar React__default = _interopDefault(React);\n\nvar UAParser = require('ua-parser-js/dist/ua-parser.min');\n\nvar ClientUAInstance = new UAParser();\nvar browser = ClientUAInstance.getBrowser();\nvar cpu = ClientUAInstance.getCPU();\nvar device = ClientUAInstance.getDevice();\nvar engine = ClientUAInstance.getEngine();\nvar os = ClientUAInstance.getOS();\nvar ua = ClientUAInstance.getUA();\nvar setUa = function setUa(userAgentString) {\n  return ClientUAInstance.setUA(userAgentString);\n};\nvar parseUserAgent = function parseUserAgent(userAgent) {\n  if (!userAgent) {\n    console.error('No userAgent string was provided');\n    return;\n  }\n\n  var UserAgentInstance = new UAParser(userAgent);\n  return {\n    UA: UserAgentInstance,\n    browser: UserAgentInstance.getBrowser(),\n    cpu: UserAgentInstance.getCPU(),\n    device: UserAgentInstance.getDevice(),\n    engine: UserAgentInstance.getEngine(),\n    os: UserAgentInstance.getOS(),\n    ua: UserAgentInstance.getUA(),\n    setUserAgent: function setUserAgent(userAgentString) {\n      return UserAgentInstance.setUA(userAgentString);\n    }\n  };\n};\n\nvar UAHelper = /*#__PURE__*/Object.freeze({\n  ClientUAInstance: ClientUAInstance,\n  browser: browser,\n  cpu: cpu,\n  device: device,\n  engine: engine,\n  os: os,\n  ua: ua,\n  setUa: setUa,\n  parseUserAgent: parseUserAgent\n});\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar DeviceTypes = {\n  Mobile: 'mobile',\n  Tablet: 'tablet',\n  SmartTv: 'smarttv',\n  Console: 'console',\n  Wearable: 'wearable',\n  Embedded: 'embedded',\n  Browser: undefined\n};\nvar BrowserTypes = {\n  Chrome: 'Chrome',\n  Firefox: 'Firefox',\n  Opera: 'Opera',\n  Yandex: 'Yandex',\n  Safari: 'Safari',\n  InternetExplorer: 'Internet Explorer',\n  Edge: 'Edge',\n  Chromium: 'Chromium',\n  Ie: 'IE',\n  MobileSafari: 'Mobile Safari',\n  EdgeChromium: 'Edge Chromium',\n  MIUI: 'MIUI Browser',\n  SamsungBrowser: 'Samsung Browser'\n};\nvar OsTypes = {\n  IOS: 'iOS',\n  Android: 'Android',\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MAC_OS: 'Mac OS'\n};\nvar InitialDeviceTypes = {\n  isMobile: false,\n  isTablet: false,\n  isBrowser: false,\n  isSmartTV: false,\n  isConsole: false,\n  isWearable: false\n};\n\nvar checkDeviceType = function checkDeviceType(type) {\n  switch (type) {\n    case DeviceTypes.Mobile:\n      return {\n        isMobile: true\n      };\n\n    case DeviceTypes.Tablet:\n      return {\n        isTablet: true\n      };\n\n    case DeviceTypes.SmartTv:\n      return {\n        isSmartTV: true\n      };\n\n    case DeviceTypes.Console:\n      return {\n        isConsole: true\n      };\n\n    case DeviceTypes.Wearable:\n      return {\n        isWearable: true\n      };\n\n    case DeviceTypes.Browser:\n      return {\n        isBrowser: true\n      };\n\n    case DeviceTypes.Embedded:\n      return {\n        isEmbedded: true\n      };\n\n    default:\n      return InitialDeviceTypes;\n  }\n};\nvar setUserAgent = function setUserAgent(userAgent) {\n  return setUa(userAgent);\n};\nvar setDefaults = function setDefaults(p) {\n  var d = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';\n  return p ? p : d;\n};\nvar getNavigatorInstance = function getNavigatorInstance() {\n  if (typeof window !== 'undefined') {\n    if (window.navigator || navigator) {\n      return window.navigator || navigator;\n    }\n  }\n\n  return false;\n};\nvar isIOS13Check = function isIOS13Check(type) {\n  var nav = getNavigatorInstance();\n  return nav && nav.platform && (nav.platform.indexOf(type) !== -1 || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1 && !window.MSStream);\n};\n\nvar browserPayload = function browserPayload(isBrowser, browser, engine, os, ua) {\n  return {\n    isBrowser: isBrowser,\n    browserMajorVersion: setDefaults(browser.major),\n    browserFullVersion: setDefaults(browser.version),\n    browserName: setDefaults(browser.name),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar mobilePayload = function mobilePayload(type, device, os, ua) {\n  return _objectSpread2({}, type, {\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    os: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    ua: setDefaults(ua)\n  });\n};\nvar smartTvPayload = function smartTvPayload(isSmartTV, engine, os, ua) {\n  return {\n    isSmartTV: isSmartTV,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar consolePayload = function consolePayload(isConsole, engine, os, ua) {\n  return {\n    isConsole: isConsole,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar wearablePayload = function wearablePayload(isWearable, engine, os, ua) {\n  return {\n    isWearable: isWearable,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar embeddedPayload = function embeddedPayload(isEmbedded, device, engine, os, ua) {\n  return {\n    isEmbedded: isEmbedded,\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\n\nfunction deviceDetect(userAgent) {\n  var _ref = userAgent ? parseUserAgent(userAgent) : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      engine = _ref.engine,\n      os = _ref.os,\n      ua = _ref.ua;\n\n  var type = checkDeviceType(device.type);\n  var isBrowser = type.isBrowser,\n      isMobile = type.isMobile,\n      isTablet = type.isTablet,\n      isSmartTV = type.isSmartTV,\n      isConsole = type.isConsole,\n      isWearable = type.isWearable,\n      isEmbedded = type.isEmbedded;\n\n  if (isBrowser) {\n    return browserPayload(isBrowser, browser, engine, os, ua);\n  }\n\n  if (isSmartTV) {\n    return smartTvPayload(isSmartTV, engine, os, ua);\n  }\n\n  if (isConsole) {\n    return consolePayload(isConsole, engine, os, ua);\n  }\n\n  if (isMobile) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isTablet) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isWearable) {\n    return wearablePayload(isWearable, engine, os, ua);\n  }\n\n  if (isEmbedded) {\n    return embeddedPayload(isEmbedded, device, engine, os, ua);\n  }\n}\n\nvar isMobileType = function isMobileType(_ref) {\n  var type = _ref.type;\n  return type === DeviceTypes.Mobile;\n};\nvar isTabletType = function isTabletType(_ref2) {\n  var type = _ref2.type;\n  return type === DeviceTypes.Tablet;\n};\nvar isMobileAndTabletType = function isMobileAndTabletType(_ref3) {\n  var type = _ref3.type;\n  return type === DeviceTypes.Mobile || type === DeviceTypes.Tablet;\n};\nvar isSmartTVType = function isSmartTVType(_ref4) {\n  var type = _ref4.type;\n  return type === DeviceTypes.SmartTv;\n};\nvar isBrowserType = function isBrowserType(_ref5) {\n  var type = _ref5.type;\n  return type === DeviceTypes.Browser;\n};\nvar isWearableType = function isWearableType(_ref6) {\n  var type = _ref6.type;\n  return type === DeviceTypes.Wearable;\n};\nvar isConsoleType = function isConsoleType(_ref7) {\n  var type = _ref7.type;\n  return type === DeviceTypes.Console;\n};\nvar isEmbeddedType = function isEmbeddedType(_ref8) {\n  var type = _ref8.type;\n  return type === DeviceTypes.Embedded;\n};\nvar getMobileVendor = function getMobileVendor(_ref9) {\n  var vendor = _ref9.vendor;\n  return setDefaults(vendor);\n};\nvar getMobileModel = function getMobileModel(_ref10) {\n  var model = _ref10.model;\n  return setDefaults(model);\n};\nvar getDeviceType = function getDeviceType(_ref11) {\n  var type = _ref11.type;\n  return setDefaults(type, 'browser');\n}; // os types\n\nvar isAndroidType = function isAndroidType(_ref12) {\n  var name = _ref12.name;\n  return name === OsTypes.Android;\n};\nvar isWindowsType = function isWindowsType(_ref13) {\n  var name = _ref13.name;\n  return name === OsTypes.Windows;\n};\nvar isMacOsType = function isMacOsType(_ref14) {\n  var name = _ref14.name;\n  return name === OsTypes.MAC_OS;\n};\nvar isWinPhoneType = function isWinPhoneType(_ref15) {\n  var name = _ref15.name;\n  return name === OsTypes.WindowsPhone;\n};\nvar isIOSType = function isIOSType(_ref16) {\n  var name = _ref16.name;\n  return name === OsTypes.IOS;\n};\nvar getOsVersion = function getOsVersion(_ref17) {\n  var version = _ref17.version;\n  return setDefaults(version);\n};\nvar getOsName = function getOsName(_ref18) {\n  var name = _ref18.name;\n  return setDefaults(name);\n}; // browser types\n\nvar isChromeType = function isChromeType(_ref19) {\n  var name = _ref19.name;\n  return name === BrowserTypes.Chrome;\n};\nvar isFirefoxType = function isFirefoxType(_ref20) {\n  var name = _ref20.name;\n  return name === BrowserTypes.Firefox;\n};\nvar isChromiumType = function isChromiumType(_ref21) {\n  var name = _ref21.name;\n  return name === BrowserTypes.Chromium;\n};\nvar isEdgeType = function isEdgeType(_ref22) {\n  var name = _ref22.name;\n  return name === BrowserTypes.Edge;\n};\nvar isYandexType = function isYandexType(_ref23) {\n  var name = _ref23.name;\n  return name === BrowserTypes.Yandex;\n};\nvar isSafariType = function isSafariType(_ref24) {\n  var name = _ref24.name;\n  return name === BrowserTypes.Safari || name === BrowserTypes.MobileSafari;\n};\nvar isMobileSafariType = function isMobileSafariType(_ref25) {\n  var name = _ref25.name;\n  return name === BrowserTypes.MobileSafari;\n};\nvar isOperaType = function isOperaType(_ref26) {\n  var name = _ref26.name;\n  return name === BrowserTypes.Opera;\n};\nvar isIEType = function isIEType(_ref27) {\n  var name = _ref27.name;\n  return name === BrowserTypes.InternetExplorer || name === BrowserTypes.Ie;\n};\nvar isMIUIType = function isMIUIType(_ref28) {\n  var name = _ref28.name;\n  return name === BrowserTypes.MIUI;\n};\nvar isSamsungBrowserType = function isSamsungBrowserType(_ref29) {\n  var name = _ref29.name;\n  return name === BrowserTypes.SamsungBrowser;\n};\nvar getBrowserFullVersion = function getBrowserFullVersion(_ref30) {\n  var version = _ref30.version;\n  return setDefaults(version);\n};\nvar getBrowserVersion = function getBrowserVersion(_ref31) {\n  var major = _ref31.major;\n  return setDefaults(major);\n};\nvar getBrowserName = function getBrowserName(_ref32) {\n  var name = _ref32.name;\n  return setDefaults(name);\n}; // engine types\n\nvar getEngineName = function getEngineName(_ref33) {\n  var name = _ref33.name;\n  return setDefaults(name);\n};\nvar getEngineVersion = function getEngineVersion(_ref34) {\n  var version = _ref34.version;\n  return setDefaults(version);\n};\nvar isElectronType = function isElectronType() {\n  var nav = getNavigatorInstance();\n  var ua = nav && nav.userAgent && nav.userAgent.toLowerCase();\n  return typeof ua === 'string' ? /electron/.test(ua) : false;\n};\nvar isEdgeChromiumType = function isEdgeChromiumType(ua) {\n  return typeof ua === 'string' && ua.indexOf('Edg/') !== -1;\n};\nvar getIOS13 = function getIOS13() {\n  var nav = getNavigatorInstance();\n  return nav && (/iPad|iPhone|iPod/.test(nav.platform) || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1) && !window.MSStream;\n};\nvar getIPad13 = function getIPad13() {\n  return isIOS13Check('iPad');\n};\nvar getIphone13 = function getIphone13() {\n  return isIOS13Check('iPhone');\n};\nvar getIPod13 = function getIPod13() {\n  return isIOS13Check('iPod');\n};\nvar getUseragent = function getUseragent(userAg) {\n  return setDefaults(userAg);\n};\n\nfunction buildSelectorsObject(options) {\n  var _ref = options ? options : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      os = _ref.os,\n      engine = _ref.engine,\n      ua = _ref.ua;\n\n  return {\n    isSmartTV: isSmartTVType(device),\n    isConsole: isConsoleType(device),\n    isWearable: isWearableType(device),\n    isEmbedded: isEmbeddedType(device),\n    isMobileSafari: isMobileSafariType(browser) || getIPad13(),\n    isChromium: isChromiumType(browser),\n    isMobile: isMobileAndTabletType(device) || getIPad13(),\n    isMobileOnly: isMobileType(device),\n    isTablet: isTabletType(device) || getIPad13(),\n    isBrowser: isBrowserType(device),\n    isDesktop: isBrowserType(device),\n    isAndroid: isAndroidType(os),\n    isWinPhone: isWinPhoneType(os),\n    isIOS: isIOSType(os) || getIPad13(),\n    isChrome: isChromeType(browser),\n    isFirefox: isFirefoxType(browser),\n    isSafari: isSafariType(browser),\n    isOpera: isOperaType(browser),\n    isIE: isIEType(browser),\n    osVersion: getOsVersion(os),\n    osName: getOsName(os),\n    fullBrowserVersion: getBrowserFullVersion(browser),\n    browserVersion: getBrowserVersion(browser),\n    browserName: getBrowserName(browser),\n    mobileVendor: getMobileVendor(device),\n    mobileModel: getMobileModel(device),\n    engineName: getEngineName(engine),\n    engineVersion: getEngineVersion(engine),\n    getUA: getUseragent(ua),\n    isEdge: isEdgeType(browser) || isEdgeChromiumType(ua),\n    isYandex: isYandexType(browser),\n    deviceType: getDeviceType(device),\n    isIOS13: getIOS13(),\n    isIPad13: getIPad13(),\n    isIPhone13: getIphone13(),\n    isIPod13: getIPod13(),\n    isElectron: isElectronType(),\n    isEdgeChromium: isEdgeChromiumType(ua),\n    isLegacyEdge: isEdgeType(browser) && !isEdgeChromiumType(ua),\n    isWindows: isWindowsType(os),\n    isMacOs: isMacOsType(os),\n    isMIUI: isMIUIType(browser),\n    isSamsungBrowser: isSamsungBrowserType(browser)\n  };\n}\n\nvar isSmartTV = isSmartTVType(device);\nvar isConsole = isConsoleType(device);\nvar isWearable = isWearableType(device);\nvar isEmbedded = isEmbeddedType(device);\nvar isMobileSafari = isMobileSafariType(browser) || getIPad13();\nvar isChromium = isChromiumType(browser);\nvar isMobile = isMobileAndTabletType(device) || getIPad13();\nvar isMobileOnly = isMobileType(device);\nvar isTablet = isTabletType(device) || getIPad13();\nvar isBrowser = isBrowserType(device);\nvar isDesktop = isBrowserType(device);\nvar isAndroid = isAndroidType(os);\nvar isWinPhone = isWinPhoneType(os);\nvar isIOS = isIOSType(os) || getIPad13();\nvar isChrome = isChromeType(browser);\nvar isFirefox = isFirefoxType(browser);\nvar isSafari = isSafariType(browser);\nvar isOpera = isOperaType(browser);\nvar isIE = isIEType(browser);\nvar osVersion = getOsVersion(os);\nvar osName = getOsName(os);\nvar fullBrowserVersion = getBrowserFullVersion(browser);\nvar browserVersion = getBrowserVersion(browser);\nvar browserName = getBrowserName(browser);\nvar mobileVendor = getMobileVendor(device);\nvar mobileModel = getMobileModel(device);\nvar engineName = getEngineName(engine);\nvar engineVersion = getEngineVersion(engine);\nvar getUA = getUseragent(ua);\nvar isEdge = isEdgeType(browser) || isEdgeChromiumType(ua);\nvar isYandex = isYandexType(browser);\nvar deviceType = getDeviceType(device);\nvar isIOS13 = getIOS13();\nvar isIPad13 = getIPad13();\nvar isIPhone13 = getIphone13();\nvar isIPod13 = getIPod13();\nvar isElectron = isElectronType();\nvar isEdgeChromium = isEdgeChromiumType(ua);\nvar isLegacyEdge = isEdgeType(browser) && !isEdgeChromiumType(ua);\nvar isWindows = isWindowsType(os);\nvar isMacOs = isMacOsType(os);\nvar isMIUI = isMIUIType(browser);\nvar isSamsungBrowser = isSamsungBrowserType(browser);\nvar getSelectorsByUserAgent = function getSelectorsByUserAgent(userAgent) {\n  if (!userAgent || typeof userAgent !== 'string') {\n    console.error('No valid user agent string was provided');\n    return;\n  }\n\n  var _UAHelper$parseUserAg = parseUserAgent(userAgent),\n      device = _UAHelper$parseUserAg.device,\n      browser = _UAHelper$parseUserAg.browser,\n      os = _UAHelper$parseUserAg.os,\n      engine = _UAHelper$parseUserAg.engine,\n      ua = _UAHelper$parseUserAg.ua;\n\n  return buildSelectorsObject({\n    device: device,\n    browser: browser,\n    os: os,\n    engine: engine,\n    ua: ua\n  });\n};\n\nvar AndroidView = function AndroidView(_ref) {\n  var renderWithFragment = _ref.renderWithFragment,\n      children = _ref.children,\n      props = _objectWithoutProperties(_ref, [\"renderWithFragment\", \"children\"]);\n\n  return isAndroid ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar BrowserView = function BrowserView(_ref2) {\n  var renderWithFragment = _ref2.renderWithFragment,\n      children = _ref2.children,\n      props = _objectWithoutProperties(_ref2, [\"renderWithFragment\", \"children\"]);\n\n  return isBrowser ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IEView = function IEView(_ref3) {\n  var renderWithFragment = _ref3.renderWithFragment,\n      children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, [\"renderWithFragment\", \"children\"]);\n\n  return isIE ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IOSView = function IOSView(_ref4) {\n  var renderWithFragment = _ref4.renderWithFragment,\n      children = _ref4.children,\n      props = _objectWithoutProperties(_ref4, [\"renderWithFragment\", \"children\"]);\n\n  return isIOS ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileView = function MobileView(_ref5) {\n  var renderWithFragment = _ref5.renderWithFragment,\n      children = _ref5.children,\n      props = _objectWithoutProperties(_ref5, [\"renderWithFragment\", \"children\"]);\n\n  return isMobile ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar TabletView = function TabletView(_ref6) {\n  var renderWithFragment = _ref6.renderWithFragment,\n      children = _ref6.children,\n      props = _objectWithoutProperties(_ref6, [\"renderWithFragment\", \"children\"]);\n\n  return isTablet ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WinPhoneView = function WinPhoneView(_ref7) {\n  var renderWithFragment = _ref7.renderWithFragment,\n      children = _ref7.children,\n      props = _objectWithoutProperties(_ref7, [\"renderWithFragment\", \"children\"]);\n\n  return isWinPhone ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileOnlyView = function MobileOnlyView(_ref8) {\n  var renderWithFragment = _ref8.renderWithFragment,\n      children = _ref8.children,\n      viewClassName = _ref8.viewClassName,\n      style = _ref8.style,\n      props = _objectWithoutProperties(_ref8, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\"]);\n\n  return isMobileOnly ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar SmartTVView = function SmartTVView(_ref9) {\n  var renderWithFragment = _ref9.renderWithFragment,\n      children = _ref9.children,\n      props = _objectWithoutProperties(_ref9, [\"renderWithFragment\", \"children\"]);\n\n  return isSmartTV ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar ConsoleView = function ConsoleView(_ref10) {\n  var renderWithFragment = _ref10.renderWithFragment,\n      children = _ref10.children,\n      props = _objectWithoutProperties(_ref10, [\"renderWithFragment\", \"children\"]);\n\n  return isConsole ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WearableView = function WearableView(_ref11) {\n  var renderWithFragment = _ref11.renderWithFragment,\n      children = _ref11.children,\n      props = _objectWithoutProperties(_ref11, [\"renderWithFragment\", \"children\"]);\n\n  return isWearable ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar CustomView = function CustomView(_ref12) {\n  var renderWithFragment = _ref12.renderWithFragment,\n      children = _ref12.children,\n      viewClassName = _ref12.viewClassName,\n      style = _ref12.style,\n      condition = _ref12.condition,\n      props = _objectWithoutProperties(_ref12, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\", \"condition\"]);\n\n  return condition ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\n\nfunction withOrientationChange(WrappedComponent) {\n  return /*#__PURE__*/function (_React$Component) {\n    _inherits(_class, _React$Component);\n\n    function _class(props) {\n      var _this;\n\n      _classCallCheck(this, _class);\n\n      _this = _possibleConstructorReturn(this, _getPrototypeOf(_class).call(this, props));\n      _this.isEventListenerAdded = false;\n      _this.handleOrientationChange = _this.handleOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onOrientationChange = _this.onOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onPageLoad = _this.onPageLoad.bind(_assertThisInitialized(_this));\n      _this.state = {\n        isLandscape: false,\n        isPortrait: false\n      };\n      return _this;\n    }\n\n    _createClass(_class, [{\n      key: \"handleOrientationChange\",\n      value: function handleOrientationChange() {\n        if (!this.isEventListenerAdded) {\n          this.isEventListenerAdded = true;\n        }\n\n        var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n        this.setState({\n          isPortrait: orientation === 0,\n          isLandscape: orientation === 90\n        });\n      }\n    }, {\n      key: \"onOrientationChange\",\n      value: function onOrientationChange() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"onPageLoad\",\n      value: function onPageLoad() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n          if (!this.isEventListenerAdded) {\n            this.handleOrientationChange();\n            window.addEventListener(\"load\", this.onPageLoad, false);\n          } else {\n            window.removeEventListener(\"load\", this.onPageLoad, false);\n          }\n\n          window.addEventListener(\"resize\", this.onOrientationChange, false);\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        window.removeEventListener(\"resize\", this.onOrientationChange, false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return React__default.createElement(WrappedComponent, _extends({}, this.props, {\n          isLandscape: this.state.isLandscape,\n          isPortrait: this.state.isPortrait\n        }));\n      }\n    }]);\n\n    return _class;\n  }(React__default.Component);\n}\n\nfunction useMobileOrientation() {\n  var _useState = React.useState(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    return {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n\n  var handleOrientationChange = React.useCallback(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    var next = {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n    state.orientation !== next.orientation && setState(next);\n  }, [state.orientation]);\n  React.useEffect(function () {\n    if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n      handleOrientationChange();\n      window.addEventListener(\"load\", handleOrientationChange, false);\n      window.addEventListener(\"resize\", handleOrientationChange, false);\n    }\n\n    return function () {\n      window.removeEventListener(\"resize\", handleOrientationChange, false);\n      window.removeEventListener(\"load\", handleOrientationChange, false);\n    };\n  }, [handleOrientationChange]);\n  return state;\n}\n\nfunction useDeviceData(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  return parseUserAgent(hookUserAgent);\n}\n\nfunction useDeviceSelectors(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  var deviceData = useDeviceData(hookUserAgent);\n  var selectors = buildSelectorsObject(deviceData);\n  return [selectors, deviceData];\n}\n\nexports.AndroidView = AndroidView;\nexports.BrowserTypes = BrowserTypes;\nexports.BrowserView = BrowserView;\nexports.ConsoleView = ConsoleView;\nexports.CustomView = CustomView;\nexports.IEView = IEView;\nexports.IOSView = IOSView;\nexports.MobileOnlyView = MobileOnlyView;\nexports.MobileView = MobileView;\nexports.OsTypes = OsTypes;\nexports.SmartTVView = SmartTVView;\nexports.TabletView = TabletView;\nexports.WearableView = WearableView;\nexports.WinPhoneView = WinPhoneView;\nexports.browserName = browserName;\nexports.browserVersion = browserVersion;\nexports.deviceDetect = deviceDetect;\nexports.deviceType = deviceType;\nexports.engineName = engineName;\nexports.engineVersion = engineVersion;\nexports.fullBrowserVersion = fullBrowserVersion;\nexports.getSelectorsByUserAgent = getSelectorsByUserAgent;\nexports.getUA = getUA;\nexports.isAndroid = isAndroid;\nexports.isBrowser = isBrowser;\nexports.isChrome = isChrome;\nexports.isChromium = isChromium;\nexports.isConsole = isConsole;\nexports.isDesktop = isDesktop;\nexports.isEdge = isEdge;\nexports.isEdgeChromium = isEdgeChromium;\nexports.isElectron = isElectron;\nexports.isEmbedded = isEmbedded;\nexports.isFirefox = isFirefox;\nexports.isIE = isIE;\nexports.isIOS = isIOS;\nexports.isIOS13 = isIOS13;\nexports.isIPad13 = isIPad13;\nexports.isIPhone13 = isIPhone13;\nexports.isIPod13 = isIPod13;\nexports.isLegacyEdge = isLegacyEdge;\nexports.isMIUI = isMIUI;\nexports.isMacOs = isMacOs;\nexports.isMobile = isMobile;\nexports.isMobileOnly = isMobileOnly;\nexports.isMobileSafari = isMobileSafari;\nexports.isOpera = isOpera;\nexports.isSafari = isSafari;\nexports.isSamsungBrowser = isSamsungBrowser;\nexports.isSmartTV = isSmartTV;\nexports.isTablet = isTablet;\nexports.isWearable = isWearable;\nexports.isWinPhone = isWinPhone;\nexports.isWindows = isWindows;\nexports.isYandex = isYandex;\nexports.mobileModel = mobileModel;\nexports.mobileVendor = mobileVendor;\nexports.osName = osName;\nexports.osVersion = osVersion;\nexports.parseUserAgent = parseUserAgent;\nexports.setUserAgent = setUserAgent;\nexports.useDeviceData = useDeviceData;\nexports.useDeviceSelectors = useDeviceSelectors;\nexports.useMobileOrientation = useMobileOrientation;\nexports.withOrientationChange = withOrientationChange;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAASA,SAAOC,YAAU;AAAC;AAAa,UAAI,aAAW,UAAS,QAAM,IAAG,UAAQ,KAAI,YAAU,YAAW,aAAW,aAAY,WAAS,UAAS,WAAS,UAAS,QAAM,SAAQ,QAAM,SAAQ,OAAK,QAAO,OAAK,QAAO,SAAO,UAAS,UAAQ,WAAU,eAAa,gBAAe,UAAQ,WAAU,SAAO,UAAS,SAAO,UAAS,UAAQ,WAAU,WAAS,YAAW,WAAS,YAAW,gBAAc;AAAI,UAAI,SAAO,UAAS,QAAM,SAAQ,OAAK,QAAO,aAAW,cAAa,UAAQ,WAAU,SAAO,UAAS,OAAK,QAAO,UAAQ,WAAU,SAAO,UAAS,SAAO,UAAS,KAAG,MAAK,YAAU,aAAY,WAAS,YAAW,QAAM,SAAQ,UAAQ,WAAU,QAAM,SAAQ,OAAK,QAAO,SAAO,UAAS,QAAM,SAAQ,WAAS,YAAW,cAAY,eAAc,SAAO;AAAS,UAAI,SAAO,SAASC,UAAQ,YAAW;AAAC,YAAI,gBAAc,CAAC;AAAE,iBAAQ,KAAKA,UAAQ;AAAC,cAAG,WAAW,CAAC,KAAG,WAAW,CAAC,EAAE,SAAO,MAAI,GAAE;AAAC,0BAAc,CAAC,IAAE,WAAW,CAAC,EAAE,OAAOA,SAAQ,CAAC,CAAC;AAAA,UAAC,OAAK;AAAC,0BAAc,CAAC,IAAEA,SAAQ,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAa,GAAE,YAAU,SAAS,KAAI;AAAC,YAAI,QAAM,CAAC;AAAE,iBAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,KAAI;AAAC,gBAAM,IAAI,CAAC,EAAE,YAAY,CAAC,IAAE,IAAI,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAK,GAAE,MAAI,SAAS,MAAK,MAAK;AAAC,eAAO,OAAO,SAAO,WAAS,SAAS,IAAI,EAAE,QAAQ,SAAS,IAAI,CAAC,MAAI,KAAG;AAAA,MAAK,GAAE,WAAS,SAAS,KAAI;AAAC,eAAO,IAAI,YAAY;AAAA,MAAC,GAAE,WAAS,SAAS,SAAQ;AAAC,eAAO,OAAO,YAAU,WAAS,QAAQ,QAAQ,YAAW,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,IAAED;AAAA,MAAS,GAAE,OAAK,SAAS,KAAI,KAAI;AAAC,YAAG,OAAO,QAAM,UAAS;AAAC,gBAAI,IAAI,QAAQ,UAAS,KAAK;AAAE,iBAAO,OAAO,QAAM,aAAW,MAAI,IAAI,UAAU,GAAE,aAAa;AAAA,QAAC;AAAA,MAAC;AAAE,UAAI,YAAU,SAAS,IAAG,QAAO;AAAC,YAAI,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,SAAQ;AAAM,eAAM,IAAE,OAAO,UAAQ,CAAC,SAAQ;AAAC,cAAI,QAAM,OAAO,CAAC,GAAE,QAAM,OAAO,IAAE,CAAC;AAAE,cAAE,IAAE;AAAE,iBAAM,IAAE,MAAM,UAAQ,CAAC,SAAQ;AAAC,gBAAG,CAAC,MAAM,CAAC,GAAE;AAAC;AAAA,YAAK;AAAC,sBAAQ,MAAM,GAAG,EAAE,KAAK,EAAE;AAAE,gBAAG,CAAC,CAAC,SAAQ;AAAC,mBAAI,IAAE,GAAE,IAAE,MAAM,QAAO,KAAI;AAAC,wBAAM,QAAQ,EAAE,CAAC;AAAE,oBAAE,MAAM,CAAC;AAAE,oBAAG,OAAO,MAAI,YAAU,EAAE,SAAO,GAAE;AAAC,sBAAG,EAAE,WAAS,GAAE;AAAC,wBAAG,OAAO,EAAE,CAAC,KAAG,WAAU;AAAC,2BAAK,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,EAAE,KAAK,MAAK,KAAK;AAAA,oBAAC,OAAK;AAAC,2BAAK,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC;AAAA,oBAAC;AAAA,kBAAC,WAAS,EAAE,WAAS,GAAE;AAAC,wBAAG,OAAO,EAAE,CAAC,MAAI,aAAW,EAAE,EAAE,CAAC,EAAE,QAAM,EAAE,CAAC,EAAE,OAAM;AAAC,2BAAK,EAAE,CAAC,CAAC,IAAE,QAAM,EAAE,CAAC,EAAE,KAAK,MAAK,OAAM,EAAE,CAAC,CAAC,IAAEA;AAAA,oBAAS,OAAK;AAAC,2BAAK,EAAE,CAAC,CAAC,IAAE,QAAM,MAAM,QAAQ,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAEA;AAAA,oBAAS;AAAA,kBAAC,WAAS,EAAE,WAAS,GAAE;AAAC,yBAAK,EAAE,CAAC,CAAC,IAAE,QAAM,EAAE,CAAC,EAAE,KAAK,MAAK,MAAM,QAAQ,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC,IAAEA;AAAA,kBAAS;AAAA,gBAAC,OAAK;AAAC,uBAAK,CAAC,IAAE,QAAM,QAAMA;AAAA,gBAAS;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAC,eAAG;AAAA,QAAC;AAAA,MAAC,GAAE,YAAU,SAAS,KAAI,KAAI;AAAC,iBAAQ,KAAK,KAAI;AAAC,cAAG,OAAO,IAAI,CAAC,MAAI,YAAU,IAAI,CAAC,EAAE,SAAO,GAAE;AAAC,qBAAQ,IAAE,GAAE,IAAE,IAAI,CAAC,EAAE,QAAO,KAAI;AAAC,kBAAG,IAAI,IAAI,CAAC,EAAE,CAAC,GAAE,GAAG,GAAE;AAAC,uBAAO,MAAI,UAAQA,aAAU;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC,WAAS,IAAI,IAAI,CAAC,GAAE,GAAG,GAAE;AAAC,mBAAO,MAAI,UAAQA,aAAU;AAAA,UAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAG;AAAE,UAAI,eAAa,EAAC,OAAM,MAAK,KAAI,MAAK,KAAI,MAAK,OAAM,QAAO,SAAQ,QAAO,SAAQ,QAAO,SAAQ,QAAO,KAAI,IAAG,GAAE,oBAAkB,EAAC,IAAG,QAAO,WAAU,UAAS,UAAS,SAAQ,KAAI,UAAS,IAAG,CAAC,UAAS,QAAQ,GAAE,OAAM,UAAS,GAAE,UAAS,GAAE,UAAS,KAAI,UAAS,IAAG,CAAC,UAAS,SAAS,GAAE,IAAG,MAAK;AAAE,UAAI,UAAQ,EAAC,SAAQ,CAAC,CAAC,8BAA8B,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,6BAA6B,GAAE,CAAC,SAAQ,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,6BAA4B,oDAAmD,yCAAyC,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,uBAAuB,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAM,OAAO,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,SAAQ,CAAC,MAAK,KAAK,CAAC,GAAE,CAAC,wDAAwD,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,wBAAuB,+DAA8D,wDAAuD,4BAA2B,gMAA+L,mCAAkC,qBAAqB,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,mDAAmD,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAK,OAAO,CAAC,GAAE,CAAC,gCAA+B,gCAA+B,4BAA4B,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,uBAAuB,GAAE,CAAC,SAAQ,CAAC,MAAK,WAAW,CAAC,GAAE,CAAC,6CAA6C,GAAE,CAAC,SAAQ,CAAC,MAAK,IAAI,CAAC,GAAE,CAAC,kCAAkC,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,uBAAuB,GAAE,CAAC,SAAQ,CAAC,MAAK,kBAAgB,OAAO,CAAC,GAAE,CAAC,yBAAyB,GAAE,CAAC,CAAC,MAAK,QAAO,eAAa,OAAO,GAAE,OAAO,GAAE,CAAC,qBAAqB,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAQ,QAAQ,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAM,QAAQ,CAAC,GAAE,CAAC,wBAAwB,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAS,CAAC,GAAE,CAAC,oBAAoB,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAS,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAM,QAAQ,CAAC,GAAE,CAAC,yBAAyB,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAQ,OAAO,CAAC,GAAE,CAAC,oBAAoB,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,+BAA+B,GAAE,CAAC,CAAC,MAAK,SAAO,OAAO,CAAC,GAAE,CAAC,kDAAkD,GAAE,CAAC,CAAC,MAAK,QAAO,QAAM,OAAO,GAAE,OAAO,GAAE,CAAC,4BAA4B,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAQ,WAAW,CAAC,GAAE,CAAC,6BAA6B,GAAE,CAAC,CAAC,MAAK,MAAK,GAAG,GAAE,OAAO,GAAE,CAAC,wBAAwB,GAAE,CAAC,SAAQ,CAAC,MAAK,gBAAgB,CAAC,GAAE,CAAC,0BAA0B,GAAE,CAAC,CAAC,MAAK,cAAc,GAAE,OAAO,GAAE,CAAC,iCAAgC,kDAAiD,4CAA4C,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,gBAAe,oBAAoB,GAAE,CAAC,IAAI,GAAE,CAAC,6DAA6D,GAAE,CAAC,CAAC,MAAK,QAAQ,GAAE,OAAO,GAAE,CAAC,wBAAuB,wCAAuC,mCAAkC,6BAA4B,6BAA4B,8BAA6B,+CAA+C,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,8BAA8B,GAAE,CAAC,SAAQ,CAAC,MAAK,KAAK,CAAC,GAAE,CAAC,4CAA4C,GAAE,CAAC,SAAQ,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,kCAAkC,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAO,WAAW,CAAC,GAAE,CAAC,6BAA6B,GAAE,CAAC,CAAC,MAAK,SAAO,UAAU,GAAE,OAAO,GAAE,CAAC,yDAAyD,GAAE,CAAC,SAAQ,CAAC,MAAK,aAAW,OAAO,CAAC,GAAE,CAAC,6DAA6D,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,8CAA8C,GAAE,CAAC,SAAQ,CAAC,MAAK,eAAe,CAAC,GAAE,CAAC,oDAAoD,GAAE,CAAC,SAAQ,IAAI,GAAE,CAAC,8CAA8C,GAAE,CAAC,MAAK,CAAC,SAAQ,WAAU,YAAY,CAAC,GAAE,CAAC,4BAA4B,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,sCAAsC,GAAE,CAAC,CAAC,MAAK,UAAU,GAAE,OAAO,GAAE,CAAC,qCAAqC,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAQ,UAAU,CAAC,GAAE,CAAC,8BAA6B,eAAc,oGAAmG,gGAA+F,yBAAwB,4CAA2C,yHAAwH,wBAAuB,oBAAoB,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,sBAAsB,GAAE,CAAC,MAAK,CAAC,SAAQ,gBAAe,EAAE,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,+CAA+C,GAAE,CAAC,CAAC,cAAa,OAAO,CAAC,GAAE,CAAC,cAAc,GAAE,CAAC,CAAC,cAAa,QAAQ,CAAC,GAAE,CAAC,wBAAwB,GAAE,CAAC,CAAC,cAAa,MAAM,CAAC,GAAE,CAAC,kCAAkC,GAAE,CAAC,CAAC,cAAa,OAAO,CAAC,GAAE,CAAC,iCAAiC,GAAE,CAAC,CAAC,cAAa,OAAO,CAAC,GAAE,CAAC,4BAA4B,GAAE,CAAC,CAAC,cAAa,KAAK,CAAC,GAAE,CAAC,wCAAwC,GAAE,CAAC,CAAC,cAAa,QAAO,OAAM,QAAQ,CAAC,GAAE,CAAC,gBAAgB,GAAE,CAAC,CAAC,cAAa,OAAO,CAAC,GAAE,CAAC,yHAAyH,GAAE,CAAC,CAAC,cAAa,QAAQ,CAAC,CAAC,GAAE,QAAO,CAAC,CAAC,iFAAiF,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,0DAAyD,wBAAuB,eAAe,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,0CAA0C,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,8BAA6B,qCAAoC,gCAAgC,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,eAAe,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,CAAC,GAAE,CAAC,+BAA+B,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,6DAA6D,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mCAAkC,oEAAoE,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mDAAkD,0BAAyB,wCAAuC,kDAAiD,6DAA4D,uGAAuG,GAAE,CAAC,CAAC,OAAM,MAAK,GAAG,GAAE,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gDAA+C,4CAA4C,GAAE,CAAC,CAAC,OAAM,MAAK,GAAG,GAAE,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,uBAAsB,iEAAiE,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,0BAAyB,kCAAkC,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iCAAiC,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,kFAAiF,6BAA4B,oDAAoD,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mCAAmC,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,+DAA+D,GAAE,CAAC,OAAM,CAAC,QAAO,EAAE,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,uDAAsD,qDAAoD,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,EAAE,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,qBAAoB,mEAAmE,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sCAAqC,wBAAwB,GAAE,CAAC,CAAC,OAAM,MAAK,GAAG,GAAE,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,cAAc,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,2CAA2C,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,wGAAwG,GAAE,CAAC,OAAM,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,qBAAoB,+BAA+B,GAAE,CAAC,CAAC,OAAM,eAAe,GAAE,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,uCAAsC,wCAAwC,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gBAAe,wCAAuC,8BAA8B,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,+CAA+C,GAAE,CAAC,CAAC,OAAM,SAAQ,eAAe,GAAE,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,8BAA8B,GAAE,CAAC,OAAM,QAAO,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iCAAgC,gBAAgB,GAAE,CAAC,OAAM,CAAC,QAAO,UAAU,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mFAAmF,GAAE,CAAC,OAAM,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,+CAA+C,GAAE,CAAC,OAAM,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,YAAY,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,4CAA2C,qCAAoC,+EAA+E,GAAE,CAAC,QAAO,CAAC,OAAM,MAAK,GAAG,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,qCAAqC,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,+BAA8B,mBAAmB,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gDAAgD,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iHAAgH,oBAAmB,kBAAiB,+BAA8B,2BAA0B,YAAW,uBAAuB,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,4BAA2B,yBAAwB,wCAAuC,wBAAuB,6BAA4B,kCAAiC,mCAAkC,+BAA8B,iCAAgC,iCAAiC,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gBAAgB,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mCAAmC,GAAE,CAAC,OAAM,CAAC,QAAO,WAAW,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,WAAW,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,cAAc,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,eAAe,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,wBAAwB,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,6CAA6C,GAAE,CAAC,OAAM,CAAC,QAAO,gBAAgB,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,OAAM,CAAC,QAAO,UAAU,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,YAAY,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iBAAiB,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iBAAiB,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,oBAAmB,oCAAoC,GAAE,CAAC,CAAC,QAAO,cAAc,GAAE,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,oBAAoB,GAAE,CAAC,OAAM,CAAC,QAAO,UAAU,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,4BAA4B,GAAE,CAAC,OAAM,CAAC,QAAO,UAAU,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,kDAAkD,GAAE,CAAC,CAAC,QAAO,OAAO,GAAE,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,yBAAyB,GAAE,CAAC,CAAC,QAAO,OAAO,GAAE,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,YAAY,GAAE,CAAC,OAAM,CAAC,QAAO,WAAW,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,qCAAqC,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,WAAW,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gBAAgB,GAAE,CAAC,OAAM,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,iBAAiB,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,oBAAoB,GAAE,CAAC,CAAC,OAAM,OAAM,GAAG,GAAE,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,uDAAuD,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,uCAAuC,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,QAAO,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,qBAAqB,GAAE,CAAC,CAAC,OAAM,KAAI,SAAS,GAAE,CAAC,QAAO,OAAO,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,4DAA4D,GAAE,CAAC,CAAC,QAAO,EAAE,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,cAAc,GAAE,CAAC,QAAO,CAAC,OAAM,QAAM,KAAK,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,QAAQ,GAAE,CAAC,CAAC,OAAM,SAAO,MAAM,GAAE,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,2BAA2B,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,wBAAuB,qBAAqB,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,0BAA0B,GAAE,CAAC,OAAM,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,2BAA2B,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,2CAA0C,2DAA2D,GAAE,CAAC,CAAC,QAAO,IAAI,GAAE,CAAC,OAAM,IAAI,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,iDAAiD,GAAE,CAAC,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,WAAU,4BAA4B,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,wBAAwB,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,iCAAiC,GAAE,CAAC,OAAM,CAAC,QAAO,IAAI,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,oCAAoC,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,GAAE,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,gBAAgB,GAAE,CAAC,QAAO,OAAM,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,sCAAsC,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,sBAAsB,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,2BAA2B,GAAE,CAAC,OAAM,CAAC,QAAO,KAAK,GAAE,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,oBAAoB,GAAE,CAAC,OAAM,CAAC,QAAO,QAAQ,GAAE,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,sCAAsC,GAAE,CAAC,QAAO,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,YAAY,GAAE,CAAC,OAAM,CAAC,QAAO,MAAM,GAAE,CAAC,MAAK,QAAQ,CAAC,GAAE,CAAC,gEAAgE,GAAE,CAAC,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,6DAA6D,GAAE,CAAC,OAAM,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,8CAA8C,GAAE,CAAC,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gEAAgE,GAAE,CAAC,CAAC,MAAK,MAAM,CAAC,GAAE,CAAC,gCAAgC,GAAE,CAAC,OAAM,CAAC,QAAO,SAAS,CAAC,CAAC,GAAE,QAAO,CAAC,CAAC,4BAA4B,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAK,MAAM,CAAC,GAAE,CAAC,2CAA2C,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,wBAAuB,uEAAsE,2BAA0B,0CAAyC,+BAA8B,aAAa,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,+BAA+B,GAAE,CAAC,SAAQ,IAAI,CAAC,GAAE,IAAG,CAAC,CAAC,iCAAiC,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,uDAAuD,GAAE,CAAC,MAAK,CAAC,SAAQ,WAAU,iBAAiB,CAAC,GAAE,CAAC,2BAA0B,4CAA2C,sCAAsC,GAAE,CAAC,CAAC,SAAQ,WAAU,iBAAiB,GAAE,CAAC,MAAK,SAAS,CAAC,GAAE,CAAC,uDAAsD,6CAA4C,sBAAsB,GAAE,CAAC,CAAC,SAAQ,MAAK,GAAG,GAAE,CAAC,MAAK,KAAK,CAAC,GAAE,CAAC,2BAA0B,uCAAuC,GAAE,CAAC,CAAC,MAAK,MAAM,GAAE,CAAC,SAAQ,MAAK,GAAG,CAAC,GAAE,CAAC,gDAAgD,GAAE,CAAC,SAAQ,IAAI,GAAE,CAAC,gFAA+E,+BAA8B,gCAA+B,gBAAgB,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,YAAY,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAU,CAAC,GAAE,CAAC,2DAA2D,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAS,CAAC,GAAE,CAAC,iFAAiF,GAAE,CAAC,SAAQ,CAAC,MAAK,UAAQ,KAAK,CAAC,GAAE,CAAC,mBAAkB,sCAAsC,GAAE,CAAC,SAAQ,CAAC,MAAK,OAAO,CAAC,GAAE,CAAC,sCAAsC,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAS,CAAC,GAAE,CAAC,mBAAmB,GAAE,CAAC,SAAQ,CAAC,MAAK,SAAO,MAAM,CAAC,GAAE,CAAC,kCAAkC,GAAE,CAAC,CAAC,MAAK,WAAW,GAAE,OAAO,GAAE,CAAC,sBAAqB,kBAAiB,4BAA2B,oDAAmD,4BAA2B,yCAAwC,0BAAyB,6BAA4B,+SAA8S,4BAA2B,qBAAoB,8EAA6E,gBAAgB,GAAE,CAAC,MAAK,OAAO,GAAE,CAAC,uBAAuB,GAAE,CAAC,CAAC,MAAK,SAAS,GAAE,OAAO,GAAE,CAAC,uCAAsC,mCAAkC,oEAAmE,oBAAoB,GAAE,CAAC,MAAK,OAAO,CAAC,EAAC;AAAE,UAAI,WAAS,SAAS,IAAG,YAAW;AAAC,YAAG,OAAO,OAAK,UAAS;AAAC,uBAAW;AAAG,eAAGA;AAAA,QAAS;AAAC,YAAG,EAAE,gBAAgB,WAAU;AAAC,iBAAO,IAAI,SAAS,IAAG,UAAU,EAAE,UAAU;AAAA,QAAC;AAAC,YAAI,aAAW,OAAOD,YAAS,cAAYA,QAAO,YAAUA,QAAO,YAAUC;AAAU,YAAI,MAAI,OAAK,cAAY,WAAW,YAAU,WAAW,YAAU;AAAO,YAAI,QAAM,cAAY,WAAW,gBAAc,WAAW,gBAAcA;AAAU,YAAI,UAAQ,aAAW,OAAO,SAAQ,UAAU,IAAE;AAAQ,YAAI,aAAW,cAAY,WAAW,aAAW;AAAI,aAAK,aAAW,WAAU;AAAC,cAAI,WAAS,CAAC;AAAE,mBAAS,IAAI,IAAEA;AAAU,mBAAS,OAAO,IAAEA;AAAU,oBAAU,KAAK,UAAS,KAAI,QAAQ,OAAO;AAAE,mBAAS,KAAK,IAAE,SAAS,SAAS,OAAO,CAAC;AAAE,cAAG,cAAY,cAAY,WAAW,SAAO,OAAO,WAAW,MAAM,WAAS,WAAU;AAAC,qBAAS,IAAI,IAAE;AAAA,UAAO;AAAC,iBAAO;AAAA,QAAQ;AAAE,aAAK,SAAO,WAAU;AAAC,cAAI,OAAK,CAAC;AAAE,eAAK,YAAY,IAAEA;AAAU,oBAAU,KAAK,MAAK,KAAI,QAAQ,GAAG;AAAE,iBAAO;AAAA,QAAI;AAAE,aAAK,YAAU,WAAU;AAAC,cAAI,UAAQ,CAAC;AAAE,kBAAQ,MAAM,IAAEA;AAAU,kBAAQ,KAAK,IAAEA;AAAU,kBAAQ,IAAI,IAAEA;AAAU,oBAAU,KAAK,SAAQ,KAAI,QAAQ,MAAM;AAAE,cAAG,cAAY,CAAC,QAAQ,IAAI,KAAG,SAAO,MAAM,QAAO;AAAC,oBAAQ,IAAI,IAAE;AAAA,UAAM;AAAC,cAAG,cAAY,QAAQ,KAAK,KAAG,eAAa,cAAY,OAAO,WAAW,eAAa,cAAY,WAAW,kBAAgB,WAAW,iBAAe,GAAE;AAAC,oBAAQ,KAAK,IAAE;AAAO,oBAAQ,IAAI,IAAE;AAAA,UAAM;AAAC,iBAAO;AAAA,QAAO;AAAE,aAAK,YAAU,WAAU;AAAC,cAAI,UAAQ,CAAC;AAAE,kBAAQ,IAAI,IAAEA;AAAU,kBAAQ,OAAO,IAAEA;AAAU,oBAAU,KAAK,SAAQ,KAAI,QAAQ,MAAM;AAAE,iBAAO;AAAA,QAAO;AAAE,aAAK,QAAM,WAAU;AAAC,cAAI,MAAI,CAAC;AAAE,cAAI,IAAI,IAAEA;AAAU,cAAI,OAAO,IAAEA;AAAU,oBAAU,KAAK,KAAI,KAAI,QAAQ,EAAE;AAAE,cAAG,cAAY,CAAC,IAAI,IAAI,KAAG,SAAO,MAAM,YAAU,WAAU;AAAC,gBAAI,IAAI,IAAE,MAAM,SAAS,QAAQ,cAAa,WAAW,EAAE,QAAQ,UAAS,MAAM;AAAA,UAAC;AAAC,iBAAO;AAAA,QAAG;AAAE,aAAK,YAAU,WAAU;AAAC,iBAAM,EAAC,IAAG,KAAK,MAAM,GAAE,SAAQ,KAAK,WAAW,GAAE,QAAO,KAAK,UAAU,GAAE,IAAG,KAAK,MAAM,GAAE,QAAO,KAAK,UAAU,GAAE,KAAI,KAAK,OAAO,EAAC;AAAA,QAAC;AAAE,aAAK,QAAM,WAAU;AAAC,iBAAO;AAAA,QAAG;AAAE,aAAK,QAAM,SAASE,KAAG;AAAC,gBAAI,OAAOA,QAAK,YAAUA,IAAG,SAAO,gBAAc,KAAKA,KAAG,aAAa,IAAEA;AAAG,iBAAO;AAAA,QAAI;AAAE,aAAK,MAAM,GAAG;AAAE,eAAO;AAAA,MAAI;AAAE,eAAS,UAAQ;AAAW,eAAS,UAAQ,UAAU,CAAC,MAAK,SAAQ,KAAK,CAAC;AAAE,eAAS,MAAI,UAAU,CAAC,YAAY,CAAC;AAAE,eAAS,SAAO,UAAU,CAAC,OAAM,QAAO,MAAK,SAAQ,QAAO,SAAQ,QAAO,UAAS,QAAQ,CAAC;AAAE,eAAS,SAAO,SAAS,KAAG,UAAU,CAAC,MAAK,OAAO,CAAC;AAAE,UAAG,OAAO,YAAU,YAAW;AAAC,YAAG,OAAO,WAAS,cAAY,OAAO,SAAQ;AAAC,oBAAQ,OAAO,UAAQ;AAAA,QAAQ;AAAC,gBAAQ,WAAS;AAAA,MAAQ,OAAK;AAAC,YAAG,OAAO,WAAS,aAAW,OAAO,KAAI;AAAC,iBAAO,WAAU;AAAC,mBAAO;AAAA,UAAQ,CAAC;AAAA,QAAC,WAAS,OAAOH,YAAS,YAAW;AAAC,UAAAA,QAAO,WAAS;AAAA,QAAQ;AAAA,MAAC;AAAC,UAAI,IAAE,OAAOA,YAAS,eAAaA,QAAO,UAAQA,QAAO;AAAO,UAAG,KAAG,CAAC,EAAE,IAAG;AAAC,YAAI,SAAO,IAAI;AAAS,UAAE,KAAG,OAAO,UAAU;AAAE,UAAE,GAAG,MAAI,WAAU;AAAC,iBAAO,OAAO,MAAM;AAAA,QAAC;AAAE,UAAE,GAAG,MAAI,SAAS,IAAG;AAAC,iBAAO,MAAM,EAAE;AAAE,cAAI,SAAO,OAAO,UAAU;AAAE,mBAAQ,QAAQ,QAAO;AAAC,cAAE,GAAG,IAAI,IAAE,OAAO,IAAI;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,GAAG,OAAO,WAAS,WAAS,SAAO,OAAI;AAAA;AAAA;;;ACHj9qB;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,aAAS,gBAAiB,IAAI;AAAE,aAAQ,MAAO,OAAO,OAAO,YAAa,aAAa,KAAM,GAAG,SAAS,IAAI;AAAA,IAAI;AAEjH,QAAI,QAAQ;AACZ,QAAI,iBAAiB,gBAAgB,KAAK;AAE1C,QAAI,WAAW;AAEf,QAAI,mBAAmB,IAAI,SAAS;AACpC,QAAI,UAAU,iBAAiB,WAAW;AAC1C,QAAI,MAAM,iBAAiB,OAAO;AAClC,QAAI,SAAS,iBAAiB,UAAU;AACxC,QAAI,SAAS,iBAAiB,UAAU;AACxC,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,KAAK,iBAAiB,MAAM;AAChC,QAAI,QAAQ,SAASI,OAAM,iBAAiB;AAC1C,aAAO,iBAAiB,MAAM,eAAe;AAAA,IAC/C;AACA,QAAI,iBAAiB,SAASC,gBAAe,WAAW;AACtD,UAAI,CAAC,WAAW;AACd,gBAAQ,MAAM,kCAAkC;AAChD;AAAA,MACF;AAEA,UAAI,oBAAoB,IAAI,SAAS,SAAS;AAC9C,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,SAAS,kBAAkB,WAAW;AAAA,QACtC,KAAK,kBAAkB,OAAO;AAAA,QAC9B,QAAQ,kBAAkB,UAAU;AAAA,QACpC,QAAQ,kBAAkB,UAAU;AAAA,QACpC,IAAI,kBAAkB,MAAM;AAAA,QAC5B,IAAI,kBAAkB,MAAM;AAAA,QAC5B,cAAc,SAASC,cAAa,iBAAiB;AACnD,iBAAO,kBAAkB,MAAM,eAAe;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAwB,OAAO,OAAO;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,aAAS,QAAQ,QAAQ,gBAAgB;AACvC,UAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,UAAI,OAAO,uBAAuB;AAChC,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,YAAI,gBAAgB;AAClB,oBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,mBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,UACtD,CAAC;AAAA,QACH;AAEA,aAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAC/B;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,QAAQ;AAC9B,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,YAAI,IAAI,GAAG;AACT,kBAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,4BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,UAC1C,CAAC;AAAA,QACH,WAAW,OAAO,2BAA2B;AAC3C,iBAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,QAC1E,OAAO;AACL,kBAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,mBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,UACjF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,QAAQ,KAAK;AACpB;AAEA,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,kBAAU,SAAUC,MAAK;AACvB,iBAAO,OAAOA;AAAA,QAChB;AAAA,MACF,OAAO;AACL,kBAAU,SAAUA,MAAK;AACvB,iBAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,QAC3H;AAAA,MACF;AAEA,aAAO,QAAQ,GAAG;AAAA,IACpB;AAEA,aAAS,gBAAgB,UAAU,aAAa;AAC9C,UAAI,EAAE,oBAAoB,cAAc;AACtC,cAAM,IAAI,UAAU,mCAAmC;AAAA,MACzD;AAAA,IACF;AAEA,aAAS,kBAAkB,QAAQ,OAAO;AACxC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,aAAa,MAAM,CAAC;AACxB,mBAAW,aAAa,WAAW,cAAc;AACjD,mBAAW,eAAe;AAC1B,YAAI,WAAW;AAAY,qBAAW,WAAW;AACjD,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MAC1D;AAAA,IACF;AAEA,aAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,UAAI;AAAY,0BAAkB,YAAY,WAAW,UAAU;AACnE,UAAI;AAAa,0BAAkB,aAAa,WAAW;AAC3D,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,UAAI,OAAO,KAAK;AACd,eAAO,eAAe,KAAK,KAAK;AAAA,UAC9B;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,OAAO;AACL,YAAI,GAAG,IAAI;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW;AAClB,iBAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,SAAS,UAAU,CAAC;AAExB,mBAAS,OAAO,QAAQ;AACtB,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,qBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACvC;AAEA,aAAS,UAAU,UAAU,YAAY;AACvC,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,cAAM,IAAI,UAAU,oDAAoD;AAAA,MAC1E;AAEA,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,QACrE,aAAa;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,MACF,CAAC;AACD,UAAI;AAAY,wBAAgB,UAAU,UAAU;AAAA,IACtD;AAEA,aAAS,gBAAgB,GAAG;AAC1B,wBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASC,iBAAgBC,IAAG;AAC5F,eAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,MAC/C;AACA,aAAO,gBAAgB,CAAC;AAAA,IAC1B;AAEA,aAAS,gBAAgB,GAAG,GAAG;AAC7B,wBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AACxE,QAAAF,GAAE,YAAYE;AACd,eAAOF;AAAA,MACT;AAEA,aAAO,gBAAgB,GAAG,CAAC;AAAA,IAC7B;AAEA,aAAS,8BAA8B,QAAQ,UAAU;AACvD,UAAI,UAAU;AAAM,eAAO,CAAC;AAC5B,UAAI,SAAS,CAAC;AACd,UAAI,aAAa,OAAO,KAAK,MAAM;AACnC,UAAI,KAAK;AAET,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,cAAM,WAAW,CAAC;AAClB,YAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,eAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MAC1B;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,yBAAyB,QAAQ,UAAU;AAClD,UAAI,UAAU;AAAM,eAAO,CAAC;AAE5B,UAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,UAAI,KAAK;AAET,UAAI,OAAO,uBAAuB;AAChC,YAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,aAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,gBAAM,iBAAiB,CAAC;AACxB,cAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,cAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAC9D,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,uBAAuB,MAAM;AACpC,UAAI,SAAS,QAAQ;AACnB,cAAM,IAAI,eAAe,2DAA2D;AAAA,MACtF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,2BAA2B,MAAM,MAAM;AAC9C,UAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,eAAO;AAAA,MACT,WAAW,SAAS,QAAQ;AAC1B,cAAM,IAAI,UAAU,0DAA0D;AAAA,MAChF;AAEA,aAAO,uBAAuB,IAAI;AAAA,IACpC;AAEA,aAAS,eAAe,KAAK,GAAG;AAC9B,aAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAAA,IAC1H;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,QAAQ,GAAG;AAAG,eAAO;AAAA,IACjC;AAEA,aAAS,sBAAsB,KAAK,GAAG;AACrC,UAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAEvG,UAAI,MAAM;AAAM;AAChB,UAAI,OAAO,CAAC;AACZ,UAAI,KAAK;AACT,UAAI,KAAK;AAET,UAAI,IAAI;AAER,UAAI;AACF,aAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,eAAK,KAAK,GAAG,KAAK;AAElB,cAAI,KAAK,KAAK,WAAW;AAAG;AAAA,QAC9B;AAAA,MACF,SAAS,KAAK;AACZ,aAAK;AACL,aAAK;AAAA,MACP,UAAE;AACA,YAAI;AACF,cAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;AAAM,eAAG,QAAQ,EAAE;AAAA,QAChD,UAAE;AACA,cAAI;AAAI,kBAAM;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,4BAA4B,GAAG,QAAQ;AAC9C,UAAI,CAAC;AAAG;AACR,UAAI,OAAO,MAAM;AAAU,eAAO,kBAAkB,GAAG,MAAM;AAC7D,UAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,UAAI,MAAM,YAAY,EAAE;AAAa,YAAI,EAAE,YAAY;AACvD,UAAI,MAAM,SAAS,MAAM;AAAO,eAAO,MAAM,KAAK,CAAC;AACnD,UAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,eAAO,kBAAkB,GAAG,MAAM;AAAA,IACjH;AAEA,aAAS,kBAAkB,KAAK,KAAK;AACnC,UAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,cAAM,IAAI;AAE/C,eAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,aAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB;AAC1B,YAAM,IAAI,UAAU,2IAA2I;AAAA,IACjK;AAEA,QAAI,cAAc;AAAA,MAChB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,IACX;AACA,QAAI,eAAe;AAAA,MACjB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AACA,QAAI,UAAU;AAAA,MACZ,KAAK;AAAA,MACL,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB;AAAA,MACvB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AAEA,QAAI,kBAAkB,SAASG,iBAAgB,MAAM;AACnD,cAAQ,MAAM;AAAA,QACZ,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,YAAY;AAAA,UACd;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QAEF,KAAK,YAAY;AACf,iBAAO;AAAA,YACL,YAAY;AAAA,UACd;AAAA,QAEF;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AACA,QAAI,eAAe,SAASN,cAAa,WAAW;AAClD,aAAO,MAAM,SAAS;AAAA,IACxB;AACA,QAAI,cAAc,SAASO,aAAY,GAAG;AACxC,UAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5E,aAAO,IAAI,IAAI;AAAA,IACjB;AACA,QAAI,uBAAuB,SAASC,wBAAuB;AACzD,UAAI,OAAO,WAAW,aAAa;AACjC,YAAI,OAAO,aAAa,WAAW;AACjC,iBAAO,OAAO,aAAa;AAAA,QAC7B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AACA,QAAI,eAAe,SAASC,cAAa,MAAM;AAC7C,UAAI,MAAM,qBAAqB;AAC/B,aAAO,OAAO,IAAI,aAAa,IAAI,SAAS,QAAQ,IAAI,MAAM,MAAM,IAAI,aAAa,cAAc,IAAI,iBAAiB,KAAK,CAAC,OAAO;AAAA,IACvI;AAEA,QAAI,iBAAiB,SAASC,gBAAeC,YAAWC,UAASC,SAAQC,KAAIC,KAAI;AAC/E,aAAO;AAAA,QACL,WAAWJ;AAAA,QACX,qBAAqB,YAAYC,SAAQ,KAAK;AAAA,QAC9C,oBAAoB,YAAYA,SAAQ,OAAO;AAAA,QAC/C,aAAa,YAAYA,SAAQ,IAAI;AAAA,QACrC,YAAY,YAAYC,QAAO,IAAI;AAAA,QACnC,eAAe,YAAYA,QAAO,OAAO;AAAA,QACzC,QAAQ,YAAYC,IAAG,IAAI;AAAA,QAC3B,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,WAAW,YAAYC,GAAE;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,gBAAgB,SAASC,eAAc,MAAMC,SAAQH,KAAIC,KAAI;AAC/D,aAAO,eAAe,CAAC,GAAG,MAAM;AAAA,QAC9B,QAAQ,YAAYE,QAAO,MAAM;AAAA,QACjC,OAAO,YAAYA,QAAO,KAAK;AAAA,QAC/B,IAAI,YAAYH,IAAG,IAAI;AAAA,QACvB,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,IAAI,YAAYC,GAAE;AAAA,MACpB,CAAC;AAAA,IACH;AACA,QAAI,iBAAiB,SAASG,gBAAeC,YAAWN,SAAQC,KAAIC,KAAI;AACtE,aAAO;AAAA,QACL,WAAWI;AAAA,QACX,YAAY,YAAYN,QAAO,IAAI;AAAA,QACnC,eAAe,YAAYA,QAAO,OAAO;AAAA,QACzC,QAAQ,YAAYC,IAAG,IAAI;AAAA,QAC3B,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,WAAW,YAAYC,GAAE;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,iBAAiB,SAASK,gBAAeC,YAAWR,SAAQC,KAAIC,KAAI;AACtE,aAAO;AAAA,QACL,WAAWM;AAAA,QACX,YAAY,YAAYR,QAAO,IAAI;AAAA,QACnC,eAAe,YAAYA,QAAO,OAAO;AAAA,QACzC,QAAQ,YAAYC,IAAG,IAAI;AAAA,QAC3B,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,WAAW,YAAYC,GAAE;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,kBAAkB,SAASO,iBAAgBC,aAAYV,SAAQC,KAAIC,KAAI;AACzE,aAAO;AAAA,QACL,YAAYQ;AAAA,QACZ,YAAY,YAAYV,QAAO,IAAI;AAAA,QACnC,eAAe,YAAYA,QAAO,OAAO;AAAA,QACzC,QAAQ,YAAYC,IAAG,IAAI;AAAA,QAC3B,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,WAAW,YAAYC,GAAE;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,kBAAkB,SAASS,iBAAgBC,aAAYR,SAAQJ,SAAQC,KAAIC,KAAI;AACjF,aAAO;AAAA,QACL,YAAYU;AAAA,QACZ,QAAQ,YAAYR,QAAO,MAAM;AAAA,QACjC,OAAO,YAAYA,QAAO,KAAK;AAAA,QAC/B,YAAY,YAAYJ,QAAO,IAAI;AAAA,QACnC,eAAe,YAAYA,QAAO,OAAO;AAAA,QACzC,QAAQ,YAAYC,IAAG,IAAI;AAAA,QAC3B,WAAW,YAAYA,IAAG,OAAO;AAAA,QACjC,WAAW,YAAYC,GAAE;AAAA,MAC3B;AAAA,IACF;AAEA,aAAS,aAAa,WAAW;AAC/B,UAAI,OAAO,YAAY,eAAe,SAAS,IAAI,UAC/CE,UAAS,KAAK,QACdL,WAAU,KAAK,SACfC,UAAS,KAAK,QACdC,MAAK,KAAK,IACVC,MAAK,KAAK;AAEd,UAAI,OAAO,gBAAgBE,QAAO,IAAI;AACtC,UAAIN,aAAY,KAAK,WACjBe,YAAW,KAAK,UAChBC,YAAW,KAAK,UAChBR,aAAY,KAAK,WACjBE,aAAY,KAAK,WACjBE,cAAa,KAAK,YAClBE,cAAa,KAAK;AAEtB,UAAId,YAAW;AACb,eAAO,eAAeA,YAAWC,UAASC,SAAQC,KAAIC,GAAE;AAAA,MAC1D;AAEA,UAAII,YAAW;AACb,eAAO,eAAeA,YAAWN,SAAQC,KAAIC,GAAE;AAAA,MACjD;AAEA,UAAIM,YAAW;AACb,eAAO,eAAeA,YAAWR,SAAQC,KAAIC,GAAE;AAAA,MACjD;AAEA,UAAIW,WAAU;AACZ,eAAO,cAAc,MAAMT,SAAQH,KAAIC,GAAE;AAAA,MAC3C;AAEA,UAAIY,WAAU;AACZ,eAAO,cAAc,MAAMV,SAAQH,KAAIC,GAAE;AAAA,MAC3C;AAEA,UAAIQ,aAAY;AACd,eAAO,gBAAgBA,aAAYV,SAAQC,KAAIC,GAAE;AAAA,MACnD;AAEA,UAAIU,aAAY;AACd,eAAO,gBAAgBA,aAAYR,SAAQJ,SAAQC,KAAIC,GAAE;AAAA,MAC3D;AAAA,IACF;AAEA,QAAI,eAAe,SAASa,cAAa,MAAM;AAC7C,UAAI,OAAO,KAAK;AAChB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,eAAe,SAASC,cAAa,OAAO;AAC9C,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,wBAAwB,SAASC,uBAAsB,OAAO;AAChE,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY,UAAU,SAAS,YAAY;AAAA,IAC7D;AACA,QAAI,gBAAgB,SAASC,eAAc,OAAO;AAChD,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,gBAAgB,SAASC,eAAc,OAAO;AAChD,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,iBAAiB,SAASC,gBAAe,OAAO;AAClD,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,gBAAgB,SAASC,eAAc,OAAO;AAChD,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,iBAAiB,SAASC,gBAAe,OAAO;AAClD,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,YAAY;AAAA,IAC9B;AACA,QAAI,kBAAkB,SAASC,iBAAgB,OAAO;AACpD,UAAI,SAAS,MAAM;AACnB,aAAO,YAAY,MAAM;AAAA,IAC3B;AACA,QAAI,iBAAiB,SAASC,gBAAe,QAAQ;AACnD,UAAI,QAAQ,OAAO;AACnB,aAAO,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,UAAI,OAAO,OAAO;AAClB,aAAO,YAAY,MAAM,SAAS;AAAA,IACpC;AAEA,QAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,cAAc,SAASC,aAAY,QAAQ;AAC7C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,iBAAiB,SAASC,gBAAe,QAAQ;AACnD,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,YAAY,SAASC,WAAU,QAAQ;AACzC,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,QAAQ;AAAA,IAC1B;AACA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,UAAU,OAAO;AACrB,aAAO,YAAY,OAAO;AAAA,IAC5B;AACA,QAAI,YAAY,SAASC,WAAU,QAAQ;AACzC,UAAI,OAAO,OAAO;AAClB,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,iBAAiB,SAASC,gBAAe,QAAQ;AACnD,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,aAAa,SAASC,YAAW,QAAQ;AAC3C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa,UAAU,SAAS,aAAa;AAAA,IAC/D;AACA,QAAI,qBAAqB,SAASC,oBAAmB,QAAQ;AAC3D,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,cAAc,SAASC,aAAY,QAAQ;AAC7C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,WAAW,SAASC,UAAS,QAAQ;AACvC,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa,oBAAoB,SAAS,aAAa;AAAA,IACzE;AACA,QAAI,aAAa,SAASC,YAAW,QAAQ;AAC3C,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,uBAAuB,SAASC,sBAAqB,QAAQ;AAC/D,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,aAAa;AAAA,IAC/B;AACA,QAAI,wBAAwB,SAASC,uBAAsB,QAAQ;AACjE,UAAI,UAAU,OAAO;AACrB,aAAO,YAAY,OAAO;AAAA,IAC5B;AACA,QAAI,oBAAoB,SAASC,mBAAkB,QAAQ;AACzD,UAAI,QAAQ,OAAO;AACnB,aAAO,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,iBAAiB,SAASC,gBAAe,QAAQ;AACnD,UAAI,OAAO,OAAO;AAClB,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,UAAI,OAAO,OAAO;AAClB,aAAO,YAAY,IAAI;AAAA,IACzB;AACA,QAAI,mBAAmB,SAASC,kBAAiB,QAAQ;AACvD,UAAI,UAAU,OAAO;AACrB,aAAO,YAAY,OAAO;AAAA,IAC5B;AACA,QAAI,iBAAiB,SAASC,kBAAiB;AAC7C,UAAI,MAAM,qBAAqB;AAC/B,UAAI/C,MAAK,OAAO,IAAI,aAAa,IAAI,UAAU,YAAY;AAC3D,aAAO,OAAOA,QAAO,WAAW,WAAW,KAAKA,GAAE,IAAI;AAAA,IACxD;AACA,QAAI,qBAAqB,SAASgD,oBAAmBhD,KAAI;AACvD,aAAO,OAAOA,QAAO,YAAYA,IAAG,QAAQ,MAAM,MAAM;AAAA,IAC1D;AACA,QAAI,WAAW,SAASiD,YAAW;AACjC,UAAI,MAAM,qBAAqB;AAC/B,aAAO,QAAQ,mBAAmB,KAAK,IAAI,QAAQ,KAAK,IAAI,aAAa,cAAc,IAAI,iBAAiB,MAAM,CAAC,OAAO;AAAA,IAC5H;AACA,QAAI,YAAY,SAASC,aAAY;AACnC,aAAO,aAAa,MAAM;AAAA,IAC5B;AACA,QAAI,cAAc,SAASC,eAAc;AACvC,aAAO,aAAa,QAAQ;AAAA,IAC9B;AACA,QAAI,YAAY,SAASC,aAAY;AACnC,aAAO,aAAa,MAAM;AAAA,IAC5B;AACA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,aAAO,YAAY,MAAM;AAAA,IAC3B;AAEA,aAAS,qBAAqB,SAAS;AACrC,UAAI,OAAO,UAAU,UAAU,UAC3BnD,UAAS,KAAK,QACdL,WAAU,KAAK,SACfE,MAAK,KAAK,IACVD,UAAS,KAAK,QACdE,MAAK,KAAK;AAEd,aAAO;AAAA,QACL,WAAW,cAAcE,OAAM;AAAA,QAC/B,WAAW,cAAcA,OAAM;AAAA,QAC/B,YAAY,eAAeA,OAAM;AAAA,QACjC,YAAY,eAAeA,OAAM;AAAA,QACjC,gBAAgB,mBAAmBL,QAAO,KAAK,UAAU;AAAA,QACzD,YAAY,eAAeA,QAAO;AAAA,QAClC,UAAU,sBAAsBK,OAAM,KAAK,UAAU;AAAA,QACrD,cAAc,aAAaA,OAAM;AAAA,QACjC,UAAU,aAAaA,OAAM,KAAK,UAAU;AAAA,QAC5C,WAAW,cAAcA,OAAM;AAAA,QAC/B,WAAW,cAAcA,OAAM;AAAA,QAC/B,WAAW,cAAcH,GAAE;AAAA,QAC3B,YAAY,eAAeA,GAAE;AAAA,QAC7B,OAAO,UAAUA,GAAE,KAAK,UAAU;AAAA,QAClC,UAAU,aAAaF,QAAO;AAAA,QAC9B,WAAW,cAAcA,QAAO;AAAA,QAChC,UAAU,aAAaA,QAAO;AAAA,QAC9B,SAAS,YAAYA,QAAO;AAAA,QAC5B,MAAM,SAASA,QAAO;AAAA,QACtB,WAAW,aAAaE,GAAE;AAAA,QAC1B,QAAQ,UAAUA,GAAE;AAAA,QACpB,oBAAoB,sBAAsBF,QAAO;AAAA,QACjD,gBAAgB,kBAAkBA,QAAO;AAAA,QACzC,aAAa,eAAeA,QAAO;AAAA,QACnC,cAAc,gBAAgBK,OAAM;AAAA,QACpC,aAAa,eAAeA,OAAM;AAAA,QAClC,YAAY,cAAcJ,OAAM;AAAA,QAChC,eAAe,iBAAiBA,OAAM;AAAA,QACtC,OAAO,aAAaE,GAAE;AAAA,QACtB,QAAQ,WAAWH,QAAO,KAAK,mBAAmBG,GAAE;AAAA,QACpD,UAAU,aAAaH,QAAO;AAAA,QAC9B,YAAY,cAAcK,OAAM;AAAA,QAChC,SAAS,SAAS;AAAA,QAClB,UAAU,UAAU;AAAA,QACpB,YAAY,YAAY;AAAA,QACxB,UAAU,UAAU;AAAA,QACpB,YAAY,eAAe;AAAA,QAC3B,gBAAgB,mBAAmBF,GAAE;AAAA,QACrC,cAAc,WAAWH,QAAO,KAAK,CAAC,mBAAmBG,GAAE;AAAA,QAC3D,WAAW,cAAcD,GAAE;AAAA,QAC3B,SAAS,YAAYA,GAAE;AAAA,QACvB,QAAQ,WAAWF,QAAO;AAAA,QAC1B,kBAAkB,qBAAqBA,QAAO;AAAA,MAChD;AAAA,IACF;AAEA,QAAI,YAAY,cAAc,MAAM;AACpC,QAAI,YAAY,cAAc,MAAM;AACpC,QAAI,aAAa,eAAe,MAAM;AACtC,QAAI,aAAa,eAAe,MAAM;AACtC,QAAI,iBAAiB,mBAAmB,OAAO,KAAK,UAAU;AAC9D,QAAI,aAAa,eAAe,OAAO;AACvC,QAAI,WAAW,sBAAsB,MAAM,KAAK,UAAU;AAC1D,QAAI,eAAe,aAAa,MAAM;AACtC,QAAI,WAAW,aAAa,MAAM,KAAK,UAAU;AACjD,QAAI,YAAY,cAAc,MAAM;AACpC,QAAI,YAAY,cAAc,MAAM;AACpC,QAAI,YAAY,cAAc,EAAE;AAChC,QAAI,aAAa,eAAe,EAAE;AAClC,QAAI,QAAQ,UAAU,EAAE,KAAK,UAAU;AACvC,QAAI,WAAW,aAAa,OAAO;AACnC,QAAI,YAAY,cAAc,OAAO;AACrC,QAAI,WAAW,aAAa,OAAO;AACnC,QAAI,UAAU,YAAY,OAAO;AACjC,QAAI,OAAO,SAAS,OAAO;AAC3B,QAAI,YAAY,aAAa,EAAE;AAC/B,QAAI,SAAS,UAAU,EAAE;AACzB,QAAI,qBAAqB,sBAAsB,OAAO;AACtD,QAAI,iBAAiB,kBAAkB,OAAO;AAC9C,QAAI,cAAc,eAAe,OAAO;AACxC,QAAI,eAAe,gBAAgB,MAAM;AACzC,QAAI,cAAc,eAAe,MAAM;AACvC,QAAI,aAAa,cAAc,MAAM;AACrC,QAAI,gBAAgB,iBAAiB,MAAM;AAC3C,QAAI,QAAQ,aAAa,EAAE;AAC3B,QAAI,SAAS,WAAW,OAAO,KAAK,mBAAmB,EAAE;AACzD,QAAI,WAAW,aAAa,OAAO;AACnC,QAAI,aAAa,cAAc,MAAM;AACrC,QAAI,UAAU,SAAS;AACvB,QAAI,WAAW,UAAU;AACzB,QAAI,aAAa,YAAY;AAC7B,QAAI,WAAW,UAAU;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,iBAAiB,mBAAmB,EAAE;AAC1C,QAAI,eAAe,WAAW,OAAO,KAAK,CAAC,mBAAmB,EAAE;AAChE,QAAI,YAAY,cAAc,EAAE;AAChC,QAAI,UAAU,YAAY,EAAE;AAC5B,QAAI,SAAS,WAAW,OAAO;AAC/B,QAAI,mBAAmB,qBAAqB,OAAO;AACnD,QAAI,0BAA0B,SAASyD,yBAAwB,WAAW;AACxE,UAAI,CAAC,aAAa,OAAO,cAAc,UAAU;AAC/C,gBAAQ,MAAM,yCAAyC;AACvD;AAAA,MACF;AAEA,UAAI,wBAAwB,eAAe,SAAS,GAChDpD,UAAS,sBAAsB,QAC/BL,WAAU,sBAAsB,SAChCE,MAAK,sBAAsB,IAC3BD,UAAS,sBAAsB,QAC/BE,MAAK,sBAAsB;AAE/B,aAAO,qBAAqB;AAAA,QAC1B,QAAQE;AAAA,QACR,SAASL;AAAA,QACT,IAAIE;AAAA,QACJ,QAAQD;AAAA,QACR,IAAIE;AAAA,MACN,CAAC;AAAA,IACH;AAEA,QAAI,cAAc,SAASuD,aAAY,MAAM;AAC3C,UAAI,qBAAqB,KAAK,oBAC1B,WAAW,KAAK,UAChB,QAAQ,yBAAyB,MAAM,CAAC,sBAAsB,UAAU,CAAC;AAE7E,aAAO,YAAY,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAChK;AACA,QAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,YAAY,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAChK;AACA,QAAI,SAAS,SAASC,QAAO,OAAO;AAClC,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,OAAO,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAC3J;AACA,QAAI,UAAU,SAASC,SAAQ,OAAO;AACpC,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,QAAQ,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAC5J;AACA,QAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,WAAW,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAC/J;AACA,QAAI,aAAa,SAASC,YAAW,OAAO;AAC1C,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,WAAW,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAC/J;AACA,QAAI,eAAe,SAASC,cAAa,OAAO;AAC9C,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,aAAa,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IACjK;AACA,QAAI,iBAAiB,SAASC,gBAAe,OAAO;AAClD,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,gBAAgB,MAAM,eACtB,QAAQ,MAAM,OACd,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,YAAY,iBAAiB,OAAO,CAAC;AAExG,aAAO,eAAe,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IACnK;AACA,QAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,UAAI,qBAAqB,MAAM,oBAC3B,WAAW,MAAM,UACjB,QAAQ,yBAAyB,OAAO,CAAC,sBAAsB,UAAU,CAAC;AAE9E,aAAO,YAAY,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAChK;AACA,QAAI,cAAc,SAASC,aAAY,QAAQ;AAC7C,UAAI,qBAAqB,OAAO,oBAC5B,WAAW,OAAO,UAClB,QAAQ,yBAAyB,QAAQ,CAAC,sBAAsB,UAAU,CAAC;AAE/E,aAAO,YAAY,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAChK;AACA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,qBAAqB,OAAO,oBAC5B,WAAW,OAAO,UAClB,QAAQ,yBAAyB,QAAQ,CAAC,sBAAsB,UAAU,CAAC;AAE/E,aAAO,aAAa,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IACjK;AACA,QAAI,aAAa,SAASC,YAAW,QAAQ;AAC3C,UAAI,qBAAqB,OAAO,oBAC5B,WAAW,OAAO,UAClB,gBAAgB,OAAO,eACvB,QAAQ,OAAO,OACf,YAAY,OAAO,WACnB,QAAQ,yBAAyB,QAAQ,CAAC,sBAAsB,YAAY,iBAAiB,SAAS,WAAW,CAAC;AAEtH,aAAO,YAAY,qBAAqB,eAAe,cAAc,MAAM,UAAU,MAAM,QAAQ,IAAI,eAAe,cAAc,OAAO,OAAO,QAAQ,IAAI;AAAA,IAChK;AAEA,aAAS,sBAAsB,kBAAkB;AAC/C,aAAoB,SAAU,kBAAkB;AAC9C,kBAAU,QAAQ,gBAAgB;AAElC,iBAAS,OAAO,OAAO;AACrB,cAAI;AAEJ,0BAAgB,MAAM,MAAM;AAE5B,kBAAQ,2BAA2B,MAAM,gBAAgB,MAAM,EAAE,KAAK,MAAM,KAAK,CAAC;AAClF,gBAAM,uBAAuB;AAC7B,gBAAM,0BAA0B,MAAM,wBAAwB,KAAK,uBAAuB,KAAK,CAAC;AAChG,gBAAM,sBAAsB,MAAM,oBAAoB,KAAK,uBAAuB,KAAK,CAAC;AACxF,gBAAM,aAAa,MAAM,WAAW,KAAK,uBAAuB,KAAK,CAAC;AACtE,gBAAM,QAAQ;AAAA,YACZ,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AACA,iBAAO;AAAA,QACT;AAEA,qBAAa,QAAQ,CAAC;AAAA,UACpB,KAAK;AAAA,UACL,OAAO,SAAS,0BAA0B;AACxC,gBAAI,CAAC,KAAK,sBAAsB;AAC9B,mBAAK,uBAAuB;AAAA,YAC9B;AAEA,gBAAI,cAAc,OAAO,aAAa,OAAO,cAAc,KAAK;AAChE,iBAAK,SAAS;AAAA,cACZ,YAAY,gBAAgB;AAAA,cAC5B,aAAa,gBAAgB;AAAA,YAC/B,CAAC;AAAA,UACH;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,UACL,OAAO,SAAS,sBAAsB;AACpC,iBAAK,wBAAwB;AAAA,UAC/B;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,UACL,OAAO,SAAS,aAAa;AAC3B,iBAAK,wBAAwB;AAAA,UAC/B;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,UACL,OAAO,SAAS,oBAAoB;AAClC,iBAAK,OAAO,WAAW,cAAc,cAAc,QAAQ,MAAM,OAAO,UAAa,UAAU;AAC7F,kBAAI,CAAC,KAAK,sBAAsB;AAC9B,qBAAK,wBAAwB;AAC7B,uBAAO,iBAAiB,QAAQ,KAAK,YAAY,KAAK;AAAA,cACxD,OAAO;AACL,uBAAO,oBAAoB,QAAQ,KAAK,YAAY,KAAK;AAAA,cAC3D;AAEA,qBAAO,iBAAiB,UAAU,KAAK,qBAAqB,KAAK;AAAA,YACnE;AAAA,UACF;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,UACL,OAAO,SAAS,uBAAuB;AACrC,mBAAO,oBAAoB,UAAU,KAAK,qBAAqB,KAAK;AAAA,UACtE;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,UACL,OAAO,SAAS,SAAS;AACvB,mBAAO,eAAe,cAAc,kBAAkB,SAAS,CAAC,GAAG,KAAK,OAAO;AAAA,cAC7E,aAAa,KAAK,MAAM;AAAA,cACxB,YAAY,KAAK,MAAM;AAAA,YACzB,CAAC,CAAC;AAAA,UACJ;AAAA,QACF,CAAC,CAAC;AAEF,eAAO;AAAA,MACT,EAAE,eAAe,SAAS;AAAA,IAC5B;AAEA,aAAS,uBAAuB;AAC9B,UAAI,YAAY,MAAM,SAAS,WAAY;AACzC,YAAI,cAAc,OAAO,aAAa,OAAO,cAAc,KAAK;AAChE,eAAO;AAAA,UACL,YAAY,gBAAgB;AAAA,UAC5B,aAAa,gBAAgB;AAAA,UAC7B,aAAa,gBAAgB,IAAI,aAAa;AAAA,QAChD;AAAA,MACF,CAAC,GACG,aAAa,eAAe,WAAW,CAAC,GACxC,QAAQ,WAAW,CAAC,GACpB,WAAW,WAAW,CAAC;AAE3B,UAAI,0BAA0B,MAAM,YAAY,WAAY;AAC1D,YAAI,cAAc,OAAO,aAAa,OAAO,cAAc,KAAK;AAChE,YAAI,OAAO;AAAA,UACT,YAAY,gBAAgB;AAAA,UAC5B,aAAa,gBAAgB;AAAA,UAC7B,aAAa,gBAAgB,IAAI,aAAa;AAAA,QAChD;AACA,cAAM,gBAAgB,KAAK,eAAe,SAAS,IAAI;AAAA,MACzD,GAAG,CAAC,MAAM,WAAW,CAAC;AACtB,YAAM,UAAU,WAAY;AAC1B,aAAK,OAAO,WAAW,cAAc,cAAc,QAAQ,MAAM,OAAO,UAAa,UAAU;AAC7F,kCAAwB;AACxB,iBAAO,iBAAiB,QAAQ,yBAAyB,KAAK;AAC9D,iBAAO,iBAAiB,UAAU,yBAAyB,KAAK;AAAA,QAClE;AAEA,eAAO,WAAY;AACjB,iBAAO,oBAAoB,UAAU,yBAAyB,KAAK;AACnE,iBAAO,oBAAoB,QAAQ,yBAAyB,KAAK;AAAA,QACnE;AAAA,MACF,GAAG,CAAC,uBAAuB,CAAC;AAC5B,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,WAAW;AAChC,UAAI,gBAAgB,YAAY,YAAY,OAAO,UAAU;AAC7D,aAAO,eAAe,aAAa;AAAA,IACrC;AAEA,aAAS,mBAAmB,WAAW;AACrC,UAAI,gBAAgB,YAAY,YAAY,OAAO,UAAU;AAC7D,UAAI,aAAa,cAAc,aAAa;AAC5C,UAAI,YAAY,qBAAqB,UAAU;AAC/C,aAAO,CAAC,WAAW,UAAU;AAAA,IAC/B;AAEA,YAAQ,cAAc;AACtB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,iBAAiB;AACzB,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,eAAe;AACvB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,iBAAiB;AACzB,YAAQ,eAAe;AACvB,YAAQ,aAAa;AACrB,YAAQ,aAAa;AACrB,YAAQ,gBAAgB;AACxB,YAAQ,qBAAqB;AAC7B,YAAQ,0BAA0B;AAClC,YAAQ,QAAQ;AAChB,YAAQ,YAAY;AACpB,YAAQ,YAAY;AACpB,YAAQ,WAAW;AACnB,YAAQ,aAAa;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY;AACpB,YAAQ,SAAS;AACjB,YAAQ,iBAAiB;AACzB,YAAQ,aAAa;AACrB,YAAQ,aAAa;AACrB,YAAQ,YAAY;AACpB,YAAQ,OAAO;AACf,YAAQ,QAAQ;AAChB,YAAQ,UAAU;AAClB,YAAQ,WAAW;AACnB,YAAQ,aAAa;AACrB,YAAQ,WAAW;AACnB,YAAQ,eAAe;AACvB,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,WAAW;AACnB,YAAQ,eAAe;AACvB,YAAQ,iBAAiB;AACzB,YAAQ,UAAU;AAClB,YAAQ,WAAW;AACnB,YAAQ,mBAAmB;AAC3B,YAAQ,YAAY;AACpB,YAAQ,WAAW;AACnB,YAAQ,aAAa;AACrB,YAAQ,aAAa;AACrB,YAAQ,YAAY;AACpB,YAAQ,WAAW;AACnB,YAAQ,cAAc;AACtB,YAAQ,eAAe;AACvB,YAAQ,SAAS;AACjB,YAAQ,YAAY;AACpB,YAAQ,iBAAiB;AACzB,YAAQ,eAAe;AACvB,YAAQ,gBAAgB;AACxB,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,wBAAwB;AAAA;AAAA;", "names": ["window", "undefined", "regexes", "ua", "setUa", "parseUserAgent", "setUserAgent", "obj", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "checkDeviceType", "setDefaults", "getNavigatorInstance", "isIOS13Check", "browserPayload", "<PERSON><PERSON><PERSON><PERSON>", "browser", "engine", "os", "ua", "mobilePayload", "device", "smartTvPayload", "isSmartTV", "consolePayload", "isConsole", "wearablePayload", "isWearable", "embeddedPayload", "isEmbedded", "isMobile", "isTablet", "isMobileType", "isTabletType", "isMobileAndTabletType", "isSmartTVType", "isBrowserType", "isWearableType", "isConsoleType", "isEmbeddedType", "getMobileVendor", "getMobileModel", "getDeviceType", "isAndroidType", "isWindowsType", "isMacOsType", "isWinPhoneType", "isIOSType", "getOsVersion", "getOsName", "isChromeType", "isFirefoxType", "isChromiumType", "isEdgeType", "isYandexType", "isSafariType", "isMobileSafariType", "isOperaType", "isIEType", "isMIUIType", "isSamsungBrowserType", "getBrowserFullVersion", "getBrowserVersion", "getBrowserName", "getEngineName", "getEngineVersion", "isElectronType", "isEdgeChromiumType", "getIOS13", "getIPad13", "getIphone13", "getIPod13", "getUseragent", "getSelectorsByUserAgent", "AndroidView", "<PERSON><PERSON><PERSON><PERSON>iew", "IEView", "IOSView", "MobileView", "TabletView", "WinPhoneView", "MobileOnlyView", "SmartTVView", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WearableView", "CustomView"]}