import React, { useEffect, useState } from 'react';
import { stats_data } from './dummy-data';
import StatsCard from './components/StatsCard';
import { Simple<PERSON>ie<PERSON><PERSON> } from 'components/charts';
import Radial<PERSON><PERSON><PERSON><PERSON> from 'components/charts/RadialBarChart';
import { Icon } from '@iconify/react';
import SimpleBar<PERSON>hart from 'components/charts/SimpleBarChart';
import { Timeline } from 'antd';
import MonthlyBarChart from 'pages/dashboard/MonthlyBarChart';
import { useNavigate } from 'react-router';
import InvoiceDetailStats from './components/InvoiceDetailStats';
import BDInvoiceOverview from './components/BDInvoiceOverview';
import { Get, Post } from 'actions/API/apiActions';
import { API_URLS } from 'constants/apiUrls';
import { message, Pagination } from 'antd';
import moment from 'moment';
import { get } from 'lodash';

const roleTimelineData = [
  {
    date: '14/11/24',
    time: '08:00 PM',
    by: 'Added Manager Role for Location London'
  },
  {
    date: '14/11/24',
    time: '05:00 PM',
    by: 'Resolved query for role Designer'
  },
  {
    date: '14/11/24',
    time: '03:00 PM',
    by: 'Created invoice for client 201'
  },
  {
    date: '14/11/24',
    time: '02:00 PM',
    by: 'Schedule demo with potential client DEF for next Thursday'
  },
  {
    date: '14/11/24',
    time: '12:02 PM',
    by: 'Schedule meeting with client John for next Monday'
  },
  {
    date: '14/11/24',
    time: '12:00 PM',
    by: 'Schedule meeting with client John for next Monday'
  }
];

const trialRolesData = [
  {
    day: 'Monday',
    data: [
      { name: 'Total', count: 20, color: '#5d7cef' },
      { name: 'Pending At Resourcer', count: 5, color: '#2caffe' },
      { name: 'Paused', count: 2, color: '#00e272' },
      { name: 'Issue', count: 0, color: '#fe6a35' },
      { name: 'Pending at ACM', count: 7, color: '#fcb92c' },
      { name: 'Done', count: 5, color: '#41cc83' }
    ]
  },
  {
    day: 'Tuesday',
    data: [
      { name: 'Total', count: 13, color: '#5d7cef' },
      { name: 'Pending At Resourcer', count: 5, color: '#2caffe' },
      { name: 'Paused', count: 2, color: '#00e272' },
      { name: 'Issue', count: 0, color: '#fe6a35' },
      { name: 'Pending at ACM', count: 6, color: '#fcb92c' },
      { name: 'Done', count: 0, color: '#41cc83' }
    ]
  },
  {
    day: 'Wednesday',
    data: [
      { name: 'Total', count: 22, color: '#5d7cef' },
      { name: 'Pending At Resourcer', count: 5, color: '#2caffe' },
      { name: 'Paused', count: 2, color: '#00e272' },
      { name: 'Issue', count: 0, color: '#fe6a35' },
      { name: 'Pending at ACM', count: 6, color: '#fcb92c' },
      { name: 'Done', count: 9, color: '#41cc83' }
    ]
  }
];

const invoicesData = [
  {
    month: 'March',
    data: [
      { name: 'Invoiced', count: 18, color: '#2caffe' },
      { name: 'Paid', count: 12, color: '#00e272' },
      { name: 'Unpaid', count: 6, color: '#fcb92c' },
      { name: 'Cancelled', count: 6, color: '#fe6a35' }
    ]
  },
  {
    month: 'April',
    data: [
      { name: 'Invoiced', count: 21, color: '#2caffe' },
      { name: 'Paid', count: 8, color: '#00e272' },
      { name: 'Unpaid', count: 6, color: '#fcb92c' },
      { name: 'Cancelled', count: 6, color: '#fe6a35' }
    ]
  },
  {
    month: 'May',
    data: [
      { name: 'Invoiced', count: 22, color: '#2caffe' },
      { name: 'Paid', count: 8, color: '#00e272' },
      { name: 'Unpaid', count: 0, color: '#fcb92c' },
      { name: 'Cancelled', count: 0, color: '#fe6a35' }
    ]
  }
];

// const sectorChartData = [
//   { name: 'Direct', count: 70, color: '#00E396' },
//   { name: 'S&R', count: 45, color: '#0090FF' }
// ];
function BusinessDevelopmentDashboard(props) {
  const navigate = useNavigate();
  const [openInvoiceDetails, setOpenInvoiceDetails] = useState(false);
  const [emailReplies, setEmailReplies] = useState([]);
  const [sectorWiseProspects, setSectorWiseProspects] = useState({ total: 0, direct: 0, sr: 0 });
  const [incomingMeetings, setIncomingMeetings] = useState([]);
  const [prospectsOverview, setProspectsOverview] = useState({
    trialFailed: 0,
    trialInProcess: 0,
    trialPending: 0,
    trialRecieved: 0,
    trialResultsRecieved: 0,
    trialSent: 0,
    trialSuccessfull: 0
  });
  const [emailStats, setEmailStats] = useState({
    autoRepliesEmails: { current: 0, lastWeek: 0, percentChange: 0 },
    bouncedEmails: { current: 0, lastWeek: 0, percentChange: 0 },
    potentialReplies: { current: 0, lastWeek: 0, percentChange: 0 },
    totalEmails: { current: 0, lastWeek: 0, percentChange: 0 }
  });
  const userId = localStorage.getItem('userId');

  const getEmailReplies = () => {
    try {
      Get(
        {
          pageSize: '5'
        },
        API_URLS.getMyTrialLeads.replace(':assigneeId', userId),
        (response) => {
          setEmailReplies(response?.data);
        },
        (error) => {
          message.error(error?.response?.data?.message || 'Failed to recent get email replies. Try again!');
          console.error(error?.response?.data?.message || 'Failed to recent get email replies. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to recent get email replies. Try again!');
    }
  };

  const getDashboard = () => {
    try {
      Get(
        {},
        API_URLS.bdDashboard,
        (response) => {
          setSectorWiseProspects(response?.data.sectorWiseProspects);
          setProspectsOverview(response?.data.prospectsOverview);
          setEmailStats(response?.data.emailsData);
        },
        (error) => {
          message.error(error?.response?.data?.message || 'Failed to get dashboard. Try again!');
          console.error(error?.response?.data?.message || 'Failed to get dashboard. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to get dashboard. Try again!');
    }
  };

  const getIncomingMeetings = () => {
    try {
      const url = API_URLS.getEventByEventType.replace(':eventType', 'MEETING').replace(':userId', userId);
      Get(
        { pageSize: '5' },
        url,
        (response) => {
          setIncomingMeetings(response?.data);
        },
        (error) => {
          message.error(error?.response?.data?.message || 'Failed to get meetings. Try again!');
          console.error(error?.response?.data?.message || 'Failed to get meetings. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to get meetings. Try again!');
    }
  };

  const sectorChartData = [
    { name: 'Direct', count: sectorWiseProspects?.direct, color: '#00E396' },
    { name: 'S&R', count: sectorWiseProspects?.sr, color: '#0090FF' }
  ];

  const total =
    (prospectsOverview?.trialRecieved || 0) +
    (prospectsOverview?.trialSent || 0) +
    (prospectsOverview?.trialPending || 0) +
    (prospectsOverview?.trialInProcess || 0) +
    (prospectsOverview?.trialResultsRecieved || 0) +
    (prospectsOverview?.trialSuccessfull || 0) +
    (prospectsOverview?.trialFailed || 0);

  const getPercent = (count) => (total ? (count / total) * 100 : 0);

  const trialChartData = [
    {
      name: 'Trial Recieved',
      y: getPercent(prospectsOverview?.trialRecieved),
      color: '#5d7cef',
      count: prospectsOverview?.trialRecieved
    },
    {
      name: 'Trial Sent',
      y: getPercent(prospectsOverview?.trialSent),
      color: '#58c9ef',
      count: prospectsOverview?.trialSent
    },
    {
      name: 'Pending Resourcer',
      y: getPercent(prospectsOverview?.trialPending),
      color: '#fcb92c',
      count: prospectsOverview?.trialPending
    },
    {
      name: 'Trial In-Progress',
      y: getPercent(prospectsOverview?.trialInProcess),
      color: '#ff751a',
      count: prospectsOverview?.trialInProcess
    },
    {
      name: 'Results Recieved',
      y: getPercent(prospectsOverview?.trialResultsRecieved),
      color: '#a47de8',
      count: prospectsOverview?.trialResultsRecieved
    },
    {
      name: 'Trial Successful',
      y: getPercent(prospectsOverview?.trialSuccessfull),
      color: '#41cc83',
      count: prospectsOverview?.trialSuccessfull
    },
    {
      name: 'Trial Failed',
      y: getPercent(prospectsOverview?.trialFailed),
      color: '#ef6e5d',
      count: prospectsOverview?.trialFailed
    }
  ];

  const stats_data = [
    {
      id: 1,
      title: 'Total Emails',
      number: emailStats?.totalEmails.current,
      percentage: emailStats?.totalEmails.percentChange,
      dateRange: 'Dec 2024 - Feb 2025'
    },
    {
      id: 2,
      title: 'Auto Replies',
      number: emailStats?.autoRepliesEmails.current,
      percentage: emailStats?.autoRepliesEmails.percentChange,
      dateRange: 'Feb 2025 - Apr 2025'
    },
    {
      id: 3,
      title: 'Bounced Emails',
      number: emailStats?.bouncedEmails.current,
      percentage: emailStats?.bouncedEmails.percentChange,
      dateRange: 'Apr 2025 - Jun 2025'
    },
    {
      id: 4,
      title: 'Potential Leads',
      number: emailStats?.potentialReplies.current,
      percentage: emailStats?.potentialReplies.percentChange,
      dateRange: 'Jun 2025 - Aug 2025'
    }
  ];

  function formatMeetingDate(startDateString) {
    const date = moment(startDateString);
    const now = moment();
    const tomorrow = moment().add(1, 'days');

    if (date.isSame(now, 'day')) {
      return `At ${date.format('hh:mm A')} Today`;
    } else if (date.isSame(tomorrow, 'day')) {
      return `At ${date.format('hh:mm A')} Tomorrow`;
    } else {
      return `At ${date.format('hh:mm A')} on ${date.format('DD MMM YYYY')}`;
    }
  }

  useEffect(() => {
    getEmailReplies();
    getDashboard();
    getIncomingMeetings();
  }, []);

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', gap: '20px' }}>
        {stats_data.map((item) => (
          <StatsCard key={item.id} title={item.title} value={item.number} percentage={item.percentage} dateRange={item.dateRange} />
        ))}
      </div>
      <div style={{ display: 'flex', marginTop: 20 }}>
        <div style={{ width: '40%', borderRadius: 15, overflow: 'hidden', backgroundColor: 'white', padding: '15px', height: '550px' }}>
          <SimplePieChart
            chartData={trialChartData}
            title={'Prospects Overview'}
            onDetailsClick={() => navigate('/trial-prospects/all-prospects')}
            showDetails={true}
          />
        </div>
        <div
          style={{
            width: '30%',
            marginLeft: '1%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px'
          }}
        >
          <RadialBarChart
            title={'Sector Wise Prospects'}
            chartData={sectorChartData}
            onNameClick={(sector) => navigate('/trial-prospects/all-prospects', { state: { sector: sector } })}
          />
        </div>
        <div
          style={{
            width: '29%',
            marginLeft: '1%',
            borderRadius: 15,
            overflow: 'scroll',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px'
          }}
        >
          <p style={{ fontSize: '20px', fontWeight: 600 }}>Recent Email Replies</p>
          {emailReplies?.length > 0 ? (
            emailReplies?.map((mail, index) => (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '15px 15px',
                  width: '100%',
                  borderBottom: '2px solid #CED0DA',
                  backgroundColor: 'white'
                }}
              >
                <div style={{ width: '20%' }}>
                  <div style={{ position: 'relative', backgroundColor: 'white' }}>
                    <div
                      style={{
                        width: '56px',
                        height: '56px',
                        fontWeight: 500,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '50%',
                        marginLeft: '-5px',
                        fontFamily: 'Poppins',
                        backgroundColor: '#dcffeb',
                        color: '#20d770'
                      }}
                    >
                      <p style={{ fontSize: '18px', margin: 0 }}>{mail?.name.charAt(0).toUpperCase()}</p>
                    </div>
                  </div>
                </div>
                <div style={{ width: '50%' }}>
                  <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>{mail?.name}</p>
                  <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                    <Icon icon="fluent:radio-button-16-regular" width="14" height="14" />
                    <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                      Replied to you {moment(mail?.created_at).fromNow()}
                    </p>
                  </div>
                </div>
                <div>
                  <p
                    style={{
                      color: 'white',
                      fontWeight: 500,
                      fontSize: '12px',
                      fontFamily: 'Poppins',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginLeft: '30px',
                      width: '70px',
                      height: '30px',
                      backgroundColor: '#1a84de',
                      cursor: 'pointer'
                    }}
                    onClick={() => navigate('/mailbox/inbox/my-trial-leads')}
                  >
                    View
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '15px 15px',
                width: '100%',
                borderBottom: '2px solid #CED0DA',
                backgroundColor: 'white'
              }}
            >
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>No replies to show</p>
            </div>
          )}
        </div>
      </div>
      <div style={{ display: 'flex', marginTop: 20 }}>
        <div
          style={{
            width: '28%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px'
          }}
        >
          <p style={{ fontSize: '20px', fontWeight: 600 }}>Incoming Meetings</p>
          {incomingMeetings?.length > 0 ? (
            incomingMeetings?.map((meeting, index) => (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '15px 15px',
                  width: '100%',
                  borderBottom: '2px solid #CED0DA',
                  backgroundColor: 'white'
                }}
              >
                <div style={{ width: '20%' }}>
                  <div style={{ position: 'relative', backgroundColor: 'white' }}>
                    <div
                      style={{
                        width: '56px',
                        height: '56px',
                        fontWeight: 500,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '50%',
                        marginLeft: '-5px',
                        fontFamily: 'Poppins',
                        backgroundColor: 'white'
                      }}
                    >
                      <Icon
                        icon={
                          meeting?.event_source === 'GOOGLE MEETS'
                            ? 'logos:google-meet'
                            : meeting?.event_source === 'TEAMS'
                              ? 'logos:microsoft-teams'
                              : 'logos:zoom'
                        }
                        width="35"
                        height="35"
                      />
                    </div>
                  </div>
                </div>
                <div style={{ width: '50%' }}>
                  <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>
                    Meeting with {meeting?.person?.first_name}
                  </p>
                  <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                    <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                      {formatMeetingDate(meeting?.start_date)}
                    </p>
                  </div>
                </div>
                <div>
                  <p
                    style={{
                      color: 'white',
                      fontWeight: 500,
                      fontSize: '12px',
                      fontFamily: 'Poppins',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginLeft: '30px',
                      width: '70px',
                      height: '30px',
                      backgroundColor: '#1a84de',
                      cursor: 'pointer'
                    }}
                  >
                    <a href={meeting?.event_link} target="_blank" rel="noreferrer" style={{ color: 'white', textDecoration: 'none' }}>
                      Join
                    </a>
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '15px 15px',
                width: '100%',
                borderBottom: '2px solid #CED0DA',
                backgroundColor: 'white'
              }}
            >
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>No meetings to show</p>
            </div>
          )}
        </div>
        <div
          style={{
            width: '45%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px',
            marginLeft: '1%'
          }}
        >
          <SimpleBarChart
            chartData={trialRolesData}
            title={'CV Sourcing Roles'}
            onDetailClick={() => {
              navigate('/trial-roles/cv-sourcing', { state: { showWeeklyRoles: true } });
            }}
            type="Roles"
            showDetails={true}
          />
        </div>
        <div
          style={{
            width: '28%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            marginLeft: '1%',
            padding: '15px',
            height: '550px'
          }}
        >
          <p style={{ fontSize: '20px', fontWeight: 600 }}>Recent Role Activity</p>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    fontWeight: 500,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '50%',
                    marginLeft: '-5px',
                    fontFamily: 'Poppins',
                    backgroundColor: '#fee8cd',
                    color: '#b99d7b'
                  }}
                >
                  <p style={{ fontSize: '18px', margin: 0 }}>H</p>
                </div>
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>Hania Saeed</p>
              <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                  Added query to Manager Role 1s ago
                </p>
              </div>
            </div>
            <div>
              <p
                style={{
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '12px',
                  fontFamily: 'Poppins',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: '30px',
                  width: '70px',
                  height: '30px',
                  backgroundColor: '#1a84de',
                  cursor: 'pointer'
                }}
                onClick={() => navigate('/CV Sourcing/1/role-details')}
              >
                View
              </p>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    fontWeight: 500,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '50%',
                    marginLeft: '-5px',
                    fontFamily: 'Poppins',
                    backgroundColor: '#fee8cd',
                    color: '#b99d7b'
                  }}
                >
                  <p style={{ fontSize: '18px', margin: 0 }}>H</p>
                </div>
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>Hania Saeed</p>
              <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                  Added query to Manager Role 1s ago
                </p>
              </div>
            </div>
            <div>
              <p
                style={{
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '12px',
                  fontFamily: 'Poppins',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: '30px',
                  width: '70px',
                  height: '30px',
                  backgroundColor: '#1a84de',
                  cursor: 'pointer'
                }}
                onClick={() => navigate('/CV Sourcing/1/role-details')}
              >
                View
              </p>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    fontWeight: 500,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '50%',
                    marginLeft: '-5px',
                    fontFamily: 'Poppins',
                    backgroundColor: '#fee8cd',
                    color: '#b99d7b'
                  }}
                >
                  <p style={{ fontSize: '18px', margin: 0 }}>H</p>
                </div>
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>Hania Saeed</p>
              <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                  Added query to Manager Role 1s ago
                </p>
              </div>
            </div>
            <div>
              <p
                style={{
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '12px',
                  fontFamily: 'Poppins',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: '30px',
                  width: '70px',
                  height: '30px',
                  backgroundColor: '#1a84de'
                }}
              >
                View
              </p>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    fontWeight: 500,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '50%',
                    marginLeft: '-5px',
                    fontFamily: 'Poppins',
                    backgroundColor: '#fee8cd',
                    color: '#b99d7b'
                  }}
                >
                  <p style={{ fontSize: '18px', margin: 0 }}>H</p>
                </div>
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>Hania Saeed</p>
              <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                  Added query to Manager Role 1s ago
                </p>
              </div>
            </div>
            <div>
              <p
                style={{
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '12px',
                  fontFamily: 'Poppins',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: '30px',
                  width: '70px',
                  height: '30px',
                  backgroundColor: '#1a84de'
                }}
              >
                View
              </p>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '15px 15px',
              width: '100%',
              borderBottom: '2px solid #CED0DA',
              backgroundColor: 'white'
            }}
          >
            <div style={{ width: '20%' }}>
              <div style={{ position: 'relative', backgroundColor: 'white' }}>
                <div
                  style={{
                    width: '56px',
                    height: '56px',
                    fontWeight: 500,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '50%',
                    marginLeft: '-5px',
                    fontFamily: 'Poppins',
                    backgroundColor: '#fee8cd',
                    color: '#b99d7b'
                  }}
                >
                  <p style={{ fontSize: '18px', margin: 0 }}>H</p>
                </div>
              </div>
            </div>
            <div style={{ width: '50%' }}>
              <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '13px', fontFamily: 'Poppins', margin: 0 }}>Hania Saeed</p>
              <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                <p style={{ color: '#676C7E', fontWeight: 500, fontSize: '10px', fontFamily: 'Poppins', margin: 0 }}>
                  Added query to Manager Role 1s ago
                </p>
              </div>
            </div>
            <div>
              <p
                style={{
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '12px',
                  fontFamily: 'Poppins',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: '30px',
                  width: '70px',
                  height: '30px',
                  backgroundColor: '#1a84de'
                }}
              >
                View
              </p>
            </div>
          </div>
        </div>
      </div>
      <div style={{ display: 'flex', marginTop: 20 }}>
        <div
          style={{
            width: '50%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px'
          }}
        >
          <SimpleBarChart
            chartData={trialRolesData}
            title={'Pre-Qualification Roles'}
            onDetailClick={() => {
              navigate('/trial-roles/pre-qualification', { state: { showWeeklyRoles: true } });
            }}
            type="Roles"
            showDetails={true}
          />
        </div>
        <div
          style={{
            width: '49%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px',
            marginLeft: '1%'
          }}
        >
          <SimpleBarChart
            chartData={trialRolesData}
            title={'360 Roles'}
            onDetailClick={() => {
              navigate('/trial-roles/direct', { state: { showWeeklyRoles: true } });
            }}
            type="Roles"
            showDetails={true}
          />
        </div>
      </div>
      <div style={{ display: 'flex', marginTop: 20 }}>
        <div
          style={{
            width: '55%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px'
          }}
        >
          <BDInvoiceOverview
            chartData={invoicesData}
            title={'Invoices Overview'}
            onDetailClick={() => {
              setOpenInvoiceDetails(true);
            }}
            showDetails={true}
          />
        </div>
        <div
          style={{
            width: '44%',
            borderRadius: 15,
            overflow: 'hidden',
            backgroundColor: 'white',
            padding: '15px',
            height: '550px',
            marginLeft: '1%'
          }}
        >
          <p style={{ fontSize: '20px', fontWeight: 600 }}>Recent Activity</p>
          <div style={{ width: '100%', marginLeft: '-35%', height: '100%' }}>
            <Timeline
              mode="alternate"
              items={[
                {
                  children: <div></div>,
                  dot: <span style={{ display: 'none' }}></span>
                },
                ...roleTimelineData.flatMap((item) => [
                  {
                    children: (
                      <div style={{ flexDirection: 'column' }}>
                        {/* <p style={{ fontWeight: 400, fontFamily: 'Poppins', fontSize: '12px', marginTop: '5px' }}>{item.date}</p> */}
                        <p style={{ fontWeight: 400, fontFamily: 'Poppins', fontSize: '12px', marginTop: '-10px' }}>{item.time}</p>
                      </div>
                    ),
                    color: '#1A84DE',
                    dot: <span style={{ display: 'none' }}></span>
                  },
                  {
                    children: (
                      <div>
                        <p style={{ fontWeight: 400, fontFamily: 'Poppins', fontSize: '12px', marginTop: '-30px', marginLeft: '15px' }}>
                          {item.by}
                        </p>
                      </div>
                    ),
                    dot: <Icon icon="line-md:circle" width="14" height="14" />
                  }
                ])
              ]}
            />
          </div>
        </div>
      </div>
      <br />
      {openInvoiceDetails && <InvoiceDetailStats open={openInvoiceDetails} onClose={() => setOpenInvoiceDetails(false)} />}
    </div>
  );
}
export default BusinessDevelopmentDashboard;
