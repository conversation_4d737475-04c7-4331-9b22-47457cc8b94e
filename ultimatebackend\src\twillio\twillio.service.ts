import { Injectable } from '@nestjs/common';
import { jwt, Twilio } from 'twilio';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { WhatsAppMessage } from './whatsapp-message.entity';
import { EmailService } from 'src/email/email.service';
import { formatToE164, formatForWhatsApp, extractFromWhatsApp } from '../utils/phone-formatter.util';

@Injectable()
export class TwillioService {
  private client: Twilio;
  private readonly whatsappNumberUK: string;
  private readonly whatsappNumberUS: string;

  constructor(
    @InjectRepository(WhatsAppMessage)
    private readonly whatsappMessageRepo: Repository<WhatsAppMessage>,

    private readonly emailService: EmailService,
  ) {
    this.client = new Twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN,
    );

    // Use production WhatsApp numbers from .env
    this.whatsappNumberUK = process.env.TWILIO_WHATSAPP_PHONE_NUMBER_UK;
    this.whatsappNumberUS = process.env.TWILIO_WHATSAPP_PHONE_NUMBER_US;
  }

  generateAccessToken(identity: string) {
    const AccessToken = jwt.AccessToken;
    const VoiceGrant = AccessToken.VoiceGrant;

    const token = new AccessToken(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_API_KEY,
      process.env.TWILIO_API_SECRET,
      { identity },
    );

    const voiceGrant = new VoiceGrant({
      outgoingApplicationSid: process.env.TWILIO_TWIML_APP_SID,
      incomingAllow: true,
    });

    token.addGrant(voiceGrant);

    return token.toJwt();
  }

  async sendWhatsAppMessage(
    to: string,
    message: string,
    mediaUrl?: string,
    region: string = 'UK',
    isTemplate = false,
    templateData?: Record<string, any>,
  ) {
    let formattedTo = to.trim();

    // Remove whatsapp: prefix if present to format the phone number
    if (formattedTo.startsWith('whatsapp:')) {
      formattedTo = formattedTo.replace('whatsapp:', '');
    }

    // Format to E.164 if not already formatted
    try {
      formattedTo = formatToE164(formattedTo);
      console.log(`📞 TWILIO-SERVICE: Phone number formatted to E.164: ${formattedTo}`);
    } catch (formatError) {
      console.error(`📞 TWILIO-SERVICE: Failed to format phone number: ${formatError.message}`);
      throw new Error(`Invalid phone number format: ${formatError.message}`);
    }

    // Add whatsapp: prefix back
    if (!formattedTo.startsWith('whatsapp:')) {
      formattedTo = `whatsapp:${formattedTo}`;
    }

    const from =
      region === 'UK' ? this.whatsappNumberUK : this.whatsappNumberUS;
    if (!from) {
      throw new Error(
        `WhatsApp number for region ${region} is not configured in .env`,
      );
    }

    try {
      let response: any;
      if (isTemplate) {
        if (!templateData) {
          throw new Error('Template data is required for template messages');
        }

        response = await this.client.messages.create({
          from: from,
          to: formattedTo,
          contentSid: process.env.TWILIO_WHATSAPP_TEMPLATE_SID,
          contentVariables: JSON.stringify({
            ...templateData,
          }),
        });
      } else {
        // Freeform message
        const payload: any = {
          from: from,
          to: formattedTo,
          body: message,
        };

        if (mediaUrl) {
          payload.mediaUrl = mediaUrl;
        }

        response = await this.client.messages.create(payload);
      }

      return response;
    } catch (error) {
      console.error('Failed to send WhatsApp message:', error);
      throw error;
    }
  }

  async saveWhatsAppStatusUpdate(messageSid: string, status: string) {
    if (!messageSid || !status) {
      throw new Error('messageSid and status are required');
    }
    console.log(
      `Saving WhatsApp status update for message SID ${messageSid}: ${status}`,
    );
    try {
      const message = await this.whatsappMessageRepo.findOne({
        where: { messageSid },
      });

      if (!message) {
        throw new Error(`Message with SID ${messageSid} not found`);
      }

      message.status = status;
      return this.whatsappMessageRepo.save(message);
    } catch (error) {
      console.error('Failed to save WhatsApp status update:', error);
      throw error;
    }
  }

  async sendMediaMessage(to: string, from: string, mediaUrl: string) {
    try {
      const response = await this.client.messages.create({
        from: `whatsapp:${from}`,
        to: `whatsapp:${to}`,
        mediaUrl: [mediaUrl],
      });
      return response;
    } catch (error) {
      console.error('Failed to send media message:', error);
      throw error;
    }
  }

  async getMessageStatus(messageSid: string) {
    try {
      const message = await this.client.messages(messageSid).fetch();
      return message.status;
    } catch (error) {
      console.error('Failed to fetch message status:', error);
      throw error;
    }
  }

  async getMediaContent(mediaSid: string) {
    try {
      const mediaList = await this.client.messages(mediaSid).media.list();
      if (!mediaList.length) {
        throw new Error('No media found for this message');
      }
      return mediaList[0].uri;
    } catch (error) {
      console.error('Failed to fetch media content:', error);
      throw error;
    }
  }

  async saveSentWhatsAppMessage(
    result: any,
    to: string,
    body: string,
    mediaUrl?: string,
    mediaContentType?: string,
  ) {
    const msg = this.whatsappMessageRepo.create({
      messageSid: result.sid,
      from: result.from,
      to,
      body,
      mediaUrl,
      mediaContentType,
      status: result.status,
    });
    return this.whatsappMessageRepo.save(msg);
  }

  async saveReceivedWhatsAppMessage(data: {
    messageSid: string;
    from: string;
    to: string;
    body: string;
    mediaUrl?: string;
    mediaContentType?: string;
    status?: string;
  }) {
    const msg = this.whatsappMessageRepo.create({
      messageSid: data.messageSid,
      from: data.from,
      to: data.to,
      body: data.body,
      mediaUrl: data.mediaUrl,
      mediaContentType: data.mediaContentType,
      status: data.status || 'received',
    });
    return this.whatsappMessageRepo.save(msg);
  }

  async sendSMSMessage(to: string, message: string) {
    try {
      // Format phone number to E.164 if not already formatted
      let formattedTo = to.trim();
      try {
        formattedTo = formatToE164(formattedTo);
        console.log(`📱 TWILIO-SERVICE: SMS phone number formatted to E.164: ${formattedTo}`);
      } catch (formatError) {
        console.error(`📱 TWILIO-SERVICE: Failed to format SMS phone number: ${formatError.message}`);
        throw new Error(`Invalid phone number format: ${formatError.message}`);
      }

      const result = await this.client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: formattedTo,
      });
      return result;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }

  async saveMessage(
    from: string,
    to: string,
    messageData: {
      messageSid?: string;
      sender?: string;
      text?: string;
      mediaUrl?: string;
      status?: string;
      timestamp?: Date;
    },
  ) {
    const message = this.whatsappMessageRepo.create({
      from,
      to,
      messageSid: messageData.messageSid,
      body: messageData.text,
      mediaUrl: messageData.mediaUrl,
      status: messageData.status || 'sent',
      createdAt: messageData.timestamp || new Date(),
    });

    return this.whatsappMessageRepo.save(message);
  }

  async saveSentSMSMessage(result: any, to: string, body: string) {
    const msg = this.whatsappMessageRepo.create({
      messageSid: result.sid,
      from: result.from,
      to,
      body,
      status: result.status,
    });
    return this.whatsappMessageRepo.save(msg);
  }

  async getWhatsAppMessagesByNumber(
    phoneNumber: string,
  ): Promise<WhatsAppMessage[]> {
    const formattedNumber = phoneNumber.startsWith('+')
      ? phoneNumber
      : `+${phoneNumber}`;

    return this.whatsappMessageRepo.find({
      where: [
        { to: ILike(`whatsapp:${formattedNumber}`) },
        { to: ILike(`%${formattedNumber}`) }, // for more flexibility
      ],
      order: { createdAt: 'ASC' },
    });
  }

  // Inside twillio.service.ts

async sendCombinedWhatsAppAndEmail(payload: any) {
  // Accepts either { candidates: [...] } or just an array
  const candidates = Array.isArray(payload)
    ? payload
    : Array.isArray(payload.candidates)
      ? payload.candidates
      : [];

  console.log('Sending combined WhatsApp and email messages to candidates:', candidates);

  const results = [];

  interface Candidate {
    first_name: string;
    email: string;
    whatsappNumbers: string;
    title: string;
    location: string;
  }

  interface ChannelResult {
    channel: 'whatsapp' | 'email';
    status: 'sent' | 'failed';
    candidate: string;
    error?: string;
  }

  interface CombinedResult {
    candidate: string;
    whatsapp: ChannelResult;
    email: ChannelResult;
  }

  await Promise.all(
    (candidates as Candidate[]).map(async (candidate: Candidate) => {
      const { first_name, email, whatsappNumbers, title, location } = candidate;
      const name = first_name;
      const phone = whatsappNumbers;

      const whatsappPromise: Promise<ChannelResult> = this.sendWhatsAppMessage(
        phone,
        '', // Twilio template message (body not used)
        undefined, // no media
        'UK',
        true,
        { name, title, location }
      ).then((): ChannelResult => ({
        channel: 'whatsapp' as const,
        status: 'sent',
        candidate: name,
      }))
        .catch((err: Error): ChannelResult => ({
          channel: 'whatsapp',
          status: 'failed',
          candidate: name,
          error: err.message,
        }));

      const emailSubject = `${title} – Competitive Salary + Benefits – ${location}`;
      const emailBody = `
        Hi ${name},<br><br>
        Coming across your profile, I thought you are a great match for my client’s <b>${title}</b> in <b>${location}</b>.<br><br>
        The role is paying Competitive Salary (DOE).<br><br>
        Let me know if you think it’s a good fit, I’ll be happy to share your profile across and arrange your interview soon.<br><br>
        Looking forward to your response.
      `;

      const emailPromise: Promise<ChannelResult> = this.emailService.sendEmailWithAttachmentsOld({
        to: [email],
        subject: emailSubject,
        body: emailBody,
        from: process.env.DEFAULT_FROM_EMAIL, // Ensure FROM is passed explicitly
        attachments: [],
      }).then((): ChannelResult => ({
        channel: 'email' as const,
        status: 'sent',
        candidate: name,
      }))
        .catch((err: Error): ChannelResult => ({
          channel: 'email',
          status: 'failed',
          candidate: name,
          error: err.message,
        }));

      const [whatsappResult, emailResult]: [ChannelResult, ChannelResult] = await Promise.all([
        whatsappPromise,
        emailPromise,
      ]);

      results.push({
        candidate: name,
        whatsapp: whatsappResult,
        email: emailResult,
      } as CombinedResult);
    })
  );

  return { results, summary: this.generateSummary(results) };
}


  private generateSummary(results: any[]) {
    const summary = { whatsappSent: 0, whatsappFailed: 0, emailSent: 0, emailFailed: 0 };

    results.forEach((res) => {
      if (res.whatsapp.status === 'sent') summary.whatsappSent += 1;
      else summary.whatsappFailed += 1;

      if (res.email.status === 'sent') summary.emailSent += 1;
      else summary.emailFailed += 1;
    });

    return summary;
  }

}
