{"version": 3, "sources": ["../../classnames/index.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,KAAC,WAAY;AACZ;AAEA,UAAI,SAAS,CAAC,EAAE;AAEhB,eAAS,aAAc;AACtB,YAAI,UAAU;AAEd,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,cAAI,MAAM,UAAU,CAAC;AACrB,cAAI,KAAK;AACR,sBAAU,YAAY,SAAS,WAAW,GAAG,CAAC;AAAA,UAC/C;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,WAAY,KAAK;AACzB,YAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACvD,iBAAO;AAAA,QACR;AAEA,YAAI,OAAO,QAAQ,UAAU;AAC5B,iBAAO;AAAA,QACR;AAEA,YAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,iBAAO,WAAW,MAAM,MAAM,GAAG;AAAA,QAClC;AAEA,YAAI,IAAI,aAAa,OAAO,UAAU,YAAY,CAAC,IAAI,SAAS,SAAS,EAAE,SAAS,eAAe,GAAG;AACrG,iBAAO,IAAI,SAAS;AAAA,QACrB;AAEA,YAAI,UAAU;AAEd,iBAAS,OAAO,KAAK;AACpB,cAAI,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG;AACtC,sBAAU,YAAY,SAAS,GAAG;AAAA,UACnC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,YAAa,OAAO,UAAU;AACtC,YAAI,CAAC,UAAU;AACd,iBAAO;AAAA,QACR;AAEA,YAAI,OAAO;AACV,iBAAO,QAAQ,MAAM;AAAA,QACtB;AAEA,eAAO,QAAQ;AAAA,MAChB;AAEA,UAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACpD,mBAAW,UAAU;AACrB,eAAO,UAAU;AAAA,MAClB,WAAW,OAAO,WAAW,cAAc,OAAO,OAAO,QAAQ,YAAY,OAAO,KAAK;AAExF,eAAO,cAAc,CAAC,GAAG,WAAY;AACpC,iBAAO;AAAA,QACR,CAAC;AAAA,MACF,OAAO;AACN,eAAO,aAAa;AAAA,MACrB;AAAA,IACD,GAAE;AAAA;AAAA;", "names": []}