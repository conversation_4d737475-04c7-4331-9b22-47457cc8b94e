{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/groovy.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = words(\n  \"abstract as assert boolean break byte case catch char class const continue def default \" +\n    \"do double else enum extends final finally float for goto if implements import in \" +\n    \"instanceof int interface long native new package private protected public return \" +\n    \"short static strictfp super switch synchronized threadsafe throw throws trait transient \" +\n    \"try void volatile while\");\nvar blockKeywords = words(\"catch class def do else enum finally for if interface switch trait try while\");\nvar standaloneKeywords = words(\"return break continue\");\nvar atoms = words(\"null true false this\");\n\nvar curPunc;\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    return startString(ch, stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    if (stream.eat(/eE/)) { stream.eat(/\\+\\-/); stream.eatWhile(/\\d/); }\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize.push(tokenComment);\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (expectExpression(state.lastToken, false)) {\n      return startString(ch, stream, state);\n    }\n  }\n  if (ch == \"-\" && stream.eat(\">\")) {\n    curPunc = \"->\";\n    return null;\n  }\n  if (/[+\\-*&%=<>!?|\\/~]/.test(ch)) {\n    stream.eatWhile(/[+\\-*&%=<>|~]/);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  if (ch == \"@\") { stream.eatWhile(/[\\w\\$_\\.]/); return \"meta\"; }\n  if (state.lastToken == \".\") return \"property\";\n  if (stream.eat(\":\")) { curPunc = \"proplabel\"; return \"property\"; }\n  var cur = stream.current();\n  if (atoms.propertyIsEnumerable(cur)) { return \"atom\"; }\n  if (keywords.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    else if (standaloneKeywords.propertyIsEnumerable(cur)) curPunc = \"standalone\";\n    return \"keyword\";\n  }\n  return \"variable\";\n}\ntokenBase.isBase = true;\n\nfunction startString(quote, stream, state) {\n  var tripleQuoted = false;\n  if (quote != \"/\" && stream.eat(quote)) {\n    if (stream.eat(quote)) tripleQuoted = true;\n    else return \"string\";\n  }\n  function t(stream, state) {\n    var escaped = false, next, end = !tripleQuoted;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        if (!tripleQuoted) { break; }\n        if (stream.match(quote + quote)) { end = true; break; }\n      }\n      if (quote == '\"' && next == \"$\" && !escaped) {\n        if (stream.eat(\"{\")) {\n          state.tokenize.push(tokenBaseUntilBrace());\n          return \"string\";\n        } else if (stream.match(/^\\w/, false)) {\n          state.tokenize.push(tokenVariableDeref);\n          return \"string\";\n        }\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize.pop();\n    return \"string\";\n  }\n  state.tokenize.push(t);\n  return t(stream, state);\n}\n\nfunction tokenBaseUntilBrace() {\n  var depth = 1;\n  function t(stream, state) {\n    if (stream.peek() == \"}\") {\n      depth--;\n      if (depth == 0) {\n        state.tokenize.pop();\n        return state.tokenize[state.tokenize.length-1](stream, state);\n      }\n    } else if (stream.peek() == \"{\") {\n      depth++;\n    }\n    return tokenBase(stream, state);\n  }\n  t.isBase = true;\n  return t;\n}\n\nfunction tokenVariableDeref(stream, state) {\n  var next = stream.match(/^(\\.|[\\w\\$_]+)/)\n  if (!next || !stream.match(next[0] == \".\" ? /^[\\w$_]/ : /^\\./)) state.tokenize.pop()\n  if (!next) return state.tokenize[state.tokenize.length-1](stream, state)\n  return next[0] == \".\" ? null : \"variable\"\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize.pop();\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction expectExpression(last, newline) {\n  return !last || last == \"operator\" || last == \"->\" || /[\\.\\[\\{\\(,;:]/.test(last) ||\n    last == \"newstatement\" || last == \"keyword\" || last == \"proplabel\" ||\n    (last == \"standalone\" && !newline);\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const groovy = {\n  name: \"groovy\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: [tokenBase],\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true,\n      lastToken: null\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n      // Automatic semicolon insertion\n      if (ctx.type == \"statement\" && !expectExpression(state.lastToken, true)) {\n        popContext(state); ctx = state.context;\n      }\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = state.tokenize[state.tokenize.length-1](stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\") && ctx.type == \"statement\") popContext(state);\n    // Handle indentation for {x -> \\n ... }\n    else if (curPunc == \"->\" && ctx.type == \"statement\" && ctx.prev.type == \"}\") {\n      popContext(state);\n      state.context.align = false;\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (ctx.type == \"}\" || ctx.type == \"top\" || (ctx.type == \"statement\" && curPunc == \"newstatement\"))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    state.lastToken = curPunc || style;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (!state.tokenize[state.tokenize.length-1].isBase) return null;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.context;\n    if (ctx.type == \"statement\" && !expectExpression(state.lastToken, true)) ctx = ctx.prev;\n    var closing = firstChar == ctx.type;\n    if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"']}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE;AAAG,QAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AACA,IAAI,WAAW;AAAA,EACb;AAI2B;AAC7B,IAAI,gBAAgB,MAAM,8EAA8E;AACxG,IAAI,qBAAqB,MAAM,uBAAuB;AACtD,IAAI,QAAQ,MAAM,sBAAsB;AAExC,IAAI;AACJ,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,WAAO,YAAY,IAAI,QAAQ,KAAK;AAAA,EACtC;AACA,MAAI,qBAAqB,KAAK,EAAE,GAAG;AACjC,cAAU;AACV,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,QAAI,OAAO,IAAI,IAAI,GAAG;AAAE,aAAO,IAAI,MAAM;AAAG,aAAO,SAAS,IAAI;AAAA,IAAG;AACnE,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,SAAS,KAAK,YAAY;AAChC,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,MAAM,WAAW,KAAK,GAAG;AAC5C,aAAO,YAAY,IAAI,QAAQ,KAAK;AAAA,IACtC;AAAA,EACF;AACA,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,cAAU;AACV,WAAO;AAAA,EACT;AACA,MAAI,oBAAoB,KAAK,EAAE,GAAG;AAChC,WAAO,SAAS,eAAe;AAC/B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAS;AACzB,MAAI,MAAM,KAAK;AAAE,WAAO,SAAS,WAAW;AAAG,WAAO;AAAA,EAAQ;AAC9D,MAAI,MAAM,aAAa;AAAK,WAAO;AACnC,MAAI,OAAO,IAAI,GAAG,GAAG;AAAE,cAAU;AAAa,WAAO;AAAA,EAAY;AACjE,MAAI,MAAM,OAAO,QAAQ;AACzB,MAAI,MAAM,qBAAqB,GAAG,GAAG;AAAE,WAAO;AAAA,EAAQ;AACtD,MAAI,SAAS,qBAAqB,GAAG,GAAG;AACtC,QAAI,cAAc,qBAAqB,GAAG;AAAG,gBAAU;AAAA,aAC9C,mBAAmB,qBAAqB,GAAG;AAAG,gBAAU;AACjE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,UAAU,SAAS;AAEnB,SAAS,YAAY,OAAO,QAAQ,OAAO;AACzC,MAAI,eAAe;AACnB,MAAI,SAAS,OAAO,OAAO,IAAI,KAAK,GAAG;AACrC,QAAI,OAAO,IAAI,KAAK;AAAG,qBAAe;AAAA;AACjC,aAAO;AAAA,EACd;AACA,WAAS,EAAEC,SAAQC,QAAO;AACxB,QAAI,UAAU,OAAO,MAAM,MAAM,CAAC;AAClC,YAAQ,OAAOD,QAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,YAAI,CAAC,cAAc;AAAE;AAAA,QAAO;AAC5B,YAAIA,QAAO,MAAM,QAAQ,KAAK,GAAG;AAAE,gBAAM;AAAM;AAAA,QAAO;AAAA,MACxD;AACA,UAAI,SAAS,OAAO,QAAQ,OAAO,CAAC,SAAS;AAC3C,YAAIA,QAAO,IAAI,GAAG,GAAG;AACnB,UAAAC,OAAM,SAAS,KAAK,oBAAoB,CAAC;AACzC,iBAAO;AAAA,QACT,WAAWD,QAAO,MAAM,OAAO,KAAK,GAAG;AACrC,UAAAC,OAAM,SAAS,KAAK,kBAAkB;AACtC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI;AAAK,MAAAA,OAAM,SAAS,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,CAAC;AACrB,SAAO,EAAE,QAAQ,KAAK;AACxB;AAEA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ;AACZ,WAAS,EAAE,QAAQ,OAAO;AACxB,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB;AACA,UAAI,SAAS,GAAG;AACd,cAAM,SAAS,IAAI;AACnB,eAAO,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC9D;AAAA,IACF,WAAW,OAAO,KAAK,KAAK,KAAK;AAC/B;AAAA,IACF;AACA,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC;AACA,IAAE,SAAS;AACX,SAAO;AACT;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI,OAAO,OAAO,MAAM,gBAAgB;AACxC,MAAI,CAAC,QAAQ,CAAC,OAAO,MAAM,KAAK,CAAC,KAAK,MAAM,YAAY,KAAK;AAAG,UAAM,SAAS,IAAI;AACnF,MAAI,CAAC;AAAM,WAAO,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AACvE,SAAO,KAAK,CAAC,KAAK,MAAM,OAAO;AACjC;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,SAAS,IAAI;AACnB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,MAAM,SAAS;AACvC,SAAO,CAAC,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,gBAAgB,KAAK,IAAI,KAC7E,QAAQ,kBAAkB,QAAQ,aAAa,QAAQ,eACtD,QAAQ,gBAAgB,CAAC;AAC9B;AAEA,SAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,OAAO;AACd;AACA,SAAS,YAAY,OAAO,KAAK,MAAM;AACrC,SAAO,MAAM,UAAU,IAAI,QAAQ,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,IAAI,MAAM,QAAQ;AACtB,MAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AAC/B,UAAM,WAAW,MAAM,QAAQ;AACjC,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AAIO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MACL,UAAU,CAAC,SAAS;AAAA,MACpB,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,KAAK;AAAA,MACjD,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,IAAI,SAAS;AAAM,YAAI,QAAQ;AACnC,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,cAAc;AAEpB,UAAI,IAAI,QAAQ,eAAe,CAAC,iBAAiB,MAAM,WAAW,IAAI,GAAG;AACvE,mBAAW,KAAK;AAAG,cAAM,MAAM;AAAA,MACjC;AAAA,IACF;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,cAAU;AACV,QAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AACjE,QAAI,SAAS;AAAW,aAAO;AAC/B,QAAI,IAAI,SAAS;AAAM,UAAI,QAAQ;AAEnC,SAAK,WAAW,OAAO,WAAW,QAAQ,IAAI,QAAQ;AAAa,iBAAW,KAAK;AAAA,aAE1E,WAAW,QAAQ,IAAI,QAAQ,eAAe,IAAI,KAAK,QAAQ,KAAK;AAC3E,iBAAW,KAAK;AAChB,YAAM,QAAQ,QAAQ;AAAA,IACxB,WACS,WAAW;AAAK,kBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW;AAAK,kBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW;AAAK,kBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,KAAK;AACvB,aAAO,IAAI,QAAQ;AAAa,cAAM,WAAW,KAAK;AACtD,UAAI,IAAI,QAAQ;AAAK,cAAM,WAAW,KAAK;AAC3C,aAAO,IAAI,QAAQ;AAAa,cAAM,WAAW,KAAK;AAAA,IACxD,WACS,WAAW,IAAI;AAAM,iBAAW,KAAK;AAAA,aACrC,IAAI,QAAQ,OAAO,IAAI,QAAQ,SAAU,IAAI,QAAQ,eAAe,WAAW;AACtF,kBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AACjD,UAAM,cAAc;AACpB,UAAM,YAAY,WAAW;AAC7B,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,CAAC,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE;AAAQ,aAAO;AAC5D,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,MAAM,MAAM;AAC9D,QAAI,IAAI,QAAQ,eAAe,CAAC,iBAAiB,MAAM,WAAW,IAAI;AAAG,YAAM,IAAI;AACnF,QAAI,UAAU,aAAa,IAAI;AAC/B,QAAI,IAAI,QAAQ;AAAa,aAAO,IAAI,YAAY,aAAa,MAAM,IAAI,GAAG;AAAA,aACrE,IAAI;AAAO,aAAO,IAAI,UAAU,UAAU,IAAI;AAAA;AAClD,aAAO,IAAI,YAAY,UAAU,IAAI,GAAG;AAAA,EAC/C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,EAAC;AAAA,EACnE;AACF;", "names": ["words", "stream", "state"]}