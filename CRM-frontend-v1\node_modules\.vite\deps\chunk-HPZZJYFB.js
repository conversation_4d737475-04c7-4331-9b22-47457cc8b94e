import {
  require_react
} from "./chunk-HLPDHYBP.js";
import {
  __toESM
} from "./chunk-ZDU32GKS.js";

// node_modules/@mui/utils/useEnhancedEffect/useEnhancedEffect.js
var React = __toESM(require_react());
var useEnhancedEffect = typeof window !== "undefined" ? React.useLayoutEffect : React.useEffect;
var useEnhancedEffect_default = useEnhancedEffect;

export {
  useEnhancedEffect_default
};
//# sourceMappingURL=chunk-HPZZJYFB.js.map
