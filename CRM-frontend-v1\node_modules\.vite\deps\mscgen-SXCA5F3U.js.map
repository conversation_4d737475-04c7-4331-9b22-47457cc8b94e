{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/mscgen.js"], "sourcesContent": ["function mkParser(lang) {\n  return {\n    name: \"mscgen\",\n    startState: startStateFn,\n    copyState: copyStateFn,\n    token: produceTokenFunction(lang),\n    languageData: {\n      commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n    }\n  }\n}\n\nexport const mscgen = mkParser({\n  \"keywords\" : [\"msc\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"], // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const msgenny = mkParser({\n  \"keywords\" : null,\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : null,\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const xu = mkParser({\n  \"keywords\" : [\"msc\", \"xu\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\", \"title\", \"deactivate\", \"activate\", \"activation\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],  // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nfunction wordRegexpBoundary(pWords) {\n  return new RegExp(\"^\\\\b(\" + pWords.join(\"|\") + \")\\\\b\", \"i\");\n}\n\nfunction wordRegexp(pWords) {\n  return new RegExp(\"^(?:\" + pWords.join(\"|\") + \")\", \"i\");\n}\n\nfunction startStateFn() {\n  return {\n    inComment : false,\n    inString : false,\n    inAttributeList : false,\n    inScript : false\n  };\n}\n\nfunction copyStateFn(pState) {\n  return {\n    inComment : pState.inComment,\n    inString : pState.inString,\n    inAttributeList : pState.inAttributeList,\n    inScript : pState.inScript\n  };\n}\n\nfunction produceTokenFunction(pConfig) {\n  return function(pStream, pState) {\n    if (pStream.match(wordRegexp(pConfig.brackets), true, true)) {\n      return \"bracket\";\n    }\n    /* comments */\n    if (!pState.inComment) {\n      if (pStream.match(/\\/\\*[^\\*\\/]*/, true, true)) {\n        pState.inComment = true;\n        return \"comment\";\n      }\n      if (pStream.match(wordRegexp(pConfig.singlecomment), true, true)) {\n        pStream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (pState.inComment) {\n      if (pStream.match(/[^\\*\\/]*\\*\\//, true, true))\n        pState.inComment = false;\n      else\n        pStream.skipToEnd();\n      return \"comment\";\n    }\n    /* strings */\n    if (!pState.inString && pStream.match(/\\\"(\\\\\\\"|[^\\\"])*/, true, true)) {\n      pState.inString = true;\n      return \"string\";\n    }\n    if (pState.inString) {\n      if (pStream.match(/[^\\\"]*\\\"/, true, true))\n        pState.inString = false;\n      else\n        pStream.skipToEnd();\n      return \"string\";\n    }\n    /* keywords & operators */\n    if (!!pConfig.keywords && pStream.match(wordRegexpBoundary(pConfig.keywords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.options), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.arcsWords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexp(pConfig.arcsOthers), true, true))\n      return \"keyword\";\n\n    if (!!pConfig.operators && pStream.match(wordRegexp(pConfig.operators), true, true))\n      return \"operator\";\n\n    if (!!pConfig.constants && pStream.match(wordRegexp(pConfig.constants), true, true))\n      return \"variable\";\n\n    /* attribute lists */\n    if (!pConfig.inAttributeList && !!pConfig.attributes && pStream.match('[', true, true)) {\n      pConfig.inAttributeList = true;\n      return \"bracket\";\n    }\n    if (pConfig.inAttributeList) {\n      if (pConfig.attributes !== null && pStream.match(wordRegexpBoundary(pConfig.attributes), true, true)) {\n        return \"attribute\";\n      }\n      if (pStream.match(']', true, true)) {\n        pConfig.inAttributeList = false;\n        return \"bracket\";\n      }\n    }\n\n    pStream.next();\n    return null\n  };\n}\n"], "mappings": ";;;AAAA,SAAS,SAAS,MAAM;AACtB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,OAAO,qBAAqB,IAAI;AAAA,IAChC,cAAc;AAAA,MACZ,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC7D;AAAA,EACF;AACF;AAEO,IAAM,SAAS,SAAS;AAAA,EAC7B,YAAa,CAAC,KAAK;AAAA,EACnB,WAAY,CAAC,UAAU,SAAS,eAAe,cAAc;AAAA,EAC7D,aAAc,CAAC,QAAQ,SAAS,MAAM,KAAK;AAAA,EAC3C,cAAe,CAAC,SAAS,SAAS,MAAM,OAAO,aAAa,cAAc,aAAa,cAAc,eAAe,gBAAgB,gBAAgB,iBAAiB,gBAAgB,iBAAiB,kBAAkB,mBAAmB,SAAS;AAAA,EACpP,YAAa,CAAC,OAAO,KAAK;AAAA;AAAA,EAC1B,aAAc,CAAC,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAC5C,cAAe,CAAC,aAAa,aAAa,OAAO,MAAM,OAAO,MAAM,SAAS,OAAO,UAAU,QAAQ,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC3L,iBAAkB,CAAC,MAAM,GAAG;AAAA,EAC5B,aAAc,CAAC,GAAG;AACpB,CAAC;AAEM,IAAM,UAAU,SAAS;AAAA,EAC9B,YAAa;AAAA,EACb,WAAY,CAAC,UAAU,SAAS,eAAe,gBAAgB,oBAAoB,WAAW;AAAA,EAC9F,aAAc,CAAC,QAAQ,SAAS,MAAM,OAAO,MAAM;AAAA,EACnD,cAAe;AAAA,EACf,YAAa,CAAC,OAAO,KAAK;AAAA,EAC1B,aAAc,CAAC,QAAQ,QAAQ,QAAQ,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO,UAAU,OAAO,YAAY,UAAU,YAAY,UAAU,QAAQ,OAAO,KAAK;AAAA,EAC5K,cAAe,CAAC,aAAa,aAAa,OAAO,MAAM,OAAO,MAAM,SAAS,OAAO,UAAU,QAAQ,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC3L,iBAAkB,CAAC,MAAM,GAAG;AAAA,EAC5B,aAAc,CAAC,GAAG;AACpB,CAAC;AAEM,IAAM,KAAK,SAAS;AAAA,EACzB,YAAa,CAAC,OAAO,IAAI;AAAA,EACzB,WAAY,CAAC,UAAU,SAAS,eAAe,gBAAgB,oBAAoB,WAAW;AAAA,EAC9F,aAAc,CAAC,QAAQ,SAAS,MAAM,OAAO,MAAM;AAAA,EACnD,cAAe,CAAC,SAAS,SAAS,MAAM,OAAO,aAAa,cAAc,aAAa,cAAc,eAAe,gBAAgB,gBAAgB,iBAAiB,gBAAgB,iBAAiB,kBAAkB,mBAAmB,WAAW,SAAS,cAAc,YAAY,YAAY;AAAA,EACrS,YAAa,CAAC,OAAO,KAAK;AAAA;AAAA,EAC1B,aAAc,CAAC,QAAQ,QAAQ,QAAQ,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO,UAAU,OAAO,YAAY,UAAU,YAAY,UAAU,QAAQ,OAAO,KAAK;AAAA,EAC5K,cAAe,CAAC,aAAa,aAAa,OAAO,MAAM,OAAO,MAAM,SAAS,OAAO,UAAU,QAAQ,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAC3L,iBAAkB,CAAC,MAAM,GAAG;AAAA,EAC5B,aAAc,CAAC,GAAG;AACpB,CAAC;AAED,SAAS,mBAAmB,QAAQ;AAClC,SAAO,IAAI,OAAO,UAAU,OAAO,KAAK,GAAG,IAAI,QAAQ,GAAG;AAC5D;AAEA,SAAS,WAAW,QAAQ;AAC1B,SAAO,IAAI,OAAO,SAAS,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG;AACxD;AAEA,SAAS,eAAe;AACtB,SAAO;AAAA,IACL,WAAY;AAAA,IACZ,UAAW;AAAA,IACX,iBAAkB;AAAA,IAClB,UAAW;AAAA,EACb;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,SAAO;AAAA,IACL,WAAY,OAAO;AAAA,IACnB,UAAW,OAAO;AAAA,IAClB,iBAAkB,OAAO;AAAA,IACzB,UAAW,OAAO;AAAA,EACpB;AACF;AAEA,SAAS,qBAAqB,SAAS;AACrC,SAAO,SAAS,SAAS,QAAQ;AAC/B,QAAI,QAAQ,MAAM,WAAW,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAG;AAC3D,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,OAAO,WAAW;AACrB,UAAI,QAAQ,MAAM,gBAAgB,MAAM,IAAI,GAAG;AAC7C,eAAO,YAAY;AACnB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,MAAM,WAAW,QAAQ,aAAa,GAAG,MAAM,IAAI,GAAG;AAChE,gBAAQ,UAAU;AAClB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO,WAAW;AACpB,UAAI,QAAQ,MAAM,gBAAgB,MAAM,IAAI;AAC1C,eAAO,YAAY;AAAA;AAEnB,gBAAQ,UAAU;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,OAAO,YAAY,QAAQ,MAAM,mBAAmB,MAAM,IAAI,GAAG;AACpE,aAAO,WAAW;AAClB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,UAAU;AACnB,UAAI,QAAQ,MAAM,YAAY,MAAM,IAAI;AACtC,eAAO,WAAW;AAAA;AAElB,gBAAQ,UAAU;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,CAAC,QAAQ,YAAY,QAAQ,MAAM,mBAAmB,QAAQ,QAAQ,GAAG,MAAM,IAAI;AACtF,aAAO;AAET,QAAI,QAAQ,MAAM,mBAAmB,QAAQ,OAAO,GAAG,MAAM,IAAI;AAC/D,aAAO;AAET,QAAI,QAAQ,MAAM,mBAAmB,QAAQ,SAAS,GAAG,MAAM,IAAI;AACjE,aAAO;AAET,QAAI,QAAQ,MAAM,WAAW,QAAQ,UAAU,GAAG,MAAM,IAAI;AAC1D,aAAO;AAET,QAAI,CAAC,CAAC,QAAQ,aAAa,QAAQ,MAAM,WAAW,QAAQ,SAAS,GAAG,MAAM,IAAI;AAChF,aAAO;AAET,QAAI,CAAC,CAAC,QAAQ,aAAa,QAAQ,MAAM,WAAW,QAAQ,SAAS,GAAG,MAAM,IAAI;AAChF,aAAO;AAGT,QAAI,CAAC,QAAQ,mBAAmB,CAAC,CAAC,QAAQ,cAAc,QAAQ,MAAM,KAAK,MAAM,IAAI,GAAG;AACtF,cAAQ,kBAAkB;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,iBAAiB;AAC3B,UAAI,QAAQ,eAAe,QAAQ,QAAQ,MAAM,mBAAmB,QAAQ,UAAU,GAAG,MAAM,IAAI,GAAG;AACpG,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAQ,kBAAkB;AAC1B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,YAAQ,KAAK;AACb,WAAO;AAAA,EACT;AACF;", "names": []}