{"version": 3, "sources": ["../../@codesandbox/sandpack-client/node_modules/outvariant/src/format.ts", "../../@codesandbox/sandpack-client/node_modules/outvariant/src/invariant.ts", "../../@codesandbox/sandpack-client/dist/utils-52664384.mjs"], "sourcesContent": ["const POSITIONALS_EXP = /(%?)(%([sdjo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const isConstructor = ErrorConstructor.prototype.name != null\n\n    const error: Error = isConstructor\n      ? // @ts-ignore\n        new ErrorConstructor(format(message, positionals))\n      : // @ts-ignore\n        ErrorConstructor(format(message, positionals))\n\n    throw error\n  }\n}\n", "import { invariant } from 'outvariant';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND F<PERSON>NESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* globalThis Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\n\nvar SandpackLogLevel;\n(function (SandpackLogLevel) {\n    SandpackLogLevel[SandpackLogLevel[\"None\"] = 0] = \"None\";\n    SandpackLogLevel[SandpackLogLevel[\"Error\"] = 10] = \"Error\";\n    SandpackLogLevel[SandpackLogLevel[\"Warning\"] = 20] = \"Warning\";\n    SandpackLogLevel[SandpackLogLevel[\"Info\"] = 30] = \"Info\";\n    SandpackLogLevel[SandpackLogLevel[\"Debug\"] = 40] = \"Debug\";\n})(SandpackLogLevel || (SandpackLogLevel = {}));\n\nvar createError = function (message) {\n    return \"[sandpack-client]: \".concat(message);\n};\nfunction nullthrows(value, err) {\n    if (err === void 0) { err = \"Value is nullish\"; }\n    invariant(value != null, createError(err));\n    return value;\n}\nvar DEPENDENCY_ERROR_MESSAGE = \"\\\"dependencies\\\" was not specified - provide either a package.json or a \\\"dependencies\\\" value\";\nvar ENTRY_ERROR_MESSAGE = \"\\\"entry\\\" was not specified - provide either a package.json with the \\\"main\\\" field or an \\\"entry\\\" value\";\nfunction createPackageJSON(dependencies, devDependencies, entry) {\n    if (dependencies === void 0) { dependencies = {}; }\n    if (devDependencies === void 0) { devDependencies = {}; }\n    if (entry === void 0) { entry = \"/index.js\"; }\n    return JSON.stringify({\n        name: \"sandpack-project\",\n        main: entry,\n        dependencies: dependencies,\n        devDependencies: devDependencies,\n    }, null, 2);\n}\nfunction addPackageJSONIfNeeded(files, dependencies, devDependencies, entry) {\n    var _a, _b;\n    var normalizedFilesPath = normalizePath(files);\n    var packageJsonFile = normalizedFilesPath[\"/package.json\"];\n    /**\n     * Create a new package json\n     */\n    if (!packageJsonFile) {\n        nullthrows(dependencies, DEPENDENCY_ERROR_MESSAGE);\n        nullthrows(entry, ENTRY_ERROR_MESSAGE);\n        normalizedFilesPath[\"/package.json\"] = {\n            code: createPackageJSON(dependencies, devDependencies, entry),\n        };\n        return normalizedFilesPath;\n    }\n    /**\n     * Merge package json with custom setup\n     */\n    if (packageJsonFile) {\n        var packageJsonContent = JSON.parse(packageJsonFile.code);\n        nullthrows(!(!dependencies && !packageJsonContent.dependencies), ENTRY_ERROR_MESSAGE);\n        if (dependencies) {\n            packageJsonContent.dependencies = __assign(__assign({}, ((_a = packageJsonContent.dependencies) !== null && _a !== void 0 ? _a : {})), (dependencies !== null && dependencies !== void 0 ? dependencies : {}));\n        }\n        if (devDependencies) {\n            packageJsonContent.devDependencies = __assign(__assign({}, ((_b = packageJsonContent.devDependencies) !== null && _b !== void 0 ? _b : {})), (devDependencies !== null && devDependencies !== void 0 ? devDependencies : {}));\n        }\n        if (entry) {\n            packageJsonContent.main = entry;\n        }\n        normalizedFilesPath[\"/package.json\"] = {\n            code: JSON.stringify(packageJsonContent, null, 2),\n        };\n    }\n    return normalizedFilesPath;\n}\nfunction extractErrorDetails(msg) {\n    var _a;\n    if (msg.title === \"SyntaxError\") {\n        var title = msg.title, path = msg.path, message = msg.message, line = msg.line, column = msg.column;\n        return { title: title, path: path, message: message, line: line, column: column };\n    }\n    var relevantStackFrame = getRelevantStackFrame((_a = msg.payload) === null || _a === void 0 ? void 0 : _a.frames);\n    if (!relevantStackFrame) {\n        return { message: msg.message };\n    }\n    var errorInCode = getErrorInOriginalCode(relevantStackFrame);\n    var errorLocation = getErrorLocation(relevantStackFrame);\n    var errorMessage = formatErrorMessage(relevantStackFrame._originalFileName, msg.message, errorLocation, errorInCode);\n    return {\n        message: errorMessage,\n        title: msg.title,\n        path: relevantStackFrame._originalFileName,\n        line: relevantStackFrame._originalLineNumber,\n        column: relevantStackFrame._originalColumnNumber,\n    };\n}\nfunction getRelevantStackFrame(frames) {\n    if (!frames) {\n        return;\n    }\n    return frames.find(function (frame) { return !!frame._originalFileName; });\n}\nfunction getErrorLocation(errorFrame) {\n    return errorFrame\n        ? \" (\".concat(errorFrame._originalLineNumber, \":\").concat(errorFrame._originalColumnNumber, \")\")\n        : \"\";\n}\nfunction getErrorInOriginalCode(errorFrame) {\n    var lastScriptLine = errorFrame._originalScriptCode[errorFrame._originalScriptCode.length - 1];\n    var numberOfLineNumberCharacters = lastScriptLine.lineNumber.toString().length;\n    var leadingCharacterOffset = 2;\n    var barSeparatorCharacterOffset = 3;\n    var extraLineLeadingSpaces = leadingCharacterOffset +\n        numberOfLineNumberCharacters +\n        barSeparatorCharacterOffset +\n        errorFrame._originalColumnNumber;\n    return errorFrame._originalScriptCode.reduce(function (result, scriptLine) {\n        var leadingChar = scriptLine.highlight ? \">\" : \" \";\n        var lineNumber = scriptLine.lineNumber.toString().length === numberOfLineNumberCharacters\n            ? \"\".concat(scriptLine.lineNumber)\n            : \" \".concat(scriptLine.lineNumber);\n        var extraLine = scriptLine.highlight\n            ? \"\\n\" + \" \".repeat(extraLineLeadingSpaces) + \"^\"\n            : \"\";\n        return (result + // accumulator\n            \"\\n\" +\n            leadingChar + // > or \" \"\n            \" \" +\n            lineNumber + // line number on equal number of characters\n            \" | \" +\n            scriptLine.content + // code\n            extraLine // line under the highlighed line to show the column index\n        );\n    }, \"\");\n}\nfunction formatErrorMessage(filePath, message, location, errorInCode) {\n    return \"\".concat(filePath, \": \").concat(message).concat(location, \"\\n\").concat(errorInCode);\n}\n/* eslint-disable @typescript-eslint/no-explicit-any */\nvar normalizePath = function (path) {\n    if (typeof path === \"string\") {\n        return (path.startsWith(\"/\") ? path : \"/\".concat(path));\n    }\n    if (Array.isArray(path)) {\n        return path.map(function (p) { return (p.startsWith(\"/\") ? p : \"/\".concat(p)); });\n    }\n    if (typeof path === \"object\" && path !== null) {\n        return Object.entries(path).reduce(function (acc, _a) {\n            var key = _a[0], content = _a[1];\n            var fileName = key.startsWith(\"/\") ? key : \"/\".concat(key);\n            acc[fileName] = content;\n            return acc;\n        }, {});\n    }\n    return null;\n};\n\nexport { SandpackLogLevel as S, __awaiter as _, __generator as a, createPackageJSON as b, createError as c, addPackageJSONIfNeeded as d, extractErrorDetails as e, normalizePath as f, __extends as g, __assign as h, __spreadArray as i, nullthrows as n };\n"], "mappings": ";AAAA,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,YAAiB,MAAmB;AAC/D,UAAQ,MAAM;IAEZ,KAAK;AACH,aAAO;IAGT,KAAK;IACL,KAAK;AACH,aAAO,OAAO,UAAU;IAG1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;IAGlC,KAAK,KAAK;AAER,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;MACT;AAEA,YAAM,OAAO,KAAK,UAAU,UAAU;AAGtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;MACT;AAEA,aAAO;IACT;EACF;AACF;AAEO,SAAS,OAAO,YAAoB,aAA4B;AACrE,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;EACT;AAEA,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,QAAQ;IAC7B;IACA,CAAC,OAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY,eAAA;AAC/B,YAAM,QAAQ,oBAAoB,YAAY,IAAI;AAElD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;MACT;AAEA,aAAO;IACT;EACF;AAGA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG,CAAA;EACrE;AAEA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAE1D,SAAO;AACT;AC/DA,IAAM,yBAAyB;AAO/B,SAAS,gBAAgB,OAAoB;AAC3C,MAAI,CAAC,MAAM,OAAO;AAChB;EACF;AAEA,QAAM,YAAY,MAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAG,sBAAsB;AAC1C,QAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AAEO,IAAM,iBAAN,cAA6B,MAAM;EAGxC,YAA4B,YAAoB,aAAoB;AAClE,UAAM,OAAO;AADa,SAAA,UAAA;AAF5B,SAAA,OAAO;AAIL,SAAK,UAAU,OAAO,SAAS,GAAG,WAAW;AAC7C,oBAAgB,IAAI;EACtB;AACF;AA2BO,IAAM,YAAuB,CAClC,WACA,YACG,gBACmB;AACtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAe,SAAS,GAAG,WAAW;EAClD;AACF;AAEA,UAAU,KAAK,CAAC,kBAAkB,WAAW,YAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAM,gBAAgB,iBAAiB,UAAU,QAAQ;AAEzD,UAAM,QAAe,gBAEjB,IAAI,iBAAiB,OAAO,SAAS,WAAW,CAAC,IAEjD,iBAAiB,OAAO,SAAS,WAAW,CAAC;AAEjD,UAAM;EACR;AACF;;;AC1DA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUA,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA;AAAG,UAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,QAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAClD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,SAAS,YAAY,SAAS,MAAM;AAChC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO;AAAG,UAAI;AACV,YAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,YAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEA,SAAS,cAAc,IAAI,MAAM,MAAM;AACnC,MAAI,QAAQ,UAAU,WAAW;AAAG,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,UAAI,MAAM,EAAE,KAAK,OAAO;AACpB,YAAI,CAAC;AAAI,eAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,WAAG,CAAC,IAAI,KAAK,CAAC;AAAA,MAClB;AAAA,IACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAC3D;AAEA,IAAI;AAAA,CACH,SAAUC,mBAAkB;AACzB,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,OAAO,IAAI,EAAE,IAAI;AACnD,EAAAA,kBAAiBA,kBAAiB,SAAS,IAAI,EAAE,IAAI;AACrD,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,EAAE,IAAI;AAClD,EAAAA,kBAAiBA,kBAAiB,OAAO,IAAI,EAAE,IAAI;AACvD,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAI,cAAc,SAAU,SAAS;AACjC,SAAO,sBAAsB,OAAO,OAAO;AAC/C;AACA,SAAS,WAAW,OAAO,KAAK;AAC5B,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAoB;AAChD,YAAU,SAAS,MAAM,YAAY,GAAG,CAAC;AACzC,SAAO;AACX;AACA,IAAI,2BAA2B;AAC/B,IAAI,sBAAsB;AAC1B,SAAS,kBAAkB,cAAc,iBAAiB,OAAO;AAC7D,MAAI,iBAAiB,QAAQ;AAAE,mBAAe,CAAC;AAAA,EAAG;AAClD,MAAI,oBAAoB,QAAQ;AAAE,sBAAkB,CAAC;AAAA,EAAG;AACxD,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAa;AAC7C,SAAO,KAAK,UAAU;AAAA,IAClB,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACJ,GAAG,MAAM,CAAC;AACd;AACA,SAAS,uBAAuB,OAAO,cAAc,iBAAiB,OAAO;AACzE,MAAI,IAAI;AACR,MAAI,sBAAsB,cAAc,KAAK;AAC7C,MAAI,kBAAkB,oBAAoB,eAAe;AAIzD,MAAI,CAAC,iBAAiB;AAClB,eAAW,cAAc,wBAAwB;AACjD,eAAW,OAAO,mBAAmB;AACrC,wBAAoB,eAAe,IAAI;AAAA,MACnC,MAAM,kBAAkB,cAAc,iBAAiB,KAAK;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AAIA,MAAI,iBAAiB;AACjB,QAAI,qBAAqB,KAAK,MAAM,gBAAgB,IAAI;AACxD,eAAW,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,eAAe,mBAAmB;AACpF,QAAI,cAAc;AACd,yBAAmB,eAAe,SAAS,SAAS,CAAC,IAAK,KAAK,mBAAmB,kBAAkB,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAE,GAAI,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe,CAAC,CAAE;AAAA,IACjN;AACA,QAAI,iBAAiB;AACjB,yBAAmB,kBAAkB,SAAS,SAAS,CAAC,IAAK,KAAK,mBAAmB,qBAAqB,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAE,GAAI,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,CAAC,CAAE;AAAA,IAChO;AACA,QAAI,OAAO;AACP,yBAAmB,OAAO;AAAA,IAC9B;AACA,wBAAoB,eAAe,IAAI;AAAA,MACnC,MAAM,KAAK,UAAU,oBAAoB,MAAM,CAAC;AAAA,IACpD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,KAAK;AAC9B,MAAI;AACJ,MAAI,IAAI,UAAU,eAAe;AAC7B,QAAI,QAAQ,IAAI,OAAO,OAAO,IAAI,MAAM,UAAU,IAAI,SAAS,OAAO,IAAI,MAAM,SAAS,IAAI;AAC7F,WAAO,EAAE,OAAc,MAAY,SAAkB,MAAY,OAAe;AAAA,EACpF;AACA,MAAI,qBAAqB,uBAAuB,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAChH,MAAI,CAAC,oBAAoB;AACrB,WAAO,EAAE,SAAS,IAAI,QAAQ;AAAA,EAClC;AACA,MAAI,cAAc,uBAAuB,kBAAkB;AAC3D,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,eAAe,mBAAmB,mBAAmB,mBAAmB,IAAI,SAAS,eAAe,WAAW;AACnH,SAAO;AAAA,IACH,SAAS;AAAA,IACT,OAAO,IAAI;AAAA,IACX,MAAM,mBAAmB;AAAA,IACzB,MAAM,mBAAmB;AAAA,IACzB,QAAQ,mBAAmB;AAAA,EAC/B;AACJ;AACA,SAAS,sBAAsB,QAAQ;AACnC,MAAI,CAAC,QAAQ;AACT;AAAA,EACJ;AACA,SAAO,OAAO,KAAK,SAAU,OAAO;AAAE,WAAO,CAAC,CAAC,MAAM;AAAA,EAAmB,CAAC;AAC7E;AACA,SAAS,iBAAiB,YAAY;AAClC,SAAO,aACD,KAAK,OAAO,WAAW,qBAAqB,GAAG,EAAE,OAAO,WAAW,uBAAuB,GAAG,IAC7F;AACV;AACA,SAAS,uBAAuB,YAAY;AACxC,MAAI,iBAAiB,WAAW,oBAAoB,WAAW,oBAAoB,SAAS,CAAC;AAC7F,MAAI,+BAA+B,eAAe,WAAW,SAAS,EAAE;AACxE,MAAI,yBAAyB;AAC7B,MAAI,8BAA8B;AAClC,MAAI,yBAAyB,yBACzB,+BACA,8BACA,WAAW;AACf,SAAO,WAAW,oBAAoB,OAAO,SAAU,QAAQ,YAAY;AACvE,QAAI,cAAc,WAAW,YAAY,MAAM;AAC/C,QAAI,aAAa,WAAW,WAAW,SAAS,EAAE,WAAW,+BACvD,GAAG,OAAO,WAAW,UAAU,IAC/B,IAAI,OAAO,WAAW,UAAU;AACtC,QAAI,YAAY,WAAW,YACrB,OAAO,IAAI,OAAO,sBAAsB,IAAI,MAC5C;AACN,WAAQ;AAAA,IACJ,OACA;AAAA,IACA,MACA;AAAA,IACA,QACA,WAAW;AAAA,IACX;AAAA,EAER,GAAG,EAAE;AACT;AACA,SAAS,mBAAmB,UAAU,SAAS,UAAU,aAAa;AAClE,SAAO,GAAG,OAAO,UAAU,IAAI,EAAE,OAAO,OAAO,EAAE,OAAO,UAAU,IAAI,EAAE,OAAO,WAAW;AAC9F;AAEA,IAAI,gBAAgB,SAAU,MAAM;AAChC,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAQ,KAAK,WAAW,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI;AAAA,EACzD;AACA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACrB,WAAO,KAAK,IAAI,SAAU,GAAG;AAAE,aAAQ,EAAE,WAAW,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC;AAAA,IAAI,CAAC;AAAA,EACpF;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC3C,WAAO,OAAO,QAAQ,IAAI,EAAE,OAAO,SAAU,KAAK,IAAI;AAClD,UAAI,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAC/B,UAAI,WAAW,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,OAAO,GAAG;AACzD,UAAI,QAAQ,IAAI;AAChB,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AAAA,EACT;AACA,SAAO;AACX;", "names": ["d", "b", "__assign", "SandpackLogLevel"]}