{"version": 3, "sources": ["../../@mui/material/Avatar/Avatar.js", "../../@mui/material/internal/svg-icons/Person.js", "../../@mui/material/Avatar/avatarClasses.js", "../../@mui/material/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"alt\", \"children\", \"className\", \"component\", \"slots\", \"slotProps\", \"imgProps\", \"sizes\", \"src\", \"srcSet\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, createUseThemeProps } from '../zero-styled';\nimport Person from '../internal/svg-icons/Person';\nimport { getAvatarUtilityClass } from './avatarClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useThemeProps = createUseThemeProps('MuiAvatar');\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    colorDefault\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, colorDefault && 'colorDefault'],\n    img: ['img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, classes);\n};\nconst AvatarRoot = styled('div', {\n  name: 'MuiAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.colorDefault && styles.colorDefault];\n  }\n})(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  flexShrink: 0,\n  width: 40,\n  height: 40,\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(20),\n  lineHeight: 1,\n  borderRadius: '50%',\n  overflow: 'hidden',\n  userSelect: 'none',\n  variants: [{\n    props: {\n      variant: 'rounded'\n    },\n    style: {\n      borderRadius: (theme.vars || theme).shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'square'\n    },\n    style: {\n      borderRadius: 0\n    }\n  }, {\n    props: {\n      colorDefault: true\n    },\n    style: _extends({\n      color: (theme.vars || theme).palette.background.default\n    }, theme.vars ? {\n      backgroundColor: theme.vars.palette.Avatar.defaultBg\n    } : _extends({\n      backgroundColor: theme.palette.grey[400]\n    }, theme.applyStyles('dark', {\n      backgroundColor: theme.palette.grey[600]\n    })))\n  }]\n}));\nconst AvatarImg = styled('img', {\n  name: 'MuiAvatar',\n  slot: 'Img',\n  overridesResolver: (props, styles) => styles.img\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image. The property isn't supported by IE11.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'MuiAvatar',\n  slot: 'Fallback',\n  overridesResolver: (props, styles) => styles.fallback\n})({\n  width: '75%',\n  height: '75%'\n});\nfunction useLoaded({\n  crossOrigin,\n  referrerPolicy,\n  src,\n  srcSet\n}) {\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    image.src = src;\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAvatar'\n  });\n  const {\n      alt,\n      children: childrenProp,\n      className,\n      component = 'div',\n      slots = {},\n      slotProps = {},\n      imgProps,\n      sizes,\n      src,\n      srcSet,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let children = null;\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded(_extends({}, imgProps, {\n    src,\n    srcSet\n  }));\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  const ownerState = _extends({}, props, {\n    colorDefault: !hasImgNotFailing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const [ImgSlot, imgSlotProps] = useSlot('img', {\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        img: _extends({}, imgProps, slotProps.img)\n      }\n    },\n    additionalProps: {\n      alt,\n      src,\n      srcSet,\n      sizes\n    },\n    ownerState\n  });\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(ImgSlot, _extends({}, imgSlotProps));\n    // We only render valid children, non valid children are rendered with a fallback\n    // We consider that invalid children are all falsy values, except 0, which is valid.\n  } else if (!!childrenProp || childrenProp === 0) {\n    children = childrenProp;\n  } else if (hasImg && alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(AvatarFallback, {\n      ownerState: ownerState,\n      className: classes.fallback\n    });\n  }\n  return /*#__PURE__*/_jsx(AvatarRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attributes) applied to the `img` element if the component is used to display an image.\n   * It can be used to listen for the loading error event.\n   * @deprecated Use `slotProps.img` instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   */\n  imgProps: PropTypes.object,\n  /**\n   * The `sizes` attribute for the `img` element.\n   */\n  sizes: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    img: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The shape of the avatar.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default Avatar;", "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from '../../utils/createSvgIcon';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n}), 'Person');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorDefault', 'circular', 'rounded', 'square', 'img', 'fallback']);\nexport default avatarClasses;", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"getSlotOwnerState\", \"internalForwardedProps\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport { appendOwnerState, resolveComponentProps, mergeSlotProps } from '@mui/base/utils';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      getSlotOwnerState,\n      internalForwardedProps\n    } = parameters,\n    useSlotPropsParams = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutPropertiesLoose(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_extends({\n      className\n    }, useSlotPropsParams, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutPropertiesLoose(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};\n  const finalOwnerState = _extends({}, ownerState, slotOwnerState);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _extends({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {\n    as: LeafComponent\n  }, {\n    ref\n  }), finalOwnerState);\n  Object.keys(slotOwnerState).forEach(propName => {\n    delete props[propName];\n  });\n  return [elementType, props];\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAAA,SAAuB;AACvB,wBAAsB;;;ACJtB,YAAuB;AAMvB,yBAA4B;AAC5B,IAAO,iBAAQ,kBAA4B,mBAAAC,KAAK,QAAQ;AAAA,EACtD,GAAG;AACL,CAAC,GAAG,QAAQ;;;ACTL,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,gBAAgB,YAAY,WAAW,UAAU,OAAO,UAAU,CAAC;AACtI,IAAO,wBAAQ;;;ACFf,IAAM,YAAY,CAAC,aAAa,eAAe,cAAc,0BAA0B,qBAAqB,wBAAwB;AAApI,IACE,aAAa,CAAC,aAAa,SAAS,WAAW;AADjD,IAEE,aAAa,CAAC,WAAW;AAiBZ,SAAR,QAOP,MAAM,YAAY;AAChB,QAAM;AAAA,IACF;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YACJ,qBAAqB,8BAA8B,YAAY,SAAS;AAC1E,QAAM;AAAA,IACF,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,EACF,IAAI,wBACJ,QAAQ,8BAA8B,wBAAwB,UAAU;AAC1E,QAAM,cAAc,MAAM,IAAI,KAAK;AAInC,QAAM,0BAA0B,sBAAsB,UAAU,IAAI,GAAG,UAAU;AACjF,QAAM,kBAAkB,eAAe,SAAS;AAAA,IAC5C;AAAA,EACF,GAAG,oBAAoB;AAAA,IACrB,wBAAwB,SAAS,SAAS,QAAQ;AAAA,IAClD,mBAAmB;AAAA,EACrB,CAAC,CAAC,GACF;AAAA,IACE,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,IACA;AAAA,EACF,IAAI,iBACJ,cAAc,8BAA8B,gBAAgB,OAAO,UAAU;AAC/E,QAAM,MAAM,WAAW,aAAa,2BAA2B,OAAO,SAAS,wBAAwB,KAAK,WAAW,GAAG;AAC1H,QAAM,iBAAiB,oBAAoB,kBAAkB,WAAW,IAAI,CAAC;AAC7E,QAAM,kBAAkB,SAAS,CAAC,GAAG,YAAY,cAAc;AAC/D,QAAM,gBAAgB,SAAS,SAAS,iBAAiB,gBAAgB;AACzE,QAAM,QAAQ,iBAAiB,aAAa,SAAS,CAAC,GAAG,SAAS,UAAU,CAAC,iBAAiB,CAAC,MAAM,IAAI,KAAK,wBAAwB,SAAS,UAAU,CAAC,MAAM,IAAI,KAAK,wBAAwB,aAAa,iBAAiB;AAAA,IAC7N,IAAI;AAAA,EACN,GAAG;AAAA,IACD;AAAA,EACF,CAAC,GAAG,eAAe;AACnB,SAAO,KAAK,cAAc,EAAE,QAAQ,cAAY;AAC9C,WAAO,MAAM,QAAQ;AAAA,EACvB,CAAC;AACD,SAAO,CAAC,aAAa,KAAK;AAC5B;;;AHpEA,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,OAAO,YAAY,aAAa,aAAa,SAAS,aAAa,YAAY,SAAS,OAAO,UAAU,SAAS;AAUrI,IAAM,gBAAgB,oBAAoB,WAAW;AACrD,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,gBAAgB,cAAc;AAAA,IACtD,KAAK,CAAC,KAAK;AAAA,IACX,UAAU,CAAC,UAAU;AAAA,EACvB;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AACA,IAAM,aAAa,eAAO,OAAO;AAAA,EAC/B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,WAAW,gBAAgB,OAAO,YAAY;AAAA,EACjG;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY,MAAM,WAAW;AAAA,EAC7B,UAAU,MAAM,WAAW,QAAQ,EAAE;AAAA,EACrC,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,IACA,OAAO,SAAS;AAAA,MACd,QAAQ,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,IAClD,GAAG,MAAM,OAAO;AAAA,MACd,iBAAiB,MAAM,KAAK,QAAQ,OAAO;AAAA,IAC7C,IAAI,SAAS;AAAA,MACX,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACzC,GAAG,MAAM,YAAY,QAAQ;AAAA,MAC3B,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACzC,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH,EAAE;AACF,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA;AAAA,EAEP,YAAY;AACd,CAAC;AACD,IAAM,iBAAiB,eAAO,gBAAQ;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,KAAK;AAChD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,OAAO,CAAC,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,cAAU,KAAK;AACf,QAAI,SAAS;AACb,UAAM,QAAQ,IAAI,MAAM;AACxB,UAAM,SAAS,MAAM;AACnB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,cAAc;AACpB,UAAM,iBAAiB;AACvB,UAAM,MAAM;AACZ,QAAI,QAAQ;AACV,YAAM,SAAS;AAAA,IACjB;AACA,WAAO,MAAM;AACX,eAAS;AAAA,IACX;AAAA,EACF,GAAG,CAAC,aAAa,gBAAgB,KAAK,MAAM,CAAC;AAC7C,SAAO;AACT;AACA,IAAM,SAA4B,kBAAW,SAASC,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,MAAI,WAAW;AAGf,QAAM,SAAS,UAAU,SAAS,CAAC,GAAG,UAAU;AAAA,IAC9C;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,SAAS,OAAO;AACtB,QAAM,mBAAmB,UAAU,WAAW;AAC9C,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC,cAAc,CAAC;AAAA,IACf;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,CAAC,SAAS,YAAY,IAAI,QAAQ,OAAO;AAAA,IAC7C,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB;AAAA,MACA,WAAW;AAAA,QACT,KAAK,SAAS,CAAC,GAAG,UAAU,UAAU,GAAG;AAAA,MAC3C;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACpB,mBAAwB,oBAAAE,KAAK,SAAS,SAAS,CAAC,GAAG,YAAY,CAAC;AAAA,EAGlE,WAAW,CAAC,CAAC,gBAAgB,iBAAiB,GAAG;AAC/C,eAAW;AAAA,EACb,WAAW,UAAU,KAAK;AACxB,eAAW,IAAI,CAAC;AAAA,EAClB,OAAO;AACL,mBAAwB,oBAAAA,KAAK,gBAAgB;AAAA,MAC3C;AAAA,MACA,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,aAAoB,oBAAAA,KAAK,YAAY,SAAS;AAAA,IAC5C,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,EACF,GAAG,OAAO;AAAA,IACR;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShF,KAAK,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,KAAK,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC7D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,KAAK,kBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,KAAK,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,WAAW,QAAQ,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAC3I,IAAI;AACJ,IAAO,iBAAQ;", "names": ["React", "_jsx", "import_jsx_runtime", "_excluded", "Avatar", "_jsx", "PropTypes"]}