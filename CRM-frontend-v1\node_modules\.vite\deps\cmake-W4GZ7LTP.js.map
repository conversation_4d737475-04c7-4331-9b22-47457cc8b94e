{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/cmake.js"], "sourcesContent": ["var variable_regex = /({)?[a-zA-Z0-9_]+(})?/;\n\nfunction tokenString(stream, state) {\n  var current, prev, found_var = false;\n  while (!stream.eol() && (current = stream.next()) != state.pending) {\n    if (current === '$' && prev != '\\\\' && state.pending == '\"') {\n      found_var = true;\n      break;\n    }\n    prev = current;\n  }\n  if (found_var) {\n    stream.backUp(1);\n  }\n  if (current == state.pending) {\n    state.continueString = false;\n  } else {\n    state.continueString = true;\n  }\n  return \"string\";\n}\n\nfunction tokenize(stream, state) {\n  var ch = stream.next();\n\n  // Have we found a variable?\n  if (ch === '$') {\n    if (stream.match(variable_regex)) {\n      return 'variableName.special';\n    }\n    return 'variable';\n  }\n  // Should we still be looking for the end of a string?\n  if (state.continueString) {\n    // If so, go through the loop again\n    stream.backUp(1);\n    return tokenString(stream, state);\n  }\n  // Do we just have a function on our hands?\n  // In 'cmake_minimum_required (VERSION 2.8.8)', 'cmake_minimum_required' is matched\n  if (stream.match(/(\\s+)?\\w+\\(/) || stream.match(/(\\s+)?\\w+\\ \\(/)) {\n    stream.backUp(1);\n    return 'def';\n  }\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // Have we found a string?\n  if (ch == \"'\" || ch == '\"') {\n    // Store the type (single or double)\n    state.pending = ch;\n    // Perform the looping function to find the end\n    return tokenString(stream, state);\n  }\n  if (ch == '(' || ch == ')') {\n    return 'bracket';\n  }\n  if (ch.match(/[0-9]/)) {\n    return 'number';\n  }\n  stream.eatWhile(/[\\w-]/);\n  return null;\n}\nexport const cmake = {\n  name: \"cmake\",\n  startState: function () {\n    var state = {};\n    state.inDefinition = false;\n    state.inInclude = false;\n    state.continueString = false;\n    state.pending = false;\n    return state;\n  },\n  token: function (stream, state) {\n    if (stream.eatSpace()) return null;\n    return tokenize(stream, state);\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AAErB,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,SAAS,MAAM,YAAY;AAC/B,SAAO,CAAC,OAAO,IAAI,MAAM,UAAU,OAAO,KAAK,MAAM,MAAM,SAAS;AAClE,QAAI,YAAY,OAAO,QAAQ,QAAQ,MAAM,WAAW,KAAK;AAC3D,kBAAY;AACZ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,WAAW;AACb,WAAO,OAAO,CAAC;AAAA,EACjB;AACA,MAAI,WAAW,MAAM,SAAS;AAC5B,UAAM,iBAAiB;AAAA,EACzB,OAAO;AACL,UAAM,iBAAiB;AAAA,EACzB;AACA,SAAO;AACT;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,gBAAgB;AAExB,WAAO,OAAO,CAAC;AACf,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AAGA,MAAI,OAAO,MAAM,aAAa,KAAK,OAAO,MAAM,eAAe,GAAG;AAChE,WAAO,OAAO,CAAC;AACf,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,MAAM,KAAK;AAE1B,UAAM,UAAU;AAEhB,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AACA,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,GAAG,MAAM,OAAO,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,OAAO;AACvB,SAAO;AACT;AACO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,QAAI,QAAQ,CAAC;AACb,UAAM,eAAe;AACrB,UAAM,YAAY;AAClB,UAAM,iBAAiB;AACvB,UAAM,UAAU;AAChB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACF;", "names": []}