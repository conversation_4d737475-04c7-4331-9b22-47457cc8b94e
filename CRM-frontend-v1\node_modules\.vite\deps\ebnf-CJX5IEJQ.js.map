{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/ebnf.js"], "sourcesContent": ["var commentType = {slash: 0, parenthesis: 1};\nvar stateType = {comment: 0, _string: 1, characterClass: 2};\n\nexport const ebnf = {\n  name: \"ebnf\",\n  startState: function () {\n    return {\n      stringType: null,\n      commentType: null,\n      braced: 0,\n      lhs: true,\n      localState: null,\n      stack: [],\n      inDefinition: false\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(stateType._string);\n      } else if (stream.match('/*')) { //comments starting with /*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.slash;\n      } else if (stream.match('(*')) { //comments starting with (*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.parenthesis;\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case stateType._string:\n      while (state.stack[0] === stateType._string && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n\n    case stateType.comment:\n      while (state.stack[0] === stateType.comment && !stream.eol()) {\n        if (state.commentType === commentType.slash && stream.match('*/')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else if (state.commentType === commentType.parenthesis && stream.match('*)')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else {\n          stream.match(/^.[^\\*]*/);\n        }\n      }\n      return \"comment\";\n\n    case stateType.characterClass:\n      while (state.stack[0] === stateType.characterClass && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match('.'))) {\n          state.stack.shift();\n        }\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(stateType.characterClass);\n      return \"bracket\";\n    case \":\":\n    case \"|\":\n    case \";\":\n      stream.next();\n      return \"operator\";\n    case \"%\":\n      if (stream.match(\"%%\")) {\n        return \"header\";\n      } else if (stream.match(/[%][A-Za-z]+/)) {\n        return \"keyword\";\n      } else if (stream.match(/[%][}]/)) {\n        return \"bracket\";\n      }\n      break;\n    case \"/\":\n      if (stream.match(/[\\/][A-Za-z]+/)) {\n        return \"keyword\";\n      }\n    case \"\\\\\":\n      if (stream.match(/[\\][a-z]+/)) {\n        return \"string.special\";\n      }\n    case \".\":\n      if (stream.match(\".\")) {\n        return \"atom\";\n      }\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n      if (stream.match(peek)) {\n        return \"atom\";\n      }\n    case \"$\":\n      if (stream.match(\"$$\")) {\n        return \"builtin\";\n      } else if (stream.match(/[$][0-9]+/)) {\n        return \"variableName.special\";\n      }\n    case \"<\":\n      if (stream.match(/<<[a-zA-Z_]+>>/)) {\n        return \"builtin\";\n      }\n    }\n\n    if (stream.match('//')) {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.match('return')) {\n      return \"operator\";\n    } else if (stream.match(/^[a-zA-Z_][a-zA-Z0-9_]*/)) {\n      if (stream.match(/(?=[\\(.])/)) {\n        return \"variable\";\n      } else if (stream.match(/(?=[\\s\\n]*[:=])/)) {\n        return \"def\";\n      }\n      return \"variableName.special\";\n    } else if ([\"[\", \"]\", \"(\", \")\"].indexOf(stream.peek()) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,cAAc,EAAC,OAAO,GAAG,aAAa,EAAC;AAC3C,IAAI,YAAY,EAAC,SAAS,GAAG,SAAS,GAAG,gBAAgB,EAAC;AAEnD,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,OAAO,CAAC;AAAA,MACR,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,CAAC;AAAQ;AAGb,QAAI,MAAM,MAAM,WAAW,GAAG;AAE5B,UAAK,OAAO,KAAK,KAAK,OAAS,OAAO,KAAK,KAAK,KAAM;AACpD,cAAM,aAAa,OAAO,KAAK;AAC/B,eAAO,KAAK;AACZ,cAAM,MAAM,QAAQ,UAAU,OAAO;AAAA,MACvC,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,cAAM,MAAM,QAAQ,UAAU,OAAO;AACrC,cAAM,cAAc,YAAY;AAAA,MAClC,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,cAAM,MAAM,QAAQ,UAAU,OAAO;AACrC,cAAM,cAAc,YAAY;AAAA,MAClC;AAAA,IACF;AAIA,YAAQ,MAAM,MAAM,CAAC,GAAG;AAAA,MACxB,KAAK,UAAU;AACb,eAAO,MAAM,MAAM,CAAC,MAAM,UAAU,WAAW,CAAC,OAAO,IAAI,GAAG;AAC5D,cAAI,OAAO,KAAK,MAAM,MAAM,YAAY;AACtC,mBAAO,KAAK;AACZ,kBAAM,MAAM,MAAM;AAAA,UACpB,WAAW,OAAO,KAAK,MAAM,MAAM;AACjC,mBAAO,KAAK;AACZ,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,mBAAO,MAAM,cAAc;AAAA,UAC7B;AAAA,QACF;AACA,eAAO,MAAM,MAAM,aAAa;AAAA,MAElC,KAAK,UAAU;AACb,eAAO,MAAM,MAAM,CAAC,MAAM,UAAU,WAAW,CAAC,OAAO,IAAI,GAAG;AAC5D,cAAI,MAAM,gBAAgB,YAAY,SAAS,OAAO,MAAM,IAAI,GAAG;AACjE,kBAAM,MAAM,MAAM;AAClB,kBAAM,cAAc;AAAA,UACtB,WAAW,MAAM,gBAAgB,YAAY,eAAe,OAAO,MAAM,IAAI,GAAG;AAC9E,kBAAM,MAAM,MAAM;AAClB,kBAAM,cAAc;AAAA,UACtB,OAAO;AACL,mBAAO,MAAM,UAAU;AAAA,UACzB;AAAA,QACF;AACA,eAAO;AAAA,MAET,KAAK,UAAU;AACb,eAAO,MAAM,MAAM,CAAC,MAAM,UAAU,kBAAkB,CAAC,OAAO,IAAI,GAAG;AACnE,cAAI,EAAE,OAAO,MAAM,WAAW,KAAK,OAAO,MAAM,GAAG,IAAI;AACrD,kBAAM,MAAM,MAAM;AAAA,UACpB;AAAA,QACF;AACA,eAAO;AAAA,IACT;AAEA,QAAI,OAAO,OAAO,KAAK;AAGvB,YAAQ,MAAM;AAAA,MACd,KAAK;AACH,eAAO,KAAK;AACZ,cAAM,MAAM,QAAQ,UAAU,cAAc;AAC5C,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK;AACZ,eAAO;AAAA,MACT,KAAK;AACH,YAAI,OAAO,MAAM,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,cAAc,GAAG;AACvC,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,QAAQ,GAAG;AACjC,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,OAAO,MAAM,eAAe,GAAG;AACjC,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AACH,YAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AACH,YAAI,OAAO,MAAM,GAAG,GAAG;AACrB,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,OAAO,MAAM,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AACH,YAAI,OAAO,MAAM,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,WAAW,GAAG;AACpC,iBAAO;AAAA,QACT;AAAA,MACF,KAAK;AACH,YAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,iBAAO;AAAA,QACT;AAAA,IACF;AAEA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAW,OAAO,MAAM,QAAQ,GAAG;AACjC,aAAO;AAAA,IACT,WAAW,OAAO,MAAM,yBAAyB,GAAG;AAClD,UAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,eAAO;AAAA,MACT,WAAW,OAAO,MAAM,iBAAiB,GAAG;AAC1C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,WAAW,CAAC,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,OAAO,KAAK,CAAC,KAAK,IAAI;AAC5D,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAW,CAAC,OAAO,SAAS,GAAG;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACF;", "names": []}