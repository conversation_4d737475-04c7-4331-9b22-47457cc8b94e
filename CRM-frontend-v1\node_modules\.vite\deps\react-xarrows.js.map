{"version": 3, "sources": ["../../react-xarrows/lib/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"), require(\"lodash\"), require(\"prop-types\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"reactXarrow\", [\"react\", \"lodash\", \"prop-types\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"reactXarrow\"] = factory(require(\"react\"), require(\"lodash\"), require(\"prop-types\"));\n\telse\n\t\troot[\"reactXarrow\"] = factory(root[\"react\"], root[\"lodash\"], root[\"prop-types\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_lodash__, __WEBPACK_EXTERNAL_MODULE_prop_types__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./src/Xarrow/utils/buzzier.js\":\n/*!*************************************!*\\\n  !*** ./src/Xarrow/utils/buzzier.js ***!\n  \\*************************************/\n/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"bzFunction\": function() { return /* binding */ bzFunction; },\n/* harmony export */   \"buzzierMinSols\": function() { return /* binding */ buzzierMinSols; }\n/* harmony export */ });\n// Buzier curve calculations\n\n/**\r\n * returns buzzier curve function with 2 controls points\r\n * bzCurve with 2 control points function(4 points total):  bz = (1−t)^3*p1 + 3(1−t)^2*t*p2 +3(1−t)*t^2*p3 + t^3*p4\r\n */\nvar bzFunction = function bzFunction(p1, p2, p3, p4) {\n  return function (t) {\n    return Math.pow(1 - t, 3) * p1 + 3 * Math.pow(1 - t, 2) * t * p2 + 3 * (1 - t) * Math.pow(t, 2) * p3 + Math.pow(t, 3) * p4;\n  };\n};\n/**\r\n * returns 2 solutions from extram points for buzzier curve with 2 controls points\r\n */\n\nvar buzzierMinSols = function buzzierMinSols(p1, p2, p3, p4) {\n  var bz = bzFunction(p1, p2, p3, p4); // dt(bz) = -3 p1 (1 - t)^2 + 3 p2 (1 - t)^2 - 6 p2 (1 - t) t + 6 p3 (1 - t) t - 3 p3 t^2 + 3 p4 t^2\n  // when p1=(x1,y1),p2=(cpx1,cpy1),p3=(cpx2,cpy2),p4=(x2,y2)\n  // then extrema points is when dt(bz) = 0\n  // solutions =>  t = ((-6 p1 + 12 p2 - 6 p3) ± sqrt((6 p1 - 12 p2 + 6 p3)^2 - 4 (3 p2 - 3 p1) (-3 p1 + 9 p2 - 9 p3 + 3 p4)))/(2 (-3 p1 + 9 p2 - 9 p3 + 3 p4))  when (p1 + 3 p3!=3 p2 + p4)\n  // if we mark A=(-6 p1 + 12 p2 - 6 p3) and B=(6 p1 - 12 p2 + 6 p3)^2 - 4 (3 p2 - 3 p1) (-3 p1 + 9 p2 - 9 p3 + 3 p4)) and C =(2 (-3 p1 + 9 p2 - 9 p3 + 3 p4) then\n  // tSol = A ± sqrt(B)\n  // then solution we want is: bz(tSol)\n\n  var A = -6 * p1 + 12 * p2 - 6 * p3;\n  var B = Math.pow(-6 * p1 + 12 * p2 - 6 * p3, 2) - 4 * (3 * p2 - 3 * p1) * (-3 * p1 + 9 * p2 - 9 * p3 + 3 * p4);\n  var C = 2 * (-3 * p1 + 9 * p2 - 9 * p3 + 3 * p4);\n  var sol1 = bz((A + Math.sqrt(B)) / C);\n  var sol2 = bz((A - Math.sqrt(B)) / C);\n  return [sol1, sol2];\n};\n\n/***/ }),\n\n/***/ \"./src/Xarrow/Xarrow.tsx\":\n/*!*******************************!*\\\n  !*** ./src/Xarrow/Xarrow.tsx ***!\n  \\*******************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"react\"));\r\nvar useXarrowProps_1 = __importDefault(__webpack_require__(/*! ./useXarrowProps */ \"./src/Xarrow/useXarrowProps.ts\"));\r\nvar Xwrapper_1 = __webpack_require__(/*! ../Xwrapper */ \"./src/Xwrapper.tsx\");\r\nvar propTypes_1 = __importDefault(__webpack_require__(/*! ./propTypes */ \"./src/Xarrow/propTypes.ts\"));\r\nvar GetPosition_1 = __webpack_require__(/*! ./utils/GetPosition */ \"./src/Xarrow/utils/GetPosition.tsx\");\r\nvar log = console.log;\r\nvar Xarrow = function (props) {\r\n    // log('xarrow update');\r\n    var _a;\r\n    var mainRef = react_1.useRef({\r\n        svgRef: react_1.useRef(null),\r\n        lineRef: react_1.useRef(null),\r\n        headRef: react_1.useRef(null),\r\n        tailRef: react_1.useRef(null),\r\n        lineDrawAnimRef: react_1.useRef(null),\r\n        lineDashAnimRef: react_1.useRef(null),\r\n        headOpacityAnimRef: react_1.useRef(null),\r\n    });\r\n    var _b = mainRef.current, svgRef = _b.svgRef, lineRef = _b.lineRef, headRef = _b.headRef, tailRef = _b.tailRef, lineDrawAnimRef = _b.lineDrawAnimRef, lineDashAnimRef = _b.lineDashAnimRef, headOpacityAnimRef = _b.headOpacityAnimRef;\r\n    react_1.useContext(Xwrapper_1.XarrowContext);\r\n    var xProps = useXarrowProps_1.default(props, mainRef.current);\r\n    var propsRefs = xProps[0];\r\n    var labels = propsRefs.labels, lineColor = propsRefs.lineColor, headColor = propsRefs.headColor, tailColor = propsRefs.tailColor, strokeWidth = propsRefs.strokeWidth, showHead = propsRefs.showHead, showTail = propsRefs.showTail, dashness = propsRefs.dashness, headShape = propsRefs.headShape, tailShape = propsRefs.tailShape, showXarrow = propsRefs.showXarrow, animateDrawing = propsRefs.animateDrawing, zIndex = propsRefs.zIndex, passProps = propsRefs.passProps, arrowBodyProps = propsRefs.arrowBodyProps, arrowHeadProps = propsRefs.arrowHeadProps, arrowTailProps = propsRefs.arrowTailProps, SVGcanvasProps = propsRefs.SVGcanvasProps, divContainerProps = propsRefs.divContainerProps, divContainerStyle = propsRefs.divContainerStyle, SVGcanvasStyle = propsRefs.SVGcanvasStyle, _debug = propsRefs._debug, shouldUpdatePosition = propsRefs.shouldUpdatePosition;\r\n    animateDrawing = props.animateDrawing;\r\n    var _c = react_1.useState(!animateDrawing), drawAnimEnded = _c[0], setDrawAnimEnded = _c[1];\r\n    var _d = react_1.useState({}), setRender = _d[1];\r\n    var forceRerender = function () { return setRender({}); };\r\n    var _e = react_1.useState({\r\n        //initial state\r\n        cx0: 0,\r\n        cy0: 0,\r\n        cw: 0,\r\n        ch: 0,\r\n        x1: 0,\r\n        y1: 0,\r\n        x2: 0,\r\n        y2: 0,\r\n        dx: 0,\r\n        dy: 0,\r\n        absDx: 0,\r\n        absDy: 0,\r\n        cpx1: 0,\r\n        cpy1: 0,\r\n        cpx2: 0,\r\n        cpy2: 0,\r\n        headOrient: 0,\r\n        tailOrient: 0,\r\n        arrowHeadOffset: { x: 0, y: 0 },\r\n        arrowTailOffset: { x: 0, y: 0 },\r\n        headOffset: 0,\r\n        excRight: 0,\r\n        excLeft: 0,\r\n        excUp: 0,\r\n        excDown: 0,\r\n        startPoints: [],\r\n        endPoints: [],\r\n        mainDivPos: { x: 0, y: 0 },\r\n        xSign: 1,\r\n        ySign: 1,\r\n        lineLength: 0,\r\n        fHeadSize: 1,\r\n        fTailSize: 1,\r\n        arrowPath: \"\",\r\n        labelStartPos: { x: 0, y: 0 },\r\n        labelMiddlePos: { x: 0, y: 0 },\r\n        labelEndPos: { x: 0, y: 0 },\r\n    }), st = _e[0], setSt = _e[1];\r\n    /**\r\n     * The Main logic of path calculation for the arrow.\r\n     * calculate new path, adjusting canvas, and set state based on given properties.\r\n     * */\r\n    react_1.useLayoutEffect(function () {\r\n        if (shouldUpdatePosition.current) {\r\n            // log('xarrow getPosition');\r\n            var pos = GetPosition_1.getPosition(xProps, mainRef);\r\n            // log('pos', pos);\r\n            setSt(pos);\r\n            shouldUpdatePosition.current = false;\r\n        }\r\n    });\r\n    // log('st', st);\r\n    var xOffsetHead = st.x2 - st.arrowHeadOffset.x;\r\n    var yOffsetHead = st.y2 - st.arrowHeadOffset.y;\r\n    var xOffsetTail = st.x1 - st.arrowTailOffset.x;\r\n    var yOffsetTail = st.y1 - st.arrowTailOffset.y;\r\n    var dashoffset = dashness.strokeLen + dashness.nonStrokeLen;\r\n    var animDirection = 1;\r\n    if (dashness.animation < 0) {\r\n        dashness.animation *= -1;\r\n        animDirection = -1;\r\n    }\r\n    var dashArray, animation, animRepeatCount, animStartValue, animEndValue = 0;\r\n    if (animateDrawing && drawAnimEnded == false) {\r\n        if (typeof animateDrawing === 'boolean')\r\n            animateDrawing = 1;\r\n        animation = animateDrawing + 's';\r\n        dashArray = st.lineLength;\r\n        animStartValue = st.lineLength;\r\n        animRepeatCount = 1;\r\n        if (animateDrawing < 0) {\r\n            _a = [animEndValue, animStartValue], animStartValue = _a[0], animEndValue = _a[1];\r\n            animation = animateDrawing * -1 + 's';\r\n        }\r\n    }\r\n    else {\r\n        dashArray = dashness.strokeLen + \" \" + dashness.nonStrokeLen;\r\n        animation = 1 / dashness.animation + \"s\";\r\n        animStartValue = dashoffset * animDirection;\r\n        animRepeatCount = 'indefinite';\r\n        animEndValue = 0;\r\n    }\r\n    // handle draw animation\r\n    react_1.useLayoutEffect(function () {\r\n        if (lineRef.current)\r\n            setSt(function (prevSt) { var _a, _b; return (__assign(__assign({}, prevSt), { lineLength: (_b = (_a = lineRef.current) === null || _a === void 0 ? void 0 : _a.getTotalLength()) !== null && _b !== void 0 ? _b : 0 })); });\r\n    }, [lineRef.current]);\r\n    // set all props on first render\r\n    react_1.useEffect(function () {\r\n        var monitorDOMchanges = function () {\r\n            window.addEventListener('resize', forceRerender);\r\n            var handleDrawAmimEnd = function () {\r\n                var _a, _b;\r\n                setDrawAnimEnded(true);\r\n                // @ts-ignore\r\n                (_a = headOpacityAnimRef.current) === null || _a === void 0 ? void 0 : _a.beginElement();\r\n                // @ts-ignore\r\n                (_b = lineDashAnimRef.current) === null || _b === void 0 ? void 0 : _b.beginElement();\r\n            };\r\n            var handleDrawAmimBegin = function () { return (headRef.current.style.opacity = '0'); };\r\n            if (lineDrawAnimRef.current && headRef.current) {\r\n                lineDrawAnimRef.current.addEventListener('endEvent', handleDrawAmimEnd);\r\n                lineDrawAnimRef.current.addEventListener('beginEvent', handleDrawAmimBegin);\r\n            }\r\n            return function () {\r\n                window.removeEventListener('resize', forceRerender);\r\n                if (lineDrawAnimRef.current) {\r\n                    lineDrawAnimRef.current.removeEventListener('endEvent', handleDrawAmimEnd);\r\n                    if (headRef.current)\r\n                        lineDrawAnimRef.current.removeEventListener('beginEvent', handleDrawAmimBegin);\r\n                }\r\n            };\r\n        };\r\n        var cleanMonitorDOMchanges = monitorDOMchanges();\r\n        return function () {\r\n            setDrawAnimEnded(false);\r\n            cleanMonitorDOMchanges();\r\n        };\r\n    }, [showXarrow]);\r\n    //todo: could make some advanced generic typescript inferring. for example get type from headShape.elem:T and\r\n    // tailShape.elem:K force the type for passProps,arrowHeadProps,arrowTailProps property. for now `as any` is used to\r\n    // avoid typescript conflicts\r\n    // so todo- fix all the `passProps as any` assertions\r\n    return (react_1.default.createElement(\"div\", __assign({}, divContainerProps, { style: __assign({ position: 'absolute', zIndex: zIndex }, divContainerStyle) }), showXarrow ? (react_1.default.createElement(react_1.default.Fragment, null,\r\n        react_1.default.createElement(\"svg\", __assign({ ref: svgRef, width: st.cw, height: st.ch, style: __assign({ position: 'absolute', left: st.cx0, top: st.cy0, pointerEvents: 'none', border: _debug ? '1px dashed yellow' : null }, SVGcanvasStyle), overflow: \"auto\" }, SVGcanvasProps),\r\n            react_1.default.createElement(\"path\", __assign({ ref: lineRef, d: st.arrowPath, stroke: lineColor, strokeDasharray: dashArray, \r\n                // strokeDasharray={'0 0'}\r\n                strokeWidth: strokeWidth, fill: \"transparent\", pointerEvents: \"visibleStroke\" }, passProps, arrowBodyProps),\r\n                react_1.default.createElement(react_1.default.Fragment, null, drawAnimEnded ? (react_1.default.createElement(react_1.default.Fragment, null, dashness.animation ? (react_1.default.createElement(\"animate\", { ref: lineDashAnimRef, attributeName: \"stroke-dashoffset\", values: dashoffset * animDirection + \";0\", dur: 1 / dashness.animation + \"s\", repeatCount: \"indefinite\" })) : null)) : (react_1.default.createElement(react_1.default.Fragment, null, animateDrawing ? (react_1.default.createElement(\"animate\", { ref: lineDrawAnimRef, id: \"svgEndAnimate\", attributeName: \"stroke-dashoffset\", values: animStartValue + \";\" + animEndValue, dur: animation, repeatCount: animRepeatCount })) : null)))),\r\n            showTail ? (react_1.default.createElement(\"g\", __assign({ fill: tailColor, pointerEvents: \"auto\", transform: \"translate(\" + xOffsetTail + \",\" + yOffsetTail + \") rotate(\" + st.tailOrient + \") scale(\" + st.fTailSize + \")\" }, passProps, arrowTailProps), tailShape.svgElem)) : null,\r\n            showHead ? (react_1.default.createElement(\"g\", __assign({ ref: headRef, \r\n                // d={normalArrowShape}\r\n                fill: headColor, pointerEvents: \"auto\", transform: \"translate(\" + xOffsetHead + \",\" + yOffsetHead + \") rotate(\" + st.headOrient + \") scale(\" + st.fHeadSize + \")\", opacity: animateDrawing && !drawAnimEnded ? 0 : 1 }, passProps, arrowHeadProps),\r\n                react_1.default.createElement(\"animate\", { ref: headOpacityAnimRef, dur: '0.4', attributeName: \"opacity\", from: \"0\", to: \"1\", begin: \"indefinite\", repeatCount: \"0\", fill: \"freeze\" }),\r\n                headShape.svgElem)) : null,\r\n            _debug ? (react_1.default.createElement(react_1.default.Fragment, null,\r\n                react_1.default.createElement(\"circle\", { r: \"5\", cx: st.cpx1, cy: st.cpy1, fill: \"green\" }),\r\n                react_1.default.createElement(\"circle\", { r: \"5\", cx: st.cpx2, cy: st.cpy2, fill: \"blue\" }),\r\n                react_1.default.createElement(\"rect\", { x: st.excLeft, y: st.excUp, width: st.absDx, height: st.absDy, fill: \"none\", stroke: \"pink\", strokeWidth: \"2px\" }))) : null),\r\n        labels.start ? (react_1.default.createElement(\"div\", { style: {\r\n                transform: st.dx < 0 ? 'translate(-100% , -50%)' : 'translate(-0% , -50%)',\r\n                width: 'max-content',\r\n                position: 'absolute',\r\n                left: st.cx0 + st.labelStartPos.x,\r\n                top: st.cy0 + st.labelStartPos.y - strokeWidth - 5,\r\n            } }, labels.start)) : null,\r\n        labels.middle ? (react_1.default.createElement(\"div\", { style: {\r\n                display: 'table',\r\n                width: 'max-content',\r\n                transform: 'translate(-50% , -50%)',\r\n                position: 'absolute',\r\n                left: st.cx0 + st.labelMiddlePos.x,\r\n                top: st.cy0 + st.labelMiddlePos.y,\r\n            } }, labels.middle)) : null,\r\n        labels.end ? (react_1.default.createElement(\"div\", { style: {\r\n                transform: st.dx > 0 ? 'translate(-100% , -50%)' : 'translate(-0% , -50%)',\r\n                width: 'max-content',\r\n                position: 'absolute',\r\n                left: st.cx0 + st.labelEndPos.x,\r\n                top: st.cy0 + st.labelEndPos.y + strokeWidth + 5,\r\n            } }, labels.end)) : null,\r\n        _debug ? (react_1.default.createElement(react_1.default.Fragment, null, __spreadArray(__spreadArray([], st.startPoints), st.endPoints).map(function (p, i) {\r\n            return (react_1.default.createElement(\"div\", { key: i, style: {\r\n                    background: 'gray',\r\n                    opacity: 0.5,\r\n                    borderRadius: '50%',\r\n                    transform: 'translate(-50%, -50%)',\r\n                    height: 5,\r\n                    width: 5,\r\n                    position: 'absolute',\r\n                    left: p.x - st.mainDivPos.x,\r\n                    top: p.y - st.mainDivPos.y,\r\n                } }));\r\n        }))) : null)) : null));\r\n};\r\n//////////////////////////////\r\n// propTypes\r\nXarrow.propTypes = propTypes_1.default;\r\nexports.default = Xarrow;\r\n\n\n/***/ }),\n\n/***/ \"./src/Xarrow/anchors.ts\":\n/*!*******************************!*\\\n  !*** ./src/Xarrow/anchors.ts ***!\n  \\*******************************/\n/***/ (function(module, exports, __webpack_require__) {\n\n/* module decorator */ module = __webpack_require__.nmd(module);\n\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.calcAnchors = void 0;\r\nvar getAnchorsDefaultOffsets = function (width, height) {\r\n    return {\r\n        middle: { x: width * 0.5, y: height * 0.5 },\r\n        left: { x: 0, y: height * 0.5 },\r\n        right: { x: width, y: height * 0.5 },\r\n        top: { x: width * 0.5, y: 0 },\r\n        bottom: { x: width * 0.5, y: height },\r\n    };\r\n};\r\nvar calcAnchors = function (anchors, anchorPos) {\r\n    // now prepare this list of anchors to object expected by the `getShortestLine` function\r\n    return anchors.map(function (anchor) {\r\n        var defsOffsets = getAnchorsDefaultOffsets(anchorPos.right - anchorPos.x, anchorPos.bottom - anchorPos.y);\r\n        var _a = defsOffsets[anchor.position], x = _a.x, y = _a.y;\r\n        return {\r\n            x: anchorPos.x + x + anchor.offset.x,\r\n            y: anchorPos.y + y + anchor.offset.y,\r\n            anchor: anchor,\r\n        };\r\n    });\r\n};\r\nexports.calcAnchors = calcAnchors;\r\nif (__webpack_require__.c[__webpack_require__.s] === module) {\r\n    // const res = parseAnchor(['auto'], {\r\n    //   x: 0,\r\n    //   y: 0,\r\n    //   bottom: 10,\r\n    //   right: 20,\r\n    // });\r\n    // console.log(res);\r\n}\r\n\n\n/***/ }),\n\n/***/ \"./src/Xarrow/propTypes.ts\":\n/*!*********************************!*\\\n  !*** ./src/Xarrow/propTypes.ts ***!\n  \\*********************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nvar prop_types_1 = __importDefault(__webpack_require__(/*! prop-types */ \"prop-types\"));\r\nvar constants_1 = __webpack_require__(/*! ../constants */ \"./src/constants.tsx\");\r\nvar pAnchorPositionType = prop_types_1.default.oneOf(constants_1.cAnchorEdge);\r\nvar pAnchorCustomPositionType = prop_types_1.default.exact({\r\n    position: pAnchorPositionType.isRequired,\r\n    offset: prop_types_1.default.exact({\r\n        x: prop_types_1.default.number,\r\n        y: prop_types_1.default.number,\r\n    }).isRequired,\r\n});\r\nvar _pAnchorType = prop_types_1.default.oneOfType([pAnchorPositionType, pAnchorCustomPositionType]);\r\nvar pAnchorType = prop_types_1.default.oneOfType([_pAnchorType, prop_types_1.default.arrayOf(_pAnchorType)]);\r\nvar pRefType = prop_types_1.default.oneOfType([prop_types_1.default.string, prop_types_1.default.exact({ current: prop_types_1.default.any })]);\r\nvar _pLabelType = prop_types_1.default.oneOfType([prop_types_1.default.element, prop_types_1.default.string]);\r\nvar pLabelsType = prop_types_1.default.exact({\r\n    start: _pLabelType,\r\n    middle: _pLabelType,\r\n    end: _pLabelType,\r\n});\r\nvar pSvgEdgeShapeType = prop_types_1.default.oneOf(Object.keys(constants_1.arrowShapes));\r\n// const pSvgElemType = PT.oneOf(cSvgElems);\r\nvar pSvgElemType = prop_types_1.default.any;\r\nvar pSvgEdgeType = prop_types_1.default.oneOfType([\r\n    pSvgEdgeShapeType,\r\n    prop_types_1.default.exact({\r\n        svgElem: pSvgElemType,\r\n        offsetForward: prop_types_1.default.number,\r\n    }).isRequired,\r\n]);\r\nvar XarrowPropTypes = {\r\n    start: pRefType.isRequired,\r\n    end: pRefType.isRequired,\r\n    startAnchor: pAnchorType,\r\n    endAnchor: pAnchorType,\r\n    labels: prop_types_1.default.oneOfType([_pLabelType, pLabelsType]),\r\n    color: prop_types_1.default.string,\r\n    lineColor: prop_types_1.default.string,\r\n    showHead: prop_types_1.default.bool,\r\n    headColor: prop_types_1.default.string,\r\n    headSize: prop_types_1.default.number,\r\n    tailSize: prop_types_1.default.number,\r\n    tailColor: prop_types_1.default.string,\r\n    strokeWidth: prop_types_1.default.number,\r\n    showTail: prop_types_1.default.bool,\r\n    path: prop_types_1.default.oneOf(constants_1.cPaths),\r\n    showXarrow: prop_types_1.default.bool,\r\n    curveness: prop_types_1.default.number,\r\n    gridBreak: prop_types_1.default.string,\r\n    dashness: prop_types_1.default.oneOfType([prop_types_1.default.bool, prop_types_1.default.object]),\r\n    headShape: pSvgEdgeType,\r\n    tailShape: pSvgEdgeType,\r\n    animateDrawing: prop_types_1.default.oneOfType([prop_types_1.default.bool, prop_types_1.default.number]),\r\n    zIndex: prop_types_1.default.number,\r\n    passProps: prop_types_1.default.object,\r\n    arrowBodyProps: prop_types_1.default.object,\r\n    arrowHeadProps: prop_types_1.default.object,\r\n    arrowTailProps: prop_types_1.default.object,\r\n    SVGcanvasProps: prop_types_1.default.object,\r\n    divContainerProps: prop_types_1.default.object,\r\n    _extendSVGcanvas: prop_types_1.default.number,\r\n    _debug: prop_types_1.default.bool,\r\n    _cpx1Offset: prop_types_1.default.number,\r\n    _cpy1Offset: prop_types_1.default.number,\r\n    _cpx2Offset: prop_types_1.default.number,\r\n    _cpy2Offset: prop_types_1.default.number,\r\n};\r\nexports.default = XarrowPropTypes;\r\n\n\n/***/ }),\n\n/***/ \"./src/Xarrow/useXarrowProps.ts\":\n/*!**************************************!*\\\n  !*** ./src/Xarrow/useXarrowProps.ts ***!\n  \\**************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"react\"));\r\nvar utils_1 = __webpack_require__(/*! ./utils */ \"./src/Xarrow/utils/index.ts\");\r\nvar lodash_1 = __importDefault(__webpack_require__(/*! lodash */ \"lodash\"));\r\nvar constants_1 = __webpack_require__(/*! ../constants */ \"./src/constants.tsx\");\r\nvar parseLabels = function (label) {\r\n    var parsedLabel = { start: null, middle: null, end: null };\r\n    if (label) {\r\n        if (typeof label === 'string' || react_1.default.isValidElement(label))\r\n            parsedLabel.middle = label;\r\n        else {\r\n            for (var key in label) {\r\n                parsedLabel[key] = label[key];\r\n            }\r\n        }\r\n    }\r\n    return parsedLabel;\r\n};\r\nvar parseAnchor = function (anchor) {\r\n    // convert to array\r\n    var anchorChoice = Array.isArray(anchor) ? anchor : [anchor];\r\n    //convert to array of objects\r\n    var anchorChoice2 = anchorChoice.map(function (anchorChoice) {\r\n        if (typeof anchorChoice === 'string') {\r\n            return { position: anchorChoice };\r\n        }\r\n        else\r\n            return anchorChoice;\r\n    });\r\n    //remove any invalid anchor names\r\n    anchorChoice2 = anchorChoice2.filter(function (an) { return constants_1.cAnchorEdge.includes(an.position); });\r\n    if (anchorChoice2.length == 0)\r\n        anchorChoice2 = [{ position: 'auto' }];\r\n    //replace any 'auto' with ['left','right','bottom','top']\r\n    var autosAncs = anchorChoice2.filter(function (an) { return an.position === 'auto'; });\r\n    if (autosAncs.length > 0) {\r\n        anchorChoice2 = anchorChoice2.filter(function (an) { return an.position !== 'auto'; });\r\n        anchorChoice2.push.apply(anchorChoice2, autosAncs.flatMap(function (anchorObj) {\r\n            return ['left', 'right', 'top', 'bottom'].map(function (anchorName) {\r\n                return __assign(__assign({}, anchorObj), { position: anchorName });\r\n            });\r\n        }));\r\n    }\r\n    // default values\r\n    var anchorChoice3 = anchorChoice2.map(function (anchorChoice) {\r\n        if (typeof anchorChoice === 'object') {\r\n            var anchorChoiceCustom = anchorChoice;\r\n            if (!anchorChoiceCustom.position)\r\n                anchorChoiceCustom.position = 'auto';\r\n            if (!anchorChoiceCustom.offset)\r\n                anchorChoiceCustom.offset = { x: 0, y: 0 };\r\n            if (!anchorChoiceCustom.offset.y)\r\n                anchorChoiceCustom.offset.y = 0;\r\n            if (!anchorChoiceCustom.offset.x)\r\n                anchorChoiceCustom.offset.x = 0;\r\n            anchorChoiceCustom = anchorChoiceCustom;\r\n            return anchorChoiceCustom;\r\n        }\r\n        else\r\n            return anchorChoice;\r\n    });\r\n    return anchorChoice3;\r\n};\r\nvar parseDashness = function (dashness, props) {\r\n    var dashStroke = 0, dashNone = 0, animDashSpeed, animDirection = 1;\r\n    if (typeof dashness === 'object') {\r\n        dashStroke = dashness.strokeLen || props.strokeWidth * 2;\r\n        dashNone = dashness.strokeLen ? dashness.nonStrokeLen : props.strokeWidth;\r\n        animDashSpeed = dashness.animation ? dashness.animation : null;\r\n    }\r\n    else if (typeof dashness === 'boolean' && dashness) {\r\n        dashStroke = props.strokeWidth * 2;\r\n        dashNone = props.strokeWidth;\r\n        animDashSpeed = null;\r\n    }\r\n    return { strokeLen: dashStroke, nonStrokeLen: dashNone, animation: animDashSpeed, animDirection: animDirection };\r\n};\r\nvar parseEdgeShape = function (svgEdge) {\r\n    if (typeof svgEdge == 'string') {\r\n        if (svgEdge in constants_1.arrowShapes)\r\n            svgEdge = constants_1.arrowShapes[svgEdge];\r\n        else {\r\n            console.warn(\"'\" + svgEdge + \"' is not supported arrow shape. the supported arrow shapes is one of \" + constants_1.cArrowShapes + \".\\n           reverting to default shape.\");\r\n            svgEdge = constants_1.arrowShapes['arrow1'];\r\n        }\r\n    }\r\n    svgEdge = svgEdge;\r\n    if ((svgEdge === null || svgEdge === void 0 ? void 0 : svgEdge.offsetForward) === undefined)\r\n        svgEdge.offsetForward = 0.25;\r\n    if ((svgEdge === null || svgEdge === void 0 ? void 0 : svgEdge.svgElem) === undefined)\r\n        svgEdge.svgElem = 'path';\r\n    // if (svgEdge?.svgProps === undefined) svgEdge.svgProps = arrowShapes.arrow1.svgProps;\r\n    return svgEdge;\r\n};\r\nvar parseGridBreak = function (gridBreak) {\r\n    var resGridBreak = utils_1.xStr2absRelative(gridBreak);\r\n    if (!resGridBreak)\r\n        resGridBreak = { relative: 0.5, abs: 0 };\r\n    return resGridBreak;\r\n};\r\n/**\r\n * should be wrapped with any changed prop that is affecting the points path positioning\r\n * @param propVal\r\n * @param updateRef\r\n */\r\nvar withUpdate = function (propVal, updateRef) {\r\n    if (updateRef)\r\n        updateRef.current = true;\r\n    return propVal;\r\n};\r\nvar noParse = function (userProp) { return userProp; };\r\nvar noParseWithUpdatePos = function (userProp, _, updatePos) { return withUpdate(userProp, updatePos); };\r\nvar parseNumWithUpdatePos = function (userProp, _, updatePos) { return withUpdate(Number(userProp), updatePos); };\r\nvar parseNum = function (userProp) { return Number(userProp); };\r\nvar parsePropsFuncs = {\r\n    start: function (userProp) { return utils_1.getElementByPropGiven(userProp); },\r\n    end: function (userProp) { return utils_1.getElementByPropGiven(userProp); },\r\n    startAnchor: function (userProp, _, updatePos) { return withUpdate(parseAnchor(userProp), updatePos); },\r\n    endAnchor: function (userProp, _, updatePos) { return withUpdate(parseAnchor(userProp), updatePos); },\r\n    labels: function (userProp) { return parseLabels(userProp); },\r\n    color: noParse,\r\n    lineColor: function (userProp, propsRefs) { return userProp || propsRefs.color; },\r\n    headColor: function (userProp, propsRefs) { return userProp || propsRefs.color; },\r\n    tailColor: function (userProp, propsRefs) { return userProp || propsRefs.color; },\r\n    strokeWidth: parseNumWithUpdatePos,\r\n    showHead: noParseWithUpdatePos,\r\n    headSize: parseNumWithUpdatePos,\r\n    showTail: noParseWithUpdatePos,\r\n    tailSize: parseNumWithUpdatePos,\r\n    path: noParseWithUpdatePos,\r\n    curveness: parseNumWithUpdatePos,\r\n    gridBreak: function (userProp, _, updatePos) { return withUpdate(parseGridBreak(userProp), updatePos); },\r\n    // // gridRadius = strokeWidth * 2, //todo\r\n    dashness: function (userProp, propsRefs) { return parseDashness(userProp, propsRefs); },\r\n    headShape: function (userProp) { return parseEdgeShape(userProp); },\r\n    tailShape: function (userProp) { return parseEdgeShape(userProp); },\r\n    showXarrow: noParse,\r\n    animateDrawing: noParse,\r\n    zIndex: parseNum,\r\n    passProps: noParse,\r\n    arrowBodyProps: noParseWithUpdatePos,\r\n    arrowHeadProps: noParseWithUpdatePos,\r\n    arrowTailProps: noParseWithUpdatePos,\r\n    SVGcanvasProps: noParseWithUpdatePos,\r\n    divContainerProps: noParseWithUpdatePos,\r\n    divContainerStyle: noParseWithUpdatePos,\r\n    SVGcanvasStyle: noParseWithUpdatePos,\r\n    _extendSVGcanvas: noParseWithUpdatePos,\r\n    _debug: noParseWithUpdatePos,\r\n    _cpx1Offset: noParseWithUpdatePos,\r\n    _cpy1Offset: noParseWithUpdatePos,\r\n    _cpx2Offset: noParseWithUpdatePos,\r\n    _cpy2Offset: noParseWithUpdatePos,\r\n};\r\n//build dependencies\r\nvar propsDeps = {};\r\n//each prop depends on himself\r\nfor (var propName in parsePropsFuncs) {\r\n    propsDeps[propName] = [propName];\r\n}\r\n// 'lineColor', 'headColor', 'tailColor' props also depends on 'color' prop\r\nfor (var _i = 0, _a = ['lineColor', 'headColor', 'tailColor']; _i < _a.length; _i++) {\r\n    var propName = _a[_i];\r\n    propsDeps[propName].push('color');\r\n}\r\nvar parseGivenProps = function (props, propsRef) {\r\n    var _a;\r\n    for (var _i = 0, _b = Object.entries(props); _i < _b.length; _i++) {\r\n        var _c = _b[_i], name_1 = _c[0], val = _c[1];\r\n        propsRef[name_1] = (_a = parsePropsFuncs === null || parsePropsFuncs === void 0 ? void 0 : parsePropsFuncs[name_1]) === null || _a === void 0 ? void 0 : _a.call(parsePropsFuncs, val, propsRef);\r\n    }\r\n    return propsRef;\r\n};\r\nvar defaultProps = {\r\n    start: null,\r\n    end: null,\r\n    startAnchor: 'auto',\r\n    endAnchor: 'auto',\r\n    labels: null,\r\n    color: 'CornflowerBlue',\r\n    lineColor: null,\r\n    headColor: null,\r\n    tailColor: null,\r\n    strokeWidth: 4,\r\n    showHead: true,\r\n    headSize: 6,\r\n    showTail: false,\r\n    tailSize: 6,\r\n    path: 'smooth',\r\n    curveness: 0.8,\r\n    gridBreak: '50%',\r\n    // gridRadius : strokeWidth * 2, //todo\r\n    dashness: false,\r\n    headShape: 'arrow1',\r\n    tailShape: 'arrow1',\r\n    showXarrow: true,\r\n    animateDrawing: false,\r\n    zIndex: 0,\r\n    passProps: {},\r\n    arrowBodyProps: {},\r\n    arrowHeadProps: {},\r\n    arrowTailProps: {},\r\n    SVGcanvasProps: {},\r\n    divContainerProps: {},\r\n    divContainerStyle: {},\r\n    SVGcanvasStyle: {},\r\n    _extendSVGcanvas: 0,\r\n    _debug: false,\r\n    _cpx1Offset: 0,\r\n    _cpy1Offset: 0,\r\n    _cpx2Offset: 0,\r\n    _cpy2Offset: 0,\r\n};\r\nvar initialParsedProps = {};\r\ninitialParsedProps = parseGivenProps(defaultProps, initialParsedProps);\r\nvar initialValVars = {\r\n    startPos: { x: 0, y: 0, right: 0, bottom: 0 },\r\n    endPos: { x: 0, y: 0, right: 0, bottom: 0 },\r\n};\r\n// const parseAllProps = () => parseGivenProps(defaultProps, initialParsedProps);\r\nfunction deepCompareEquals(a, b) {\r\n    return lodash_1.default.isEqual(a, b);\r\n}\r\nfunction useDeepCompareMemoize(value) {\r\n    var ref = react_1.useRef();\r\n    // it can be done by using useMemo as well\r\n    // but useRef is rather cleaner and easier\r\n    if (!deepCompareEquals(value, ref.current)) {\r\n        ref.current = value;\r\n    }\r\n    return ref.current;\r\n}\r\nfunction useDeepCompareEffect(callback, dependencies) {\r\n    react_1.useLayoutEffect(callback, dependencies.map(useDeepCompareMemoize));\r\n}\r\n/**\r\n * smart hook that provides parsed props to Xarrow and will trigger rerender whenever given prop is changed.\r\n */\r\nvar useXarrowProps = function (userProps, refs) {\r\n    var _a = react_1.useState(initialParsedProps), propsRefs = _a[0], setPropsRefs = _a[1];\r\n    var shouldUpdatePosition = react_1.useRef(false);\r\n    // const _propsRefs = useRef(initialParsedProps);\r\n    // const propsRefs = _propsRefs.current;\r\n    propsRefs['shouldUpdatePosition'] = shouldUpdatePosition;\r\n    var curProps = __assign(__assign({}, defaultProps), userProps);\r\n    var _loop_1 = function (propName) {\r\n        react_1.useLayoutEffect(function () {\r\n            var _a;\r\n            propsRefs[propName] = (_a = parsePropsFuncs === null || parsePropsFuncs === void 0 ? void 0 : parsePropsFuncs[propName]) === null || _a === void 0 ? void 0 : _a.call(parsePropsFuncs, curProps[propName], propsRefs, shouldUpdatePosition);\r\n            // console.log('prop update:', propName, 'with value', propsRefs[propName]);\r\n            setPropsRefs(__assign({}, propsRefs));\r\n        }, propsDeps[propName].map(function (name) { return userProps[name]; }));\r\n    };\r\n    // react states the number of hooks per render must stay constant,\r\n    // this is ok we are using these hooks in a loop, because the number of props in defaultProps is constant,\r\n    // so the number of hook we will fire each render will always be the same.\r\n    // update the value of the ref that represents the corresponding prop\r\n    // for example: if given 'start' prop would change call getElementByPropGiven(props.start) and save value into propsRefs.start.current\r\n    // why to save refs to props parsed values? some of the props require relatively expensive computations(like 'start' and 'startAnchor').\r\n    // this will always run in the same order and THAT'S WAY ITS LEGAL\r\n    for (var propName in defaultProps) {\r\n        _loop_1(propName);\r\n    }\r\n    // rerender whenever position of start element or end element changes\r\n    var _b = react_1.useState(initialValVars), valVars = _b[0], setValVars = _b[1];\r\n    var startPos = utils_1.getElemPos(propsRefs.start);\r\n    useDeepCompareEffect(function () {\r\n        valVars.startPos = startPos;\r\n        shouldUpdatePosition.current = true;\r\n        setValVars(__assign({}, valVars));\r\n        // console.log('start update pos', startPos);\r\n    }, [startPos]);\r\n    var endPos = utils_1.getElemPos(propsRefs.end);\r\n    useDeepCompareEffect(function () {\r\n        valVars.endPos = endPos;\r\n        shouldUpdatePosition.current = true;\r\n        setValVars(__assign({}, valVars));\r\n        // console.log('end update pos', endPos);\r\n    }, [endPos]);\r\n    react_1.useLayoutEffect(function () {\r\n        // console.log('svg shape changed!');\r\n        shouldUpdatePosition.current = true;\r\n        setValVars(__assign({}, valVars));\r\n    }, [propsRefs.headShape.svgElem, propsRefs.tailShape.svgElem]);\r\n    return [propsRefs, valVars];\r\n};\r\nexports.default = useXarrowProps;\r\n\n\n/***/ }),\n\n/***/ \"./src/Xarrow/utils/GetPosition.tsx\":\n/*!******************************************!*\\\n  !*** ./src/Xarrow/utils/GetPosition.tsx ***!\n  \\******************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getPosition = void 0;\r\nvar anchors_1 = __webpack_require__(/*! ../anchors */ \"./src/Xarrow/anchors.ts\");\r\nvar index_1 = __webpack_require__(/*! ./index */ \"./src/Xarrow/utils/index.ts\");\r\nvar lodash_1 = __importDefault(__webpack_require__(/*! lodash */ \"lodash\"));\r\nvar constants_1 = __webpack_require__(/*! ../../constants */ \"./src/constants.tsx\");\r\nvar buzzier_1 = __webpack_require__(/*! ./buzzier */ \"./src/Xarrow/utils/buzzier.js\");\r\n/**\r\n * The Main logic of path calculation for the arrow.\r\n * calculate new path, adjusting canvas, and set state based on given properties.\r\n * */\r\nvar getPosition = function (xProps, mainRef) {\r\n    var _a, _b;\r\n    var _c, _d;\r\n    var propsRefs = xProps[0], valVars = xProps[1];\r\n    var startAnchor = propsRefs.startAnchor, endAnchor = propsRefs.endAnchor, strokeWidth = propsRefs.strokeWidth, showHead = propsRefs.showHead, headSize = propsRefs.headSize, showTail = propsRefs.showTail, tailSize = propsRefs.tailSize, path = propsRefs.path, curveness = propsRefs.curveness, gridBreak = propsRefs.gridBreak, headShape = propsRefs.headShape, tailShape = propsRefs.tailShape, _extendSVGcanvas = propsRefs._extendSVGcanvas, _cpx1Offset = propsRefs._cpx1Offset, _cpy1Offset = propsRefs._cpy1Offset, _cpx2Offset = propsRefs._cpx2Offset, _cpy2Offset = propsRefs._cpy2Offset;\r\n    var startPos = valVars.startPos, endPos = valVars.endPos;\r\n    var _e = mainRef.current, svgRef = _e.svgRef, lineRef = _e.lineRef;\r\n    var headOrient = 0;\r\n    var tailOrient = 0;\r\n    // convert startAnchor and endAnchor to list of objects represents allowed anchors.\r\n    var startPoints = anchors_1.calcAnchors(startAnchor, startPos);\r\n    var endPoints = anchors_1.calcAnchors(endAnchor, endPos);\r\n    // choose the smallest path for 2 points from these possibilities.\r\n    var _f = index_1.getShortestLine(startPoints, endPoints), chosenStart = _f.chosenStart, chosenEnd = _f.chosenEnd;\r\n    var startAnchorPosition = chosenStart.anchor.position, endAnchorPosition = chosenEnd.anchor.position;\r\n    var startPoint = lodash_1.default.pick(chosenStart, ['x', 'y']), endPoint = lodash_1.default.pick(chosenEnd, ['x', 'y']);\r\n    var mainDivPos = index_1.getSvgPos(svgRef);\r\n    var cx0 = Math.min(startPoint.x, endPoint.x) - mainDivPos.x;\r\n    var cy0 = Math.min(startPoint.y, endPoint.y) - mainDivPos.y;\r\n    var dx = endPoint.x - startPoint.x;\r\n    var dy = endPoint.y - startPoint.y;\r\n    var absDx = Math.abs(endPoint.x - startPoint.x);\r\n    var absDy = Math.abs(endPoint.y - startPoint.y);\r\n    var xSign = dx > 0 ? 1 : -1;\r\n    var ySign = dy > 0 ? 1 : -1;\r\n    var _g = [headShape.offsetForward, tailShape.offsetForward], headOffset = _g[0], tailOffset = _g[1];\r\n    var fHeadSize = headSize * strokeWidth; //factored head size\r\n    var fTailSize = tailSize * strokeWidth; //factored head size\r\n    // const { current: _headBox } = headBox;\r\n    var xHeadOffset = 0;\r\n    var yHeadOffset = 0;\r\n    var xTailOffset = 0;\r\n    var yTailOffset = 0;\r\n    var _headOffset = fHeadSize * headOffset;\r\n    var _tailOffset = fTailSize * tailOffset;\r\n    var cu = Number(curveness);\r\n    // gridRadius = Number(gridRadius);\r\n    if (!constants_1.cPaths.includes(path))\r\n        path = 'smooth';\r\n    if (path === 'straight') {\r\n        cu = 0;\r\n        path = 'smooth';\r\n    }\r\n    var biggerSide = headSize > tailSize ? headSize : tailSize;\r\n    var _calc = strokeWidth + (strokeWidth * biggerSide) / 2;\r\n    var excRight = _calc;\r\n    var excLeft = _calc;\r\n    var excUp = _calc;\r\n    var excDown = _calc;\r\n    excLeft += Number(_extendSVGcanvas);\r\n    excRight += Number(_extendSVGcanvas);\r\n    excUp += Number(_extendSVGcanvas);\r\n    excDown += Number(_extendSVGcanvas);\r\n    ////////////////////////////////////\r\n    // arrow point to point calculations\r\n    var x1 = 0, x2 = absDx, y1 = 0, y2 = absDy;\r\n    if (dx < 0)\r\n        _a = [x2, x1], x1 = _a[0], x2 = _a[1];\r\n    if (dy < 0)\r\n        _b = [y2, y1], y1 = _b[0], y2 = _b[1];\r\n    ////////////////////////////////////\r\n    // arrow curviness and arrowhead placement calculations\r\n    if (cu === 0) {\r\n        // in case of straight path\r\n        var headAngel = Math.atan(absDy / absDx);\r\n        if (showHead) {\r\n            x2 -= fHeadSize * (1 - headOffset) * xSign * Math.cos(headAngel);\r\n            y2 -= fHeadSize * (1 - headOffset) * ySign * Math.sin(headAngel);\r\n            headAngel *= ySign;\r\n            if (xSign < 0)\r\n                headAngel = (Math.PI - headAngel * xSign) * xSign;\r\n            xHeadOffset = Math.cos(headAngel) * _headOffset - (Math.sin(headAngel) * fHeadSize) / 2;\r\n            yHeadOffset = (Math.cos(headAngel) * fHeadSize) / 2 + Math.sin(headAngel) * _headOffset;\r\n            headOrient = (headAngel * 180) / Math.PI;\r\n        }\r\n        var tailAngel = Math.atan(absDy / absDx);\r\n        if (showTail) {\r\n            x1 += fTailSize * (1 - tailOffset) * xSign * Math.cos(tailAngel);\r\n            y1 += fTailSize * (1 - tailOffset) * ySign * Math.sin(tailAngel);\r\n            tailAngel *= -ySign;\r\n            if (xSign > 0)\r\n                tailAngel = (Math.PI - tailAngel * xSign) * xSign;\r\n            xTailOffset = Math.cos(tailAngel) * _tailOffset - (Math.sin(tailAngel) * fTailSize) / 2;\r\n            yTailOffset = (Math.cos(tailAngel) * fTailSize) / 2 + Math.sin(tailAngel) * _tailOffset;\r\n            tailOrient = (tailAngel * 180) / Math.PI;\r\n        }\r\n    }\r\n    else {\r\n        // in case of smooth path\r\n        if (endAnchorPosition === 'middle') {\r\n            // in case a middle anchor is chosen for endAnchor choose from which side to attach to the middle of the element\r\n            if (absDx > absDy) {\r\n                endAnchorPosition = xSign ? 'left' : 'right';\r\n            }\r\n            else {\r\n                endAnchorPosition = ySign ? 'top' : 'bottom';\r\n            }\r\n        }\r\n        if (showHead) {\r\n            if (['left', 'right'].includes(endAnchorPosition)) {\r\n                xHeadOffset += _headOffset * xSign;\r\n                x2 -= fHeadSize * (1 - headOffset) * xSign; //same!\r\n                yHeadOffset += (fHeadSize * xSign) / 2;\r\n                if (endAnchorPosition === 'left') {\r\n                    headOrient = 0;\r\n                    if (xSign < 0)\r\n                        headOrient += 180;\r\n                }\r\n                else {\r\n                    headOrient = 180;\r\n                    if (xSign > 0)\r\n                        headOrient += 180;\r\n                }\r\n            }\r\n            else if (['top', 'bottom'].includes(endAnchorPosition)) {\r\n                xHeadOffset += (fHeadSize * -ySign) / 2;\r\n                yHeadOffset += _headOffset * ySign;\r\n                y2 -= fHeadSize * ySign - yHeadOffset;\r\n                if (endAnchorPosition === 'top') {\r\n                    headOrient = 270;\r\n                    if (ySign > 0)\r\n                        headOrient += 180;\r\n                }\r\n                else {\r\n                    headOrient = 90;\r\n                    if (ySign < 0)\r\n                        headOrient += 180;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (showTail && cu !== 0) {\r\n        if (['left', 'right'].includes(startAnchorPosition)) {\r\n            xTailOffset += _tailOffset * -xSign;\r\n            x1 += fTailSize * xSign + xTailOffset;\r\n            yTailOffset += -(fTailSize * xSign) / 2;\r\n            if (startAnchorPosition === 'left') {\r\n                tailOrient = 180;\r\n                if (xSign < 0)\r\n                    tailOrient += 180;\r\n            }\r\n            else {\r\n                tailOrient = 0;\r\n                if (xSign > 0)\r\n                    tailOrient += 180;\r\n            }\r\n        }\r\n        else if (['top', 'bottom'].includes(startAnchorPosition)) {\r\n            yTailOffset += _tailOffset * -ySign;\r\n            y1 += fTailSize * ySign + yTailOffset;\r\n            xTailOffset += (fTailSize * ySign) / 2;\r\n            if (startAnchorPosition === 'top') {\r\n                tailOrient = 90;\r\n                if (ySign > 0)\r\n                    tailOrient += 180;\r\n            }\r\n            else {\r\n                tailOrient = 270;\r\n                if (ySign < 0)\r\n                    tailOrient += 180;\r\n            }\r\n        }\r\n    }\r\n    var arrowHeadOffset = { x: xHeadOffset, y: yHeadOffset };\r\n    var arrowTailOffset = { x: xTailOffset, y: yTailOffset };\r\n    var cpx1 = x1, cpy1 = y1, cpx2 = x2, cpy2 = y2;\r\n    var curvesPossibilities = {};\r\n    if (path === 'smooth')\r\n        curvesPossibilities = {\r\n            hh: function () {\r\n                //horizontal - from right to left or the opposite\r\n                cpx1 += absDx * cu * xSign;\r\n                cpx2 -= absDx * cu * xSign;\r\n            },\r\n            vv: function () {\r\n                //vertical - from top to bottom or opposite\r\n                cpy1 += absDy * cu * ySign;\r\n                cpy2 -= absDy * cu * ySign;\r\n            },\r\n            hv: function () {\r\n                // start horizontally then vertically\r\n                // from v side to h side\r\n                cpx1 += absDx * cu * xSign;\r\n                cpy2 -= absDy * cu * ySign;\r\n            },\r\n            vh: function () {\r\n                // start vertically then horizontally\r\n                // from h side to v side\r\n                cpy1 += absDy * cu * ySign;\r\n                cpx2 -= absDx * cu * xSign;\r\n            },\r\n        };\r\n    else if (path === 'grid') {\r\n        curvesPossibilities = {\r\n            hh: function () {\r\n                cpx1 += (absDx * gridBreak.relative + gridBreak.abs) * xSign;\r\n                cpx2 -= (absDx * (1 - gridBreak.relative) - gridBreak.abs) * xSign;\r\n                if (showHead) {\r\n                    cpx1 -= ((fHeadSize * (1 - headOffset)) / 2) * xSign;\r\n                    cpx2 += ((fHeadSize * (1 - headOffset)) / 2) * xSign;\r\n                }\r\n                if (showTail) {\r\n                    cpx1 -= ((fTailSize * (1 - tailOffset)) / 2) * xSign;\r\n                    cpx2 += ((fTailSize * (1 - tailOffset)) / 2) * xSign;\r\n                }\r\n            },\r\n            vv: function () {\r\n                cpy1 += (absDy * gridBreak.relative + gridBreak.abs) * ySign;\r\n                cpy2 -= (absDy * (1 - gridBreak.relative) - gridBreak.abs) * ySign;\r\n                if (showHead) {\r\n                    cpy1 -= ((fHeadSize * (1 - headOffset)) / 2) * ySign;\r\n                    cpy2 += ((fHeadSize * (1 - headOffset)) / 2) * ySign;\r\n                }\r\n                if (showTail) {\r\n                    cpy1 -= ((fTailSize * (1 - tailOffset)) / 2) * ySign;\r\n                    cpy2 += ((fTailSize * (1 - tailOffset)) / 2) * ySign;\r\n                }\r\n            },\r\n            hv: function () {\r\n                cpx1 = x2;\r\n            },\r\n            vh: function () {\r\n                cpy1 = y2;\r\n            },\r\n        };\r\n    }\r\n    // smart select best curve for the current anchors\r\n    var selectedCurviness = '';\r\n    if (['left', 'right'].includes(startAnchorPosition))\r\n        selectedCurviness += 'h';\r\n    else if (['bottom', 'top'].includes(startAnchorPosition))\r\n        selectedCurviness += 'v';\r\n    else if (startAnchorPosition === 'middle')\r\n        selectedCurviness += 'm';\r\n    if (['left', 'right'].includes(endAnchorPosition))\r\n        selectedCurviness += 'h';\r\n    else if (['bottom', 'top'].includes(endAnchorPosition))\r\n        selectedCurviness += 'v';\r\n    else if (endAnchorPosition === 'middle')\r\n        selectedCurviness += 'm';\r\n    if (absDx > absDy)\r\n        selectedCurviness = selectedCurviness.replace(/m/g, 'h');\r\n    else\r\n        selectedCurviness = selectedCurviness.replace(/m/g, 'v');\r\n    curvesPossibilities[selectedCurviness]();\r\n    cpx1 += _cpx1Offset;\r\n    cpy1 += _cpy1Offset;\r\n    cpx2 += _cpx2Offset;\r\n    cpy2 += _cpy2Offset;\r\n    ////////////////////////////////////\r\n    // canvas smart size adjustments\r\n    var _h = buzzier_1.buzzierMinSols(x1, cpx1, cpx2, x2), xSol1 = _h[0], xSol2 = _h[1];\r\n    var _j = buzzier_1.buzzierMinSols(y1, cpy1, cpy2, y2), ySol1 = _j[0], ySol2 = _j[1];\r\n    if (xSol1 < 0)\r\n        excLeft += -xSol1;\r\n    if (xSol2 > absDx)\r\n        excRight += xSol2 - absDx;\r\n    if (ySol1 < 0)\r\n        excUp += -ySol1;\r\n    if (ySol2 > absDy)\r\n        excDown += ySol2 - absDy;\r\n    if (path === 'grid') {\r\n        excLeft += _calc;\r\n        excRight += _calc;\r\n        excUp += _calc;\r\n        excDown += _calc;\r\n    }\r\n    x1 += excLeft;\r\n    x2 += excLeft;\r\n    y1 += excUp;\r\n    y2 += excUp;\r\n    cpx1 += excLeft;\r\n    cpx2 += excLeft;\r\n    cpy1 += excUp;\r\n    cpy2 += excUp;\r\n    var cw = absDx + excLeft + excRight, ch = absDy + excUp + excDown;\r\n    cx0 -= excLeft;\r\n    cy0 -= excUp;\r\n    //labels\r\n    var bzx = buzzier_1.bzFunction(x1, cpx1, cpx2, x2);\r\n    var bzy = buzzier_1.bzFunction(y1, cpy1, cpy2, y2);\r\n    var labelStartPos = { x: bzx(0.01), y: bzy(0.01) };\r\n    var labelMiddlePos = { x: bzx(0.5), y: bzy(0.5) };\r\n    var labelEndPos = { x: bzx(0.99), y: bzy(0.99) };\r\n    var arrowPath;\r\n    if (path === 'grid') {\r\n        // todo: support gridRadius\r\n        //  arrowPath = `M ${x1} ${y1} L  ${cpx1 - 10} ${cpy1} a10,10 0 0 1 10,10\r\n        // L ${cpx2} ${cpy2 - 10} a10,10 0 0 0 10,10 L  ${x2} ${y2}`;\r\n        arrowPath = \"M \" + x1 + \" \" + y1 + \" L  \" + cpx1 + \" \" + cpy1 + \" L \" + cpx2 + \" \" + cpy2 + \" \" + x2 + \" \" + y2;\r\n    }\r\n    else if (path === 'smooth')\r\n        arrowPath = \"M \" + x1 + \" \" + y1 + \" C \" + cpx1 + \" \" + cpy1 + \", \" + cpx2 + \" \" + cpy2 + \", \" + x2 + \" \" + y2;\r\n    return {\r\n        cx0: cx0,\r\n        cy0: cy0,\r\n        x1: x1,\r\n        x2: x2,\r\n        y1: y1,\r\n        y2: y2,\r\n        cw: cw,\r\n        ch: ch,\r\n        cpx1: cpx1,\r\n        cpy1: cpy1,\r\n        cpx2: cpx2,\r\n        cpy2: cpy2,\r\n        dx: dx,\r\n        dy: dy,\r\n        absDx: absDx,\r\n        absDy: absDy,\r\n        headOrient: headOrient,\r\n        tailOrient: tailOrient,\r\n        labelStartPos: labelStartPos,\r\n        labelMiddlePos: labelMiddlePos,\r\n        labelEndPos: labelEndPos,\r\n        excLeft: excLeft,\r\n        excRight: excRight,\r\n        excUp: excUp,\r\n        excDown: excDown,\r\n        headOffset: _headOffset,\r\n        arrowHeadOffset: arrowHeadOffset,\r\n        arrowTailOffset: arrowTailOffset,\r\n        startPoints: startPoints,\r\n        endPoints: endPoints,\r\n        mainDivPos: mainDivPos,\r\n        xSign: xSign,\r\n        ySign: ySign,\r\n        lineLength: (_d = (_c = lineRef.current) === null || _c === void 0 ? void 0 : _c.getTotalLength()) !== null && _d !== void 0 ? _d : 0,\r\n        fHeadSize: fHeadSize,\r\n        fTailSize: fTailSize,\r\n        arrowPath: arrowPath,\r\n    };\r\n};\r\nexports.getPosition = getPosition;\r\n\n\n/***/ }),\n\n/***/ \"./src/Xarrow/utils/index.ts\":\n/*!***********************************!*\\\n  !*** ./src/Xarrow/utils/index.ts ***!\n  \\***********************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getSvgPos = exports.getElemPos = exports.getShortestLine = exports.xStr2absRelative = exports.factorDpathStr = exports.getElementByPropGiven = void 0;\r\nvar getElementByPropGiven = function (ref) {\r\n    var myRef;\r\n    if (typeof ref === 'string') {\r\n        // myRef = document.getElementById(ref);\r\n        myRef = document.getElementById(ref);\r\n    }\r\n    else\r\n        myRef = ref === null || ref === void 0 ? void 0 : ref.current;\r\n    return myRef;\r\n};\r\nexports.getElementByPropGiven = getElementByPropGiven;\r\n// receives string representing a d path and factoring only the numbers\r\nvar factorDpathStr = function (d, factor) {\r\n    var l = d.split(/(\\d+(?:\\.\\d+)?)/);\r\n    l = l.map(function (s) {\r\n        if (Number(s))\r\n            return (Number(s) * factor).toString();\r\n        else\r\n            return s;\r\n    });\r\n    return l.join('');\r\n};\r\nexports.factorDpathStr = factorDpathStr;\r\n// return relative,abs\r\nvar xStr2absRelative = function (str) {\r\n    if (typeof str !== 'string')\r\n        return { abs: 0, relative: 0.5 };\r\n    var sp = str.split('%');\r\n    var absLen = 0, percentLen = 0;\r\n    if (sp.length == 1) {\r\n        var p = parseFloat(sp[0]);\r\n        if (!isNaN(p)) {\r\n            absLen = p;\r\n            return { abs: absLen, relative: 0 };\r\n        }\r\n    }\r\n    else if (sp.length == 2) {\r\n        var _a = [parseFloat(sp[0]), parseFloat(sp[1])], p1 = _a[0], p2 = _a[1];\r\n        if (!isNaN(p1))\r\n            percentLen = p1 / 100;\r\n        if (!isNaN(p2))\r\n            absLen = p2;\r\n        if (!isNaN(p1) || !isNaN(p2))\r\n            return { abs: absLen, relative: percentLen };\r\n    }\r\n};\r\nexports.xStr2absRelative = xStr2absRelative;\r\nvar dist = function (p1, p2) {\r\n    //length of line\r\n    return Math.sqrt(Math.pow((p1.x - p2.x), 2) + Math.pow((p1.y - p2.y), 2));\r\n};\r\nvar getShortestLine = function (sPoints, ePoints) {\r\n    // closes tPair Of Points which feet to the specified anchors\r\n    var minDist = Infinity, d = Infinity;\r\n    var closestPair;\r\n    sPoints.forEach(function (sp) {\r\n        ePoints.forEach(function (ep) {\r\n            d = dist(sp, ep);\r\n            if (d < minDist) {\r\n                minDist = d;\r\n                closestPair = { chosenStart: sp, chosenEnd: ep };\r\n            }\r\n        });\r\n    });\r\n    return closestPair;\r\n};\r\nexports.getShortestLine = getShortestLine;\r\nvar getElemPos = function (elem) {\r\n    if (!elem)\r\n        return { x: 0, y: 0, right: 0, bottom: 0 };\r\n    var pos = elem.getBoundingClientRect();\r\n    return {\r\n        x: pos.left,\r\n        y: pos.top,\r\n        right: pos.right,\r\n        bottom: pos.bottom,\r\n    };\r\n};\r\nexports.getElemPos = getElemPos;\r\nvar getSvgPos = function (svgRef) {\r\n    if (!svgRef.current)\r\n        return { x: 0, y: 0 };\r\n    var _a = svgRef.current.getBoundingClientRect(), xarrowElemX = _a.left, xarrowElemY = _a.top;\r\n    var xarrowStyle = getComputedStyle(svgRef.current);\r\n    var xarrowStyleLeft = Number(xarrowStyle.left.slice(0, -2));\r\n    var xarrowStyleTop = Number(xarrowStyle.top.slice(0, -2));\r\n    return {\r\n        x: xarrowElemX - xarrowStyleLeft,\r\n        y: xarrowElemY - xarrowStyleTop,\r\n    };\r\n};\r\nexports.getSvgPos = getSvgPos;\r\n\n\n/***/ }),\n\n/***/ \"./src/Xwrapper.tsx\":\n/*!**************************!*\\\n  !*** ./src/Xwrapper.tsx ***!\n  \\**************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.XarrowContext = exports.XelemContext = void 0;\r\nvar react_1 = __importStar(__webpack_require__(/*! react */ \"react\"));\r\nexports.XelemContext = react_1.default.createContext(null);\r\nexports.XarrowContext = react_1.default.createContext(null);\r\nvar updateRef = {};\r\nvar updateRefCount = 0;\r\nvar log = console.log;\r\nvar XarrowProvider = function (_a) {\r\n    var children = _a.children, instanceCount = _a.instanceCount;\r\n    var _b = react_1.useState({}), setRender = _b[1];\r\n    var updateXarrow = function () { return setRender({}); };\r\n    react_1.useEffect(function () {\r\n        instanceCount.current = updateRefCount; // so this instance would know what is id\r\n        updateRef[instanceCount.current] = updateXarrow;\r\n    }, []);\r\n    // log('XarrowProvider', updateRefCount);\r\n    return react_1.default.createElement(exports.XarrowContext.Provider, { value: updateXarrow }, children);\r\n};\r\nvar XelemProvider = function (_a) {\r\n    var children = _a.children, instanceCount = _a.instanceCount;\r\n    return react_1.default.createElement(exports.XelemContext.Provider, { value: updateRef[instanceCount.current] }, children);\r\n};\r\nvar Xwrapper = function (_a) {\r\n    var children = _a.children;\r\n    var instanceCount = react_1.useRef(updateRefCount);\r\n    var _b = react_1.useState({}), setRender = _b[1];\r\n    react_1.useEffect(function () {\r\n        updateRefCount++;\r\n        setRender({});\r\n        return function () {\r\n            delete updateRef[instanceCount.current];\r\n        };\r\n    }, []);\r\n    return (react_1.default.createElement(XelemProvider, { instanceCount: instanceCount },\r\n        react_1.default.createElement(XarrowProvider, { instanceCount: instanceCount }, children)));\r\n};\r\nexports.default = Xwrapper;\r\n\n\n/***/ }),\n\n/***/ \"./src/constants.tsx\":\n/*!***************************!*\\\n  !*** ./src/constants.tsx ***!\n  \\***************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.cArrowShapes = exports.arrowShapes = exports.cSvgElems = exports.cPaths = exports.cAnchorEdge = void 0;\r\n// constants used for typescript and proptypes definitions\r\nvar react_1 = __importDefault(__webpack_require__(/*! react */ \"react\"));\r\nexports.cAnchorEdge = ['middle', 'left', 'right', 'top', 'bottom', 'auto'];\r\nexports.cPaths = ['smooth', 'grid', 'straight'];\r\nexports.cSvgElems = ['circle', 'ellipse', 'line', 'path', 'polygon', 'polyline', 'rect'];\r\n//default arrows svgs\r\nexports.arrowShapes = {\r\n    arrow1: { svgElem: react_1.default.createElement(\"path\", { d: \"M 0 0 L 1 0.5 L 0 1 L 0.25 0.5 z\" }), offsetForward: 0.25 },\r\n    heart: {\r\n        svgElem: (react_1.default.createElement(\"path\", { d: \"M 0,0.25 A 0.125,0.125 0,0,1 0.5,0.25 A 0.125,0.125 0,0,1 1,0.25 Q 1,0.625 0.5,1 Q 0,0.625 0,0.25 z\" })),\r\n        offsetForward: 0.1,\r\n    },\r\n    circle: {\r\n        svgElem: react_1.default.createElement(\"circle\", { r: 0.5, cx: 0.5, cy: 0.5 }),\r\n        offsetForward: 0,\r\n    },\r\n};\r\nexports.cArrowShapes = Object.keys(exports.arrowShapes);\r\n\n\n/***/ }),\n\n/***/ \"./src/index.tsx\":\n/*!***********************!*\\\n  !*** ./src/index.tsx ***!\n  \\***********************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.useXarrow = exports.Xwrapper = exports.default = void 0;\r\nvar Xarrow_1 = __webpack_require__(/*! ./Xarrow/Xarrow */ \"./src/Xarrow/Xarrow.tsx\");\r\nObject.defineProperty(exports, \"default\", ({ enumerable: true, get: function () { return __importDefault(Xarrow_1).default; } }));\r\n__exportStar(__webpack_require__(/*! ./types */ \"./src/types.ts\"), exports);\r\n__exportStar(__webpack_require__(/*! ./constants */ \"./src/constants.tsx\"), exports);\r\nvar Xwrapper_1 = __webpack_require__(/*! ./Xwrapper */ \"./src/Xwrapper.tsx\");\r\nObject.defineProperty(exports, \"Xwrapper\", ({ enumerable: true, get: function () { return __importDefault(Xwrapper_1).default; } }));\r\nvar useXarrow_1 = __webpack_require__(/*! ./useXarrow */ \"./src/useXarrow.tsx\");\r\nObject.defineProperty(exports, \"useXarrow\", ({ enumerable: true, get: function () { return __importDefault(useXarrow_1).default; } }));\r\n\n\n/***/ }),\n\n/***/ \"./src/types.ts\":\n/*!**********************!*\\\n  !*** ./src/types.ts ***!\n  \\**********************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\n\n\n/***/ }),\n\n/***/ \"./src/useXarrow.tsx\":\n/*!***************************!*\\\n  !*** ./src/useXarrow.tsx ***!\n  \\***************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nvar react_1 = __webpack_require__(/*! react */ \"react\");\r\nvar Xwrapper_1 = __webpack_require__(/*! ./Xwrapper */ \"./src/Xwrapper.tsx\");\r\nvar useXarrow = function () {\r\n    var _a = react_1.useState({}), setRender = _a[1];\r\n    var reRender = function () { return setRender({}); };\r\n    var updateXarrow = react_1.useContext(Xwrapper_1.XelemContext);\r\n    if (!updateXarrow)\r\n        updateXarrow = function () { };\r\n    // throw new Error(\r\n    //   \"'Xwrapper' is required around element using 'useXarrow' hook! wrap your xarrows and connected elements with Xwrapper! \"\r\n    // );\r\n    react_1.useLayoutEffect(function () {\r\n        updateXarrow();\r\n    });\r\n    return reRender;\r\n};\r\nexports.default = useXarrow;\r\n\n\n/***/ }),\n\n/***/ \"lodash\":\n/*!*************************!*\\\n  !*** external \"lodash\" ***!\n  \\*************************/\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_lodash__;\n\n/***/ }),\n\n/***/ \"prop-types\":\n/*!*****************************!*\\\n  !*** external \"prop-types\" ***!\n  \\*****************************/\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_prop_types__;\n\n/***/ }),\n\n/***/ \"react\":\n/*!************************!*\\\n  !*** external \"react\" ***!\n  \\************************/\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_react__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = __webpack_module_cache__;\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/node module decorator */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.nmd = function(module) {\n/******/ \t\t\tmodule.paths = [];\n/******/ \t\t\tif (!module.children) module.children = [];\n/******/ \t\t\treturn module;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// module cache are used so entry inlining is disabled\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \tvar __webpack_exports__ = __webpack_require__(__webpack_require__.s = \"./src/index.tsx\");\n/******/ \t\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ,iBAAkB,kBAAmB,oBAAqB;AAAA,eAC5E,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,eAAe,CAAC,SAAS,UAAU,YAAY,GAAG,OAAO;AAAA,eACzD,OAAO,YAAY;AAC1B,gBAAQ,aAAa,IAAI,QAAQ,iBAAkB,kBAAmB,oBAAqB;AAAA;AAE3F,aAAK,aAAa,IAAI,QAAQ,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK,YAAY,CAAC;AAAA,IACjF,GAAG,SAAM,SAAS,mCAAmC,oCAAoC,wCAAwC;AACjI;AAAA;AAAA,QAAiB,WAAW;AAClB;AACA,cAAI,sBAAuB;AAAA;AAAA,YAE/B;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,sBAAqBC,sBAAqB;AAEnF,gBAAAA,qBAAoB,EAAED,oBAAmB;AACpB,gBAAAC,qBAAoB,EAAED,sBAAqB;AAAA;AAAA,kBACzC,cAAc,WAAW;AAAE;AAAA;AAAA,sBAAqB;AAAA;AAAA,kBAAY;AAAA;AAAA,kBAC5D,kBAAkB,WAAW;AAAE;AAAA;AAAA,sBAAqB;AAAA;AAAA,kBAAgB;AAAA;AAAA,gBACtE,CAAC;AAOtB,oBAAI,aAAa,SAASE,YAAW,IAAI,IAAI,IAAI,IAAI;AACnD,yBAAO,SAAU,GAAG;AAClB,2BAAO,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,kBAC1H;AAAA,gBACF;AAKA,oBAAI,iBAAiB,SAASC,gBAAe,IAAI,IAAI,IAAI,IAAI;AAC3D,sBAAI,KAAK,WAAW,IAAI,IAAI,IAAI,EAAE;AAQlC,sBAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI;AAChC,sBAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAC3G,sBAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAC7C,sBAAI,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC;AACpC,sBAAI,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC;AACpC,yBAAO,CAAC,MAAM,IAAI;AAAA,gBACpB;AAAA,cAEM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBC,UAASH,sBAAqB;AAGvE,oBAAI,WAAY,QAAQ,KAAK,YAAa,WAAY;AAClD,6BAAW,OAAO,UAAU,SAAS,GAAG;AACpC,6BAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,0BAAI,UAAU,CAAC;AACf,+BAAS,KAAK;AAAG,4BAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,4BAAE,CAAC,IAAI,EAAE,CAAC;AAAA,oBAClB;AACA,2BAAO;AAAA,kBACX;AACA,yBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,gBACzC;AACA,oBAAI,kBAAmB,QAAQ,KAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,sBAAI,OAAO;AAAW,yBAAK;AAC3B,yBAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,2BAAO,EAAE,CAAC;AAAA,kBAAG,EAAE,CAAC;AAAA,gBACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,sBAAI,OAAO;AAAW,yBAAK;AAC3B,oBAAE,EAAE,IAAI,EAAE,CAAC;AAAA,gBACf;AACA,oBAAI,qBAAsB,QAAQ,KAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,yBAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,gBACtE,IAAK,SAAS,GAAG,GAAG;AAChB,oBAAE,SAAS,IAAI;AAAA,gBACnB;AACA,oBAAI,eAAgB,QAAQ,KAAK,gBAAiB,SAAU,KAAK;AAC7D,sBAAI,OAAO,IAAI;AAAY,2BAAO;AAClC,sBAAI,SAAS,CAAC;AACd,sBAAI,OAAO;AAAM,6BAAS,KAAK;AAAK,0BAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC;AAAG,wCAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,qCAAmB,QAAQ,GAAG;AAC9B,yBAAO;AAAA,gBACX;AACA,oBAAI,gBAAiB,QAAQ,KAAK,iBAAkB,SAAU,IAAI,MAAM;AACpE,2BAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,GAAG,QAAQ,IAAI,IAAI,KAAK;AAC1D,uBAAG,CAAC,IAAI,KAAK,CAAC;AAClB,yBAAO;AAAA,gBACX;AACA,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,oBAAI,UAAU,aAAaH;AAAA;AAAA,kBAAiC;AAAA,gBAAO,CAAC;AACpE,oBAAI,mBAAmB,gBAAgBA;AAAA;AAAA,kBAA4C;AAAA,gBAAgC,CAAC;AACpH,oBAAI,aAAaA;AAAA;AAAA,kBAAuC;AAAA,gBAAoB;AAC5E,oBAAI,cAAc,gBAAgBA;AAAA;AAAA,kBAAuC;AAAA,gBAA2B,CAAC;AACrG,oBAAI,gBAAgBA;AAAA;AAAA,kBAA+C;AAAA,gBAAoC;AACvG,oBAAI,MAAM,QAAQ;AAClB,oBAAI,SAAS,SAAU,OAAO;AAE1B,sBAAI;AACJ,sBAAI,UAAU,QAAQ,OAAO;AAAA,oBACzB,QAAQ,QAAQ,OAAO,IAAI;AAAA,oBAC3B,SAAS,QAAQ,OAAO,IAAI;AAAA,oBAC5B,SAAS,QAAQ,OAAO,IAAI;AAAA,oBAC5B,SAAS,QAAQ,OAAO,IAAI;AAAA,oBAC5B,iBAAiB,QAAQ,OAAO,IAAI;AAAA,oBACpC,iBAAiB,QAAQ,OAAO,IAAI;AAAA,oBACpC,oBAAoB,QAAQ,OAAO,IAAI;AAAA,kBAC3C,CAAC;AACD,sBAAI,KAAK,QAAQ,SAAS,SAAS,GAAG,QAAQ,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,kBAAkB,GAAG,iBAAiB,kBAAkB,GAAG,iBAAiB,qBAAqB,GAAG;AACpN,0BAAQ,WAAW,WAAW,aAAa;AAC3C,sBAAI,SAAS,iBAAiB,QAAQ,OAAO,QAAQ,OAAO;AAC5D,sBAAI,YAAY,OAAO,CAAC;AACxB,sBAAI,SAAS,UAAU,QAAQ,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,cAAc,UAAU,aAAa,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,aAAa,UAAU,YAAY,iBAAiB,UAAU,gBAAgB,SAAS,UAAU,QAAQ,YAAY,UAAU,WAAW,iBAAiB,UAAU,gBAAgB,iBAAiB,UAAU,gBAAgB,iBAAiB,UAAU,gBAAgB,iBAAiB,UAAU,gBAAgB,oBAAoB,UAAU,mBAAmB,oBAAoB,UAAU,mBAAmB,iBAAiB,UAAU,gBAAgB,SAAS,UAAU,QAAQ,uBAAuB,UAAU;AACr0B,mCAAiB,MAAM;AACvB,sBAAI,KAAK,QAAQ,SAAS,CAAC,cAAc,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAC1F,sBAAI,KAAK,QAAQ,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C,sBAAI,gBAAgB,WAAY;AAAE,2BAAO,UAAU,CAAC,CAAC;AAAA,kBAAG;AACxD,sBAAI,KAAK,QAAQ,SAAS;AAAA;AAAA,oBAEtB,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,OAAO;AAAA,oBACP,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,YAAY;AAAA,oBACZ,YAAY;AAAA,oBACZ,iBAAiB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,oBAC9B,iBAAiB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,oBAC9B,YAAY;AAAA,oBACZ,UAAU;AAAA,oBACV,SAAS;AAAA,oBACT,OAAO;AAAA,oBACP,SAAS;AAAA,oBACT,aAAa,CAAC;AAAA,oBACd,WAAW,CAAC;AAAA,oBACZ,YAAY,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,oBACzB,OAAO;AAAA,oBACP,OAAO;AAAA,oBACP,YAAY;AAAA,oBACZ,WAAW;AAAA,oBACX,WAAW;AAAA,oBACX,WAAW;AAAA,oBACX,eAAe,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,oBAC5B,gBAAgB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,oBAC7B,aAAa,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,kBAC9B,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAK5B,0BAAQ,gBAAgB,WAAY;AAChC,wBAAI,qBAAqB,SAAS;AAE9B,0BAAI,MAAM,cAAc,YAAY,QAAQ,OAAO;AAEnD,4BAAM,GAAG;AACT,2CAAqB,UAAU;AAAA,oBACnC;AAAA,kBACJ,CAAC;AAED,sBAAI,cAAc,GAAG,KAAK,GAAG,gBAAgB;AAC7C,sBAAI,cAAc,GAAG,KAAK,GAAG,gBAAgB;AAC7C,sBAAI,cAAc,GAAG,KAAK,GAAG,gBAAgB;AAC7C,sBAAI,cAAc,GAAG,KAAK,GAAG,gBAAgB;AAC7C,sBAAI,aAAa,SAAS,YAAY,SAAS;AAC/C,sBAAI,gBAAgB;AACpB,sBAAI,SAAS,YAAY,GAAG;AACxB,6BAAS,aAAa;AACtB,oCAAgB;AAAA,kBACpB;AACA,sBAAI,WAAW,WAAW,iBAAiB,gBAAgB,eAAe;AAC1E,sBAAI,kBAAkB,iBAAiB,OAAO;AAC1C,wBAAI,OAAO,mBAAmB;AAC1B,uCAAiB;AACrB,gCAAY,iBAAiB;AAC7B,gCAAY,GAAG;AACf,qCAAiB,GAAG;AACpB,sCAAkB;AAClB,wBAAI,iBAAiB,GAAG;AACpB,2BAAK,CAAC,cAAc,cAAc,GAAG,iBAAiB,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAChF,kCAAY,iBAAiB,KAAK;AAAA,oBACtC;AAAA,kBACJ,OACK;AACD,gCAAY,SAAS,YAAY,MAAM,SAAS;AAChD,gCAAY,IAAI,SAAS,YAAY;AACrC,qCAAiB,aAAa;AAC9B,sCAAkB;AAClB,mCAAe;AAAA,kBACnB;AAEA,0BAAQ,gBAAgB,WAAY;AAChC,wBAAI,QAAQ;AACR,4BAAM,SAAU,QAAQ;AAAE,4BAAII,KAAIC;AAAI,+BAAQ,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,aAAaA,OAAMD,MAAK,QAAQ,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,eAAe,OAAO,QAAQC,QAAO,SAASA,MAAK,EAAE,CAAC;AAAA,sBAAI,CAAC;AAAA,kBACnO,GAAG,CAAC,QAAQ,OAAO,CAAC;AAEpB,0BAAQ,UAAU,WAAY;AAC1B,wBAAI,oBAAoB,WAAY;AAChC,6BAAO,iBAAiB,UAAU,aAAa;AAC/C,0BAAI,oBAAoB,WAAY;AAChC,4BAAID,KAAIC;AACR,yCAAiB,IAAI;AAErB,yBAACD,MAAK,mBAAmB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa;AAEvF,yBAACC,MAAK,gBAAgB,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa;AAAA,sBACxF;AACA,0BAAI,sBAAsB,WAAY;AAAE,+BAAQ,QAAQ,QAAQ,MAAM,UAAU;AAAA,sBAAM;AACtF,0BAAI,gBAAgB,WAAW,QAAQ,SAAS;AAC5C,wCAAgB,QAAQ,iBAAiB,YAAY,iBAAiB;AACtE,wCAAgB,QAAQ,iBAAiB,cAAc,mBAAmB;AAAA,sBAC9E;AACA,6BAAO,WAAY;AACf,+BAAO,oBAAoB,UAAU,aAAa;AAClD,4BAAI,gBAAgB,SAAS;AACzB,0CAAgB,QAAQ,oBAAoB,YAAY,iBAAiB;AACzE,8BAAI,QAAQ;AACR,4CAAgB,QAAQ,oBAAoB,cAAc,mBAAmB;AAAA,wBACrF;AAAA,sBACJ;AAAA,oBACJ;AACA,wBAAI,yBAAyB,kBAAkB;AAC/C,2BAAO,WAAY;AACf,uCAAiB,KAAK;AACtB,6CAAuB;AAAA,oBAC3B;AAAA,kBACJ,GAAG,CAAC,UAAU,CAAC;AAKf,yBAAQ,QAAQ,QAAQ,cAAc,OAAO,SAAS,CAAC,GAAG,mBAAmB,EAAE,OAAO,SAAS,EAAE,UAAU,YAAY,OAAe,GAAG,iBAAiB,EAAE,CAAC,GAAG,aAAc,QAAQ,QAAQ;AAAA,oBAAc,QAAQ,QAAQ;AAAA,oBAAU;AAAA,oBAClO,QAAQ,QAAQ;AAAA,sBAAc;AAAA,sBAAO,SAAS,EAAE,KAAK,QAAQ,OAAO,GAAG,IAAI,QAAQ,GAAG,IAAI,OAAO,SAAS,EAAE,UAAU,YAAY,MAAM,GAAG,KAAK,KAAK,GAAG,KAAK,eAAe,QAAQ,QAAQ,SAAS,sBAAsB,KAAK,GAAG,cAAc,GAAG,UAAU,OAAO,GAAG,cAAc;AAAA,sBAClR,QAAQ,QAAQ;AAAA,wBAAc;AAAA,wBAAQ,SAAS;AAAA,0BAAE,KAAK;AAAA,0BAAS,GAAG,GAAG;AAAA,0BAAW,QAAQ;AAAA,0BAAW,iBAAiB;AAAA;AAAA,0BAEhH;AAAA,0BAA0B,MAAM;AAAA,0BAAe,eAAe;AAAA,wBAAgB,GAAG,WAAW,cAAc;AAAA,wBAC1G,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,UAAU,MAAM,gBAAiB,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,UAAU,MAAM,SAAS,YAAa,QAAQ,QAAQ,cAAc,WAAW,EAAE,KAAK,iBAAiB,eAAe,qBAAqB,QAAQ,aAAa,gBAAgB,MAAM,KAAK,IAAI,SAAS,YAAY,KAAK,aAAa,aAAa,CAAC,IAAK,IAAI,IAAM,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,UAAU,MAAM,iBAAkB,QAAQ,QAAQ,cAAc,WAAW,EAAE,KAAK,iBAAiB,IAAI,iBAAiB,eAAe,qBAAqB,QAAQ,iBAAiB,MAAM,cAAc,KAAK,WAAW,aAAa,gBAAgB,CAAC,IAAK,IAAI,CAAE;AAAA,sBAAC;AAAA,sBACrrB,WAAY,QAAQ,QAAQ,cAAc,KAAK,SAAS,EAAE,MAAM,WAAW,eAAe,QAAQ,WAAW,eAAe,cAAc,MAAM,cAAc,cAAc,GAAG,aAAa,aAAa,GAAG,YAAY,IAAI,GAAG,WAAW,cAAc,GAAG,UAAU,OAAO,IAAK;AAAA,sBACjR,WAAY,QAAQ,QAAQ;AAAA,wBAAc;AAAA,wBAAK,SAAS;AAAA,0BAAE,KAAK;AAAA;AAAA,0BAE3D,MAAM;AAAA,0BAAW,eAAe;AAAA,0BAAQ,WAAW,eAAe,cAAc,MAAM,cAAc,cAAc,GAAG,aAAa,aAAa,GAAG,YAAY;AAAA,0BAAK,SAAS,kBAAkB,CAAC,gBAAgB,IAAI;AAAA,wBAAE,GAAG,WAAW,cAAc;AAAA,wBACjP,QAAQ,QAAQ,cAAc,WAAW,EAAE,KAAK,oBAAoB,KAAK,OAAO,eAAe,WAAW,MAAM,KAAK,IAAI,KAAK,OAAO,cAAc,aAAa,KAAK,MAAM,SAAS,CAAC;AAAA,wBACrL,UAAU;AAAA,sBAAO,IAAK;AAAA,sBAC1B,SAAU,QAAQ,QAAQ;AAAA,wBAAc,QAAQ,QAAQ;AAAA,wBAAU;AAAA,wBAC9D,QAAQ,QAAQ,cAAc,UAAU,EAAE,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG,MAAM,MAAM,QAAQ,CAAC;AAAA,wBAC3F,QAAQ,QAAQ,cAAc,UAAU,EAAE,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG,MAAM,MAAM,OAAO,CAAC;AAAA,wBAC1F,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,GAAG,SAAS,GAAG,GAAG,OAAO,OAAO,GAAG,OAAO,QAAQ,GAAG,OAAO,MAAM,QAAQ,QAAQ,QAAQ,aAAa,MAAM,CAAC;AAAA,sBAAC,IAAK;AAAA,oBAAI;AAAA,oBAC3K,OAAO,QAAS,QAAQ,QAAQ,cAAc,OAAO,EAAE,OAAO;AAAA,sBACtD,WAAW,GAAG,KAAK,IAAI,4BAA4B;AAAA,sBACnD,OAAO;AAAA,sBACP,UAAU;AAAA,sBACV,MAAM,GAAG,MAAM,GAAG,cAAc;AAAA,sBAChC,KAAK,GAAG,MAAM,GAAG,cAAc,IAAI,cAAc;AAAA,oBACrD,EAAE,GAAG,OAAO,KAAK,IAAK;AAAA,oBAC1B,OAAO,SAAU,QAAQ,QAAQ,cAAc,OAAO,EAAE,OAAO;AAAA,sBACvD,SAAS;AAAA,sBACT,OAAO;AAAA,sBACP,WAAW;AAAA,sBACX,UAAU;AAAA,sBACV,MAAM,GAAG,MAAM,GAAG,eAAe;AAAA,sBACjC,KAAK,GAAG,MAAM,GAAG,eAAe;AAAA,oBACpC,EAAE,GAAG,OAAO,MAAM,IAAK;AAAA,oBAC3B,OAAO,MAAO,QAAQ,QAAQ,cAAc,OAAO,EAAE,OAAO;AAAA,sBACpD,WAAW,GAAG,KAAK,IAAI,4BAA4B;AAAA,sBACnD,OAAO;AAAA,sBACP,UAAU;AAAA,sBACV,MAAM,GAAG,MAAM,GAAG,YAAY;AAAA,sBAC9B,KAAK,GAAG,MAAM,GAAG,YAAY,IAAI,cAAc;AAAA,oBACnD,EAAE,GAAG,OAAO,GAAG,IAAK;AAAA,oBACxB,SAAU,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,UAAU,MAAM,cAAc,cAAc,CAAC,GAAG,GAAG,WAAW,GAAG,GAAG,SAAS,EAAE,IAAI,SAAU,GAAG,GAAG;AACvJ,6BAAQ,QAAQ,QAAQ,cAAc,OAAO,EAAE,KAAK,GAAG,OAAO;AAAA,wBACtD,YAAY;AAAA,wBACZ,SAAS;AAAA,wBACT,cAAc;AAAA,wBACd,WAAW;AAAA,wBACX,QAAQ;AAAA,wBACR,OAAO;AAAA,wBACP,UAAU;AAAA,wBACV,MAAM,EAAE,IAAI,GAAG,WAAW;AAAA,wBAC1B,KAAK,EAAE,IAAI,GAAG,WAAW;AAAA,sBAC7B,EAAE,CAAC;AAAA,oBACX,CAAC,CAAC,IAAK;AAAA,kBAAI,IAAK,IAAI;AAAA,gBAC5B;AAGA,uBAAO,YAAY,YAAY;AAC/B,gBAAAF,SAAQ,UAAU;AAAA,cAGZ;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAASG,SAAQH,UAASH,sBAAqB;AAE/B,gBAAAM,UAASN,qBAAoB,IAAIM,OAAM;AAE9D,uBAAO,eAAeH,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,cAAc;AACtB,oBAAI,2BAA2B,SAAU,OAAO,QAAQ;AACpD,yBAAO;AAAA,oBACH,QAAQ,EAAE,GAAG,QAAQ,KAAK,GAAG,SAAS,IAAI;AAAA,oBAC1C,MAAM,EAAE,GAAG,GAAG,GAAG,SAAS,IAAI;AAAA,oBAC9B,OAAO,EAAE,GAAG,OAAO,GAAG,SAAS,IAAI;AAAA,oBACnC,KAAK,EAAE,GAAG,QAAQ,KAAK,GAAG,EAAE;AAAA,oBAC5B,QAAQ,EAAE,GAAG,QAAQ,KAAK,GAAG,OAAO;AAAA,kBACxC;AAAA,gBACJ;AACA,oBAAI,cAAc,SAAU,SAAS,WAAW;AAE5C,yBAAO,QAAQ,IAAI,SAAU,QAAQ;AACjC,wBAAI,cAAc,yBAAyB,UAAU,QAAQ,UAAU,GAAG,UAAU,SAAS,UAAU,CAAC;AACxG,wBAAI,KAAK,YAAY,OAAO,QAAQ,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG;AACxD,2BAAO;AAAA,sBACH,GAAG,UAAU,IAAI,IAAI,OAAO,OAAO;AAAA,sBACnC,GAAG,UAAU,IAAI,IAAI,OAAO,OAAO;AAAA,sBACnC;AAAA,oBACJ;AAAA,kBACJ,CAAC;AAAA,gBACL;AACA,gBAAAA,SAAQ,cAAc;AACtB,oBAAIH,qBAAoB,EAAEA,qBAAoB,CAAC,MAAMM,SAAQ;AAAA,gBAQ7D;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBH,UAASH,sBAAqB;AAGvE,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,oBAAI,eAAe,gBAAgBH;AAAA;AAAA,kBAAsC;AAAA,gBAAY,CAAC;AACtF,oBAAI,cAAcA;AAAA;AAAA,kBAAwC;AAAA,gBAAqB;AAC/E,oBAAI,sBAAsB,aAAa,QAAQ,MAAM,YAAY,WAAW;AAC5E,oBAAI,4BAA4B,aAAa,QAAQ,MAAM;AAAA,kBACvD,UAAU,oBAAoB;AAAA,kBAC9B,QAAQ,aAAa,QAAQ,MAAM;AAAA,oBAC/B,GAAG,aAAa,QAAQ;AAAA,oBACxB,GAAG,aAAa,QAAQ;AAAA,kBAC5B,CAAC,EAAE;AAAA,gBACP,CAAC;AACD,oBAAI,eAAe,aAAa,QAAQ,UAAU,CAAC,qBAAqB,yBAAyB,CAAC;AAClG,oBAAI,cAAc,aAAa,QAAQ,UAAU,CAAC,cAAc,aAAa,QAAQ,QAAQ,YAAY,CAAC,CAAC;AAC3G,oBAAI,WAAW,aAAa,QAAQ,UAAU,CAAC,aAAa,QAAQ,QAAQ,aAAa,QAAQ,MAAM,EAAE,SAAS,aAAa,QAAQ,IAAI,CAAC,CAAC,CAAC;AAC9I,oBAAI,cAAc,aAAa,QAAQ,UAAU,CAAC,aAAa,QAAQ,SAAS,aAAa,QAAQ,MAAM,CAAC;AAC5G,oBAAI,cAAc,aAAa,QAAQ,MAAM;AAAA,kBACzC,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,KAAK;AAAA,gBACT,CAAC;AACD,oBAAI,oBAAoB,aAAa,QAAQ,MAAM,OAAO,KAAK,YAAY,WAAW,CAAC;AAEvF,oBAAI,eAAe,aAAa,QAAQ;AACxC,oBAAI,eAAe,aAAa,QAAQ,UAAU;AAAA,kBAC9C;AAAA,kBACA,aAAa,QAAQ,MAAM;AAAA,oBACvB,SAAS;AAAA,oBACT,eAAe,aAAa,QAAQ;AAAA,kBACxC,CAAC,EAAE;AAAA,gBACP,CAAC;AACD,oBAAI,kBAAkB;AAAA,kBAClB,OAAO,SAAS;AAAA,kBAChB,KAAK,SAAS;AAAA,kBACd,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,QAAQ,aAAa,QAAQ,UAAU,CAAC,aAAa,WAAW,CAAC;AAAA,kBACjE,OAAO,aAAa,QAAQ;AAAA,kBAC5B,WAAW,aAAa,QAAQ;AAAA,kBAChC,UAAU,aAAa,QAAQ;AAAA,kBAC/B,WAAW,aAAa,QAAQ;AAAA,kBAChC,UAAU,aAAa,QAAQ;AAAA,kBAC/B,UAAU,aAAa,QAAQ;AAAA,kBAC/B,WAAW,aAAa,QAAQ;AAAA,kBAChC,aAAa,aAAa,QAAQ;AAAA,kBAClC,UAAU,aAAa,QAAQ;AAAA,kBAC/B,MAAM,aAAa,QAAQ,MAAM,YAAY,MAAM;AAAA,kBACnD,YAAY,aAAa,QAAQ;AAAA,kBACjC,WAAW,aAAa,QAAQ;AAAA,kBAChC,WAAW,aAAa,QAAQ;AAAA,kBAChC,UAAU,aAAa,QAAQ,UAAU,CAAC,aAAa,QAAQ,MAAM,aAAa,QAAQ,MAAM,CAAC;AAAA,kBACjG,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,gBAAgB,aAAa,QAAQ,UAAU,CAAC,aAAa,QAAQ,MAAM,aAAa,QAAQ,MAAM,CAAC;AAAA,kBACvG,QAAQ,aAAa,QAAQ;AAAA,kBAC7B,WAAW,aAAa,QAAQ;AAAA,kBAChC,gBAAgB,aAAa,QAAQ;AAAA,kBACrC,gBAAgB,aAAa,QAAQ;AAAA,kBACrC,gBAAgB,aAAa,QAAQ;AAAA,kBACrC,gBAAgB,aAAa,QAAQ;AAAA,kBACrC,mBAAmB,aAAa,QAAQ;AAAA,kBACxC,kBAAkB,aAAa,QAAQ;AAAA,kBACvC,QAAQ,aAAa,QAAQ;AAAA,kBAC7B,aAAa,aAAa,QAAQ;AAAA,kBAClC,aAAa,aAAa,QAAQ;AAAA,kBAClC,aAAa,aAAa,QAAQ;AAAA,kBAClC,aAAa,aAAa,QAAQ;AAAA,gBACtC;AACA,gBAAAG,SAAQ,UAAU;AAAA,cAGZ;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,oBAAI,WAAY,QAAQ,KAAK,YAAa,WAAY;AAClD,6BAAW,OAAO,UAAU,SAAS,GAAG;AACpC,6BAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,0BAAI,UAAU,CAAC;AACf,+BAAS,KAAK;AAAG,4BAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,4BAAE,CAAC,IAAI,EAAE,CAAC;AAAA,oBAClB;AACA,2BAAO;AAAA,kBACX;AACA,yBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,gBACzC;AACA,oBAAI,kBAAmB,QAAQ,KAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,sBAAI,OAAO;AAAW,yBAAK;AAC3B,yBAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,2BAAO,EAAE,CAAC;AAAA,kBAAG,EAAE,CAAC;AAAA,gBACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,sBAAI,OAAO;AAAW,yBAAK;AAC3B,oBAAE,EAAE,IAAI,EAAE,CAAC;AAAA,gBACf;AACA,oBAAI,qBAAsB,QAAQ,KAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,yBAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,gBACtE,IAAK,SAAS,GAAG,GAAG;AAChB,oBAAE,SAAS,IAAI;AAAA,gBACnB;AACA,oBAAI,eAAgB,QAAQ,KAAK,gBAAiB,SAAU,KAAK;AAC7D,sBAAI,OAAO,IAAI;AAAY,2BAAO;AAClC,sBAAI,SAAS,CAAC;AACd,sBAAI,OAAO;AAAM,6BAAS,KAAK;AAAK,0BAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC;AAAG,wCAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,qCAAmB,QAAQ,GAAG;AAC9B,yBAAO;AAAA,gBACX;AACA,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,oBAAI,UAAU,aAAaH;AAAA;AAAA,kBAAiC;AAAA,gBAAO,CAAC;AACpE,oBAAI,UAAUA;AAAA;AAAA,kBAAmC;AAAA,gBAA6B;AAC9E,oBAAI,WAAW,gBAAgBA;AAAA;AAAA,kBAAkC;AAAA,gBAAQ,CAAC;AAC1E,oBAAI,cAAcA;AAAA;AAAA,kBAAwC;AAAA,gBAAqB;AAC/E,oBAAI,cAAc,SAAU,OAAO;AAC/B,sBAAI,cAAc,EAAE,OAAO,MAAM,QAAQ,MAAM,KAAK,KAAK;AACzD,sBAAI,OAAO;AACP,wBAAI,OAAO,UAAU,YAAY,QAAQ,QAAQ,eAAe,KAAK;AACjE,kCAAY,SAAS;AAAA,yBACpB;AACD,+BAAS,OAAO,OAAO;AACnB,oCAAY,GAAG,IAAI,MAAM,GAAG;AAAA,sBAChC;AAAA,oBACJ;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,oBAAI,cAAc,SAAU,QAAQ;AAEhC,sBAAI,eAAe,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAE3D,sBAAI,gBAAgB,aAAa,IAAI,SAAUO,eAAc;AACzD,wBAAI,OAAOA,kBAAiB,UAAU;AAClC,6BAAO,EAAE,UAAUA,cAAa;AAAA,oBACpC;AAEI,6BAAOA;AAAA,kBACf,CAAC;AAED,kCAAgB,cAAc,OAAO,SAAU,IAAI;AAAE,2BAAO,YAAY,YAAY,SAAS,GAAG,QAAQ;AAAA,kBAAG,CAAC;AAC5G,sBAAI,cAAc,UAAU;AACxB,oCAAgB,CAAC,EAAE,UAAU,OAAO,CAAC;AAEzC,sBAAI,YAAY,cAAc,OAAO,SAAU,IAAI;AAAE,2BAAO,GAAG,aAAa;AAAA,kBAAQ,CAAC;AACrF,sBAAI,UAAU,SAAS,GAAG;AACtB,oCAAgB,cAAc,OAAO,SAAU,IAAI;AAAE,6BAAO,GAAG,aAAa;AAAA,oBAAQ,CAAC;AACrF,kCAAc,KAAK,MAAM,eAAe,UAAU,QAAQ,SAAU,WAAW;AAC3E,6BAAO,CAAC,QAAQ,SAAS,OAAO,QAAQ,EAAE,IAAI,SAAU,YAAY;AAChE,+BAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,UAAU,WAAW,CAAC;AAAA,sBACrE,CAAC;AAAA,oBACL,CAAC,CAAC;AAAA,kBACN;AAEA,sBAAI,gBAAgB,cAAc,IAAI,SAAUA,eAAc;AAC1D,wBAAI,OAAOA,kBAAiB,UAAU;AAClC,0BAAI,qBAAqBA;AACzB,0BAAI,CAAC,mBAAmB;AACpB,2CAAmB,WAAW;AAClC,0BAAI,CAAC,mBAAmB;AACpB,2CAAmB,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE;AAC7C,0BAAI,CAAC,mBAAmB,OAAO;AAC3B,2CAAmB,OAAO,IAAI;AAClC,0BAAI,CAAC,mBAAmB,OAAO;AAC3B,2CAAmB,OAAO,IAAI;AAClC,2CAAqB;AACrB,6BAAO;AAAA,oBACX;AAEI,6BAAOA;AAAA,kBACf,CAAC;AACD,yBAAO;AAAA,gBACX;AACA,oBAAI,gBAAgB,SAAU,UAAU,OAAO;AAC3C,sBAAI,aAAa,GAAG,WAAW,GAAG,eAAe,gBAAgB;AACjE,sBAAI,OAAO,aAAa,UAAU;AAC9B,iCAAa,SAAS,aAAa,MAAM,cAAc;AACvD,+BAAW,SAAS,YAAY,SAAS,eAAe,MAAM;AAC9D,oCAAgB,SAAS,YAAY,SAAS,YAAY;AAAA,kBAC9D,WACS,OAAO,aAAa,aAAa,UAAU;AAChD,iCAAa,MAAM,cAAc;AACjC,+BAAW,MAAM;AACjB,oCAAgB;AAAA,kBACpB;AACA,yBAAO,EAAE,WAAW,YAAY,cAAc,UAAU,WAAW,eAAe,cAA6B;AAAA,gBACnH;AACA,oBAAI,iBAAiB,SAAU,SAAS;AACpC,sBAAI,OAAO,WAAW,UAAU;AAC5B,wBAAI,WAAW,YAAY;AACvB,gCAAU,YAAY,YAAY,OAAO;AAAA,yBACxC;AACD,8BAAQ,KAAK,MAAM,UAAU,0EAA0E,YAAY,eAAe,2CAA2C;AAC7K,gCAAU,YAAY,YAAY,QAAQ;AAAA,oBAC9C;AAAA,kBACJ;AACA,4BAAU;AACV,uBAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,mBAAmB;AAC9E,4BAAQ,gBAAgB;AAC5B,uBAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa;AACxE,4BAAQ,UAAU;AAEtB,yBAAO;AAAA,gBACX;AACA,oBAAI,iBAAiB,SAAU,WAAW;AACtC,sBAAI,eAAe,QAAQ,iBAAiB,SAAS;AACrD,sBAAI,CAAC;AACD,mCAAe,EAAE,UAAU,KAAK,KAAK,EAAE;AAC3C,yBAAO;AAAA,gBACX;AAMA,oBAAI,aAAa,SAAU,SAAS,WAAW;AAC3C,sBAAI;AACA,8BAAU,UAAU;AACxB,yBAAO;AAAA,gBACX;AACA,oBAAI,UAAU,SAAU,UAAU;AAAE,yBAAO;AAAA,gBAAU;AACrD,oBAAI,uBAAuB,SAAU,UAAU,GAAG,WAAW;AAAE,yBAAO,WAAW,UAAU,SAAS;AAAA,gBAAG;AACvG,oBAAI,wBAAwB,SAAU,UAAU,GAAG,WAAW;AAAE,yBAAO,WAAW,OAAO,QAAQ,GAAG,SAAS;AAAA,gBAAG;AAChH,oBAAI,WAAW,SAAU,UAAU;AAAE,yBAAO,OAAO,QAAQ;AAAA,gBAAG;AAC9D,oBAAI,kBAAkB;AAAA,kBAClB,OAAO,SAAU,UAAU;AAAE,2BAAO,QAAQ,sBAAsB,QAAQ;AAAA,kBAAG;AAAA,kBAC7E,KAAK,SAAU,UAAU;AAAE,2BAAO,QAAQ,sBAAsB,QAAQ;AAAA,kBAAG;AAAA,kBAC3E,aAAa,SAAU,UAAU,GAAG,WAAW;AAAE,2BAAO,WAAW,YAAY,QAAQ,GAAG,SAAS;AAAA,kBAAG;AAAA,kBACtG,WAAW,SAAU,UAAU,GAAG,WAAW;AAAE,2BAAO,WAAW,YAAY,QAAQ,GAAG,SAAS;AAAA,kBAAG;AAAA,kBACpG,QAAQ,SAAU,UAAU;AAAE,2BAAO,YAAY,QAAQ;AAAA,kBAAG;AAAA,kBAC5D,OAAO;AAAA,kBACP,WAAW,SAAU,UAAU,WAAW;AAAE,2BAAO,YAAY,UAAU;AAAA,kBAAO;AAAA,kBAChF,WAAW,SAAU,UAAU,WAAW;AAAE,2BAAO,YAAY,UAAU;AAAA,kBAAO;AAAA,kBAChF,WAAW,SAAU,UAAU,WAAW;AAAE,2BAAO,YAAY,UAAU;AAAA,kBAAO;AAAA,kBAChF,aAAa;AAAA,kBACb,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX,WAAW,SAAU,UAAU,GAAG,WAAW;AAAE,2BAAO,WAAW,eAAe,QAAQ,GAAG,SAAS;AAAA,kBAAG;AAAA;AAAA,kBAEvG,UAAU,SAAU,UAAU,WAAW;AAAE,2BAAO,cAAc,UAAU,SAAS;AAAA,kBAAG;AAAA,kBACtF,WAAW,SAAU,UAAU;AAAE,2BAAO,eAAe,QAAQ;AAAA,kBAAG;AAAA,kBAClE,WAAW,SAAU,UAAU;AAAE,2BAAO,eAAe,QAAQ;AAAA,kBAAG;AAAA,kBAClE,YAAY;AAAA,kBACZ,gBAAgB;AAAA,kBAChB,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,gBAAgB;AAAA,kBAChB,gBAAgB;AAAA,kBAChB,gBAAgB;AAAA,kBAChB,gBAAgB;AAAA,kBAChB,mBAAmB;AAAA,kBACnB,mBAAmB;AAAA,kBACnB,gBAAgB;AAAA,kBAChB,kBAAkB;AAAA,kBAClB,QAAQ;AAAA,kBACR,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,aAAa;AAAA,gBACjB;AAEA,oBAAI,YAAY,CAAC;AAEjB,yBAAS,YAAY,iBAAiB;AAClC,4BAAU,QAAQ,IAAI,CAAC,QAAQ;AAAA,gBACnC;AAEA,yBAAS,KAAK,GAAG,KAAK,CAAC,aAAa,aAAa,WAAW,GAAG,KAAK,GAAG,QAAQ,MAAM;AACjF,sBAAI,WAAW,GAAG,EAAE;AACpB,4BAAU,QAAQ,EAAE,KAAK,OAAO;AAAA,gBACpC;AACA,oBAAI,kBAAkB,SAAU,OAAO,UAAU;AAC7C,sBAAIH;AACJ,2BAASI,MAAK,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAGA,MAAK,GAAG,QAAQA,OAAM;AAC/D,wBAAI,KAAK,GAAGA,GAAE,GAAG,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAC3C,6BAAS,MAAM,KAAKJ,MAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,MAAM,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,iBAAiB,KAAK,QAAQ;AAAA,kBACnM;AACA,yBAAO;AAAA,gBACX;AACA,oBAAI,eAAe;AAAA,kBACf,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,QAAQ;AAAA,kBACR,OAAO;AAAA,kBACP,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,aAAa;AAAA,kBACb,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX,WAAW;AAAA;AAAA,kBAEX,UAAU;AAAA,kBACV,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,gBAAgB;AAAA,kBAChB,QAAQ;AAAA,kBACR,WAAW,CAAC;AAAA,kBACZ,gBAAgB,CAAC;AAAA,kBACjB,gBAAgB,CAAC;AAAA,kBACjB,gBAAgB,CAAC;AAAA,kBACjB,gBAAgB,CAAC;AAAA,kBACjB,mBAAmB,CAAC;AAAA,kBACpB,mBAAmB,CAAC;AAAA,kBACpB,gBAAgB,CAAC;AAAA,kBACjB,kBAAkB;AAAA,kBAClB,QAAQ;AAAA,kBACR,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,aAAa;AAAA,gBACjB;AACA,oBAAI,qBAAqB,CAAC;AAC1B,qCAAqB,gBAAgB,cAAc,kBAAkB;AACrE,oBAAI,iBAAiB;AAAA,kBACjB,UAAU,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAAA,kBAC5C,QAAQ,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAAA,gBAC9C;AAEA,yBAAS,kBAAkB,GAAG,GAAG;AAC7B,yBAAO,SAAS,QAAQ,QAAQ,GAAG,CAAC;AAAA,gBACxC;AACA,yBAAS,sBAAsB,OAAO;AAClC,sBAAI,MAAM,QAAQ,OAAO;AAGzB,sBAAI,CAAC,kBAAkB,OAAO,IAAI,OAAO,GAAG;AACxC,wBAAI,UAAU;AAAA,kBAClB;AACA,yBAAO,IAAI;AAAA,gBACf;AACA,yBAAS,qBAAqB,UAAU,cAAc;AAClD,0BAAQ,gBAAgB,UAAU,aAAa,IAAI,qBAAqB,CAAC;AAAA,gBAC7E;AAIA,oBAAI,iBAAiB,SAAU,WAAW,MAAM;AAC5C,sBAAIA,MAAK,QAAQ,SAAS,kBAAkB,GAAG,YAAYA,IAAG,CAAC,GAAG,eAAeA,IAAG,CAAC;AACrF,sBAAI,uBAAuB,QAAQ,OAAO,KAAK;AAG/C,4BAAU,sBAAsB,IAAI;AACpC,sBAAI,WAAW,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG,SAAS;AAC7D,sBAAI,UAAU,SAAUK,WAAU;AAC9B,4BAAQ,gBAAgB,WAAY;AAChC,0BAAIL;AACJ,gCAAUK,SAAQ,KAAKL,MAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgBK,SAAQ,OAAO,QAAQL,QAAO,SAAS,SAASA,IAAG,KAAK,iBAAiB,SAASK,SAAQ,GAAG,WAAW,oBAAoB;AAE1O,mCAAa,SAAS,CAAC,GAAG,SAAS,CAAC;AAAA,oBACxC,GAAG,UAAUA,SAAQ,EAAE,IAAI,SAAU,MAAM;AAAE,6BAAO,UAAU,IAAI;AAAA,oBAAG,CAAC,CAAC;AAAA,kBAC3E;AAQA,2BAASA,aAAY,cAAc;AAC/B,4BAAQA,SAAQ;AAAA,kBACpB;AAEA,sBAAI,KAAK,QAAQ,SAAS,cAAc,GAAG,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC7E,sBAAI,WAAW,QAAQ,WAAW,UAAU,KAAK;AACjD,uCAAqB,WAAY;AAC7B,4BAAQ,WAAW;AACnB,yCAAqB,UAAU;AAC/B,+BAAW,SAAS,CAAC,GAAG,OAAO,CAAC;AAAA,kBAEpC,GAAG,CAAC,QAAQ,CAAC;AACb,sBAAI,SAAS,QAAQ,WAAW,UAAU,GAAG;AAC7C,uCAAqB,WAAY;AAC7B,4BAAQ,SAAS;AACjB,yCAAqB,UAAU;AAC/B,+BAAW,SAAS,CAAC,GAAG,OAAO,CAAC;AAAA,kBAEpC,GAAG,CAAC,MAAM,CAAC;AACX,0BAAQ,gBAAgB,WAAY;AAEhC,yCAAqB,UAAU;AAC/B,+BAAW,SAAS,CAAC,GAAG,OAAO,CAAC;AAAA,kBACpC,GAAG,CAAC,UAAU,UAAU,SAAS,UAAU,UAAU,OAAO,CAAC;AAC7D,yBAAO,CAAC,WAAW,OAAO;AAAA,gBAC9B;AACA,gBAAAN,SAAQ,UAAU;AAAA,cAGZ;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,cAAc;AACtB,oBAAI,YAAYH;AAAA;AAAA,kBAAsC;AAAA,gBAAyB;AAC/E,oBAAI,UAAUA;AAAA;AAAA,kBAAmC;AAAA,gBAA6B;AAC9E,oBAAI,WAAW,gBAAgBA;AAAA;AAAA,kBAAkC;AAAA,gBAAQ,CAAC;AAC1E,oBAAI,cAAcA;AAAA;AAAA,kBAA2C;AAAA,gBAAqB;AAClF,oBAAI,YAAYA;AAAA;AAAA,kBAAqC;AAAA,gBAA+B;AAKpF,oBAAI,cAAc,SAAU,QAAQ,SAAS;AACzC,sBAAI,IAAI;AACR,sBAAI,IAAI;AACR,sBAAI,YAAY,OAAO,CAAC,GAAG,UAAU,OAAO,CAAC;AAC7C,sBAAI,cAAc,UAAU,aAAa,YAAY,UAAU,WAAW,cAAc,UAAU,aAAa,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,OAAO,UAAU,MAAM,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,YAAY,UAAU,WAAW,mBAAmB,UAAU,kBAAkB,cAAc,UAAU,aAAa,cAAc,UAAU,aAAa,cAAc,UAAU,aAAa,cAAc,UAAU;AAC5jB,sBAAI,WAAW,QAAQ,UAAU,SAAS,QAAQ;AAClD,sBAAI,KAAK,QAAQ,SAAS,SAAS,GAAG,QAAQ,UAAU,GAAG;AAC3D,sBAAI,aAAa;AACjB,sBAAI,aAAa;AAEjB,sBAAI,cAAc,UAAU,YAAY,aAAa,QAAQ;AAC7D,sBAAI,YAAY,UAAU,YAAY,WAAW,MAAM;AAEvD,sBAAI,KAAK,QAAQ,gBAAgB,aAAa,SAAS,GAAG,cAAc,GAAG,aAAa,YAAY,GAAG;AACvG,sBAAI,sBAAsB,YAAY,OAAO,UAAU,oBAAoB,UAAU,OAAO;AAC5F,sBAAI,aAAa,SAAS,QAAQ,KAAK,aAAa,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,SAAS,QAAQ,KAAK,WAAW,CAAC,KAAK,GAAG,CAAC;AACvH,sBAAI,aAAa,QAAQ,UAAU,MAAM;AACzC,sBAAI,MAAM,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC,IAAI,WAAW;AAC1D,sBAAI,MAAM,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC,IAAI,WAAW;AAC1D,sBAAI,KAAK,SAAS,IAAI,WAAW;AACjC,sBAAI,KAAK,SAAS,IAAI,WAAW;AACjC,sBAAI,QAAQ,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAC9C,sBAAI,QAAQ,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAC9C,sBAAI,QAAQ,KAAK,IAAI,IAAI;AACzB,sBAAI,QAAQ,KAAK,IAAI,IAAI;AACzB,sBAAI,KAAK,CAAC,UAAU,eAAe,UAAU,aAAa,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAClG,sBAAI,YAAY,WAAW;AAC3B,sBAAI,YAAY,WAAW;AAE3B,sBAAI,cAAc;AAClB,sBAAI,cAAc;AAClB,sBAAI,cAAc;AAClB,sBAAI,cAAc;AAClB,sBAAI,cAAc,YAAY;AAC9B,sBAAI,cAAc,YAAY;AAC9B,sBAAI,KAAK,OAAO,SAAS;AAEzB,sBAAI,CAAC,YAAY,OAAO,SAAS,IAAI;AACjC,2BAAO;AACX,sBAAI,SAAS,YAAY;AACrB,yBAAK;AACL,2BAAO;AAAA,kBACX;AACA,sBAAI,aAAa,WAAW,WAAW,WAAW;AAClD,sBAAI,QAAQ,cAAe,cAAc,aAAc;AACvD,sBAAI,WAAW;AACf,sBAAI,UAAU;AACd,sBAAI,QAAQ;AACZ,sBAAI,UAAU;AACd,6BAAW,OAAO,gBAAgB;AAClC,8BAAY,OAAO,gBAAgB;AACnC,2BAAS,OAAO,gBAAgB;AAChC,6BAAW,OAAO,gBAAgB;AAGlC,sBAAI,KAAK,GAAG,KAAK,OAAO,KAAK,GAAG,KAAK;AACrC,sBAAI,KAAK;AACL,yBAAK,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AACxC,sBAAI,KAAK;AACL,yBAAK,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AAGxC,sBAAI,OAAO,GAAG;AAEV,wBAAI,YAAY,KAAK,KAAK,QAAQ,KAAK;AACvC,wBAAI,UAAU;AACV,4BAAM,aAAa,IAAI,cAAc,QAAQ,KAAK,IAAI,SAAS;AAC/D,4BAAM,aAAa,IAAI,cAAc,QAAQ,KAAK,IAAI,SAAS;AAC/D,mCAAa;AACb,0BAAI,QAAQ;AACR,qCAAa,KAAK,KAAK,YAAY,SAAS;AAChD,oCAAc,KAAK,IAAI,SAAS,IAAI,cAAe,KAAK,IAAI,SAAS,IAAI,YAAa;AACtF,oCAAe,KAAK,IAAI,SAAS,IAAI,YAAa,IAAI,KAAK,IAAI,SAAS,IAAI;AAC5E,mCAAc,YAAY,MAAO,KAAK;AAAA,oBAC1C;AACA,wBAAI,YAAY,KAAK,KAAK,QAAQ,KAAK;AACvC,wBAAI,UAAU;AACV,4BAAM,aAAa,IAAI,cAAc,QAAQ,KAAK,IAAI,SAAS;AAC/D,4BAAM,aAAa,IAAI,cAAc,QAAQ,KAAK,IAAI,SAAS;AAC/D,mCAAa,CAAC;AACd,0BAAI,QAAQ;AACR,qCAAa,KAAK,KAAK,YAAY,SAAS;AAChD,oCAAc,KAAK,IAAI,SAAS,IAAI,cAAe,KAAK,IAAI,SAAS,IAAI,YAAa;AACtF,oCAAe,KAAK,IAAI,SAAS,IAAI,YAAa,IAAI,KAAK,IAAI,SAAS,IAAI;AAC5E,mCAAc,YAAY,MAAO,KAAK;AAAA,oBAC1C;AAAA,kBACJ,OACK;AAED,wBAAI,sBAAsB,UAAU;AAEhC,0BAAI,QAAQ,OAAO;AACf,4CAAoB,QAAQ,SAAS;AAAA,sBACzC,OACK;AACD,4CAAoB,QAAQ,QAAQ;AAAA,sBACxC;AAAA,oBACJ;AACA,wBAAI,UAAU;AACV,0BAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,iBAAiB,GAAG;AAC/C,uCAAe,cAAc;AAC7B,8BAAM,aAAa,IAAI,cAAc;AACrC,uCAAgB,YAAY,QAAS;AACrC,4BAAI,sBAAsB,QAAQ;AAC9B,uCAAa;AACb,8BAAI,QAAQ;AACR,0CAAc;AAAA,wBACtB,OACK;AACD,uCAAa;AACb,8BAAI,QAAQ;AACR,0CAAc;AAAA,wBACtB;AAAA,sBACJ,WACS,CAAC,OAAO,QAAQ,EAAE,SAAS,iBAAiB,GAAG;AACpD,uCAAgB,YAAY,CAAC,QAAS;AACtC,uCAAe,cAAc;AAC7B,8BAAM,YAAY,QAAQ;AAC1B,4BAAI,sBAAsB,OAAO;AAC7B,uCAAa;AACb,8BAAI,QAAQ;AACR,0CAAc;AAAA,wBACtB,OACK;AACD,uCAAa;AACb,8BAAI,QAAQ;AACR,0CAAc;AAAA,wBACtB;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ;AACA,sBAAI,YAAY,OAAO,GAAG;AACtB,wBAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,mBAAmB,GAAG;AACjD,qCAAe,cAAc,CAAC;AAC9B,4BAAM,YAAY,QAAQ;AAC1B,qCAAe,EAAE,YAAY,SAAS;AACtC,0BAAI,wBAAwB,QAAQ;AAChC,qCAAa;AACb,4BAAI,QAAQ;AACR,wCAAc;AAAA,sBACtB,OACK;AACD,qCAAa;AACb,4BAAI,QAAQ;AACR,wCAAc;AAAA,sBACtB;AAAA,oBACJ,WACS,CAAC,OAAO,QAAQ,EAAE,SAAS,mBAAmB,GAAG;AACtD,qCAAe,cAAc,CAAC;AAC9B,4BAAM,YAAY,QAAQ;AAC1B,qCAAgB,YAAY,QAAS;AACrC,0BAAI,wBAAwB,OAAO;AAC/B,qCAAa;AACb,4BAAI,QAAQ;AACR,wCAAc;AAAA,sBACtB,OACK;AACD,qCAAa;AACb,4BAAI,QAAQ;AACR,wCAAc;AAAA,sBACtB;AAAA,oBACJ;AAAA,kBACJ;AACA,sBAAI,kBAAkB,EAAE,GAAG,aAAa,GAAG,YAAY;AACvD,sBAAI,kBAAkB,EAAE,GAAG,aAAa,GAAG,YAAY;AACvD,sBAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;AAC5C,sBAAI,sBAAsB,CAAC;AAC3B,sBAAI,SAAS;AACT,0CAAsB;AAAA,sBAClB,IAAI,WAAY;AAEZ,gCAAQ,QAAQ,KAAK;AACrB,gCAAQ,QAAQ,KAAK;AAAA,sBACzB;AAAA,sBACA,IAAI,WAAY;AAEZ,gCAAQ,QAAQ,KAAK;AACrB,gCAAQ,QAAQ,KAAK;AAAA,sBACzB;AAAA,sBACA,IAAI,WAAY;AAGZ,gCAAQ,QAAQ,KAAK;AACrB,gCAAQ,QAAQ,KAAK;AAAA,sBACzB;AAAA,sBACA,IAAI,WAAY;AAGZ,gCAAQ,QAAQ,KAAK;AACrB,gCAAQ,QAAQ,KAAK;AAAA,sBACzB;AAAA,oBACJ;AAAA,2BACK,SAAS,QAAQ;AACtB,0CAAsB;AAAA,sBAClB,IAAI,WAAY;AACZ,iCAAS,QAAQ,UAAU,WAAW,UAAU,OAAO;AACvD,iCAAS,SAAS,IAAI,UAAU,YAAY,UAAU,OAAO;AAC7D,4BAAI,UAAU;AACV,kCAAU,aAAa,IAAI,cAAe,IAAK;AAC/C,kCAAU,aAAa,IAAI,cAAe,IAAK;AAAA,wBACnD;AACA,4BAAI,UAAU;AACV,kCAAU,aAAa,IAAI,cAAe,IAAK;AAC/C,kCAAU,aAAa,IAAI,cAAe,IAAK;AAAA,wBACnD;AAAA,sBACJ;AAAA,sBACA,IAAI,WAAY;AACZ,iCAAS,QAAQ,UAAU,WAAW,UAAU,OAAO;AACvD,iCAAS,SAAS,IAAI,UAAU,YAAY,UAAU,OAAO;AAC7D,4BAAI,UAAU;AACV,kCAAU,aAAa,IAAI,cAAe,IAAK;AAC/C,kCAAU,aAAa,IAAI,cAAe,IAAK;AAAA,wBACnD;AACA,4BAAI,UAAU;AACV,kCAAU,aAAa,IAAI,cAAe,IAAK;AAC/C,kCAAU,aAAa,IAAI,cAAe,IAAK;AAAA,wBACnD;AAAA,sBACJ;AAAA,sBACA,IAAI,WAAY;AACZ,+BAAO;AAAA,sBACX;AAAA,sBACA,IAAI,WAAY;AACZ,+BAAO;AAAA,sBACX;AAAA,oBACJ;AAAA,kBACJ;AAEA,sBAAI,oBAAoB;AACxB,sBAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,mBAAmB;AAC9C,yCAAqB;AAAA,2BAChB,CAAC,UAAU,KAAK,EAAE,SAAS,mBAAmB;AACnD,yCAAqB;AAAA,2BAChB,wBAAwB;AAC7B,yCAAqB;AACzB,sBAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,iBAAiB;AAC5C,yCAAqB;AAAA,2BAChB,CAAC,UAAU,KAAK,EAAE,SAAS,iBAAiB;AACjD,yCAAqB;AAAA,2BAChB,sBAAsB;AAC3B,yCAAqB;AACzB,sBAAI,QAAQ;AACR,wCAAoB,kBAAkB,QAAQ,MAAM,GAAG;AAAA;AAEvD,wCAAoB,kBAAkB,QAAQ,MAAM,GAAG;AAC3D,sCAAoB,iBAAiB,EAAE;AACvC,0BAAQ;AACR,0BAAQ;AACR,0BAAQ;AACR,0BAAQ;AAGR,sBAAI,KAAK,UAAU,eAAe,IAAI,MAAM,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAClF,sBAAI,KAAK,UAAU,eAAe,IAAI,MAAM,MAAM,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAClF,sBAAI,QAAQ;AACR,+BAAW,CAAC;AAChB,sBAAI,QAAQ;AACR,gCAAY,QAAQ;AACxB,sBAAI,QAAQ;AACR,6BAAS,CAAC;AACd,sBAAI,QAAQ;AACR,+BAAW,QAAQ;AACvB,sBAAI,SAAS,QAAQ;AACjB,+BAAW;AACX,gCAAY;AACZ,6BAAS;AACT,+BAAW;AAAA,kBACf;AACA,wBAAM;AACN,wBAAM;AACN,wBAAM;AACN,wBAAM;AACN,0BAAQ;AACR,0BAAQ;AACR,0BAAQ;AACR,0BAAQ;AACR,sBAAI,KAAK,QAAQ,UAAU,UAAU,KAAK,QAAQ,QAAQ;AAC1D,yBAAO;AACP,yBAAO;AAEP,sBAAI,MAAM,UAAU,WAAW,IAAI,MAAM,MAAM,EAAE;AACjD,sBAAI,MAAM,UAAU,WAAW,IAAI,MAAM,MAAM,EAAE;AACjD,sBAAI,gBAAgB,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE;AACjD,sBAAI,iBAAiB,EAAE,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE;AAChD,sBAAI,cAAc,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE;AAC/C,sBAAI;AACJ,sBAAI,SAAS,QAAQ;AAIjB,gCAAY,OAAO,KAAK,MAAM,KAAK,SAAS,OAAO,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM;AAAA,kBACjH,WACS,SAAS;AACd,gCAAY,OAAO,KAAK,MAAM,KAAK,QAAQ,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,KAAK,MAAM;AAChH,yBAAO;AAAA,oBACH;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,YAAY;AAAA,oBACZ;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,aAAa,MAAM,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACpI;AAAA,oBACA;AAAA,oBACA;AAAA,kBACJ;AAAA,gBACJ;AACA,gBAAAG,SAAQ,cAAc;AAAA,cAGhB;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,YAAYA,SAAQ,aAAaA,SAAQ,kBAAkBA,SAAQ,mBAAmBA,SAAQ,iBAAiBA,SAAQ,wBAAwB;AACvJ,oBAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAI;AACJ,sBAAI,OAAO,QAAQ,UAAU;AAEzB,4BAAQ,SAAS,eAAe,GAAG;AAAA,kBACvC;AAEI,4BAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI;AAC1D,yBAAO;AAAA,gBACX;AACA,gBAAAA,SAAQ,wBAAwB;AAEhC,oBAAI,iBAAiB,SAAU,GAAG,QAAQ;AACtC,sBAAI,IAAI,EAAE,MAAM,iBAAiB;AACjC,sBAAI,EAAE,IAAI,SAAU,GAAG;AACnB,wBAAI,OAAO,CAAC;AACR,8BAAQ,OAAO,CAAC,IAAI,QAAQ,SAAS;AAAA;AAErC,6BAAO;AAAA,kBACf,CAAC;AACD,yBAAO,EAAE,KAAK,EAAE;AAAA,gBACpB;AACA,gBAAAA,SAAQ,iBAAiB;AAEzB,oBAAI,mBAAmB,SAAU,KAAK;AAClC,sBAAI,OAAO,QAAQ;AACf,2BAAO,EAAE,KAAK,GAAG,UAAU,IAAI;AACnC,sBAAI,KAAK,IAAI,MAAM,GAAG;AACtB,sBAAI,SAAS,GAAG,aAAa;AAC7B,sBAAI,GAAG,UAAU,GAAG;AAChB,wBAAI,IAAI,WAAW,GAAG,CAAC,CAAC;AACxB,wBAAI,CAAC,MAAM,CAAC,GAAG;AACX,+BAAS;AACT,6BAAO,EAAE,KAAK,QAAQ,UAAU,EAAE;AAAA,oBACtC;AAAA,kBACJ,WACS,GAAG,UAAU,GAAG;AACrB,wBAAI,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AACtE,wBAAI,CAAC,MAAM,EAAE;AACT,mCAAa,KAAK;AACtB,wBAAI,CAAC,MAAM,EAAE;AACT,+BAAS;AACb,wBAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;AACvB,6BAAO,EAAE,KAAK,QAAQ,UAAU,WAAW;AAAA,kBACnD;AAAA,gBACJ;AACA,gBAAAA,SAAQ,mBAAmB;AAC3B,oBAAI,OAAO,SAAU,IAAI,IAAI;AAEzB,yBAAO,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,GAAG,GAAI,CAAC,IAAI,KAAK,IAAK,GAAG,IAAI,GAAG,GAAI,CAAC,CAAC;AAAA,gBAC5E;AACA,oBAAI,kBAAkB,SAAU,SAAS,SAAS;AAE9C,sBAAI,UAAU,UAAU,IAAI;AAC5B,sBAAI;AACJ,0BAAQ,QAAQ,SAAU,IAAI;AAC1B,4BAAQ,QAAQ,SAAU,IAAI;AAC1B,0BAAI,KAAK,IAAI,EAAE;AACf,0BAAI,IAAI,SAAS;AACb,kCAAU;AACV,sCAAc,EAAE,aAAa,IAAI,WAAW,GAAG;AAAA,sBACnD;AAAA,oBACJ,CAAC;AAAA,kBACL,CAAC;AACD,yBAAO;AAAA,gBACX;AACA,gBAAAA,SAAQ,kBAAkB;AAC1B,oBAAI,aAAa,SAAU,MAAM;AAC7B,sBAAI,CAAC;AACD,2BAAO,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAC7C,sBAAI,MAAM,KAAK,sBAAsB;AACrC,yBAAO;AAAA,oBACH,GAAG,IAAI;AAAA,oBACP,GAAG,IAAI;AAAA,oBACP,OAAO,IAAI;AAAA,oBACX,QAAQ,IAAI;AAAA,kBAChB;AAAA,gBACJ;AACA,gBAAAA,SAAQ,aAAa;AACrB,oBAAI,YAAY,SAAU,QAAQ;AAC9B,sBAAI,CAAC,OAAO;AACR,2BAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACxB,sBAAI,KAAK,OAAO,QAAQ,sBAAsB,GAAG,cAAc,GAAG,MAAM,cAAc,GAAG;AACzF,sBAAI,cAAc,iBAAiB,OAAO,OAAO;AACjD,sBAAI,kBAAkB,OAAO,YAAY,KAAK,MAAM,GAAG,EAAE,CAAC;AAC1D,sBAAI,iBAAiB,OAAO,YAAY,IAAI,MAAM,GAAG,EAAE,CAAC;AACxD,yBAAO;AAAA,oBACH,GAAG,cAAc;AAAA,oBACjB,GAAG,cAAc;AAAA,kBACrB;AAAA,gBACJ;AACA,gBAAAA,SAAQ,YAAY;AAAA,cAGd;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,oBAAI,kBAAmB,QAAQ,KAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,sBAAI,OAAO;AAAW,yBAAK;AAC3B,yBAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,2BAAO,EAAE,CAAC;AAAA,kBAAG,EAAE,CAAC;AAAA,gBACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,sBAAI,OAAO;AAAW,yBAAK;AAC3B,oBAAE,EAAE,IAAI,EAAE,CAAC;AAAA,gBACf;AACA,oBAAI,qBAAsB,QAAQ,KAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,yBAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,gBACtE,IAAK,SAAS,GAAG,GAAG;AAChB,oBAAE,SAAS,IAAI;AAAA,gBACnB;AACA,oBAAI,eAAgB,QAAQ,KAAK,gBAAiB,SAAU,KAAK;AAC7D,sBAAI,OAAO,IAAI;AAAY,2BAAO;AAClC,sBAAI,SAAS,CAAC;AACd,sBAAI,OAAO;AAAM,6BAAS,KAAK;AAAK,0BAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC;AAAG,wCAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,qCAAmB,QAAQ,GAAG;AAC9B,yBAAO;AAAA,gBACX;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,gBAAgBA,SAAQ,eAAe;AAC/C,oBAAI,UAAU,aAAaH;AAAA;AAAA,kBAAiC;AAAA,gBAAO,CAAC;AACpE,gBAAAG,SAAQ,eAAe,QAAQ,QAAQ,cAAc,IAAI;AACzD,gBAAAA,SAAQ,gBAAgB,QAAQ,QAAQ,cAAc,IAAI;AAC1D,oBAAI,YAAY,CAAC;AACjB,oBAAI,iBAAiB;AACrB,oBAAI,MAAM,QAAQ;AAClB,oBAAI,iBAAiB,SAAU,IAAI;AAC/B,sBAAI,WAAW,GAAG,UAAU,gBAAgB,GAAG;AAC/C,sBAAI,KAAK,QAAQ,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C,sBAAI,eAAe,WAAY;AAAE,2BAAO,UAAU,CAAC,CAAC;AAAA,kBAAG;AACvD,0BAAQ,UAAU,WAAY;AAC1B,kCAAc,UAAU;AACxB,8BAAU,cAAc,OAAO,IAAI;AAAA,kBACvC,GAAG,CAAC,CAAC;AAEL,yBAAO,QAAQ,QAAQ,cAAcA,SAAQ,cAAc,UAAU,EAAE,OAAO,aAAa,GAAG,QAAQ;AAAA,gBAC1G;AACA,oBAAI,gBAAgB,SAAU,IAAI;AAC9B,sBAAI,WAAW,GAAG,UAAU,gBAAgB,GAAG;AAC/C,yBAAO,QAAQ,QAAQ,cAAcA,SAAQ,aAAa,UAAU,EAAE,OAAO,UAAU,cAAc,OAAO,EAAE,GAAG,QAAQ;AAAA,gBAC7H;AACA,oBAAI,WAAW,SAAU,IAAI;AACzB,sBAAI,WAAW,GAAG;AAClB,sBAAI,gBAAgB,QAAQ,OAAO,cAAc;AACjD,sBAAI,KAAK,QAAQ,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C,0BAAQ,UAAU,WAAY;AAC1B;AACA,8BAAU,CAAC,CAAC;AACZ,2BAAO,WAAY;AACf,6BAAO,UAAU,cAAc,OAAO;AAAA,oBAC1C;AAAA,kBACJ,GAAG,CAAC,CAAC;AACL,yBAAQ,QAAQ,QAAQ;AAAA,oBAAc;AAAA,oBAAe,EAAE,cAA6B;AAAA,oBAChF,QAAQ,QAAQ,cAAc,gBAAgB,EAAE,cAA6B,GAAG,QAAQ;AAAA,kBAAC;AAAA,gBACjG;AACA,gBAAAA,SAAQ,UAAU;AAAA,cAGZ;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,eAAeA,SAAQ,cAAcA,SAAQ,YAAYA,SAAQ,SAASA,SAAQ,cAAc;AAExG,oBAAI,UAAU,gBAAgBH;AAAA;AAAA,kBAAiC;AAAA,gBAAO,CAAC;AACvE,gBAAAG,SAAQ,cAAc,CAAC,UAAU,QAAQ,SAAS,OAAO,UAAU,MAAM;AACzE,gBAAAA,SAAQ,SAAS,CAAC,UAAU,QAAQ,UAAU;AAC9C,gBAAAA,SAAQ,YAAY,CAAC,UAAU,WAAW,QAAQ,QAAQ,WAAW,YAAY,MAAM;AAEvF,gBAAAA,SAAQ,cAAc;AAAA,kBAClB,QAAQ,EAAE,SAAS,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,mCAAmC,CAAC,GAAG,eAAe,KAAK;AAAA,kBACzH,OAAO;AAAA,oBACH,SAAU,QAAQ,QAAQ,cAAc,QAAQ,EAAE,GAAG,sGAAsG,CAAC;AAAA,oBAC5J,eAAe;AAAA,kBACnB;AAAA,kBACA,QAAQ;AAAA,oBACJ,SAAS,QAAQ,QAAQ,cAAc,UAAU,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,oBAC7E,eAAe;AAAA,kBACnB;AAAA,gBACJ;AACA,gBAAAA,SAAQ,eAAe,OAAO,KAAKA,SAAQ,WAAW;AAAA,cAGhD;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,oBAAI,kBAAmB,QAAQ,KAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,sBAAI,OAAO;AAAW,yBAAK;AAC3B,yBAAO,eAAe,GAAG,IAAI,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,2BAAO,EAAE,CAAC;AAAA,kBAAG,EAAE,CAAC;AAAA,gBACvF,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,sBAAI,OAAO;AAAW,yBAAK;AAC3B,oBAAE,EAAE,IAAI,EAAE,CAAC;AAAA,gBACf;AACA,oBAAI,eAAgB,QAAQ,KAAK,gBAAiB,SAAS,GAAGG,UAAS;AACnE,2BAAS,KAAK;AAAG,wBAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC;AAAG,sCAAgBA,UAAS,GAAG,CAAC;AAAA,gBAC5H;AACA,oBAAI,kBAAmB,QAAQ,KAAK,mBAAoB,SAAU,KAAK;AACnE,yBAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,gBAC5D;AACA,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,YAAYA,SAAQ,WAAWA,SAAQ,UAAU;AACzD,oBAAI,WAAWH;AAAA;AAAA,kBAA2C;AAAA,gBAAyB;AACnF,uBAAO,eAAeG,UAAS,WAAY,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,yBAAO,gBAAgB,QAAQ,EAAE;AAAA,gBAAS,EAAE,CAAE;AAChI,6BAAaH;AAAA;AAAA,kBAAmC;AAAA,gBAAgB,GAAGG,QAAO;AAC1E,6BAAaH;AAAA;AAAA,kBAAuC;AAAA,gBAAqB,GAAGG,QAAO;AACnF,oBAAI,aAAaH;AAAA;AAAA,kBAAsC;AAAA,gBAAoB;AAC3E,uBAAO,eAAeG,UAAS,YAAa,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,yBAAO,gBAAgB,UAAU,EAAE;AAAA,gBAAS,EAAE,CAAE;AACnI,oBAAI,cAAcH;AAAA;AAAA,kBAAuC;AAAA,gBAAqB;AAC9E,uBAAO,eAAeG,UAAS,aAAc,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,yBAAO,gBAAgB,WAAW,EAAE;AAAA,gBAAS,EAAE,CAAE;AAAA,cAG/H;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAAA,cAGxD;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAAS,yBAAyBA,UAASH,sBAAqB;AAGvE,uBAAO,eAAeG,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,oBAAI,UAAUH;AAAA;AAAA,kBAAiC;AAAA,gBAAO;AACtD,oBAAI,aAAaA;AAAA;AAAA,kBAAsC;AAAA,gBAAoB;AAC3E,oBAAI,YAAY,WAAY;AACxB,sBAAI,KAAK,QAAQ,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C,sBAAI,WAAW,WAAY;AAAE,2BAAO,UAAU,CAAC,CAAC;AAAA,kBAAG;AACnD,sBAAI,eAAe,QAAQ,WAAW,WAAW,YAAY;AAC7D,sBAAI,CAAC;AACD,mCAAe,WAAY;AAAA,oBAAE;AAIjC,0BAAQ,gBAAgB,WAAY;AAChC,iCAAa;AAAA,kBACjB,CAAC;AACD,yBAAO;AAAA,gBACX;AACA,gBAAAG,SAAQ,UAAU;AAAA,cAGZ;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAASG,SAAQ;AAExB,gBAAAA,QAAO,UAAU;AAAA,cAEX;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAASA,SAAQ;AAExB,gBAAAA,QAAO,UAAU;AAAA,cAEX;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA;AAAA;AAAA,cAIC,SAASA,SAAQ;AAExB,gBAAAA,QAAO,UAAU;AAAA,cAEX;AAAA;AAAA;AAAA,UAEI;AAGA,cAAI,2BAA2B,CAAC;AAGhC,mBAAS,oBAAoB,UAAU;AAEtC,gBAAI,eAAe,yBAAyB,QAAQ;AACpD,gBAAI,iBAAiB,QAAW;AAC/B,qBAAO,aAAa;AAAA,YACrB;AAEA,gBAAIA,UAAS,yBAAyB,QAAQ,IAAI;AAAA;AAAA,cACjD,IAAI;AAAA;AAAA,cACJ,QAAQ;AAAA;AAAA,cACR,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,gCAAoB,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAG9F,YAAAA,QAAO,SAAS;AAGhB,mBAAOA,QAAO;AAAA,UACf;AAGA,8BAAoB,IAAI;AAIxB,WAAC,WAAW;AAEX,gCAAoB,IAAI,SAASH,UAAS,YAAY;AACrD,uBAAQ,OAAO,YAAY;AAC1B,oBAAG,oBAAoB,EAAE,YAAY,GAAG,KAAK,CAAC,oBAAoB,EAAEA,UAAS,GAAG,GAAG;AAClF,yBAAO,eAAeA,UAAS,KAAK,EAAE,YAAY,MAAM,KAAK,WAAW,GAAG,EAAE,CAAC;AAAA,gBAC/E;AAAA,cACD;AAAA,YACD;AAAA,UACD,EAAE;AAGF,WAAC,WAAW;AACX,gCAAoB,IAAI,SAAS,KAAK,MAAM;AAAE,qBAAO,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI;AAAA,YAAG;AAAA,UACvG,EAAE;AAGF,WAAC,WAAW;AAEX,gCAAoB,IAAI,SAASA,UAAS;AACzC,kBAAG,OAAO,WAAW,eAAe,OAAO,aAAa;AACvD,uBAAO,eAAeA,UAAS,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC;AAAA,cACvE;AACA,qBAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,YAC7D;AAAA,UACD,EAAE;AAGF,WAAC,WAAW;AACX,gCAAoB,MAAM,SAASG,SAAQ;AAC1C,cAAAA,QAAO,QAAQ,CAAC;AAChB,kBAAI,CAACA,QAAO;AAAU,gBAAAA,QAAO,WAAW,CAAC;AACzC,qBAAOA;AAAA,YACR;AAAA,UACD,EAAE;AAOF,cAAI,sBAAsB,oBAAoB,oBAAoB,IAAI,iBAAiB;AAEvF,iBAAO;AAAA,QACR,EAAG;AAAA;AAAA,IAEZ,CAAC;AAAA;AAAA;", "names": ["__webpack_exports__", "__webpack_require__", "bzFunction", "buzzierMinSols", "exports", "_a", "_b", "module", "anchorChoice", "_i", "propName"]}