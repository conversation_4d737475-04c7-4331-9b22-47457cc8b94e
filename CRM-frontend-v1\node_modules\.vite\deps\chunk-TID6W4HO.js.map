{"version": 3, "sources": ["../../@mui/utils/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  var _muiName, _element$type;\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf( // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  (_muiName = element.type.muiName) != null ? _muiName : (_element$type = element.type) == null || (_element$type = _element$type._payload) == null || (_element$type = _element$type.value) == null ? void 0 : _element$type.muiName) !== -1;\n}"], "mappings": ";;;;;;;;AAAA,YAAuB;AACR,SAAR,aAA8B,SAAS,UAAU;AACtD,MAAI,UAAU;AACd,SAA0B,qBAAe,OAAO,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA,KAG7D,WAAW,QAAQ,KAAK,YAAY,OAAO,YAAY,gBAAgB,QAAQ,SAAS,SAAS,gBAAgB,cAAc,aAAa,SAAS,gBAAgB,cAAc,UAAU,OAAO,SAAS,cAAc;AAAA,EAAO,MAAM;AAC3O;", "names": []}