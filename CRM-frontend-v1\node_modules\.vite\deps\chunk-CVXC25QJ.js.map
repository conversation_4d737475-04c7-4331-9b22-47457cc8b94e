{"version": 3, "sources": ["../../@lezer/css/dist/index.js", "../../@codemirror/lang-css/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, LRParser, LocalTokenGroup } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst descendantOp = 100,\n  Unit = 1,\n  callee = 101,\n  identifier = 102,\n  VariableName = 2;\n\n/* Hand-written tokenizers for CSS tokens that can't be\n   expressed by <PERSON><PERSON>'s built-in tokenizer. */\n\nconst space = [9, 10, 11, 12, 13, 32, 133, 160, 5760, 8192, 8193, 8194, 8195, 8196, 8197,\n               8198, 8199, 8200, 8201, 8202, 8232, 8233, 8239, 8287, 12288];\nconst colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46,\n      hash = 35, percent = 37, ampersand = 38, backslash = 92, newline = 10;\n\nfunction isAlpha(ch) { return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161 }\n\nfunction isDigit(ch) { return ch >= 48 && ch <= 57 }\n\nconst identifiers = new ExternalTokenizer((input, stack) => {\n  for (let inside = false, dashes = 0, i = 0;; i++) {\n    let {next} = input;\n    if (isAlpha(next) || next == dash || next == underscore || (inside && isDigit(next))) {\n      if (!inside && (next != dash || i > 0)) inside = true;\n      if (dashes === i && next == dash) dashes++;\n      input.advance();\n    } else if (next == backslash && input.peek(1) != newline) {\n      input.advance();\n      if (input.next > -1) input.advance();\n      inside = true;\n    } else {\n      if (inside)\n        input.acceptToken(next == parenL ? callee : dashes == 2 && stack.canShift(VariableName) ? VariableName : identifier);\n      break\n    }\n  }\n});\n\nconst descendant = new ExternalTokenizer(input => {\n  if (space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (isAlpha(next) || next == underscore || next == hash || next == period ||\n        next == bracketL || next == colon && isAlpha(input.peek(1)) ||\n        next == dash || next == ampersand)\n      input.acceptToken(descendantOp);\n  }\n});\n\nconst unitToken = new ExternalTokenizer(input => {\n  if (!space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (next == percent) { input.advance(); input.acceptToken(Unit); }\n    if (isAlpha(next)) {\n      do { input.advance(); } while (isAlpha(input.next) || isDigit(input.next))\n      input.acceptToken(Unit);\n    }\n  }\n});\n\nconst cssHighlighting = styleTags({\n  \"AtKeyword import charset namespace keyframes media supports\": tags.definitionKeyword,\n  \"from to selector\": tags.keyword,\n  NamespaceName: tags.namespace,\n  KeyframeName: tags.labelName,\n  KeyframeRangeName: tags.operatorKeyword,\n  TagName: tags.tagName,\n  ClassName: tags.className,\n  PseudoClassName: tags.constant(tags.className),\n  IdName: tags.labelName,\n  \"FeatureName PropertyName\": tags.propertyName,\n  AttributeName: tags.attributeName,\n  NumberLiteral: tags.number,\n  KeywordQuery: tags.keyword,\n  UnaryQueryOp: tags.operatorKeyword,\n  \"CallTag ValueName\": tags.atom,\n  VariableName: tags.variableName,\n  Callee: tags.operatorKeyword,\n  Unit: tags.unit,\n  \"UniversalSelector NestingSelector\": tags.definitionOperator,\n  MatchOp: tags.compareOperator,\n  \"ChildOp SiblingOp, LogicOp\": tags.logicOperator,\n  BinOp: tags.arithmeticOperator,\n  Important: tags.modifier,\n  Comment: tags.blockComment,\n  ColorLiteral: tags.color,\n  \"ParenthesizedContent StringLiteral\": tags.string,\n  \":\": tags.punctuation,\n  \"PseudoOp #\": tags.derefOperator,\n  \"; ,\": tags.separator,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_callee = {__proto__:null,lang:34, \"nth-child\":34, \"nth-last-child\":34, \"nth-of-type\":34, \"nth-last-of-type\":34, dir:34, \"host-context\":34, url:62, \"url-prefix\":62, domain:62, regexp:62, selector:140};\nconst spec_AtKeyword = {__proto__:null,\"@import\":120, \"@media\":144, \"@charset\":148, \"@namespace\":152, \"@keyframes\":158, \"@supports\":170};\nconst spec_identifier = {__proto__:null,not:134, only:134};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \":jQYQ[OOO#_Q[OOP#fOWOOOOQP'#Cd'#CdOOQP'#Cc'#CcO#kQ[O'#CfO$_QXO'#CaO$fQ[O'#CiO$qQ[O'#DUO$vQ[O'#DXOOQP'#En'#EnO${QdO'#DhO%jQ[O'#DuO${QdO'#DwO%{Q[O'#DyO&WQ[O'#D|O&`Q[O'#ESO&nQ[O'#EUOOQS'#Em'#EmOOQS'#EX'#EXQYQ[OOO&uQXO'#CdO'jQWO'#DdO'oQWO'#EsO'zQ[O'#EsQOQWOOP(UO#tO'#C_POOO)C@])C@]OOQP'#Ch'#ChOOQP,59Q,59QO#kQ[O,59QO(aQ[O'#E]O({QWO,58{O)TQ[O,59TO$qQ[O,59pO$vQ[O,59sO(aQ[O,59vO(aQ[O,59xO(aQ[O,59yO)`Q[O'#DcOOQS,58{,58{OOQP'#Cl'#ClOOQO'#DS'#DSOOQP,59T,59TO)gQWO,59TO)lQWO,59TOOQP'#DW'#DWOOQP,59p,59pOOQO'#DY'#DYO)qQ`O,59sOOQS'#Cq'#CqO${QdO'#CrO)yQvO'#CtO+ZQtO,5:SOOQO'#Cy'#CyO)lQWO'#CxO+oQWO'#CzO+tQ[O'#DPOOQS'#Ep'#EpOOQO'#Dk'#DkO+|Q[O'#DrO,[QWO'#EtO&`Q[O'#DpO,jQWO'#DsOOQO'#Eu'#EuO)OQWO,5:aO,oQpO,5:cOOQS'#D{'#D{O,wQWO,5:eO,|Q[O,5:eOOQO'#EO'#EOO-UQWO,5:hO-ZQWO,5:nO-cQWO,5:pOOQS-E8V-E8VO-kQdO,5:OO-{Q[O'#E_O.YQWO,5;_O.YQWO,5;_POOO'#EW'#EWP.eO#tO,58yPOOO,58y,58yOOQP1G.l1G.lO/[QXO,5:wOOQO-E8Z-E8ZOOQS1G.g1G.gOOQP1G.o1G.oO)gQWO1G.oO)lQWO1G.oOOQP1G/[1G/[O/iQ`O1G/_O0SQXO1G/bO0jQXO1G/dO1QQXO1G/eO1hQWO,59}O1mQ[O'#DTO1tQdO'#CpOOQP1G/_1G/_O${QdO1G/_O1{QpO,59^OOQS,59`,59`O${QdO,59bO2TQWO1G/nOOQS,59d,59dO2YQ!bO,59fOOQS'#DQ'#DQOOQS'#EZ'#EZO2eQ[O,59kOOQS,59k,59kO2mQWO'#DkO2xQWO,5:WO2}QWO,5:^O&`Q[O,5:YO&`Q[O'#E`O3VQWO,5;`O3bQWO,5:[O(aQ[O,5:_OOQS1G/{1G/{OOQS1G/}1G/}OOQS1G0P1G0PO3sQWO1G0PO3xQdO'#EPOOQS1G0S1G0SOOQS1G0Y1G0YOOQS1G0[1G0[O4TQtO1G/jOOQO1G/j1G/jOOQO,5:y,5:yO4kQ[O,5:yOOQO-E8]-E8]O4xQWO1G0yPOOO-E8U-E8UPOOO1G.e1G.eOOQP7+$Z7+$ZOOQP7+$y7+$yO${QdO7+$yOOQS1G/i1G/iO5TQXO'#ErO5[QWO,59oO5aQtO'#EYO6XQdO'#EoO6cQWO,59[O6hQpO7+$yOOQS1G.x1G.xOOQS1G.|1G.|OOQS7+%Y7+%YOOQS1G/Q1G/QO6pQWO1G/QOOQS-E8X-E8XOOQS1G/V1G/VO${QdO1G/rOOQO1G/x1G/xOOQO1G/t1G/tO6uQWO,5:zOOQO-E8^-E8^O7TQXO1G/yOOQS7+%k7+%kO7[QYO'#CtOOQO'#ER'#ERO7gQ`O'#EQOOQO'#EQ'#EQO7rQWO'#EaO7zQdO,5:kOOQS,5:k,5:kO8VQtO'#E^O${QdO'#E^O9WQdO7+%UOOQO7+%U7+%UOOQO1G0e1G0eO9kQpO<<HeO9sQWO,5;^OOQP1G/Z1G/ZOOQS-E8W-E8WO${QdO'#E[O9{QWO,5;ZOOQT1G.v1G.vOOQP<<He<<HeOOQS7+$l7+$lO:TQdO7+%^OOQO7+%e7+%eOOQO,5:l,5:lO3{QdO'#EbO7rQWO,5:{OOQS,5:{,5:{OOQS-E8_-E8_OOQS1G0V1G0VO:[QtO,5:xOOQS-E8[-E8[OOQO<<Hp<<HpOOQPAN>PAN>PO;]QdO,5:vOOQO-E8Y-E8YOOQO<<Hx<<HxOOQO,5:|,5:|OOQO-E8`-E8`OOQS1G0g1G0g\",\n  stateData: \";o~O#[OS#]QQ~OUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#YRO~OQfOUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#YeO~O#V#gP~P!ZO#]jO~O#YlO~OZnO^qO_qOrsOuoOyrO!PtO!SvO#WuO~O!UwO~P#pOa}O#XzO#YyO~O#Y!OO~O#Y!QO~OQ![Oc!TOg![Oi![Oo!YOr!ZO#X!WO#Y!SO#e!UO~Oc!^O!e!`O!h!aO#Y!]O!U#hP~Oi!fOo!YO#Y!eO~Oi!hO#Y!hO~Oc!^O!e!`O!h!aO#Y!]O~O!Z#hP~P%jOZWX^WX^!XX_WXrWXuWXyWX!PWX!SWX!UWX#WWX~O^!mO~O!Z!nO#V#gX!T#gX~O#V#gX!T#gX~P!ZO#^!qO#_!qO#`!sO~OUYOXYOZTO^VO_VOrXOyWO#YRO~OuoO!UwO~Oa!zO#XzO#YyO~O!T#gP~P!ZOc#RO~Oc#SO~Oq#TO}#UO~OP#WOchXkhX!ZhX!ehX!hhX#YhXbhXQhXghXihXohXrhXuhX!YhX#VhX#XhX#ehXqhX!ThX~Oc!^Ok#XO!e!`O!h!aO#Y!]O!Z#hP~Oc#[O~Oq#`O#Y#]O~Oc!^O!e!`O!h!aO#Y#aO~Ou#eO!c#dO!U#hX!Z#hX~Oc#hO~Ok#XO!Z#jO~O!Z#kO~Oi#lOo!YO~O!U#mO~O!UwO!c#dO~O!UwO!Z#pO~O!Y#rO!Z!Wa#V!Wa!T!Wa~P${O!Z#RX#V#RX!T#RX~P!ZO!Z!nO#V#ga!T#ga~O#^!qO#_!qO#`#xO~OZnO^qO_qOrsOyrO!PtO!SvO#WuO~Ou#Pa!U#Pab#Pa~P.pOq#zO}#{O~OZnO^qO_qOrsOyrO~Ou!Oi!P!Oi!S!Oi!U!Oi#W!Oib!Oi~P/qOu!Qi!P!Qi!S!Qi!U!Qi#W!Qib!Qi~P/qOu!Ri!P!Ri!S!Ri!U!Ri#W!Rib!Ri~P/qO!T#|O~Ob#fP~P(aOb#cP~P${Ob$TOk#XO~O!Z$VO~Ob$WOi$XOp$XO~Oq$ZO#Y#]O~O^!aXb!_X!c!_X~O^$[O~Ob$]O!c#dO~Ou#eO!U#ha!Z#ha~O!c#dOu!da!U!da!Z!dab!da~O!Z$bO~O!T$iO#Y$dO#e$cO~Ok#XOu$kO!Y$mO!Z!Wi#V!Wi!T!Wi~P${O!Z#Ra#V#Ra!T#Ra~P!ZO!Z!nO#V#gi!T#gi~Ob#fX~P#pOb$qO~Ok#XOQ!|Xb!|Xc!|Xg!|Xi!|Xo!|Xr!|Xu!|X#X!|X#Y!|X#e!|X~Ou$sOb#cX~P${Ob$uO~Ok#XOq$vO~Ob$wO~O!c#dOu#Sa!U#Sa!Z#Sa~Ob$yO~P.pOP#WOuhX!UhX~O#e$cOu!tX!U!tX~Ou${O!UwO~O!T%PO#Y$dO#e$cO~Ok#XOQ#QXc#QXg#QXi#QXo#QXr#QXu#QX!Y#QX!Z#QX#V#QX#X#QX#Y#QX#e#QX!T#QX~Ou$kO!Y%SO!Z!Wq#V!Wq!T!Wq~P${Ok#XOq%TO~OuoOb#fa~Ou$sOb#ca~Ob%WO~P${Ok#XOQ#Qac#Qag#Qai#Qao#Qar#Qau#Qa!Y#Qa!Z#Qa#V#Qa#X#Qa#Y#Qa#e#Qa!T#Qa~Ob#Oau#Oa~P${O#[p#]#ek!S#e~\",\n  goto: \"-g#jPPP#kP#nP#w$WP#wP$g#wPP$mPPP$s$|$|P%`P$|P$|%z&^PPPP$|&vP&z'Q#wP'W#w'^P#wP#w#wPPP'd'y(WPP#nPP(_(_(i(_P(_P(_(_P#nP#nP#nP(l#nP(o(r(u(|#nP#nP)R)X)h)v)|*S*^*d*n*t*zPPPPPPPPPP+Q+Z+v+yP,o,r,x-RRkQ_bOPdhw!n#tkYOPdhotuvw!n#R#h#tkSOPdhotuvw!n#R#h#tQmTR!tnQ{VR!xqQ!x}Q#Z!XR#y!zq![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UU$f#m$h${R$z$eq!XZ]!T!m#S#U#X#q#{$Q$[$k$l$s$x%Up![Z]!T!m#S#U#X#q#{$Q$[$k$l$s$x%UQ!f^R#l!gT#^!Z#_Q|VR!yqQ!x|R#y!yQ!PWR!{rQ!RXR!|sQxUQ!wpQ#i!cQ#o!jQ#p!kQ$}$gR%Z$|SgPwQ!phQ#s!nR$n#tZfPhw!n#ta!b[`a!V!^!`#d#eR#b!^R!g^R!i_R#n!iS$g#m$hR%X${V$e#m$h${Q!rjR#w!rQdOShPwU!ldh#tR#t!nQ$Q#SU$r$Q$x%UQ$x$[R%U$sQ#_!ZR$Y#_Q$t$QR%V$tQpUS!vp$pR$p#}Q$l#qR%R$lQ!ogS#u!o#vR#v!pQ#f!_R$`#fQ$h#mR%O$hQ$|$gR%Y$|_cOPdhw!n#t^UOPdhw!n#tQ!uoQ!}tQ#OuQ#PvQ#}#RR$a#hR$R#SQ!VZQ!d]Q#V!TQ#q!m[$P#S$Q$[$s$x%UQ$S#UQ$U#XS$j#q$lQ$o#{R%Q$kR$O#RQiPR#QwQ!c[Q!kaR#Y!VU!_[a!VQ!j`Q#c!^Q#g!`Q$^#dR$_#e\",\n  nodeNames: \"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName , PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports AtRule Styles\",\n  maxTerm: 117,\n  nodeProps: [\n    [\"isolate\", -2,3,25,\"\"],\n    [\"openedBy\", 18,\"(\",33,\"[\",51,\"{\"],\n    [\"closedBy\", 19,\")\",34,\"]\",52,\"}\"]\n  ],\n  propSources: [cssHighlighting],\n  skippedNodes: [0,3,88],\n  repeatNodeCount: 11,\n  tokenData: \"J^~R!^OX$}X^%u^p$}pq%uqr)Xrs.Rst/utu6duv$}vw7^wx7oxy9^yz9oz{9t{|:_|}?Q}!O?c!O!P@Q!P!Q@i!Q![Ab![!]B]!]!^CX!^!_$}!_!`Cj!`!aC{!a!b$}!b!cDw!c!}$}!}#OFa#O#P$}#P#QFr#Q#R6d#R#T$}#T#UGT#U#c$}#c#dHf#d#o$}#o#pH{#p#q6d#q#rI^#r#sIo#s#y$}#y#z%u#z$f$}$f$g%u$g#BY$}#BY#BZ%u#BZ$IS$}$IS$I_%u$I_$I|$}$I|$JO%u$JO$JT$}$JT$JU%u$JU$KV$}$KV$KW%u$KW&FU$}&FU&FV%u&FV;'S$};'S;=`JW<%lO$}`%QSOy%^z;'S%^;'S;=`%o<%lO%^`%cSp`Oy%^z;'S%^;'S;=`%o<%lO%^`%rP;=`<%l%^~%zh#[~OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^~'mh#[~p`OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^l)[UOy%^z#]%^#]#^)n#^;'S%^;'S;=`%o<%lO%^l)sUp`Oy%^z#a%^#a#b*V#b;'S%^;'S;=`%o<%lO%^l*[Up`Oy%^z#d%^#d#e*n#e;'S%^;'S;=`%o<%lO%^l*sUp`Oy%^z#c%^#c#d+V#d;'S%^;'S;=`%o<%lO%^l+[Up`Oy%^z#f%^#f#g+n#g;'S%^;'S;=`%o<%lO%^l+sUp`Oy%^z#h%^#h#i,V#i;'S%^;'S;=`%o<%lO%^l,[Up`Oy%^z#T%^#T#U,n#U;'S%^;'S;=`%o<%lO%^l,sUp`Oy%^z#b%^#b#c-V#c;'S%^;'S;=`%o<%lO%^l-[Up`Oy%^z#h%^#h#i-n#i;'S%^;'S;=`%o<%lO%^l-uS!Y[p`Oy%^z;'S%^;'S;=`%o<%lO%^~.UWOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o<%lO.R~.sOi~~.vRO;'S.R;'S;=`/P;=`O.R~/SXOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o;=`<%l.R<%lO.R~/rP;=`<%l.Rn/zYyQOy%^z!Q%^!Q![0j![!c%^!c!i0j!i#T%^#T#Z0j#Z;'S%^;'S;=`%o<%lO%^l0oYp`Oy%^z!Q%^!Q![1_![!c%^!c!i1_!i#T%^#T#Z1_#Z;'S%^;'S;=`%o<%lO%^l1dYp`Oy%^z!Q%^!Q![2S![!c%^!c!i2S!i#T%^#T#Z2S#Z;'S%^;'S;=`%o<%lO%^l2ZYg[p`Oy%^z!Q%^!Q![2y![!c%^!c!i2y!i#T%^#T#Z2y#Z;'S%^;'S;=`%o<%lO%^l3QYg[p`Oy%^z!Q%^!Q![3p![!c%^!c!i3p!i#T%^#T#Z3p#Z;'S%^;'S;=`%o<%lO%^l3uYp`Oy%^z!Q%^!Q![4e![!c%^!c!i4e!i#T%^#T#Z4e#Z;'S%^;'S;=`%o<%lO%^l4lYg[p`Oy%^z!Q%^!Q![5[![!c%^!c!i5[!i#T%^#T#Z5[#Z;'S%^;'S;=`%o<%lO%^l5aYp`Oy%^z!Q%^!Q![6P![!c%^!c!i6P!i#T%^#T#Z6P#Z;'S%^;'S;=`%o<%lO%^l6WSg[p`Oy%^z;'S%^;'S;=`%o<%lO%^d6gUOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^d7QS}Sp`Oy%^z;'S%^;'S;=`%o<%lO%^b7cSXQOy%^z;'S%^;'S;=`%o<%lO%^~7rWOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W<%lO7o~8_RO;'S7o;'S;=`8h;=`O7o~8kXOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W;=`<%l7o<%lO7o~9ZP;=`<%l7on9cSc^Oy%^z;'S%^;'S;=`%o<%lO%^~9tOb~n9{UUQkWOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^n:fWkW!SQOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^l;TUp`Oy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^l;nYp`#e[Oy%^z!Q%^!Q![;g![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^l<cYp`Oy%^z{%^{|=R|}%^}!O=R!O!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=WUp`Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=qUp`#e[Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l>[[p`#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^n?VSu^Oy%^z;'S%^;'S;=`%o<%lO%^l?hWkWOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^n@VUZQOy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^~@nTkWOy%^z{@}{;'S%^;'S;=`%o<%lO%^~AUSp`#]~Oy%^z;'S%^;'S;=`%o<%lO%^lAg[#e[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^bBbU^QOy%^z![%^![!]Bt!];'S%^;'S;=`%o<%lO%^bB{S_Qp`Oy%^z;'S%^;'S;=`%o<%lO%^nC^S!Z^Oy%^z;'S%^;'S;=`%o<%lO%^dCoS}SOy%^z;'S%^;'S;=`%o<%lO%^bDQU!PQOy%^z!`%^!`!aDd!a;'S%^;'S;=`%o<%lO%^bDkS!PQp`Oy%^z;'S%^;'S;=`%o<%lO%^bDzWOy%^z!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^bEk[!]Qp`Oy%^z}%^}!OEd!O!Q%^!Q![Ed![!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^nFfSr^Oy%^z;'S%^;'S;=`%o<%lO%^nFwSq^Oy%^z;'S%^;'S;=`%o<%lO%^bGWUOy%^z#b%^#b#cGj#c;'S%^;'S;=`%o<%lO%^bGoUp`Oy%^z#W%^#W#XHR#X;'S%^;'S;=`%o<%lO%^bHYS!cQp`Oy%^z;'S%^;'S;=`%o<%lO%^bHiUOy%^z#f%^#f#gHR#g;'S%^;'S;=`%o<%lO%^fIQS!UUOy%^z;'S%^;'S;=`%o<%lO%^nIcS!T^Oy%^z;'S%^;'S;=`%o<%lO%^fItU!SQOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^`JZP;=`<%l$}\",\n  tokenizers: [descendant, unitToken, identifiers, 1, 2, 3, 4, new LocalTokenGroup(\"m~RRYZ[z{a~~g~aO#_~~dP!P!Qg~lO#`~~\", 28, 106)],\n  topRules: {\"StyleSheet\":[0,4],\"Styles\":[1,87]},\n  specialized: [{term: 101, get: (value) => spec_callee[value] || -1},{term: 59, get: (value) => spec_AtKeyword[value] || -1},{term: 102, get: (value) => spec_identifier[value] || -1}],\n  tokenPrec: 1219\n});\n\nexport { parser };\n", "import { parser } from '@lezer/css';\nimport { syntax<PERSON>ree, LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { NodeWeakMap, IterMode } from '@lezer/common';\n\nlet _properties = null;\nfunction properties() {\n    if (!_properties && typeof document == \"object\" && document.body) {\n        let { style } = document.body, names = [], seen = new Set;\n        for (let prop in style)\n            if (prop != \"cssText\" && prop != \"cssFloat\") {\n                if (typeof style[prop] == \"string\") {\n                    if (/[A-Z]/.test(prop))\n                        prop = prop.replace(/[A-Z]/g, ch => \"-\" + ch.toLowerCase());\n                    if (!seen.has(prop)) {\n                        names.push(prop);\n                        seen.add(prop);\n                    }\n                }\n            }\n        _properties = names.sort().map(name => ({ type: \"property\", label: name, apply: name + \": \" }));\n    }\n    return _properties || [];\n}\nconst pseudoClasses = /*@__PURE__*/[\n    \"active\", \"after\", \"any-link\", \"autofill\", \"backdrop\", \"before\",\n    \"checked\", \"cue\", \"default\", \"defined\", \"disabled\", \"empty\",\n    \"enabled\", \"file-selector-button\", \"first\", \"first-child\",\n    \"first-letter\", \"first-line\", \"first-of-type\", \"focus\",\n    \"focus-visible\", \"focus-within\", \"fullscreen\", \"has\", \"host\",\n    \"host-context\", \"hover\", \"in-range\", \"indeterminate\", \"invalid\",\n    \"is\", \"lang\", \"last-child\", \"last-of-type\", \"left\", \"link\", \"marker\",\n    \"modal\", \"not\", \"nth-child\", \"nth-last-child\", \"nth-last-of-type\",\n    \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\",\n    \"part\", \"placeholder\", \"placeholder-shown\", \"read-only\", \"read-write\",\n    \"required\", \"right\", \"root\", \"scope\", \"selection\", \"slotted\", \"target\",\n    \"target-text\", \"valid\", \"visited\", \"where\"\n].map(name => ({ type: \"class\", label: name }));\nconst values = /*@__PURE__*/[\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"after-white-space\",\n    \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\", \"always\",\n    \"antialiased\", \"appworkspace\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\",\n    \"avoid-page\", \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\",\n    \"bidi-override\", \"blink\", \"block\", \"block-axis\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"bullets\", \"button\", \"button-bevel\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"capitalize\",\n    \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\", \"cell\", \"center\", \"checkbox\", \"circle\",\n    \"cjk-decimal\", \"clear\", \"clip\", \"close-quote\", \"col-resize\", \"collapse\", \"color\", \"color-burn\",\n    \"color-dodge\", \"column\", \"column-reverse\", \"compact\", \"condensed\", \"contain\", \"content\",\n    \"contents\", \"content-box\", \"context-menu\", \"continuous\", \"copy\", \"counter\", \"counters\", \"cover\",\n    \"crop\", \"cross\", \"crosshair\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\", \"destination-in\",\n    \"destination-out\", \"destination-over\", \"difference\", \"disc\", \"discard\", \"disclosure-closed\",\n    \"disclosure-open\", \"document\", \"dot-dash\", \"dot-dot-dash\", \"dotted\", \"double\", \"down\", \"e-resize\",\n    \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\", \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\",\n    \"ethiopic-abegede-gez\", \"ethiopic-halehame-aa-er\", \"ethiopic-halehame-gez\", \"ew-resize\", \"exclusion\",\n    \"expanded\", \"extends\", \"extra-condensed\", \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\",\n    \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\", \"forwards\", \"from\",\n    \"geometricPrecision\", \"graytext\", \"grid\", \"groove\", \"hand\", \"hard-light\", \"help\", \"hidden\", \"hide\",\n    \"higher\", \"highlight\", \"highlighttext\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\", \"infobackground\", \"infotext\",\n    \"inherit\", \"initial\", \"inline\", \"inline-axis\", \"inline-block\", \"inline-flex\", \"inline-grid\",\n    \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\", \"italic\", \"justify\", \"keep-all\",\n    \"landscape\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\", \"line-through\", \"linear\",\n    \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\", \"local\", \"logical\", \"loud\", \"lower\",\n    \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\", \"lowercase\", \"ltr\", \"luminosity\", \"manipulation\",\n    \"match\", \"matrix\", \"matrix3d\", \"medium\", \"menu\", \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"n-resize\", \"narrower\",\n    \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\", \"no-open-quote\", \"no-repeat\", \"none\",\n    \"normal\", \"not-allowed\", \"nowrap\", \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\",\n    \"oblique\", \"opacity\", \"open-quote\", \"optimizeLegibility\", \"optimizeSpeed\", \"outset\", \"outside\",\n    \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\", \"painted\", \"page\", \"paused\",\n    \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\", \"pointer\", \"polygon\", \"portrait\",\n    \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\", \"progress\", \"push-button\", \"radial-gradient\", \"radio\",\n    \"read-only\", \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\", \"relative\", \"repeat\",\n    \"repeating-linear-gradient\", \"repeating-radial-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\", \"rotateZ\", \"round\",\n    \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\", \"s-resize\", \"sans-serif\", \"saturation\",\n    \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\", \"scroll\", \"scrollbar\", \"scroll-position\",\n    \"se-resize\", \"self-start\", \"self-end\", \"semi-condensed\", \"semi-expanded\", \"separate\", \"serif\", \"show\",\n    \"single\", \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\", \"small\", \"small-caps\",\n    \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"source-atop\", \"source-in\", \"source-out\",\n    \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\", \"start\",\n    \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\", \"subpixel-antialiased\", \"svg_masks\",\n    \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\", \"table-caption\", \"table-cell\",\n    \"table-column\", \"table-column-group\", \"table-footer-group\", \"table-header-group\", \"table-row\",\n    \"table-row-group\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thick\", \"thin\",\n    \"threeddarkshadow\", \"threedface\", \"threedhighlight\", \"threedlightshadow\", \"threedshadow\", \"to\", \"top\",\n    \"transform\", \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\", \"transparent\",\n    \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\", \"upper-latin\",\n    \"uppercase\", \"url\", \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\",\n    \"visiblePainted\", \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\", \"window\", \"windowframe\",\n    \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\", \"xx-large\", \"xx-small\"\n].map(name => ({ type: \"keyword\", label: name })).concat(/*@__PURE__*/[\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n].map(name => ({ type: \"constant\", label: name })));\nconst tags = /*@__PURE__*/[\n    \"a\", \"abbr\", \"address\", \"article\", \"aside\", \"b\", \"bdi\", \"bdo\", \"blockquote\", \"body\",\n    \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"dd\", \"del\",\n    \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"figcaption\", \"figure\", \"footer\",\n    \"form\", \"header\", \"hgroup\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"hr\", \"html\", \"i\", \"iframe\",\n    \"img\", \"input\", \"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"meter\", \"nav\", \"ol\", \"output\",\n    \"p\", \"pre\", \"ruby\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"sub\", \"summary\",\n    \"sup\", \"table\", \"tbody\", \"td\", \"template\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"tr\", \"u\", \"ul\"\n].map(name => ({ type: \"type\", label: name }));\nconst atRules = /*@__PURE__*/[\n    \"@charset\", \"@color-profile\", \"@container\", \"@counter-style\", \"@font-face\", \"@font-feature-values\",\n    \"@font-palette-values\", \"@import\", \"@keyframes\", \"@layer\", \"@media\", \"@namespace\", \"@page\",\n    \"@position-try\", \"@property\", \"@scope\", \"@starting-style\", \"@supports\", \"@view-transition\"\n].map(label => ({ type: \"keyword\", label }));\nconst identifier = /^(\\w[\\w-]*|-\\w[\\w-]*|)$/, variable = /^-(-[\\w-]*)?$/;\nfunction isVarArg(node, doc) {\n    var _a;\n    if (node.name == \"(\" || node.type.isError)\n        node = node.parent || node;\n    if (node.name != \"ArgList\")\n        return false;\n    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;\n    if ((callee === null || callee === void 0 ? void 0 : callee.name) != \"Callee\")\n        return false;\n    return doc.sliceString(callee.from, callee.to) == \"var\";\n}\nconst VariablesByNode = /*@__PURE__*/new NodeWeakMap();\nconst declSelector = [\"Declaration\"];\nfunction astTop(node) {\n    for (let cur = node;;) {\n        if (cur.type.isTop)\n            return cur;\n        if (!(cur = cur.parent))\n            return node;\n    }\n}\nfunction variableNames(doc, node, isVariable) {\n    if (node.to - node.from > 4096) {\n        let known = VariablesByNode.get(node);\n        if (known)\n            return known;\n        let result = [], seen = new Set, cursor = node.cursor(IterMode.IncludeAnonymous);\n        if (cursor.firstChild())\n            do {\n                for (let option of variableNames(doc, cursor.node, isVariable))\n                    if (!seen.has(option.label)) {\n                        seen.add(option.label);\n                        result.push(option);\n                    }\n            } while (cursor.nextSibling());\n        VariablesByNode.set(node, result);\n        return result;\n    }\n    else {\n        let result = [], seen = new Set;\n        node.cursor().iterate(node => {\n            var _a;\n            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == \":\") {\n                let name = doc.sliceString(node.from, node.to);\n                if (!seen.has(name)) {\n                    seen.add(name);\n                    result.push({ label: name, type: \"variable\" });\n                }\n            }\n        });\n        return result;\n    }\n}\n/**\nCreate a completion source for a CSS dialect, providing a\npredicate for determining what kind of syntax node can act as a\ncompletable variable. This is used by language modes like Sass and\nLess to reuse this package's completion logic.\n*/\nconst defineCSSCompletionSource = (isVariable) => context => {\n    let { state, pos } = context, node = syntaxTree(state).resolveInner(pos, -1);\n    let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == \"-\";\n    if (node.name == \"PropertyName\" ||\n        (isDash || node.name == \"TagName\") && /^(Block|Styles)$/.test(node.resolve(node.to).name))\n        return { from: node.from, options: properties(), validFor: identifier };\n    if (node.name == \"ValueName\")\n        return { from: node.from, options: values, validFor: identifier };\n    if (node.name == \"PseudoClassName\")\n        return { from: node.from, options: pseudoClasses, validFor: identifier };\n    if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc))\n        return { from: isVariable(node) || isDash ? node.from : pos,\n            options: variableNames(state.doc, astTop(node), isVariable),\n            validFor: variable };\n    if (node.name == \"TagName\") {\n        for (let { parent } = node; parent; parent = parent.parent)\n            if (parent.name == \"Block\")\n                return { from: node.from, options: properties(), validFor: identifier };\n        return { from: node.from, options: tags, validFor: identifier };\n    }\n    if (node.name == \"AtKeyword\")\n        return { from: node.from, options: atRules, validFor: identifier };\n    if (!context.explicit)\n        return null;\n    let above = node.resolve(pos), before = above.childBefore(pos);\n    if (before && before.name == \":\" && above.name == \"PseudoClassSelector\")\n        return { from: pos, options: pseudoClasses, validFor: identifier };\n    if (before && before.name == \":\" && above.name == \"Declaration\" || above.name == \"ArgList\")\n        return { from: pos, options: values, validFor: identifier };\n    if (above.name == \"Block\" || above.name == \"Styles\")\n        return { from: pos, options: properties(), validFor: identifier };\n    return null;\n};\n/**\nCSS property, variable, and value keyword completion source.\n*/\nconst cssCompletionSource = /*@__PURE__*/defineCSSCompletionSource(n => n.name == \"VariableName\");\n\n/**\nA language provider based on the [Lezer CSS\nparser](https://github.com/lezer-parser/css), extended with\nhighlighting and indentation information.\n*/\nconst cssLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"css\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Declaration: /*@__PURE__*/continuedIndent()\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Block KeyframeList\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"-\"\n    }\n});\n/**\nLanguage support for CSS.\n*/\nfunction css() {\n    return new LanguageSupport(cssLanguage, cssLanguage.data.of({ autocomplete: cssCompletionSource }));\n}\n\nexport { css, cssCompletionSource, cssLanguage, defineCSSCompletionSource };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,IAAM,eAAe;AAArB,IACE,OAAO;AADT,IAEE,SAAS;AAFX,IAGE,aAAa;AAHf,IAIE,eAAe;AAKjB,IAAM,QAAQ;AAAA,EAAC;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EACrE;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAK;AAC1E,IAAM,QAAQ;AAAd,IAAkB,SAAS;AAA3B,IAA+B,aAAa;AAA5C,IAAgD,WAAW;AAA3D,IAA+D,OAAO;AAAtE,IAA0E,SAAS;AAAnF,IACM,OAAO;AADb,IACiB,UAAU;AAD3B,IAC+B,YAAY;AAD3C,IAC+C,YAAY;AAD3D,IAC+D,UAAU;AAEzE,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAAI;AAEzF,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM;AAAG;AAEnD,IAAM,cAAc,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC1D,WAAS,SAAS,OAAO,SAAS,GAAG,IAAI,KAAI,KAAK;AAChD,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,QAAQ,cAAe,UAAU,QAAQ,IAAI,GAAI;AACpF,UAAI,CAAC,WAAW,QAAQ,QAAQ,IAAI;AAAI,iBAAS;AACjD,UAAI,WAAW,KAAK,QAAQ;AAAM;AAClC,YAAM,QAAQ;AAAA,IAChB,WAAW,QAAQ,aAAa,MAAM,KAAK,CAAC,KAAK,SAAS;AACxD,YAAM,QAAQ;AACd,UAAI,MAAM,OAAO;AAAI,cAAM,QAAQ;AACnC,eAAS;AAAA,IACX,OAAO;AACL,UAAI;AACF,cAAM,YAAY,QAAQ,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,YAAY,IAAI,eAAe,UAAU;AACrH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa,IAAI,kBAAkB,WAAS;AAChD,MAAI,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AAClC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,UAC/D,QAAQ,YAAY,QAAQ,SAAS,QAAQ,MAAM,KAAK,CAAC,CAAC,KAC1D,QAAQ,QAAQ,QAAQ;AAC1B,YAAM,YAAY,YAAY;AAAA,EAClC;AACF,CAAC;AAED,IAAM,YAAY,IAAI,kBAAkB,WAAS;AAC/C,MAAI,CAAC,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AACnC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,SAAS;AAAE,YAAM,QAAQ;AAAG,YAAM,YAAY,IAAI;AAAA,IAAG;AACjE,QAAI,QAAQ,IAAI,GAAG;AACjB,SAAG;AAAE,cAAM,QAAQ;AAAA,MAAG,SAAS,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI;AACxE,YAAM,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AACF,CAAC;AAED,IAAM,kBAAkB,UAAU;AAAA,EAChC,+DAA+D,KAAK;AAAA,EACpE,oBAAoB,KAAK;AAAA,EACzB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,mBAAmB,KAAK;AAAA,EACxB,SAAS,KAAK;AAAA,EACd,WAAW,KAAK;AAAA,EAChB,iBAAiB,KAAK,SAAS,KAAK,SAAS;AAAA,EAC7C,QAAQ,KAAK;AAAA,EACb,4BAA4B,KAAK;AAAA,EACjC,eAAe,KAAK;AAAA,EACpB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,cAAc,KAAK;AAAA,EACnB,qBAAqB,KAAK;AAAA,EAC1B,cAAc,KAAK;AAAA,EACnB,QAAQ,KAAK;AAAA,EACb,MAAM,KAAK;AAAA,EACX,qCAAqC,KAAK;AAAA,EAC1C,SAAS,KAAK;AAAA,EACd,8BAA8B,KAAK;AAAA,EACnC,OAAO,KAAK;AAAA,EACZ,WAAW,KAAK;AAAA,EAChB,SAAS,KAAK;AAAA,EACd,cAAc,KAAK;AAAA,EACnB,sCAAsC,KAAK;AAAA,EAC3C,KAAK,KAAK;AAAA,EACV,cAAc,KAAK;AAAA,EACnB,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,cAAc,EAAC,WAAU,MAAK,MAAK,IAAI,aAAY,IAAI,kBAAiB,IAAI,eAAc,IAAI,oBAAmB,IAAI,KAAI,IAAI,gBAAe,IAAI,KAAI,IAAI,cAAa,IAAI,QAAO,IAAI,QAAO,IAAI,UAAS,IAAG;AACjN,IAAM,iBAAiB,EAAC,WAAU,MAAK,WAAU,KAAK,UAAS,KAAK,YAAW,KAAK,cAAa,KAAK,cAAa,KAAK,aAAY,IAAG;AACvI,IAAM,kBAAkB,EAAC,WAAU,MAAK,KAAI,KAAK,MAAK,IAAG;AACzD,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,IAAG,EAAE;AAAA,IACtB,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,IACjC,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,EACnC;AAAA,EACA,aAAa,CAAC,eAAe;AAAA,EAC7B,cAAc,CAAC,GAAE,GAAE,EAAE;AAAA,EACrB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,YAAY,WAAW,aAAa,GAAG,GAAG,GAAG,GAAG,IAAI,gBAAgB,sCAAsC,IAAI,GAAG,CAAC;AAAA,EAC/H,UAAU,EAAC,cAAa,CAAC,GAAE,CAAC,GAAE,UAAS,CAAC,GAAE,EAAE,EAAC;AAAA,EAC7C,aAAa,CAAC,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,YAAY,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,IAAI,KAAK,CAAC,UAAU,eAAe,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,gBAAgB,KAAK,KAAK,GAAE,CAAC;AAAA,EACrL,WAAW;AACb,CAAC;;;ACrHD,IAAI,cAAc;AAClB,SAAS,aAAa;AAClB,MAAI,CAAC,eAAe,OAAO,YAAY,YAAY,SAAS,MAAM;AAC9D,QAAI,EAAE,MAAM,IAAI,SAAS,MAAM,QAAQ,CAAC,GAAG,OAAO,oBAAI;AACtD,aAAS,QAAQ;AACb,UAAI,QAAQ,aAAa,QAAQ,YAAY;AACzC,YAAI,OAAO,MAAM,IAAI,KAAK,UAAU;AAChC,cAAI,QAAQ,KAAK,IAAI;AACjB,mBAAO,KAAK,QAAQ,UAAU,QAAM,MAAM,GAAG,YAAY,CAAC;AAC9D,cAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACjB,kBAAM,KAAK,IAAI;AACf,iBAAK,IAAI,IAAI;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACJ,kBAAc,MAAM,KAAK,EAAE,IAAI,WAAS,EAAE,MAAM,YAAY,OAAO,MAAM,OAAO,OAAO,KAAK,EAAE;AAAA,EAClG;AACA,SAAO,eAAe,CAAC;AAC3B;AACA,IAAM,gBAA6B;AAAA,EAC/B;AAAA,EAAU;AAAA,EAAS;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EACvD;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EACpD;AAAA,EAAW;AAAA,EAAwB;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAiB;AAAA,EAC/C;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAO;AAAA,EACtD;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAiB;AAAA,EACtD;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAC5D;AAAA,EAAS;AAAA,EAAO;AAAA,EAAa;AAAA,EAAkB;AAAA,EAC/C;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EACzD;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAqB;AAAA,EAAa;AAAA,EACzD;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAC9D;AAAA,EAAe;AAAA,EAAS;AAAA,EAAW;AACvC,EAAE,IAAI,WAAS,EAAE,MAAM,SAAS,OAAO,KAAK,EAAE;AAC9C,IAAM,SAAsB;AAAA,EACxB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAiB;AAAA,EAClE;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAc;AAAA,EAAa;AAAA,EAClE;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAClF;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EACjF;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAa;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAC3E;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAQ;AAAA,EACvE;AAAA,EAAuB;AAAA,EAAW;AAAA,EAAe;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAY;AAAA,EACxF;AAAA,EAAe;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EAClF;AAAA,EAAe;AAAA,EAAU;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAa;AAAA,EAAW;AAAA,EAC9E;AAAA,EAAY;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EACxF;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EACvF;AAAA,EAAwB;AAAA,EAAW;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAoB;AAAA,EAClF;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAW;AAAA,EACxE;AAAA,EAAmB;AAAA,EAAY;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAe;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EACzF;AAAA,EAAwB;AAAA,EAA2B;AAAA,EAAyB;AAAA,EAAa;AAAA,EACzF;AAAA,EAAY;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EAC5E;AAAA,EAAsB;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAU;AAAA,EAC5F;AAAA,EAAU;AAAA,EAAa;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAuB;AAAA,EAAY;AAAA,EAAkB;AAAA,EAC1F;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAe;AAAA,EAC9E;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAa;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAC/E;AAAA,EAAa;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAgB;AAAA,EACvF;AAAA,EAAmB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAQ;AAAA,EAC5F;AAAA,EAAqB;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAa;AAAA,EAAO;AAAA,EAAc;AAAA,EACzF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EACtF;AAAA,EAAO;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAwB;AAAA,EAAY;AAAA,EAAY;AAAA,EACxF;AAAA,EAAa;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAa;AAAA,EACvF;AAAA,EAAU;AAAA,EAAe;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAa;AAAA,EACnF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAU;AAAA,EACrF;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAe;AAAA,EAAW;AAAA,EAAQ;AAAA,EACrF;AAAA,EAAe;AAAA,EAAc;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAClF;AAAA,EAAO;AAAA,EAAY;AAAA,EAAY;AAAA,EAAe;AAAA,EAAY;AAAA,EAAe;AAAA,EAAmB;AAAA,EAC5F;AAAA,EAAa;AAAA,EAAc;AAAA,EAA6B;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EAC3F;AAAA,EAA6B;AAAA,EAA6B;AAAA,EAAY;AAAA,EAAY;AAAA,EAAS;AAAA,EAC3F;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EACxF;AAAA,EAAO;AAAA,EAAc;AAAA,EAAe;AAAA,EAAO;AAAA,EAAU;AAAA,EAAW;AAAA,EAAY;AAAA,EAAc;AAAA,EAC1F;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAa;AAAA,EACnF;AAAA,EAAa;AAAA,EAAc;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAS;AAAA,EAC/F;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAoB;AAAA,EAAS;AAAA,EACjE;AAAA,EAAmB;AAAA,EAA0B;AAAA,EAAwB;AAAA,EAAQ;AAAA,EAAS;AAAA,EACtF;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAS;AAAA,EAAe;AAAA,EAAa;AAAA,EAC/E;AAAA,EAAe;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAU;AAAA,EAChG;AAAA,EAAU;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAAc;AAAA,EAAO;AAAA,EAAwB;AAAA,EAC1F;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAAS;AAAA,EAAiB;AAAA,EACpF;AAAA,EAAgB;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAClF;AAAA,EAAmB;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAY;AAAA,EAAY;AAAA,EAAa;AAAA,EAAS;AAAA,EACxF;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAAgB;AAAA,EAAM;AAAA,EAChG;AAAA,EAAa;AAAA,EAAa;AAAA,EAAe;AAAA,EAAc;AAAA,EAAc;AAAA,EAAc;AAAA,EACnF;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAS;AAAA,EAAM;AAAA,EACvF;AAAA,EAAa;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAW;AAAA,EAC/E;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAC5F;AAAA,EAAc;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAO;AAAA,EAAY;AAC5F,EAAE,IAAI,WAAS,EAAE,MAAM,WAAW,OAAO,KAAK,EAAE,EAAE,OAAoB;AAAA,EAClE;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAS;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAa;AAAA,EAAc;AAAA,EAAa;AAAA,EAAS;AAAA,EAC9D;AAAA,EAAY;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EACvD;AAAA,EAAY;AAAA,EAAa;AAAA,EAAa;AAAA,EAAe;AAAA,EACrD;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EAAc;AAAA,EACrD;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAiB;AAAA,EACnD;AAAA,EAAY;AAAA,EAAe;AAAA,EAAW;AAAA,EAAc;AAAA,EACpD;AAAA,EAAe;AAAA,EAAe;AAAA,EAAW;AAAA,EAAa;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAe;AAAA,EAC7D;AAAA,EAAW;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EACpD;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAa;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAwB;AAAA,EAAa;AAAA,EAAc;AAAA,EAChE;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAChD;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAC/D;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC5D;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAC1D;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAa;AAAA,EAC7D;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAU;AAAA,EAClE;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAiB;AAAA,EACzD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACnD;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAa;AAAA,EAAa;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAc;AAAA,EAAY;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EACpE;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAa;AAAA,EAC9D;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7D;AAAA,EAAc;AAAA,EAAU;AAC5B,EAAE,IAAI,WAAS,EAAE,MAAM,YAAY,OAAO,KAAK,EAAE,CAAC;AAClD,IAAMA,QAAoB;AAAA,EACtB;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAK;AAAA,EAAO;AAAA,EAAO;AAAA,EAAc;AAAA,EAC7E;AAAA,EAAM;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAM;AAAA,EAC9E;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAc;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAK;AAAA,EACnF;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAA,EACrF;AAAA,EAAK;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EACrF;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAY;AAAA,EAAY;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAAM;AAAA,EAAK;AAC9F,EAAE,IAAI,WAAS,EAAE,MAAM,QAAQ,OAAO,KAAK,EAAE;AAC7C,IAAM,UAAuB;AAAA,EACzB;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAc;AAAA,EAC5E;AAAA,EAAwB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAU;AAAA,EAAU;AAAA,EAAc;AAAA,EACnF;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAU;AAAA,EAAmB;AAAA,EAAa;AAC5E,EAAE,IAAI,YAAU,EAAE,MAAM,WAAW,MAAM,EAAE;AAC3C,IAAMC,cAAa;AAAnB,IAA8C,WAAW;AACzD,SAAS,SAAS,MAAM,KAAK;AACzB,MAAI;AACJ,MAAI,KAAK,QAAQ,OAAO,KAAK,KAAK;AAC9B,WAAO,KAAK,UAAU;AAC1B,MAAI,KAAK,QAAQ;AACb,WAAO;AACX,MAAIC,WAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxE,OAAKA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,SAAS;AACjE,WAAO;AACX,SAAO,IAAI,YAAYA,QAAO,MAAMA,QAAO,EAAE,KAAK;AACtD;AACA,IAAM,kBAA+B,IAAI,YAAY;AACrD,IAAM,eAAe,CAAC,aAAa;AACnC,SAAS,OAAO,MAAM;AAClB,WAAS,MAAM,UAAQ;AACnB,QAAI,IAAI,KAAK;AACT,aAAO;AACX,QAAI,EAAE,MAAM,IAAI;AACZ,aAAO;AAAA,EACf;AACJ;AACA,SAAS,cAAc,KAAK,MAAM,YAAY;AAC1C,MAAI,KAAK,KAAK,KAAK,OAAO,MAAM;AAC5B,QAAI,QAAQ,gBAAgB,IAAI,IAAI;AACpC,QAAI;AACA,aAAO;AACX,QAAI,SAAS,CAAC,GAAG,OAAO,oBAAI,OAAK,SAAS,KAAK,OAAO,SAAS,gBAAgB;AAC/E,QAAI,OAAO,WAAW;AAClB,SAAG;AACC,iBAAS,UAAU,cAAc,KAAK,OAAO,MAAM,UAAU;AACzD,cAAI,CAAC,KAAK,IAAI,OAAO,KAAK,GAAG;AACzB,iBAAK,IAAI,OAAO,KAAK;AACrB,mBAAO,KAAK,MAAM;AAAA,UACtB;AAAA,MACR,SAAS,OAAO,YAAY;AAChC,oBAAgB,IAAI,MAAM,MAAM;AAChC,WAAO;AAAA,EACX,OACK;AACD,QAAI,SAAS,CAAC,GAAG,OAAO,oBAAI;AAC5B,SAAK,OAAO,EAAE,QAAQ,CAAAC,UAAQ;AAC1B,UAAI;AACJ,UAAI,WAAWA,KAAI,KAAKA,MAAK,aAAa,YAAY,OAAO,KAAKA,MAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,KAAK;AAC3I,YAAI,OAAO,IAAI,YAAYA,MAAK,MAAMA,MAAK,EAAE;AAC7C,YAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACjB,eAAK,IAAI,IAAI;AACb,iBAAO,KAAK,EAAE,OAAO,MAAM,MAAM,WAAW,CAAC;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAOA,IAAM,4BAA4B,CAAC,eAAe,aAAW;AACzD,MAAI,EAAE,OAAO,IAAI,IAAI,SAAS,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE;AAC3E,MAAI,SAAS,KAAK,KAAK,WAAW,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE,KAAK;AAC3G,MAAI,KAAK,QAAQ,mBACZ,UAAU,KAAK,QAAQ,cAAc,mBAAmB,KAAK,KAAK,QAAQ,KAAK,EAAE,EAAE,IAAI;AACxF,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,WAAW,GAAG,UAAUF,YAAW;AAC1E,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,QAAQ,UAAUA,YAAW;AACpE,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,eAAe,UAAUA,YAAW;AAC3E,MAAI,WAAW,IAAI,MAAM,QAAQ,YAAY,WAAW,SAAS,MAAM,MAAM,GAAG;AAC5E,WAAO;AAAA,MAAE,MAAM,WAAW,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,MACpD,SAAS,cAAc,MAAM,KAAK,OAAO,IAAI,GAAG,UAAU;AAAA,MAC1D,UAAU;AAAA,IAAS;AAC3B,MAAI,KAAK,QAAQ,WAAW;AACxB,aAAS,EAAE,OAAO,IAAI,MAAM,QAAQ,SAAS,OAAO;AAChD,UAAI,OAAO,QAAQ;AACf,eAAO,EAAE,MAAM,KAAK,MAAM,SAAS,WAAW,GAAG,UAAUA,YAAW;AAC9E,WAAO,EAAE,MAAM,KAAK,MAAM,SAASD,OAAM,UAAUC,YAAW;AAAA,EAClE;AACA,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,SAAS,UAAUA,YAAW;AACrE,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,MAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,SAAS,MAAM,YAAY,GAAG;AAC7D,MAAI,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ;AAC9C,WAAO,EAAE,MAAM,KAAK,SAAS,eAAe,UAAUA,YAAW;AACrE,MAAI,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,iBAAiB,MAAM,QAAQ;AAC7E,WAAO,EAAE,MAAM,KAAK,SAAS,QAAQ,UAAUA,YAAW;AAC9D,MAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ;AACvC,WAAO,EAAE,MAAM,KAAK,SAAS,WAAW,GAAG,UAAUA,YAAW;AACpE,SAAO;AACX;AAIA,IAAM,sBAAmC,0BAA0B,OAAK,EAAE,QAAQ,cAAc;AAOhG,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,aAA0B,gBAAgB;AAAA,MAC9C,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,sBAAsB;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,EAAE;AAAA,IACpD,eAAe;AAAA,IACf,WAAW;AAAA,EACf;AACJ,CAAC;AAID,SAAS,MAAM;AACX,SAAO,IAAI,gBAAgB,aAAa,YAAY,KAAK,GAAG,EAAE,cAAc,oBAAoB,CAAC,CAAC;AACtG;", "names": ["tags", "identifier", "callee", "node"]}