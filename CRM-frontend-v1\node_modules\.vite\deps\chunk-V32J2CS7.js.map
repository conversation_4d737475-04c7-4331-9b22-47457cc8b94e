{"version": 3, "sources": ["../../@mui/system/esm/styleFunctionSx/extendSxProp.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"sx\"];\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from './defaultSxConfig';\nconst splitProps = props => {\n  var _props$theme$unstable, _props$theme;\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = (_props$theme$unstable = props == null || (_props$theme = props.theme) == null ? void 0 : _props$theme.unstable_sxConfig) != null ? _props$theme$unstable : defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n      sx: inSx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return _extends({}, systemProps, result);\n    };\n  } else {\n    finalSx = _extends({}, systemProps, inSx);\n  }\n  return _extends({}, otherProps, {\n    sx: finalSx\n  });\n}"], "mappings": ";;;;;;;;;;;AAGA;AADA,IAAM,YAAY,CAAC,IAAI;AAGvB,IAAM,aAAa,WAAS;AAC1B,MAAI,uBAAuB;AAC3B,QAAM,SAAS;AAAA,IACb,aAAa,CAAC;AAAA,IACd,YAAY,CAAC;AAAA,EACf;AACA,QAAM,UAAU,wBAAwB,SAAS,SAAS,eAAe,MAAM,UAAU,OAAO,SAAS,aAAa,sBAAsB,OAAO,wBAAwB;AAC3K,SAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AACjC,QAAI,OAAO,IAAI,GAAG;AAChB,aAAO,YAAY,IAAI,IAAI,MAAM,IAAI;AAAA,IACvC,OAAO;AACL,aAAO,WAAW,IAAI,IAAI,MAAM,IAAI;AAAA,IACtC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACe,SAAR,aAA8B,OAAO;AAC1C,QAAM;AAAA,IACF,IAAI;AAAA,EACN,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,WAAW,KAAK;AACpB,MAAI;AACJ,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,cAAU,CAAC,aAAa,GAAG,IAAI;AAAA,EACjC,WAAW,OAAO,SAAS,YAAY;AACrC,cAAU,IAAI,SAAS;AACrB,YAAM,SAAS,KAAK,GAAG,IAAI;AAC3B,UAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,aAAO,SAAS,CAAC,GAAG,aAAa,MAAM;AAAA,IACzC;AAAA,EACF,OAAO;AACL,cAAU,SAAS,CAAC,GAAG,aAAa,IAAI;AAAA,EAC1C;AACA,SAAO,SAAS,CAAC,GAAG,YAAY;AAAA,IAC9B,IAAI;AAAA,EACN,CAAC;AACH;", "names": []}