import {
  require_react
} from "./chunk-HLPDHYBP.js";
import {
  __toESM
} from "./chunk-ZDU32GKS.js";

// node_modules/@mui/material/FormControl/useFormControl.js
var React2 = __toESM(require_react());

// node_modules/@mui/material/FormControl/FormControlContext.js
var React = __toESM(require_react());
var FormControlContext = React.createContext(void 0);
if (true) {
  FormControlContext.displayName = "FormControlContext";
}
var FormControlContext_default = FormControlContext;

// node_modules/@mui/material/FormControl/useFormControl.js
function useFormControl() {
  return React2.useContext(FormControlContext_default);
}

export {
  FormControlContext_default,
  useFormControl
};
//# sourceMappingURL=chunk-WXSW3VIV.js.map
