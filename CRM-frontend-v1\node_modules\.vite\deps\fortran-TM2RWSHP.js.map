{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/fortran.js"], "sourcesContent": ["function words(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i]] = true;\n  }\n  return keys;\n}\n\nvar keywords = words([\n  \"abstract\", \"accept\", \"allocatable\", \"allocate\",\n  \"array\", \"assign\", \"asynchronous\", \"backspace\",\n  \"bind\", \"block\", \"byte\", \"call\", \"case\",\n  \"class\", \"close\", \"common\", \"contains\",\n  \"continue\", \"cycle\", \"data\", \"deallocate\",\n  \"decode\", \"deferred\", \"dimension\", \"do\",\n  \"elemental\", \"else\", \"encode\", \"end\",\n  \"endif\", \"entry\", \"enumerator\", \"equivalence\",\n  \"exit\", \"external\", \"extrinsic\", \"final\",\n  \"forall\", \"format\", \"function\", \"generic\",\n  \"go\", \"goto\", \"if\", \"implicit\", \"import\", \"include\",\n  \"inquire\", \"intent\", \"interface\", \"intrinsic\",\n  \"module\", \"namelist\", \"non_intrinsic\",\n  \"non_overridable\", \"none\", \"nopass\",\n  \"nullify\", \"open\", \"optional\", \"options\",\n  \"parameter\", \"pass\", \"pause\", \"pointer\",\n  \"print\", \"private\", \"program\", \"protected\",\n  \"public\", \"pure\", \"read\", \"recursive\", \"result\",\n  \"return\", \"rewind\", \"save\", \"select\", \"sequence\",\n  \"stop\", \"subroutine\", \"target\", \"then\", \"to\", \"type\",\n  \"use\", \"value\", \"volatile\", \"where\", \"while\",\n  \"write\"]);\nvar builtins = words([\"abort\", \"abs\", \"access\", \"achar\", \"acos\",\n                      \"adjustl\", \"adjustr\", \"aimag\", \"aint\", \"alarm\",\n                      \"all\", \"allocated\", \"alog\", \"amax\", \"amin\",\n                      \"amod\", \"and\", \"anint\", \"any\", \"asin\",\n                      \"associated\", \"atan\", \"besj\", \"besjn\", \"besy\",\n                      \"besyn\", \"bit_size\", \"btest\", \"cabs\", \"ccos\",\n                      \"ceiling\", \"cexp\", \"char\", \"chdir\", \"chmod\",\n                      \"clog\", \"cmplx\", \"command_argument_count\",\n                      \"complex\", \"conjg\", \"cos\", \"cosh\", \"count\",\n                      \"cpu_time\", \"cshift\", \"csin\", \"csqrt\", \"ctime\",\n                      \"c_funloc\", \"c_loc\", \"c_associated\", \"c_null_ptr\",\n                      \"c_null_funptr\", \"c_f_pointer\", \"c_null_char\",\n                      \"c_alert\", \"c_backspace\", \"c_form_feed\",\n                      \"c_new_line\", \"c_carriage_return\",\n                      \"c_horizontal_tab\", \"c_vertical_tab\", \"dabs\",\n                      \"dacos\", \"dasin\", \"datan\", \"date_and_time\",\n                      \"dbesj\", \"dbesj\", \"dbesjn\", \"dbesy\", \"dbesy\",\n                      \"dbesyn\", \"dble\", \"dcos\", \"dcosh\", \"ddim\", \"derf\",\n                      \"derfc\", \"dexp\", \"digits\", \"dim\", \"dint\", \"dlog\",\n                      \"dlog\", \"dmax\", \"dmin\", \"dmod\", \"dnint\",\n                      \"dot_product\", \"dprod\", \"dsign\", \"dsinh\",\n                      \"dsin\", \"dsqrt\", \"dtanh\", \"dtan\", \"dtime\",\n                      \"eoshift\", \"epsilon\", \"erf\", \"erfc\", \"etime\",\n                      \"exit\", \"exp\", \"exponent\", \"extends_type_of\",\n                      \"fdate\", \"fget\", \"fgetc\", \"float\", \"floor\",\n                      \"flush\", \"fnum\", \"fputc\", \"fput\", \"fraction\",\n                      \"fseek\", \"fstat\", \"ftell\", \"gerror\", \"getarg\",\n                      \"get_command\", \"get_command_argument\",\n                      \"get_environment_variable\", \"getcwd\",\n                      \"getenv\", \"getgid\", \"getlog\", \"getpid\",\n                      \"getuid\", \"gmtime\", \"hostnm\", \"huge\", \"iabs\",\n                      \"iachar\", \"iand\", \"iargc\", \"ibclr\", \"ibits\",\n                      \"ibset\", \"ichar\", \"idate\", \"idim\", \"idint\",\n                      \"idnint\", \"ieor\", \"ierrno\", \"ifix\", \"imag\",\n                      \"imagpart\", \"index\", \"int\", \"ior\", \"irand\",\n                      \"isatty\", \"ishft\", \"ishftc\", \"isign\",\n                      \"iso_c_binding\", \"is_iostat_end\", \"is_iostat_eor\",\n                      \"itime\", \"kill\", \"kind\", \"lbound\", \"len\", \"len_trim\",\n                      \"lge\", \"lgt\", \"link\", \"lle\", \"llt\", \"lnblnk\", \"loc\",\n                      \"log\", \"logical\", \"long\", \"lshift\", \"lstat\", \"ltime\",\n                      \"matmul\", \"max\", \"maxexponent\", \"maxloc\", \"maxval\",\n                      \"mclock\", \"merge\", \"move_alloc\", \"min\", \"minexponent\",\n                      \"minloc\", \"minval\", \"mod\", \"modulo\", \"mvbits\",\n                      \"nearest\", \"new_line\", \"nint\", \"not\", \"or\", \"pack\",\n                      \"perror\", \"precision\", \"present\", \"product\", \"radix\",\n                      \"rand\", \"random_number\", \"random_seed\", \"range\",\n                      \"real\", \"realpart\", \"rename\", \"repeat\", \"reshape\",\n                      \"rrspacing\", \"rshift\", \"same_type_as\", \"scale\",\n                      \"scan\", \"second\", \"selected_int_kind\",\n                      \"selected_real_kind\", \"set_exponent\", \"shape\",\n                      \"short\", \"sign\", \"signal\", \"sinh\", \"sin\", \"sleep\",\n                      \"sngl\", \"spacing\", \"spread\", \"sqrt\", \"srand\", \"stat\",\n                      \"sum\", \"symlnk\", \"system\", \"system_clock\", \"tan\",\n                      \"tanh\", \"time\", \"tiny\", \"transfer\", \"transpose\",\n                      \"trim\", \"ttynam\", \"ubound\", \"umask\", \"unlink\",\n                      \"unpack\", \"verify\", \"xor\", \"zabs\", \"zcos\", \"zexp\",\n                      \"zlog\", \"zsin\", \"zsqrt\"]);\n\nvar dataTypes =  words([\"c_bool\", \"c_char\", \"c_double\", \"c_double_complex\",\n                        \"c_float\", \"c_float_complex\", \"c_funptr\", \"c_int\",\n                        \"c_int16_t\", \"c_int32_t\", \"c_int64_t\", \"c_int8_t\",\n                        \"c_int_fast16_t\", \"c_int_fast32_t\", \"c_int_fast64_t\",\n                        \"c_int_fast8_t\", \"c_int_least16_t\", \"c_int_least32_t\",\n                        \"c_int_least64_t\", \"c_int_least8_t\", \"c_intmax_t\",\n                        \"c_intptr_t\", \"c_long\", \"c_long_double\",\n                        \"c_long_double_complex\", \"c_long_long\", \"c_ptr\",\n                        \"c_short\", \"c_signed_char\", \"c_size_t\", \"character\",\n                        \"complex\", \"double\", \"integer\", \"logical\", \"real\"]);\nvar isOperatorChar = /[+\\-*&=<>\\/\\:]/;\nvar litOperator = /^\\.(and|or|eq|lt|le|gt|ge|ne|not|eqv|neqv)\\./i;\n\nfunction tokenBase(stream, state) {\n\n  if (stream.match(litOperator)){\n    return 'operator';\n  }\n\n  var ch = stream.next();\n  if (ch == \"!\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]\\(\\),]/.test(ch)) {\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var word = stream.current().toLowerCase();\n\n  if (keywords.hasOwnProperty(word)){\n    return 'keyword';\n  }\n  if (builtins.hasOwnProperty(word) || dataTypes.hasOwnProperty(word)) {\n    return 'builtin';\n  }\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) state.tokenize = null;\n    return \"string\";\n  };\n}\n\n// Interface\n\nexport const fortran = {\n  name: \"fortran\",\n  startState: function() {\n    return {tokenize: null};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    return style;\n  }\n};\n\n"], "mappings": ";;;AAAA,SAAS,MAAM,OAAO;AACpB,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,SAAK,MAAM,CAAC,CAAC,IAAI;AAAA,EACnB;AACA,SAAO;AACT;AAEA,IAAI,WAAW,MAAM;AAAA,EACnB;AAAA,EAAY;AAAA,EAAU;AAAA,EAAe;AAAA,EACrC;AAAA,EAAS;AAAA,EAAU;AAAA,EAAgB;AAAA,EACnC;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACjC;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAC5B;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAC7B;AAAA,EAAU;AAAA,EAAY;AAAA,EAAa;AAAA,EACnC;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAU;AAAA,EAC/B;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAChC;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAa;AAAA,EACjC;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EAChC;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAY;AAAA,EAAU;AAAA,EAC1C;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAClC;AAAA,EAAU;AAAA,EAAY;AAAA,EACtB;AAAA,EAAmB;AAAA,EAAQ;AAAA,EAC3B;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAC/B;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC9B;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAC/B;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAa;AAAA,EACvC;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EACtC;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAM;AAAA,EAC9C;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAA,EAAS;AAAA,EACrC;AAAO,CAAC;AACV,IAAI,WAAW,MAAM;AAAA,EAAC;AAAA,EAAS;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EACnC;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAQ;AAAA,EACvC;AAAA,EAAO;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACpC;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAC/B;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EACvC;AAAA,EAAS;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EACtC;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EACpC;AAAA,EAAQ;AAAA,EAAS;AAAA,EACjB;AAAA,EAAW;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EACnC;AAAA,EAAY;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EACvC;AAAA,EAAY;AAAA,EAAS;AAAA,EAAgB;AAAA,EACrC;AAAA,EAAiB;AAAA,EAAe;AAAA,EAChC;AAAA,EAAW;AAAA,EAAe;AAAA,EAC1B;AAAA,EAAc;AAAA,EACd;AAAA,EAAoB;AAAA,EAAkB;AAAA,EACtC;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAC3B;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EACrC;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAC3C;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAC1C;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAChC;AAAA,EAAe;AAAA,EAAS;AAAA,EAAS;AAAA,EACjC;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAClC;AAAA,EAAW;AAAA,EAAW;AAAA,EAAO;AAAA,EAAQ;AAAA,EACrC;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAC3B;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EACnC;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAClC;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EACrC;AAAA,EAAe;AAAA,EACf;AAAA,EAA4B;AAAA,EAC5B;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAC9B;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EACtC;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EACpC;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EACnC;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EACpC;AAAA,EAAY;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EACnC;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAC7B;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAClC;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAC1C;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAC9C;AAAA,EAAO;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7C;AAAA,EAAU;AAAA,EAAO;AAAA,EAAe;AAAA,EAAU;AAAA,EAC1C;AAAA,EAAU;AAAA,EAAS;AAAA,EAAc;AAAA,EAAO;AAAA,EACxC;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAU;AAAA,EACrC;AAAA,EAAW;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAM;AAAA,EAC5C;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAC7C;AAAA,EAAQ;AAAA,EAAiB;AAAA,EAAe;AAAA,EACxC;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EACxC;AAAA,EAAa;AAAA,EAAU;AAAA,EAAgB;AAAA,EACvC;AAAA,EAAQ;AAAA,EAAU;AAAA,EAClB;AAAA,EAAsB;AAAA,EAAgB;AAAA,EACtC;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAO;AAAA,EAC1C;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC9C;AAAA,EAAO;AAAA,EAAU;AAAA,EAAU;AAAA,EAAgB;AAAA,EAC3C;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EACpC;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAS;AAAA,EACrC;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAC3C;AAAA,EAAQ;AAAA,EAAQ;AAAO,CAAC;AAE9C,IAAI,YAAa,MAAM;AAAA,EAAC;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EAChC;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAY;AAAA,EAC1C;AAAA,EAAa;AAAA,EAAa;AAAA,EAAa;AAAA,EACvC;AAAA,EAAkB;AAAA,EAAkB;AAAA,EACpC;AAAA,EAAiB;AAAA,EAAmB;AAAA,EACpC;AAAA,EAAmB;AAAA,EAAkB;AAAA,EACrC;AAAA,EAAc;AAAA,EAAU;AAAA,EACxB;AAAA,EAAyB;AAAA,EAAe;AAAA,EACxC;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAY;AAAA,EACxC;AAAA,EAAW;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAM,CAAC;AAC1E,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAElB,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,OAAO,MAAM,WAAW,GAAE;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,KAAK;AACb,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACA,MAAI,cAAc,KAAK,EAAE,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAS;AACzB,MAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AAExC,MAAI,SAAS,eAAe,IAAI,GAAE;AAChC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,eAAe,IAAI,KAAK,UAAU,eAAe,IAAI,GAAG;AACnE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,cAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,OAAO,CAAC;AAAS,YAAM,WAAW;AACtC,WAAO;AAAA,EACT;AACF;AAIO,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO,EAAC,UAAU,KAAI;AAAA,EACxB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS,aAAa,SAAS;AAAQ,aAAO;AAClD,WAAO;AAAA,EACT;AACF;", "names": []}