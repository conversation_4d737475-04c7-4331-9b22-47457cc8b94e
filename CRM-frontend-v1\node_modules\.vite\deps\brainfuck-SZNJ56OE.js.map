{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/brainfuck.js"], "sourcesContent": ["var reserve = \"><+-.,[]\".split(\"\");\n/*\n  comments can be either:\n  placed behind lines\n\n  +++    this is a comment\n\n  where reserved characters cannot be used\n  or in a loop\n  [\n  this is ok to use [ ] and stuff\n  ]\n  or preceded by #\n*/\nexport const brainfuck = {\n  name: \"brainfuck\",\n  startState: function() {\n    return {\n      commentLine: false,\n      left: 0,\n      right: 0,\n      commentLoop: false\n    }\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null\n    if(stream.sol()){\n      state.commentLine = false;\n    }\n    var ch = stream.next().toString();\n    if(reserve.indexOf(ch) !== -1){\n      if(state.commentLine === true){\n        if(stream.eol()){\n          state.commentLine = false;\n        }\n        return \"comment\";\n      }\n      if(ch === \"]\" || ch === \"[\"){\n        if(ch === \"[\"){\n          state.left++;\n        }\n        else{\n          state.right++;\n        }\n        return \"bracket\";\n      }\n      else if(ch === \"+\" || ch === \"-\"){\n        return \"keyword\";\n      }\n      else if(ch === \"<\" || ch === \">\"){\n        return \"atom\";\n      }\n      else if(ch === \".\" || ch === \",\"){\n        return \"def\";\n      }\n    }\n    else{\n      state.commentLine = true;\n      if(stream.eol()){\n        state.commentLine = false;\n      }\n      return \"comment\";\n    }\n    if(stream.eol()){\n      state.commentLine = false;\n    }\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,UAAU,WAAW,MAAM,EAAE;AAc1B,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAG,OAAO,IAAI,GAAE;AACd,YAAM,cAAc;AAAA,IACtB;AACA,QAAI,KAAK,OAAO,KAAK,EAAE,SAAS;AAChC,QAAG,QAAQ,QAAQ,EAAE,MAAM,IAAG;AAC5B,UAAG,MAAM,gBAAgB,MAAK;AAC5B,YAAG,OAAO,IAAI,GAAE;AACd,gBAAM,cAAc;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AACA,UAAG,OAAO,OAAO,OAAO,KAAI;AAC1B,YAAG,OAAO,KAAI;AACZ,gBAAM;AAAA,QACR,OACI;AACF,gBAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT,WACQ,OAAO,OAAO,OAAO,KAAI;AAC/B,eAAO;AAAA,MACT,WACQ,OAAO,OAAO,OAAO,KAAI;AAC/B,eAAO;AAAA,MACT,WACQ,OAAO,OAAO,OAAO,KAAI;AAC/B,eAAO;AAAA,MACT;AAAA,IACF,OACI;AACF,YAAM,cAAc;AACpB,UAAG,OAAO,IAAI,GAAE;AACd,cAAM,cAAc;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AACA,QAAG,OAAO,IAAI,GAAE;AACd,YAAM,cAAc;AAAA,IACtB;AAAA,EACF;AACF;", "names": []}