{"version": 3, "sources": ["../../@lezer/json/dist/index.js", "../../@codemirror/lang-json/dist/index.js"], "sourcesContent": ["import { LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\nconst jsonHighlighting = styleTags({\n  String: tags.string,\n  Number: tags.number,\n  \"True False\": tags.bool,\n  PropertyName: tags.propertyName,\n  Null: tags.null,\n  \", :\": tags.separator,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#ClOOQO'#Cr'#CrQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CtOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59W,59WO!iQPO,59WOVQPO,59QOqQPO'#CmO!nQPO,59`OOQO1G.k1G.kOVQPO'#CnO!vQPO,59aOOQO1G.r1G.rOOQO1G.l1G.lOOQO,59X,59XOOQO-E6k-E6kOOQO,59Y,59YOOQO-E6l-E6l\",\n  stateData: \"#O~OeOS~OQSORSOSSOTSOWQO_ROgPO~OVXOgUO~O^[O~PVO[^O~O]_OVhX~OVaO~O]bO^iX~O^dO~O]_OVha~O]bO^ia~O\",\n  goto: \"!kjPPPPPPkPPkqwPPPPk{!RPPP!XP!e!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R\",\n  nodeNames: \"⚠ JsonText True False Null Number String } { Object Property PropertyName : , ] [ Array\",\n  maxTerm: 25,\n  nodeProps: [\n    [\"isolate\", -2,6,11,\"\"],\n    [\"openedBy\", 7,\"{\",14,\"[\"],\n    [\"closedBy\", 8,\"}\",15,\"]\"]\n  ],\n  propSources: [jsonHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 2,\n  tokenData: \"(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oe~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Og~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zO]~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yO[~~'OO_~~'TO^~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~\",\n  tokenizers: [0],\n  topRules: {\"JsonText\":[0,1]},\n  tokenPrec: 0\n});\n\nexport { parser };\n", "import { parser } from '@lezer/json';\nimport { LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\n\n/**\nCalls\n[`JSON.parse`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse)\non the document and, if that throws an error, reports it as a\nsingle diagnostic.\n*/\nconst jsonParseLinter = () => (view) => {\n    try {\n        JSON.parse(view.state.doc.toString());\n    }\n    catch (e) {\n        if (!(e instanceof SyntaxError))\n            throw e;\n        const pos = getErrorPosition(e, view.state.doc);\n        return [{\n                from: pos,\n                message: e.message,\n                severity: 'error',\n                to: pos\n            }];\n    }\n    return [];\n};\nfunction getErrorPosition(error, doc) {\n    let m;\n    if (m = error.message.match(/at position (\\d+)/))\n        return Math.min(+m[1], doc.length);\n    if (m = error.message.match(/at line (\\d+) column (\\d+)/))\n        return Math.min(doc.line(+m[1]).from + (+m[2]) - 1, doc.length);\n    return 0;\n}\n\n/**\nA language provider that provides JSON parsing.\n*/\nconst jsonLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"json\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Object: /*@__PURE__*/continuedIndent({ except: /^\\s*\\}/ }),\n                Array: /*@__PURE__*/continuedIndent({ except: /^\\s*\\]/ })\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Object Array\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n        indentOnInput: /^\\s*[\\}\\]]$/\n    }\n});\n/**\nJSON language support.\n*/\nfunction json() {\n    return new LanguageSupport(jsonLanguage);\n}\n\nexport { json, jsonLanguage, jsonParseLinter };\n"], "mappings": ";;;;;;;;;;;;;;;AAGA,IAAM,mBAAmB,UAAU;AAAA,EACjC,QAAQ,KAAK;AAAA,EACb,QAAQ,KAAK;AAAA,EACb,cAAc,KAAK;AAAA,EACnB,cAAc,KAAK;AAAA,EACnB,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,IAAG,EAAE;AAAA,IACtB,CAAC,YAAY,GAAE,KAAI,IAAG,GAAG;AAAA,IACzB,CAAC,YAAY,GAAE,KAAI,IAAG,GAAG;AAAA,EAC3B;AAAA,EACA,aAAa,CAAC,gBAAgB;AAAA,EAC9B,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,CAAC;AAAA,EACd,UAAU,EAAC,YAAW,CAAC,GAAE,CAAC,EAAC;AAAA,EAC3B,WAAW;AACb,CAAC;;;ACzBD,IAAM,kBAAkB,MAAM,CAAC,SAAS;AACpC,MAAI;AACA,SAAK,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC;AAAA,EACxC,SACO,GAAG;AACN,QAAI,EAAE,aAAa;AACf,YAAM;AACV,UAAM,MAAM,iBAAiB,GAAG,KAAK,MAAM,GAAG;AAC9C,WAAO,CAAC;AAAA,MACA,MAAM;AAAA,MACN,SAAS,EAAE;AAAA,MACX,UAAU;AAAA,MACV,IAAI;AAAA,IACR,CAAC;AAAA,EACT;AACA,SAAO,CAAC;AACZ;AACA,SAAS,iBAAiB,OAAO,KAAK;AAClC,MAAI;AACJ,MAAI,IAAI,MAAM,QAAQ,MAAM,mBAAmB;AAC3C,WAAO,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM;AACrC,MAAI,IAAI,MAAM,QAAQ,MAAM,4BAA4B;AACpD,WAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,OAAQ,CAAC,EAAE,CAAC,IAAK,GAAG,IAAI,MAAM;AAClE,SAAO;AACX;AAKA,IAAM,eAA4B,WAAW,OAAO;AAAA,EAChD,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,QAAqB,gBAAgB,EAAE,QAAQ,SAAS,CAAC;AAAA,QACzD,OAAoB,gBAAgB,EAAE,QAAQ,SAAS,CAAC;AAAA,MAC5D,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,UAAU,CAAC,KAAK,KAAK,GAAG,EAAE;AAAA,IAC3C,eAAe;AAAA,EACnB;AACJ,CAAC;AAID,SAAS,OAAO;AACZ,SAAO,IAAI,gBAAgB,YAAY;AAC3C;", "names": []}