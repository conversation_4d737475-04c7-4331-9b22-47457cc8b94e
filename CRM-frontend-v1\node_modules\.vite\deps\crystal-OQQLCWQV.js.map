{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/crystal.js"], "sourcesContent": ["function wordRegExp(words, end) {\n  return new RegExp((end ? \"\" : \"^\") + \"(?:\" + words.join(\"|\") + \")\" + (end ? \"$\" : \"\\\\b\"));\n}\n\nfunction chain(tokenize, stream, state) {\n  state.tokenize.push(tokenize);\n  return tokenize(stream, state);\n}\n\nvar operators = /^(?:[-+/%|&^]|\\*\\*?|[<>]{2})/;\nvar conditionalOperators = /^(?:[=!]~|===|<=>|[<>=!]=?|[|&]{2}|~)/;\nvar indexingOperators = /^(?:\\[\\][?=]?)/;\nvar anotherOperators = /^(?:\\.(?:\\.{2})?|->|[?:])/;\nvar idents = /^[a-z_\\u009F-\\uFFFF][a-zA-Z0-9_\\u009F-\\uFFFF]*/;\nvar types = /^[A-Z_\\u009F-\\uFFFF][a-zA-Z0-9_\\u009F-\\uFFFF]*/;\nvar keywords = wordRegExp([\n  \"abstract\", \"alias\", \"as\", \"asm\", \"begin\", \"break\", \"case\", \"class\", \"def\", \"do\",\n  \"else\", \"elsif\", \"end\", \"ensure\", \"enum\", \"extend\", \"for\", \"fun\", \"if\",\n  \"include\", \"instance_sizeof\", \"lib\", \"macro\", \"module\", \"next\", \"of\", \"out\", \"pointerof\",\n  \"private\", \"protected\", \"rescue\", \"return\", \"require\", \"select\", \"sizeof\", \"struct\",\n  \"super\", \"then\", \"type\", \"typeof\", \"uninitialized\", \"union\", \"unless\", \"until\", \"when\", \"while\", \"with\",\n  \"yield\", \"__DIR__\", \"__END_LINE__\", \"__FILE__\", \"__LINE__\"\n]);\nvar atomWords = wordRegExp([\"true\", \"false\", \"nil\", \"self\"]);\nvar indentKeywordsArray = [\n  \"def\", \"fun\", \"macro\",\n  \"class\", \"module\", \"struct\", \"lib\", \"enum\", \"union\",\n  \"do\", \"for\"\n];\nvar indentKeywords = wordRegExp(indentKeywordsArray);\nvar indentExpressionKeywordsArray = [\"if\", \"unless\", \"case\", \"while\", \"until\", \"begin\", \"then\"];\nvar indentExpressionKeywords = wordRegExp(indentExpressionKeywordsArray);\nvar dedentKeywordsArray = [\"end\", \"else\", \"elsif\", \"rescue\", \"ensure\"];\nvar dedentKeywords = wordRegExp(dedentKeywordsArray);\nvar dedentPunctualsArray = [\"\\\\)\", \"\\\\}\", \"\\\\]\"];\nvar dedentPunctuals = new RegExp(\"^(?:\" + dedentPunctualsArray.join(\"|\") + \")$\");\nvar nextTokenizer = {\n  \"def\": tokenFollowIdent, \"fun\": tokenFollowIdent, \"macro\": tokenMacroDef,\n  \"class\": tokenFollowType, \"module\": tokenFollowType, \"struct\": tokenFollowType,\n  \"lib\": tokenFollowType, \"enum\": tokenFollowType, \"union\": tokenFollowType\n};\nvar matching = {\"[\": \"]\", \"{\": \"}\", \"(\": \")\", \"<\": \">\"};\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  // Macros\n  if (state.lastToken != \"\\\\\" && stream.match(\"{%\", false)) {\n    return chain(tokenMacro(\"%\", \"%\"), stream, state);\n  }\n\n  if (state.lastToken != \"\\\\\" && stream.match(\"{{\", false)) {\n    return chain(tokenMacro(\"{\", \"}\"), stream, state);\n  }\n\n  // Comments\n  if (stream.peek() == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Variables and keywords\n  var matched;\n  if (stream.match(idents)) {\n    stream.eat(/[?!]/);\n\n    matched = stream.current();\n    if (stream.eat(\":\")) {\n      return \"atom\";\n    } else if (state.lastToken == \".\") {\n      return \"property\";\n    } else if (keywords.test(matched)) {\n      if (indentKeywords.test(matched)) {\n        if (!(matched == \"fun\" && state.blocks.indexOf(\"lib\") >= 0) && !(matched == \"def\" && state.lastToken == \"abstract\")) {\n          state.blocks.push(matched);\n          state.currentIndent += 1;\n        }\n      } else if ((state.lastStyle == \"operator\" || !state.lastStyle) && indentExpressionKeywords.test(matched)) {\n        state.blocks.push(matched);\n        state.currentIndent += 1;\n      } else if (matched == \"end\") {\n        state.blocks.pop();\n        state.currentIndent -= 1;\n      }\n\n      if (nextTokenizer.hasOwnProperty(matched)) {\n        state.tokenize.push(nextTokenizer[matched]);\n      }\n\n      return \"keyword\";\n    } else if (atomWords.test(matched)) {\n      return \"atom\";\n    }\n\n    return \"variable\";\n  }\n\n  // Class variables and instance variables\n  // or attributes\n  if (stream.eat(\"@\")) {\n    if (stream.peek() == \"[\") {\n      return chain(tokenNest(\"[\", \"]\", \"meta\"), stream, state);\n    }\n\n    stream.eat(\"@\");\n    stream.match(idents) || stream.match(types);\n    return \"propertyName\";\n  }\n\n  // Constants and types\n  if (stream.match(types)) {\n    return \"tag\";\n  }\n\n  // Symbols or ':' operator\n  if (stream.eat(\":\")) {\n    if (stream.eat(\"\\\"\")) {\n      return chain(tokenQuote(\"\\\"\", \"atom\", false), stream, state);\n    } else if (stream.match(idents) || stream.match(types) ||\n               stream.match(operators) || stream.match(conditionalOperators) || stream.match(indexingOperators)) {\n      return \"atom\";\n    }\n    stream.eat(\":\");\n    return \"operator\";\n  }\n\n  // Strings\n  if (stream.eat(\"\\\"\")) {\n    return chain(tokenQuote(\"\\\"\", \"string\", true), stream, state);\n  }\n\n  // Strings or regexps or macro variables or '%' operator\n  if (stream.peek() == \"%\") {\n    var style = \"string\";\n    var embed = true;\n    var delim;\n\n    if (stream.match(\"%r\")) {\n      // Regexps\n      style = \"string.special\";\n      delim = stream.next();\n    } else if (stream.match(\"%w\")) {\n      embed = false;\n      delim = stream.next();\n    } else if (stream.match(\"%q\")) {\n      embed = false;\n      delim = stream.next();\n    } else {\n      if(delim = stream.match(/^%([^\\w\\s=])/)) {\n        delim = delim[1];\n      } else if (stream.match(/^%[a-zA-Z_\\u009F-\\uFFFF][\\w\\u009F-\\uFFFF]*/)) {\n        // Macro variables\n        return \"meta\";\n      } else if (stream.eat('%')) {\n        // '%' operator\n        return \"operator\";\n      }\n    }\n\n    if (matching.hasOwnProperty(delim)) {\n      delim = matching[delim];\n    }\n    return chain(tokenQuote(delim, style, embed), stream, state);\n  }\n\n  // Here Docs\n  if (matched = stream.match(/^<<-('?)([A-Z]\\w*)\\1/)) {\n    return chain(tokenHereDoc(matched[2], !matched[1]), stream, state)\n  }\n\n  // Characters\n  if (stream.eat(\"'\")) {\n    stream.match(/^(?:[^']|\\\\(?:[befnrtv0'\"]|[0-7]{3}|u(?:[0-9a-fA-F]{4}|\\{[0-9a-fA-F]{1,6}\\})))/);\n    stream.eat(\"'\");\n    return \"atom\";\n  }\n\n  // Numbers\n  if (stream.eat(\"0\")) {\n    if (stream.eat(\"x\")) {\n      stream.match(/^[0-9a-fA-F_]+/);\n    } else if (stream.eat(\"o\")) {\n      stream.match(/^[0-7_]+/);\n    } else if (stream.eat(\"b\")) {\n      stream.match(/^[01_]+/);\n    }\n    return \"number\";\n  }\n\n  if (stream.eat(/^\\d/)) {\n    stream.match(/^[\\d_]*(?:\\.[\\d_]+)?(?:[eE][+-]?\\d+)?/);\n    return \"number\";\n  }\n\n  // Operators\n  if (stream.match(operators)) {\n    stream.eat(\"=\"); // Operators can follow assign symbol.\n    return \"operator\";\n  }\n\n  if (stream.match(conditionalOperators) || stream.match(anotherOperators)) {\n    return \"operator\";\n  }\n\n  // Parens and braces\n  if (matched = stream.match(/[({[]/, false)) {\n    matched = matched[0];\n    return chain(tokenNest(matched, matching[matched], null), stream, state);\n  }\n\n  // Escapes\n  if (stream.eat(\"\\\\\")) {\n    stream.next();\n    return \"meta\";\n  }\n\n  stream.next();\n  return null;\n}\n\nfunction tokenNest(begin, end, style, started) {\n  return function (stream, state) {\n    if (!started && stream.match(begin)) {\n      state.tokenize[state.tokenize.length - 1] = tokenNest(begin, end, style, true);\n      state.currentIndent += 1;\n      return style;\n    }\n\n    var nextStyle = tokenBase(stream, state);\n    if (stream.current() === end) {\n      state.tokenize.pop();\n      state.currentIndent -= 1;\n      nextStyle = style;\n    }\n\n    return nextStyle;\n  };\n}\n\nfunction tokenMacro(begin, end, started) {\n  return function (stream, state) {\n    if (!started && stream.match(\"{\" + begin)) {\n      state.currentIndent += 1;\n      state.tokenize[state.tokenize.length - 1] = tokenMacro(begin, end, true);\n      return \"meta\";\n    }\n\n    if (stream.match(end + \"}\")) {\n      state.currentIndent -= 1;\n      state.tokenize.pop();\n      return \"meta\";\n    }\n\n    return tokenBase(stream, state);\n  };\n}\n\nfunction tokenMacroDef(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var matched;\n  if (matched = stream.match(idents)) {\n    if (matched == \"def\") {\n      return \"keyword\";\n    }\n    stream.eat(/[?!]/);\n  }\n\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenFollowIdent(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if (stream.match(idents)) {\n    stream.eat(/[!?]/);\n  } else {\n    stream.match(operators) || stream.match(conditionalOperators) || stream.match(indexingOperators);\n  }\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenFollowType(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  stream.match(types);\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenQuote(end, style, embed) {\n  return function (stream, state) {\n    var escaped = false;\n\n    while (stream.peek()) {\n      if (!escaped) {\n        if (stream.match(\"{%\", false)) {\n          state.tokenize.push(tokenMacro(\"%\", \"%\"));\n          return style;\n        }\n\n        if (stream.match(\"{{\", false)) {\n          state.tokenize.push(tokenMacro(\"{\", \"}\"));\n          return style;\n        }\n\n        if (embed && stream.match(\"#{\", false)) {\n          state.tokenize.push(tokenNest(\"#{\", \"}\", \"meta\"));\n          return style;\n        }\n\n        var ch = stream.next();\n\n        if (ch == end) {\n          state.tokenize.pop();\n          return style;\n        }\n\n        escaped = embed && ch == \"\\\\\";\n      } else {\n        stream.next();\n        escaped = false;\n      }\n    }\n\n    return style;\n  };\n}\n\nfunction tokenHereDoc(phrase, embed) {\n  return function (stream, state) {\n    if (stream.sol()) {\n      stream.eatSpace()\n      if (stream.match(phrase)) {\n        state.tokenize.pop();\n        return \"string\";\n      }\n    }\n\n    var escaped = false;\n    while (stream.peek()) {\n      if (!escaped) {\n        if (stream.match(\"{%\", false)) {\n          state.tokenize.push(tokenMacro(\"%\", \"%\"));\n          return \"string\";\n        }\n\n        if (stream.match(\"{{\", false)) {\n          state.tokenize.push(tokenMacro(\"{\", \"}\"));\n          return \"string\";\n        }\n\n        if (embed && stream.match(\"#{\", false)) {\n          state.tokenize.push(tokenNest(\"#{\", \"}\", \"meta\"));\n          return \"string\";\n        }\n\n        escaped = stream.next() == \"\\\\\" && embed;\n      } else {\n        stream.next();\n        escaped = false;\n      }\n    }\n\n    return \"string\";\n  }\n}\n\nexport const crystal = {\n  name: \"crystal\",\n  startState: function () {\n    return {\n      tokenize: [tokenBase],\n      currentIndent: 0,\n      lastToken: null,\n      lastStyle: null,\n      blocks: []\n    };\n  },\n\n  token: function (stream, state) {\n    var style = state.tokenize[state.tokenize.length - 1](stream, state);\n    var token = stream.current();\n\n    if (style && style != \"comment\") {\n      state.lastToken = token;\n      state.lastStyle = style;\n    }\n\n    return style;\n  },\n\n  indent: function (state, textAfter, cx) {\n    textAfter = textAfter.replace(/^\\s*(?:\\{%)?\\s*|\\s*(?:%\\})?\\s*$/g, \"\");\n\n    if (dedentKeywords.test(textAfter) || dedentPunctuals.test(textAfter)) {\n      return cx.unit * (state.currentIndent - 1);\n    }\n\n    return cx.unit * state.currentIndent;\n  },\n\n  languageData: {\n    indentOnInput: wordRegExp(dedentPunctualsArray.concat(dedentKeywordsArray), true),\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO,KAAK;AAC9B,SAAO,IAAI,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,GAAG,IAAI,OAAO,MAAM,MAAM,MAAM;AAC1F;AAEA,SAAS,MAAM,UAAU,QAAQ,OAAO;AACtC,QAAM,SAAS,KAAK,QAAQ;AAC5B,SAAO,SAAS,QAAQ,KAAK;AAC/B;AAEA,IAAI,YAAY;AAChB,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,WAAW,WAAW;AAAA,EACxB;AAAA,EAAY;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAC5E;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EAClE;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAO;AAAA,EAC7E;AAAA,EAAW;AAAA,EAAa;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAC3E;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EACjG;AAAA,EAAS;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAY;AAClD,CAAC;AACD,IAAI,YAAY,WAAW,CAAC,QAAQ,SAAS,OAAO,MAAM,CAAC;AAC3D,IAAI,sBAAsB;AAAA,EACxB;AAAA,EAAO;AAAA,EAAO;AAAA,EACd;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAC5C;AAAA,EAAM;AACR;AACA,IAAI,iBAAiB,WAAW,mBAAmB;AACnD,IAAI,gCAAgC,CAAC,MAAM,UAAU,QAAQ,SAAS,SAAS,SAAS,MAAM;AAC9F,IAAI,2BAA2B,WAAW,6BAA6B;AACvE,IAAI,sBAAsB,CAAC,OAAO,QAAQ,SAAS,UAAU,QAAQ;AACrE,IAAI,iBAAiB,WAAW,mBAAmB;AACnD,IAAI,uBAAuB,CAAC,OAAO,OAAO,KAAK;AAC/C,IAAI,kBAAkB,IAAI,OAAO,SAAS,qBAAqB,KAAK,GAAG,IAAI,IAAI;AAC/E,IAAI,gBAAgB;AAAA,EAClB,OAAO;AAAA,EAAkB,OAAO;AAAA,EAAkB,SAAS;AAAA,EAC3D,SAAS;AAAA,EAAiB,UAAU;AAAA,EAAiB,UAAU;AAAA,EAC/D,OAAO;AAAA,EAAiB,QAAQ;AAAA,EAAiB,SAAS;AAC5D;AACA,IAAI,WAAW,EAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG;AAEtD,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,aAAa,QAAQ,OAAO,MAAM,MAAM,KAAK,GAAG;AACxD,WAAO,MAAM,WAAW,KAAK,GAAG,GAAG,QAAQ,KAAK;AAAA,EAClD;AAEA,MAAI,MAAM,aAAa,QAAQ,OAAO,MAAM,MAAM,KAAK,GAAG;AACxD,WAAO,MAAM,WAAW,KAAK,GAAG,GAAG,QAAQ,KAAK;AAAA,EAClD;AAGA,MAAI,OAAO,KAAK,KAAK,KAAK;AACxB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI;AACJ,MAAI,OAAO,MAAM,MAAM,GAAG;AACxB,WAAO,IAAI,MAAM;AAEjB,cAAU,OAAO,QAAQ;AACzB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO;AAAA,IACT,WAAW,MAAM,aAAa,KAAK;AACjC,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,OAAO,GAAG;AACjC,UAAI,eAAe,KAAK,OAAO,GAAG;AAChC,YAAI,EAAE,WAAW,SAAS,MAAM,OAAO,QAAQ,KAAK,KAAK,MAAM,EAAE,WAAW,SAAS,MAAM,aAAa,aAAa;AACnH,gBAAM,OAAO,KAAK,OAAO;AACzB,gBAAM,iBAAiB;AAAA,QACzB;AAAA,MACF,YAAY,MAAM,aAAa,cAAc,CAAC,MAAM,cAAc,yBAAyB,KAAK,OAAO,GAAG;AACxG,cAAM,OAAO,KAAK,OAAO;AACzB,cAAM,iBAAiB;AAAA,MACzB,WAAW,WAAW,OAAO;AAC3B,cAAM,OAAO,IAAI;AACjB,cAAM,iBAAiB;AAAA,MACzB;AAEA,UAAI,cAAc,eAAe,OAAO,GAAG;AACzC,cAAM,SAAS,KAAK,cAAc,OAAO,CAAC;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,OAAO,GAAG;AAClC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB,aAAO,MAAM,UAAU,KAAK,KAAK,MAAM,GAAG,QAAQ,KAAK;AAAA,IACzD;AAEA,WAAO,IAAI,GAAG;AACd,WAAO,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK;AAC1C,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,QAAI,OAAO,IAAI,GAAI,GAAG;AACpB,aAAO,MAAM,WAAW,KAAM,QAAQ,KAAK,GAAG,QAAQ,KAAK;AAAA,IAC7D,WAAW,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,KAC1C,OAAO,MAAM,SAAS,KAAK,OAAO,MAAM,oBAAoB,KAAK,OAAO,MAAM,iBAAiB,GAAG;AAC3G,aAAO;AAAA,IACT;AACA,WAAO,IAAI,GAAG;AACd,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,IAAI,GAAI,GAAG;AACpB,WAAO,MAAM,WAAW,KAAM,UAAU,IAAI,GAAG,QAAQ,KAAK;AAAA,EAC9D;AAGA,MAAI,OAAO,KAAK,KAAK,KAAK;AACxB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI;AAEJ,QAAI,OAAO,MAAM,IAAI,GAAG;AAEtB,cAAQ;AACR,cAAQ,OAAO,KAAK;AAAA,IACtB,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,cAAQ;AACR,cAAQ,OAAO,KAAK;AAAA,IACtB,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,cAAQ;AACR,cAAQ,OAAO,KAAK;AAAA,IACtB,OAAO;AACL,UAAG,QAAQ,OAAO,MAAM,cAAc,GAAG;AACvC,gBAAQ,MAAM,CAAC;AAAA,MACjB,WAAW,OAAO,MAAM,4CAA4C,GAAG;AAErE,eAAO;AAAA,MACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAE1B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,SAAS,eAAe,KAAK,GAAG;AAClC,cAAQ,SAAS,KAAK;AAAA,IACxB;AACA,WAAO,MAAM,WAAW,OAAO,OAAO,KAAK,GAAG,QAAQ,KAAK;AAAA,EAC7D;AAGA,MAAI,UAAU,OAAO,MAAM,sBAAsB,GAAG;AAClD,WAAO,MAAM,aAAa,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACnE;AAGA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,WAAO,MAAM,gFAAgF;AAC7F,WAAO,IAAI,GAAG;AACd,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,MAAM,gBAAgB;AAAA,IAC/B,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,MAAM,UAAU;AAAA,IACzB,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,MAAM,SAAS;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI,KAAK,GAAG;AACrB,WAAO,MAAM,uCAAuC;AACpD,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,WAAO,IAAI,GAAG;AACd,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,oBAAoB,KAAK,OAAO,MAAM,gBAAgB,GAAG;AACxE,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,OAAO,MAAM,SAAS,KAAK,GAAG;AAC1C,cAAU,QAAQ,CAAC;AACnB,WAAO,MAAM,UAAU,SAAS,SAAS,OAAO,GAAG,IAAI,GAAG,QAAQ,KAAK;AAAA,EACzE;AAGA,MAAI,OAAO,IAAI,IAAI,GAAG;AACpB,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,UAAU,OAAO,KAAK,OAAO,SAAS;AAC7C,SAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,CAAC,WAAW,OAAO,MAAM,KAAK,GAAG;AACnC,YAAM,SAAS,MAAM,SAAS,SAAS,CAAC,IAAI,UAAU,OAAO,KAAK,OAAO,IAAI;AAC7E,YAAM,iBAAiB;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,UAAU,QAAQ,KAAK;AACvC,QAAI,OAAO,QAAQ,MAAM,KAAK;AAC5B,YAAM,SAAS,IAAI;AACnB,YAAM,iBAAiB;AACvB,kBAAY;AAAA,IACd;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAW,OAAO,KAAK,SAAS;AACvC,SAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,CAAC,WAAW,OAAO,MAAM,MAAM,KAAK,GAAG;AACzC,YAAM,iBAAiB;AACvB,YAAM,SAAS,MAAM,SAAS,SAAS,CAAC,IAAI,WAAW,OAAO,KAAK,IAAI;AACvE,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,MAAM,GAAG,GAAG;AAC3B,YAAM,iBAAiB;AACvB,YAAM,SAAS,IAAI;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC;AACF;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI,UAAU,OAAO,MAAM,MAAM,GAAG;AAClC,QAAI,WAAW,OAAO;AACpB,aAAO;AAAA,IACT;AACA,WAAO,IAAI,MAAM;AAAA,EACnB;AAEA,QAAM,SAAS,IAAI;AACnB,SAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,MAAM,GAAG;AACxB,WAAO,IAAI,MAAM;AAAA,EACnB,OAAO;AACL,WAAO,MAAM,SAAS,KAAK,OAAO,MAAM,oBAAoB,KAAK,OAAO,MAAM,iBAAiB;AAAA,EACjG;AACA,QAAM,SAAS,IAAI;AACnB,SAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,KAAK;AAClB,QAAM,SAAS,IAAI;AACnB,SAAO;AACT;AAEA,SAAS,WAAW,KAAK,OAAO,OAAO;AACrC,SAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,UAAU;AAEd,WAAO,OAAO,KAAK,GAAG;AACpB,UAAI,CAAC,SAAS;AACZ,YAAI,OAAO,MAAM,MAAM,KAAK,GAAG;AAC7B,gBAAM,SAAS,KAAK,WAAW,KAAK,GAAG,CAAC;AACxC,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,MAAM,MAAM,KAAK,GAAG;AAC7B,gBAAM,SAAS,KAAK,WAAW,KAAK,GAAG,CAAC;AACxC,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,OAAO,MAAM,MAAM,KAAK,GAAG;AACtC,gBAAM,SAAS,KAAK,UAAU,MAAM,KAAK,MAAM,CAAC;AAChD,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,OAAO,KAAK;AAErB,YAAI,MAAM,KAAK;AACb,gBAAM,SAAS,IAAI;AACnB,iBAAO;AAAA,QACT;AAEA,kBAAU,SAAS,MAAM;AAAA,MAC3B,OAAO;AACL,eAAO,KAAK;AACZ,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,SAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,IAAI,GAAG;AAChB,aAAO,SAAS;AAChB,UAAI,OAAO,MAAM,MAAM,GAAG;AACxB,cAAM,SAAS,IAAI;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,UAAU;AACd,WAAO,OAAO,KAAK,GAAG;AACpB,UAAI,CAAC,SAAS;AACZ,YAAI,OAAO,MAAM,MAAM,KAAK,GAAG;AAC7B,gBAAM,SAAS,KAAK,WAAW,KAAK,GAAG,CAAC;AACxC,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,MAAM,MAAM,KAAK,GAAG;AAC7B,gBAAM,SAAS,KAAK,WAAW,KAAK,GAAG,CAAC;AACxC,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,OAAO,MAAM,MAAM,KAAK,GAAG;AACtC,gBAAM,SAAS,KAAK,UAAU,MAAM,KAAK,MAAM,CAAC;AAChD,iBAAO;AAAA,QACT;AAEA,kBAAU,OAAO,KAAK,KAAK,QAAQ;AAAA,MACrC,OAAO;AACL,eAAO,KAAK;AACZ,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,UAAU,CAAC,SAAS;AAAA,MACpB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,QAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAAA,EAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,EAAE,QAAQ,KAAK;AACnE,QAAI,QAAQ,OAAO,QAAQ;AAE3B,QAAI,SAAS,SAAS,WAAW;AAC/B,YAAM,YAAY;AAClB,YAAM,YAAY;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAU,OAAO,WAAW,IAAI;AACtC,gBAAY,UAAU,QAAQ,oCAAoC,EAAE;AAEpE,QAAI,eAAe,KAAK,SAAS,KAAK,gBAAgB,KAAK,SAAS,GAAG;AACrE,aAAO,GAAG,QAAQ,MAAM,gBAAgB;AAAA,IAC1C;AAEA,WAAO,GAAG,OAAO,MAAM;AAAA,EACzB;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,WAAW,qBAAqB,OAAO,mBAAmB,GAAG,IAAI;AAAA,IAChF,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}